# 分布式调度器使用指南

## 概述

基于 Redis 的分布式 gocron 调度器，支持多 Pod 环境下的任务调度，确保同一时间只有一个实例执行特定任务。

## 核心特性

### 🔒 分布式锁机制
- 使用 Redis `SETNX` 命令实现分布式锁
- Lua 脚本确保锁释放的原子性
- 自动锁超时防止死锁

### 🏷️ 实例标识
- 每个调度器实例有唯一 ID
- 便于日志追踪和问题排查

### ⚡ 高可用性
- 多实例部署，单点故障自动切换
- 锁超时后其他实例自动接管

## 使用方式

### 1. 创建分布式调度器

```go
import "github.com/wlnil/life-log-be/internal/pkg/task"

// 创建调度器
scheduler := task.NewDistributedScheduler(redisClient, logger)

// 启动调度器
scheduler.Start()

// 停止调度器
defer scheduler.Stop()
```

### 2. 添加分布式任务

```go
// 配置任务
config := task.ScheduleConfig{
    Name:        "push_notification_task",    // 任务名称（用于分布式锁）
    Interval:    1 * time.Minute,            // 执行间隔
    LockTimeout: 2 * time.Minute,            // 锁超时时间
    TaskFunc: func() error {                 // 任务执行函数
        // 执行具体业务逻辑
        return processNotifications()
    },
}

// 添加任务到调度器
err := scheduler.AddTask(config)
```

### 3. 任务配置说明

| 参数 | 说明 | 建议值 |
|------|------|--------|
| `Name` | 任务唯一标识 | 使用有意义的名称 |
| `Interval` | 执行间隔 | 根据业务需求设置 |
| `LockTimeout` | 锁超时时间 | 比 Interval 长 50%-100% |
| `TaskFunc` | 执行函数 | 返回 error 类型 |

## 分布式锁机制

### 锁的生命周期

```
1. 尝试获取锁 (SETNX)
   ↓
2. 获取成功 → 执行任务
   ↓
3. 任务完成 → 释放锁 (Lua脚本)
   ↓
4. 其他实例可以获取锁
```

### 锁的 Redis 键格式

```
task_lock:{任务名称}
```

### 锁的值格式

```
{实例ID}:{时间戳}
```

## 日志示例

### 正常执行
```
INFO 启动分布式调度器，实例ID: a1b2c3d4e5f6g7h8
INFO 添加分布式任务: push_notification_task, 间隔: 1m0s
INFO 实例 a1b2c3d4e5f6g7h8 开始执行任务: push_notification_task
INFO 任务执行成功: push_notification_task, 耗时: 1.234s
```

### 锁竞争
```
DEBUG 任务 push_notification_task 正在其他实例执行，跳过
```

### 错误处理
```
ERROR 获取分布式锁失败: connection refused
ERROR 任务执行失败: push_notification_task, 错误: database connection timeout
```

## 最佳实践

### 1. 锁超时设置
```go
// ✅ 推荐：锁超时时间比执行间隔长
LockTimeout: 2 * time.Minute,  // 执行间隔的 2 倍
Interval:    1 * time.Minute,

// ❌ 避免：锁超时时间过短
LockTimeout: 30 * time.Second, // 可能导致任务重复执行
Interval:    1 * time.Minute,
```

### 2. 任务函数设计
```go
TaskFunc: func() error {
    // ✅ 推荐：包含错误处理
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
    defer cancel()
    
    if err := doWork(ctx); err != nil {
        return fmt.Errorf("任务执行失败: %w", err)
    }
    
    return nil
}
```

### 3. 监控和告警
- 监控任务执行频率
- 监控锁竞争情况
- 设置任务执行时间告警

## 故障排查

### 常见问题

1. **任务不执行**
   - 检查 Redis 连接
   - 检查调度器是否启动
   - 查看日志中的错误信息

2. **任务重复执行**
   - 检查锁超时时间设置
   - 确认 Redis 时间同步

3. **锁无法释放**
   - 检查 Lua 脚本执行
   - 查看 Redis 中的锁状态

### 调试命令

```bash
# 查看当前锁状态
redis-cli GET task_lock:push_notification_task

# 查看所有任务锁
redis-cli KEYS "task_lock:*"

# 手动释放锁（紧急情况）
redis-cli DEL task_lock:push_notification_task
```

## 性能考虑

- Redis 连接池配置
- 任务执行时间监控
- 锁竞争频率统计
- 网络延迟影响

## 扩展功能

可以基于当前框架扩展：
- 任务优先级
- 任务依赖关系
- 动态任务配置
- 任务执行历史记录
