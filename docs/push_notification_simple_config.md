# life-log-be 推送通知配置简化方案

## 📋 方案概述

基于life-log-be项目的推送通知优化设计，本文档提供了一个简化的配置管理方案。该方案使用config.proto配置文件来管理推送服务配置，避免过度工程化，保持简单性和可维护性，符合项目当前的配置管理模式。

## 🎯 设计原则

### 核心要求
- ✅ **使用config.proto配置文件**：将推送服务配置直接添加到现有的`internal/conf/conf.proto`文件中
- ✅ **精简化实现**：避免过度工程化，不需要复杂的加密、多级缓存、动态更新等高级功能
- ✅ **保持简单**：配置随应用启动加载，通过重启服务来更新配置
- ✅ **架构兼容**：确保与现有登录优化架构的兼容性

### 避免的复杂性
- ❌ 不需要数据库存储配置
- ❌ 不需要配置加密机制
- ❌ 不需要动态配置更新
- ❌ 不需要复杂的缓存策略
- ❌ 不需要配置管理API

## 🔧 具体实现

### 1. 扩展conf.proto配置文件

在`internal/conf/conf.proto`中扩展推送服务配置：

```protobuf
message JPush {
  string app_key = 1;
  string app_secret = 2;
  string api_endpoint = 3;
  int32 timeout = 4;
  int32 max_retry = 5;
  bool enabled = 6;
}

message APNs {
  string key_id = 1;
  string team_id = 2;
  string bundle_id = 3;
  string key_path = 4;
  bool is_product = 5;
  string api_endpoint = 6;
  int32 timeout = 7;
  int32 max_retry = 8;
  bool enabled = 9;
}

message FCM {
  string server_key = 1;
  string project_id = 2;
  string api_endpoint = 3;
  int32 timeout = 4;
  int32 max_retry = 5;
  bool enabled = 6;
}



message PushConfig {
  repeated string enabled_services = 1;
  string default_service = 2;
  bool enable_fallback = 3;
  int32 health_check_interval = 4;
  bool enable_domestic_routing = 5;
  string domestic_service = 6;
  string overseas_service = 7;
}

message Data {
  // ... 现有字段保持不变
  JPush jpush = 17;
  APNs apns = 18;
  FCM fcm = 19;
  PushConfig push_config = 20;
}
```

### 2. 更新config.yaml配置文件

在`configs/config.yaml`中添加推送服务配置：

```yaml
data:
  # ... 现有配置保持不变
  
  # 推送服务配置
  jpush:
    app_key: "your_jpush_app_key"
    app_secret: "your_jpush_app_secret"
    api_endpoint: "https://api.jpush.cn/v3/push"
    timeout: 30
    max_retry: 3
    enabled: true
  
  apns:
    key_id: "YK2PK93XPB"
    team_id: "3HYS7DKY3Z"
    bundle_id: "com.dxwvv.LifeHabit"
    key_path: "./certs/apns_key.p8"
    is_product: false
    api_endpoint: "https://api.development.push.apple.com"
    timeout: 30
    max_retry: 3
    enabled: true
  
  fcm:
    server_key: "your_fcm_server_key"
    project_id: "your_firebase_project_id"
    api_endpoint: "https://fcm.googleapis.com/fcm/send"
    timeout: 30
    max_retry: 3
    enabled: true
  

  
  push_config:
    enabled_services: ["jpush", "fcm", "apns"]
    default_service: "jpush"
    enable_fallback: true
    health_check_interval: 300
    enable_domestic_routing: true
    domestic_service: "jpush"
    overseas_service: "fcm"
```

### 3. 创建简化的推送配置管理器

创建`internal/biz/push_config_simple.go`文件：

```go
package biz

import (
    "context"
    "fmt"
    "strings"
    "time"

    "github.com/go-kratos/kratos/v2/log"
    "github.com/wlnil/life-log-be/internal/conf"
)

// PushConfigManager 简化的推送配置管理器
type PushConfigManager struct {
    config *conf.Data
    log    *log.Helper
}

// PushServiceConfig 推送服务配置
type PushServiceConfig struct {
    ServiceName string                 `json:"service_name"`
    Config      map[string]interface{} `json:"config"`
    Enabled     bool                   `json:"enabled"`
    Timeout     int32                  `json:"timeout"`
    MaxRetry    int32                  `json:"max_retry"`
}

// NewPushConfigManager 创建简化的推送配置管理器
func NewPushConfigManager(config *conf.Data, logger log.Logger) *PushConfigManager {
    return &PushConfigManager{
        config: config,
        log:    log.NewHelper(logger),
    }
}

// GetServiceConfig 获取推送服务配置
func (pcm *PushConfigManager) GetServiceConfig(ctx context.Context, serviceName string) (*PushServiceConfig, error) {
    switch strings.ToLower(serviceName) {
    case "jpush":
        return pcm.getJPushConfig(), nil
    case "apns":
        return pcm.getAPNsConfig(), nil
    case "fcm":
        return pcm.getFCMConfig(), nil
    default:
        return nil, fmt.Errorf("unsupported push service: %s", serviceName)
    }
}

// GetEnabledServices 获取启用的推送服务列表
func (pcm *PushConfigManager) GetEnabledServices() []string {
    if pcm.config.PushConfig == nil {
        return []string{"jpush"} // 默认启用极光推送
    }
    return pcm.config.PushConfig.EnabledServices
}

// GetDefaultService 获取默认推送服务
func (pcm *PushConfigManager) GetDefaultService() string {
    if pcm.config.PushConfig == nil {
        return "jpush"
    }
    if pcm.config.PushConfig.DefaultService == "" {
        return "jpush"
    }
    return pcm.config.PushConfig.DefaultService
}

// GetServiceForUser 根据用户信息获取推送服务
func (pcm *PushConfigManager) GetServiceForUser(user *User, platform string) string {
    // 如果没有启用智能路由，返回默认服务
    if pcm.config.PushConfig == nil || !pcm.config.PushConfig.EnableDomesticRouting {
        return pcm.GetDefaultService()
    }

    // 根据用户区域选择推送服务
    if user != nil && user.Region == "china" {
        if pcm.config.PushConfig.DomesticService != "" {
            return pcm.config.PushConfig.DomesticService
        }
        return "jpush"
    }

    // 海外用户根据平台选择
    switch strings.ToLower(platform) {
    case "ios":
        if pcm.isServiceEnabled("apns") {
            return "apns"
        }
    case "android":
        if pcm.isServiceEnabled("fcm") {
            return "fcm"
        }
    }

    // 备用服务
    if pcm.config.PushConfig.OverseasService != "" {
        return pcm.config.PushConfig.OverseasService
    }
    return "fcm"
}

// GetFallbackService 获取备用推送服务
func (pcm *PushConfigManager) GetFallbackService(currentService string) string {
    if pcm.config.PushConfig == nil || !pcm.config.PushConfig.EnableFallback {
        return ""
    }

    // 简单的备用策略
    enabledServices := pcm.GetEnabledServices()
    for _, service := range enabledServices {
        if service != currentService {
            return service
        }
    }
    return ""
}

// IsServiceEnabled 检查服务是否启用
func (pcm *PushConfigManager) IsServiceEnabled(serviceName string) bool {
    return pcm.isServiceEnabled(serviceName)
}

// LogConfigInfo 记录配置信息（用于调试）
func (pcm *PushConfigManager) LogConfigInfo() {
    enabledServices := pcm.GetEnabledServices()
    defaultService := pcm.GetDefaultService()
    
    pcm.log.Infof("推送服务配置加载完成:")
    pcm.log.Infof("- 启用的服务: %v", enabledServices)
    pcm.log.Infof("- 默认服务: %s", defaultService)
    
    if pcm.config.PushConfig != nil {
        pcm.log.Infof("- 启用备用服务: %v", pcm.config.PushConfig.EnableFallback)
        pcm.log.Infof("- 启用智能路由: %v", pcm.config.PushConfig.EnableDomesticRouting)
        if pcm.config.PushConfig.EnableDomesticRouting {
            pcm.log.Infof("- 国内服务: %s", pcm.config.PushConfig.DomesticService)
            pcm.log.Infof("- 海外服务: %s", pcm.config.PushConfig.OverseasService)
        }
    }
}

// 内部方法实现...
```

### 4. 集成到UserUsecase

在`internal/biz/user.go`中集成推送配置管理器：

```go
type UserUsecase struct {
    // ... 现有字段
    pushConfigManager *PushConfigManager
}

func NewUserUsecase(..., confData *conf.Data) *UserUsecase {
    uc := &UserUsecase{
        // ... 现有初始化
    }
    
    // 初始化推送配置管理器
    uc.pushConfigManager = NewPushConfigManager(confData, logger)
    
    // 记录配置信息
    uc.pushConfigManager.LogConfigInfo()
    
    return uc
}
```

### 5. 修改异步推送设置更新

在`internal/biz/async_operations.go`中修改推送设置更新逻辑：

```go
// updatePushSettingsAsync 异步更新推送设置
func (uc *UserUsecase) updatePushSettingsAsync(ctx context.Context, userID int32, req *v1.LoginRequest) error {
    // 只有在有推送相关参数时才更新
    if req.FcmToken == "" && req.ApnsToken == "" && req.JpushRegistrationId == "" {
        return nil
    }

    // 获取用户信息用于智能路由
    user := &User{ID: userID}
    if err := uc.repo.FindByID(ctx, user); err != nil {
        uc.log.Warnf("获取用户信息失败，使用默认推送服务: %v", err)
    }

    // 使用配置管理器确定推送服务
    var preferredService string
    if uc.pushConfigManager != nil {
        preferredService = uc.pushConfigManager.GetServiceForUser(user, req.Platform)
    } else {
        preferredService = uc.determinePushService(req.PushServiceType, req.Platform)
    }

    pushSetting := &UserPushSetting{
        UserID:                userID,
        PushPermissionGranted: req.PushPermissionGranted,
        PreferredPushService:  preferredService,
        PushChannel:           req.RecommendedPushChannel,
        FirebaseEnabled:       true,
        HabitReminderEnabled:  true, // 默认启用习惯提醒
    }

    // 更新推送设置
    return uc.repo.CreateOrUpdateUserPushSetting(ctx, pushSetting)
}
```

## 🚀 使用方式

### 1. 获取推送服务配置

```go
// 获取极光推送配置
jpushConfig, err := uc.pushConfigManager.GetServiceConfig(ctx, "jpush")
if err != nil {
    return err
}

// 使用配置
appKey := jpushConfig.Config["app_key"].(string)
appSecret := jpushConfig.Config["app_secret"].(string)
apiEndpoint := jpushConfig.Config["api_endpoint"].(string)
timeout := jpushConfig.Timeout
maxRetry := jpushConfig.MaxRetry
```

### 2. 智能选择推送服务

```go
// 根据用户信息和平台选择最佳推送服务
serviceName := uc.pushConfigManager.GetServiceForUser(user, platform)

// 获取备用服务（如果启用）
fallbackService := uc.pushConfigManager.GetFallbackService(serviceName)

// 检查服务是否启用
if !uc.pushConfigManager.IsServiceEnabled("fcm") {
    // 使用备用服务
    serviceName = uc.pushConfigManager.GetDefaultService()
}
```

### 3. 获取服务列表

```go
// 获取所有启用的推送服务
enabledServices := uc.pushConfigManager.GetEnabledServices()

// 获取默认推送服务
defaultService := uc.pushConfigManager.GetDefaultService()
```

## 📋 配置示例

### 基本配置（最小化）

```yaml
data:
  jpush:
    app_key: "your_jpush_app_key"
    app_secret: "your_jpush_app_secret"
    enabled: true
  
  push_config:
    enabled_services: ["jpush"]
    default_service: "jpush"
```

### 多服务配置

```yaml
data:
  jpush:
    app_key: "your_jpush_app_key"
    app_secret: "your_jpush_app_secret"
    enabled: true
  
  fcm:
    server_key: "your_fcm_server_key"
    project_id: "your_firebase_project_id"
    enabled: true
  
  apns:
    key_id: "your_key_id"
    team_id: "your_team_id"
    bundle_id: "com.example.app"
    key_path: "./certs/apns_key.p8"
    is_product: false
    enabled: true
  
  push_config:
    enabled_services: ["jpush", "fcm", "apns"]
    default_service: "jpush"
    enable_fallback: true
    enable_domestic_routing: true
    domestic_service: "jpush"
    overseas_service: "fcm"
```

### 智能路由配置

```yaml
data:
  # ... 推送服务配置
  
  push_config:
    enabled_services: ["jpush", "fcm", "apns"]
    default_service: "jpush"
    enable_fallback: true
    health_check_interval: 300
    enable_domestic_routing: true
    domestic_service: "jpush"      # 中国大陆用户使用极光推送
    overseas_service: "fcm"        # 海外用户使用FCM
```

## 🎯 方案优势

### 1. 简单性
- ✅ **配置即代码**：易于理解和维护
- ✅ **无复杂依赖**：不需要数据库、加密等额外组件
- ✅ **启动即可用**：配置随应用启动加载

### 2. 一致性
- ✅ **符合现有架构**：与项目当前的配置管理模式一致
- ✅ **类型安全**：Protobuf提供强类型检查
- ✅ **版本控制**：配置变更可通过Git跟踪

### 3. 实用性
- ✅ **智能路由**：支持基于用户区域和平台的服务选择
- ✅ **备用机制**：支持推送服务的自动备用
- ✅ **灵活配置**：支持多种推送服务的组合使用

### 4. 可维护性
- ✅ **集中管理**：所有推送配置集中在一个文件中
- ✅ **易于调试**：配置信息在启动时记录到日志
- ✅ **环境隔离**：不同环境使用不同的配置文件

## 📝 部署说明

### 1. 生成Protobuf代码

```bash
# 重新生成protobuf代码
make api
# 或者
buf generate
```

### 2. 更新配置文件

```bash
# 复制配置模板
cp configs/config.yaml.example configs/config.yaml

# 编辑配置文件，添加推送服务配置
vim configs/config.yaml
```

### 3. 重启应用

```bash
# 重启应用以加载新配置
./life-log-be -conf configs/config.yaml
```

## 🔍 故障排查

### 1. 配置加载问题

```bash
# 检查配置文件语法
yamllint configs/config.yaml

# 查看应用启动日志
tail -f logs/app.log | grep "推送服务配置"
```

### 2. 推送服务问题

```go
// 在代码中添加调试日志
uc.pushConfigManager.LogConfigInfo()

// 检查服务是否启用
if !uc.pushConfigManager.IsServiceEnabled("jpush") {
    uc.log.Warnf("极光推送服务未启用")
}
```

## 📋 总结

这个简化的推送配置管理方案完全符合项目要求：

1. **使用config.proto配置文件**：将推送服务配置直接添加到现有配置结构中
2. **精简化实现**：避免过度工程化，保持代码简洁
3. **保持简单**：配置随应用启动加载，通过重启更新
4. **架构兼容**：与现有登录优化架构无缝集成

通过这种方式，可以轻松地配置和管理多种推送服务，同时保持系统的简单性和可维护性。
