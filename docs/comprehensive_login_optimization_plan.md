# life-log-be 登录系统综合优化方案

## 🎯 概述

基于`complete_login_flow_analysis.md`和`login_optimization_implementation_plan.md`的分析，结合最新的Firebase认证优化和LoginRequest精简，本文档提供了一份全面的登录系统优化方案。

## 📊 当前状态评估

### 已完成的优化 ✅

#### 1. **Firebase统一认证架构** ✅
- **实现状态**：已完成
- **优化效果**：统一了Google和Apple登录流程
- **代码位置**：`internal/biz/user.go#loginByFirebase`、`internal/pkg/firebase/auth.go`
- **安全提升**：用户信息从服务器端token验证获取，消除了数据篡改风险

#### 2. **LoginRequest参数精简** ✅
- **实现状态**：已完成
- **优化效果**：从45个参数减少到21个参数（减少53%）
- **性能提升**：网络传输优化，请求体积显著减少
- **代码位置**：`api/user/v1/user.proto`

#### 3. **数据库结构优化** ✅
- **实现状态**：已完成
- **优化内容**：添加了`firebase_uid`字段和相关索引
- **迁移脚本**：`sql/optimize_firebase_auth.sql`

### 待实施的优化 🔄

根据原始分析文档，以下优化仍需实施：

## 🚀 核心性能优化方案

### 第一阶段：立即实施（1-3天）⭐⭐⭐

#### 1.1 数据库连接池优化
**目标**：减少连接建立时间，提升15%性能
**预期收益**：200-300ms

```go
// 修改 internal/data/data.go
func NewData(c *conf.Data, logger log.Logger) (*Data, func(), error) {
    // 数据库连接池优化
    db.SetMaxOpenConns(50)    // 当前: 10 → 优化: 50
    db.SetMaxIdleConns(20)    // 当前: 5  → 优化: 20
    db.SetConnMaxLifetime(30 * time.Minute)
    db.SetConnMaxIdleTime(10 * time.Minute)
    
    // Redis连接池优化
    rdb := redis.NewClient(&redis.Options{
        PoolSize:     20,              // 当前: 10 → 优化: 20
        MinIdleConns: 5,               // 新增
        MaxRetries:   3,               // 新增
        DialTimeout:  5 * time.Second,
        ReadTimeout:  3 * time.Second,
        WriteTimeout: 3 * time.Second,
    })
}
```

#### 1.2 数据库索引优化
**目标**：减少查询时间，提升20%性能
**预期收益**：300-500ms

```sql
-- 基于最新的Firebase认证架构优化索引
-- Firebase用户查询优化
CREATE INDEX idx_user_firebase_lookup ON tb_user(firebase_uid, status);

-- 传统登录查询优化
CREATE INDEX idx_user_login_lookup ON tb_user(phone, email, status);

-- Token查询优化
CREATE INDEX idx_user_token_active ON tb_user_token(user_id, platform, is_valid);

-- 用户设置查询优化
CREATE INDEX idx_user_setting_lookup ON tb_user_setting(user_id);

-- 审计日志查询优化
CREATE INDEX idx_audit_log_user_time ON tb_auth_audit_log(user_id, created_at);
```

### 第二阶段：SQL查询优化（3-7天）⭐⭐⭐⭐⭐

#### 2.1 合并用户查询逻辑
**问题分析**：当前登录流程存在重复查询
```go
// 当前问题：用户信息被查询3次
UC->>DB: FindBySearch(phone/email)  // SQL-1: 登录验证
UC->>DB: Update(user)               // SQL-2: 更新登录时间
UC->>DB: FindByID(userID)          // SQL-5: 缓存用户信息时再次查询
```

**优化方案**：
```go
// 新增文件：internal/biz/user_login_optimized.go
func (uc *UserUsecase) LoginOptimized(ctx context.Context, req *v1.LoginRequest) (*v1.LoginReply, error) {
    res := &v1.LoginReply{}
    
    // 参数校验
    if err := uc.validateLoginRequest(req); err != nil {
        return nil, err
    }
    
    u := &User{}
    var err error
    
    // 根据登录类型进行认证
    switch {
    case req.LoginType == int32(enums.UserLoginTypeGoogle) || req.LoginType == int32(enums.UserLoginTypeApple):
        err = uc.authenticateFirebase(ctx, u, req)
    case req.PassType == int32(enums.UserPassTypeCode):
        err = uc.authenticateByCode(ctx, u, req)
    default:
        err = uc.authenticateByPassword(ctx, u, req)
    }
    
    if err != nil {
        return res, err
    }
    
    // 批量执行数据库操作（减少往返次数）
    if err := uc.batchUpdateUserAndToken(ctx, u, req); err != nil {
        return nil, err
    }
    
    // 构建响应（使用已查询的用户对象，避免重复查询）
    return uc.buildLoginResponse(ctx, u, req)
}

// 批量数据库操作
func (uc *UserUsecase) batchUpdateUserAndToken(ctx context.Context, u *User, req *v1.LoginRequest) error {
    return uc.repo.Transaction(ctx, func(tx *gorm.DB) error {
        // 1. 更新用户登录信息
        now := time.Now().Unix()
        u.LastLoginAt = now
        u.LoginCount++
        if req.TimezoneName != "" {
            u.TimezoneName = req.TimezoneName
        }
        if req.TimezoneOffset != "" {
            u.TimezoneOffset = req.TimezoneOffset
        }
        
        if err := tx.Save(u).Error; err != nil {
            return err
        }
        
        // 2. 处理Token（在同一事务中）
        platform := tool.ParseUserAgent("")
        
        // 使旧Token失效
        if err := tx.Model(&UserToken{}).
            Where("user_id = ? AND platform = ?", u.ID, platform).
            Update("is_valid", false).Error; err != nil {
            return err
        }
        
        // 创建新Token
        expireAt := time.Now().Add(time.Second * time.Duration(auth.Secret.ExpireAt))
        token, err := auth.GenerateToken(u.ID, expireAt)
        if err != nil {
            return err
        }
        
        ut := &UserToken{
            UserID:   u.ID,
            Token:    token,
            Device:   "",
            ExpireAt: expireAt.Unix(),
            Platform: platform,
            IsValid:  true,
        }
        
        return tx.Create(ut).Error
    })
}
```

**预期效果**：减少3-4个SQL查询，节省750-1000ms

#### 2.2 异步处理优化
**目标**：将非核心操作异步化，提升60%性能
**预期收益**：1200-1500ms

```go
func (uc *UserUsecase) LoginOptimized(ctx context.Context, req *v1.LoginRequest) (*v1.LoginReply, error) {
    // 核心登录逻辑（同步，必须成功）
    user, token, err := uc.performCoreLogin(ctx, req)
    if err != nil {
        return nil, err
    }
    
    // 立即构建并返回响应
    response := uc.buildQuickResponse(user, token)
    
    // 非核心操作异步执行
    go uc.performAsyncOperations(context.Background(), user, req)
    
    return response, nil
}

func (uc *UserUsecase) performAsyncOperations(ctx context.Context, user *User, req *v1.LoginRequest) {
    // 1. 缓存用户信息
    if _, err := uc.commonUseCase.CacheUserInfo(ctx, user.ID, user, nil); err != nil {
        uc.log.Errorf("缓存用户信息失败: %v", err)
    }
    
    // 2. 查询并缓存用户设置
    us := &UserSetting{UserID: user.ID}
    if err := uc.repo.GetUserSetting(ctx, us); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
        uc.log.Errorf("查询用户设置失败: %v", err)
    }
    
    // 3. 记录审计日志
    if uc.auditHelper != nil {
        uc.auditHelper.LogLogin(user.ID, req, true, "", "")
    }
    
    // 4. 新用户初始化（如果需要）
    if user.CreatedAt == user.UpdatedAt { // 新创建的用户
        uc.initializeNewUser(ctx, user)
    }
    
    // 5. 预加载常用数据
    uc.preloadUserData(ctx, user.ID)
}
```

### 第三阶段：缓存优化（7-14天）⭐⭐⭐⭐

#### 3.1 多级缓存实现
**目标**：减少Redis查询，提升30%性能
**预期收益**：400-600ms

```go
// 新增文件：internal/pkg/cache/local_cache.go
package cache

import (
    "sync"
    "time"
)

type LocalCache struct {
    data   sync.Map
    expire sync.Map
}

type CacheItem struct {
    Value     interface{}
    ExpireAt  time.Time
}

func NewLocalCache() *LocalCache {
    lc := &LocalCache{}
    // 启动清理协程
    go lc.cleanup()
    return lc
}

// 优化后的用户信息获取
func (c *CommonUsecase) GetUserInfoByUserIDOptimized(ctx context.Context, userID int32) (UserInfo, error) {
    cacheKey := fmt.Sprintf("user_info_%d", userID)
    
    // 1. 本地缓存查询（0ms延迟）
    if value, ok := c.localCache.Get(cacheKey); ok {
        return value.(UserInfo), nil
    }
    
    // 2. Redis缓存查询（150ms延迟）
    redisKey := fmt.Sprintf("%v%v", enums.RedisUserInfoPrefix, userID)
    if cacheUser, err := cache.RedisClient.Get(ctx, redisKey).Result(); err == nil {
        var userInfo UserInfo
        if err := json.Unmarshal([]byte(cacheUser), &userInfo); err == nil {
            // 写入本地缓存
            c.localCache.Set(cacheKey, userInfo, 5*time.Minute)
            return userInfo, nil
        }
    }
    
    // 3. 数据库查询（250ms延迟）
    userInfo, err := c.getUserInfoFromDatabase(ctx, userID)
    if err != nil {
        return UserInfo{}, err
    }
    
    // 写入两级缓存
    c.localCache.Set(cacheKey, userInfo, 5*time.Minute)
    c.cacheUserInfoToRedis(ctx, userID, userInfo)
    
    return userInfo, nil
}
```

#### 3.2 缓存预热机制
**目标**：提升后续API响应速度

```go
func (uc *UserUsecase) preloadUserData(ctx context.Context, userID int32) {
    // 预加载用户习惯数据
    go func() {
        if habits, err := uc.habitUsecase.GetUserHabits(ctx, userID); err == nil {
            uc.cacheUserHabits(ctx, userID, habits)
        }
    }()
    
    // 预加载用户统计数据
    go func() {
        if stats, err := uc.statsUsecase.GetUserStats(ctx, userID); err == nil {
            uc.cacheUserStats(ctx, userID, stats)
        }
    }()
}
```

## 📊 性能提升预期

### 基于最新代码的性能分析

#### 当前性能基线（已优化后）
- **Firebase登录**：约1800-2200ms（已减少Firebase参数处理时间）
- **密码登录**：约1600-2000ms（已减少参数验证时间）
- **SQL查询次数**：5-6次（已减少冗余查询）

#### 进一步优化目标
| 优化项目 | 当前耗时 | 优化后耗时 | 节省时间 | 实施难度 |
|----------|----------|------------|----------|----------|
| 连接池优化 | 200ms | 100ms | 100ms | 低 |
| 数据库索引 | 300ms | 150ms | 150ms | 低 |
| SQL查询合并 | 1250ms | 500ms | 750ms | 中 |
| 异步处理 | 800ms | 200ms | 600ms | 中 |
| 多级缓存 | 300ms | 50ms | 250ms | 中 |
| **总计** | **2850ms** | **1000ms** | **1850ms** | - |

### 最终性能目标
- **Firebase登录**：2200ms → 800ms（提升64%）
- **密码登录**：2000ms → 700ms（提升65%）
- **SQL查询次数**：5-6次 → 2-3次（减少50%）
- **缓存命中率**：60% → 85%（提升42%）

## 🎯 实施优先级

### 高优先级（立即实施）
1. **连接池优化** - 1天，低风险，立即见效
2. **数据库索引** - 1天，低风险，显著提升
3. **SQL查询合并** - 3天，中风险，最大收益

### 中优先级（1-2周）
4. **异步处理优化** - 3天，低风险，用户体验提升
5. **多级缓存** - 7天，中风险，长期收益

### 低优先级（长期规划）
6. **读写分离** - 1个月，高风险，架构级优化
7. **CDN加速** - 2周，低风险，间接提升

## 🔍 监控和验证

### 性能监控指标
```go
// 基于最新代码的监控指标
var (
    LoginDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "login_duration_seconds",
            Help: "Login request duration",
            Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0},
        },
        []string{"login_type", "firebase_provider", "status"},
    )
    
    FirebaseTokenVerificationDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "firebase_token_verification_duration_seconds",
            Help: "Firebase token verification duration",
        },
        []string{"provider"},
    )
    
    DatabaseQueryDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "database_query_duration_seconds",
            Help: "Database query duration",
        },
        []string{"operation", "table", "optimized"},
    )
)
```

### A/B测试方案
```go
func (uc *UserUsecase) Login(ctx context.Context, req *v1.LoginRequest) (*v1.LoginReply, error) {
    // 基于用户ID进行A/B测试
    if uc.shouldUseOptimizedVersion(req) {
        return uc.LoginOptimized(ctx, req)
    }
    return uc.LoginLegacy(ctx, req)
}

func (uc *UserUsecase) shouldUseOptimizedVersion(req *v1.LoginRequest) bool {
    // 基于设备ID进行分流，逐步增加优化版本的比例
    hash := crc32.ChecksumIEEE([]byte(req.DeviceId))
    return hash%100 < uc.optimizedVersionPercentage // 从10%开始，逐步增加到100%
}
```

## 🎉 总结

基于最新的Firebase认证优化和LoginRequest精简，我们已经完成了重要的基础优化。接下来的核心优化重点是：

1. **SQL查询优化**：通过合并查询和事务优化，减少数据库往返次数
2. **异步处理**：将非核心操作异步化，提升响应速度
3. **多级缓存**：实现本地缓存+Redis缓存，减少网络延迟
4. **连接池优化**：提升数据库和Redis连接效率

通过这些优化，预计可以将登录响应时间从当前的2000-2200ms降至700-800ms，实现60%以上的性能提升。

## 📋 具体实施步骤

### 第一步：立即实施的基础优化（Day 1-3）

#### 1.1 连接池优化实施
```bash
# 1. 修改配置文件
vim configs/config.yaml

# 2. 更新数据库连接配置
data:
  database:
    max_open_conns: 50
    max_idle_conns: 20
    conn_max_lifetime: 30m
    conn_max_idle_time: 10m
  redis:
    pool_size: 20
    min_idle_conns: 5
    max_retries: 3
```

#### 1.2 数据库索引优化脚本
```sql
-- 创建文件：sql/performance_indexes.sql
-- Firebase认证优化索引
CREATE INDEX IF NOT EXISTS idx_user_firebase_lookup
ON tb_user(firebase_uid, status)
WHERE firebase_uid IS NOT NULL;

-- 传统登录优化索引
CREATE INDEX IF NOT EXISTS idx_user_phone_status
ON tb_user(phone, status)
WHERE phone IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_user_email_status
ON tb_user(email, status)
WHERE email IS NOT NULL;

-- Token查询优化
CREATE INDEX IF NOT EXISTS idx_user_token_lookup
ON tb_user_token(user_id, platform, is_valid, expire_at);

-- 用户设置查询优化
CREATE INDEX IF NOT EXISTS idx_user_setting_user_id
ON tb_user_setting(user_id);

-- 审计日志查询优化
CREATE INDEX IF NOT EXISTS idx_audit_log_user_time
ON tb_auth_audit_log(user_id, created_at DESC);

-- 验证索引效果
ANALYZE TABLE tb_user, tb_user_token, tb_user_setting, tb_auth_audit_log;
```

### 第二步：核心SQL优化（Day 4-7）

#### 2.1 创建优化版本的登录方法
```go
// 创建文件：internal/biz/user_login_optimized.go
package biz

import (
    "context"
    "time"
    "gorm.io/gorm"
    v1 "github.com/wlnil/life-log-be/api/user/v1"
)

// LoginOptimized 优化版本的登录方法
func (uc *UserUsecase) LoginOptimized(ctx context.Context, req *v1.LoginRequest) (*v1.LoginReply, error) {
    startTime := time.Now()
    defer func() {
        // 记录性能指标
        LoginDuration.WithLabelValues(
            fmt.Sprintf("%d", req.LoginType),
            "optimized",
            "success",
        ).Observe(time.Since(startTime).Seconds())
    }()

    // 1. 参数校验（复用现有逻辑）
    if err := uc.validateLoginRequest(req); err != nil {
        return nil, err
    }

    // 2. 执行核心登录逻辑
    user, userToken, err := uc.performCoreLoginOptimized(ctx, req)
    if err != nil {
        return nil, err
    }

    // 3. 立即构建响应（避免额外查询）
    response := uc.buildLoginResponseOptimized(user, userToken)

    // 4. 异步执行非核心操作
    go uc.performAsyncOperationsOptimized(context.Background(), user, req)

    return response, nil
}

// performCoreLoginOptimized 核心登录逻辑优化
func (uc *UserUsecase) performCoreLoginOptimized(ctx context.Context, req *v1.LoginRequest) (*User, *UserToken, error) {
    var user *User
    var err error

    // 根据登录类型进行认证
    switch {
    case req.LoginType == int32(enums.UserLoginTypeGoogle) || req.LoginType == int32(enums.UserLoginTypeApple):
        user, err = uc.authenticateFirebaseOptimized(ctx, req)
    case req.PassType == int32(enums.UserPassTypeCode):
        user, err = uc.authenticateByCodeOptimized(ctx, req)
    default:
        user, err = uc.authenticateByPasswordOptimized(ctx, req)
    }

    if err != nil {
        return nil, nil, err
    }

    // 在事务中批量处理用户更新和Token创建
    var userToken *UserToken
    err = uc.repo.Transaction(ctx, func(tx *gorm.DB) error {
        // 更新用户登录信息
        if err := uc.updateUserLoginInfoInTx(tx, user, req); err != nil {
            return err
        }

        // 创建新Token（在同一事务中）
        token, err := uc.createUserTokenInTx(tx, user.ID, req)
        if err != nil {
            return err
        }
        userToken = token

        return nil
    })

    return user, userToken, err
}
```

#### 2.2 Firebase认证优化
```go
// authenticateFirebaseOptimized Firebase认证优化版本
func (uc *UserUsecase) authenticateFirebaseOptimized(ctx context.Context, req *v1.LoginRequest) (*User, error) {
    // 1. 验证Firebase ID Token（已优化，8秒超时）
    firebaseUser, err := uc.firebaseAuth.VerifyIDToken(ctx, req.ThirdPartyToken)
    if err != nil {
        uc.log.Errorf("【Firebase登录优化】验证Firebase token失败, err: %v", err)
        return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrThirdPartyTokenWrongMsg, nil))
    }

    // 2. 查找用户（使用优化索引）
    user := &User{}
    where := Where{"firebase_uid =": firebaseUser.UID, "status !=": enums.UserStatusDelete}

    err = uc.repo.FindBySearch(ctx, user, where)
    if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
        return nil, err
    }

    // 3. 用户不存在则创建，存在则直接返回（更新操作在事务中进行）
    if errors.Is(err, gorm.ErrRecordNotFound) {
        return uc.createFirebaseUserOptimized(ctx, firebaseUser, req)
    }

    // 4. 更新用户信息（在调用方的事务中进行）
    uc.syncFirebaseUserInfo(user, firebaseUser)
    return user, nil
}

// createFirebaseUserOptimized 优化的Firebase用户创建
func (uc *UserUsecase) createFirebaseUserOptimized(ctx context.Context, firebaseUser *firebase.FirebaseUser, req *v1.LoginRequest) (*User, error) {
    now := time.Now()
    nowUnix := now.Unix()

    user := &User{
        BasicModel: BasicModel{
            CreatedAt: nowUnix,
            UpdatedAt: nowUnix,
        },
        FirebaseUID:    firebaseUser.UID,
        Email:          firebaseUser.Email,
        Name:           uc.generateUserName(firebaseUser),
        AvatarUrl:      firebaseUser.Picture,
        EmailVerified:  firebaseUser.EmailVerified,
        Status:         enums.UserStatusNormal,
        LastLoginAt:    nowUnix,
        RoleType:       enums.UserRoleTypeNormal,
        TimezoneName:   uc.getTimezoneOrDefault(req.TimezoneName),
        TimezoneOffset: uc.getTimezoneOffsetOrDefault(req.TimezoneOffset),
        CountryCode:    req.CountryCode,
        Locale:         uc.getLocaleOrDefault(firebaseUser.Locale, ""),
        Region:         uc.determineUserRegion(req.CountryCode),
    }

    if err := uc.repo.Create(ctx, user); err != nil {
        return nil, err
    }

    // 异步初始化新用户数据（不阻塞登录响应）
    go uc.initializeNewUserAsync(context.Background(), user, firebaseUser)

    return user, nil
}
```

### 第三步：异步处理优化（Day 8-10）

#### 3.1 异步操作管理器
```go
// 创建文件：internal/biz/async_operations.go
package biz

import (
    "context"
    "sync"
    "time"
)

type AsyncOperationManager struct {
    workerPool chan struct{}
    wg         sync.WaitGroup
    log        *log.Helper
}

func NewAsyncOperationManager(maxWorkers int, logger log.Logger) *AsyncOperationManager {
    return &AsyncOperationManager{
        workerPool: make(chan struct{}, maxWorkers),
        log:        log.NewHelper(logger),
    }
}

// performAsyncOperationsOptimized 优化的异步操作
func (uc *UserUsecase) performAsyncOperationsOptimized(ctx context.Context, user *User, req *v1.LoginRequest) {
    operations := []func(){
        // 1. 缓存用户信息
        func() {
            if err := uc.cacheUserInfoOptimized(ctx, user); err != nil {
                uc.log.Errorf("缓存用户信息失败: %v", err)
            }
        },

        // 2. 预加载用户设置
        func() {
            if err := uc.preloadUserSettings(ctx, user.ID); err != nil {
                uc.log.Errorf("预加载用户设置失败: %v", err)
            }
        },

        // 3. 记录审计日志
        func() {
            if uc.auditHelper != nil {
                uc.auditHelper.LogLogin(user.ID, req, true, "", "")
            }
        },

        // 4. 预加载常用数据
        func() {
            uc.preloadUserDataOptimized(ctx, user.ID)
        },
    }

    // 并发执行异步操作
    for _, operation := range operations {
        go func(op func()) {
            defer func() {
                if r := recover(); r != nil {
                    uc.log.Errorf("异步操作panic: %v", r)
                }
            }()
            op()
        }(operation)
    }
}
```

### 第四步：多级缓存实施（Day 11-14）

#### 4.1 本地缓存实现
```go
// 创建文件：internal/pkg/cache/multi_level_cache.go
package cache

import (
    "context"
    "encoding/json"
    "fmt"
    "sync"
    "time"
)

type MultiLevelCache struct {
    localCache  *LocalCache
    redisClient *redis.Client
    log         *log.Helper
}

func NewMultiLevelCache(redisClient *redis.Client, logger log.Logger) *MultiLevelCache {
    return &MultiLevelCache{
        localCache:  NewLocalCache(),
        redisClient: redisClient,
        log:         log.NewHelper(logger),
    }
}

// GetUserInfo 多级缓存获取用户信息
func (mlc *MultiLevelCache) GetUserInfo(ctx context.Context, userID int32) (*UserInfo, error) {
    cacheKey := fmt.Sprintf("user_info_%d", userID)

    // 1. 本地缓存查询（0ms延迟）
    if value, ok := mlc.localCache.Get(cacheKey); ok {
        CacheHitRate.WithLabelValues("local").Inc()
        return value.(*UserInfo), nil
    }

    // 2. Redis缓存查询（150ms延迟）
    redisKey := fmt.Sprintf("%v%v", enums.RedisUserInfoPrefix, userID)
    if cacheUser, err := mlc.redisClient.Get(ctx, redisKey).Result(); err == nil {
        var userInfo UserInfo
        if err := json.Unmarshal([]byte(cacheUser), &userInfo); err == nil {
            // 写入本地缓存
            mlc.localCache.Set(cacheKey, &userInfo, 5*time.Minute)
            CacheHitRate.WithLabelValues("redis").Inc()
            return &userInfo, nil
        }
    }

    // 3. 缓存未命中
    CacheHitRate.WithLabelValues("miss").Inc()
    return nil, fmt.Errorf("cache miss")
}

// SetUserInfo 设置多级缓存
func (mlc *MultiLevelCache) SetUserInfo(ctx context.Context, userID int32, userInfo *UserInfo) error {
    cacheKey := fmt.Sprintf("user_info_%d", userID)

    // 1. 设置本地缓存
    mlc.localCache.Set(cacheKey, userInfo, 5*time.Minute)

    // 2. 设置Redis缓存
    redisKey := fmt.Sprintf("%v%v", enums.RedisUserInfoPrefix, userID)
    userInfoBytes, err := json.Marshal(userInfo)
    if err != nil {
        return err
    }

    return mlc.redisClient.Set(ctx, redisKey, userInfoBytes, time.Hour).Err()
}
```

## 🔧 部署和验证

### 部署脚本
```bash
#!/bin/bash
# 创建文件：scripts/deploy_login_optimization.sh

echo "🚀 开始部署登录优化..."

# 1. 备份当前版本
echo "📦 备份当前版本..."
cp -r internal/biz internal/biz.backup.$(date +%Y%m%d_%H%M%S)

# 2. 执行数据库索引优化
echo "🗄️ 执行数据库索引优化..."
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < sql/performance_indexes.sql

# 3. 编译新版本
echo "🔨 编译新版本..."
go build -o life-log-be-optimized ./cmd/life-log-be

# 4. 运行测试
echo "🧪 运行测试..."
go test ./internal/biz/... -v

# 5. 启动A/B测试（10%流量）
echo "🔄 启动A/B测试..."
export OPTIMIZED_VERSION_PERCENTAGE=10

# 6. 监控性能指标
echo "📊 监控性能指标..."
echo "请访问 http://localhost:8080/metrics 查看性能指标"

echo "✅ 部署完成！"
```

### 验证脚本
```bash
#!/bin/bash
# 创建文件：scripts/verify_optimization.sh

echo "🔍 验证登录优化效果..."

# 1. 测试登录性能
echo "⏱️ 测试登录性能..."
for i in {1..10}; do
    start_time=$(date +%s%3N)
    curl -s -X POST "http://localhost:8080/api/v1/user/login" \
        -H "Content-Type: application/json" \
        -d '{
            "login_type": 2,
            "third_party_token": "mock_google_test_token_'$i'",
            "timezone_name": "Asia/Shanghai",
            "timezone_offset": "+08:00",
            "country_code": "CN",
            "device_id": "test_device_'$i'",
            "platform": "ios"
        }' > /dev/null
    end_time=$(date +%s%3N)
    duration=$((end_time - start_time))
    echo "登录测试 $i: ${duration}ms"
done

# 2. 检查数据库索引使用情况
echo "🗄️ 检查数据库索引使用情况..."
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME -e "
EXPLAIN SELECT * FROM tb_user WHERE firebase_uid = 'test_uid' AND status != 2;
EXPLAIN SELECT * FROM tb_user WHERE phone = '+8613800138000' AND status != 2;
EXPLAIN SELECT * FROM tb_user_token WHERE user_id = 1 AND platform = 1 AND is_valid = 1;
"

# 3. 检查缓存命中率
echo "📊 检查缓存命中率..."
curl -s "http://localhost:8080/metrics" | grep cache_hit_rate

echo "✅ 验证完成！"
```

通过这个综合优化方案，我们将系统性地提升life-log-be的登录性能，实现从2000-2200ms到700-800ms的显著改进。
