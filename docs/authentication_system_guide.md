# 认证系统完整流程文档

## 🏗️ 系统架构概览

```mermaid
graph TB
    subgraph "客户端层"
        A[iOS App] 
        B[Android App]
        C[Web App]
    end
    
    subgraph "认证层"
        D[JWT中间件]
        E[OAuth服务]
        F[Firebase Auth]
    end
    
    subgraph "业务层"
        G[UserUsecase]
        H[UserAuthUsecase]
    end
    
    subgraph "数据层"
        I[用户表]
        J[Token表]
    end
    
    subgraph "第三方服务"
        K[Google OAuth]
        L[Apple Sign-In]
        M[Firebase]
    end
    
    A --> D
    B --> D
    C --> D
    D --> G
    E --> K
    E --> L
    F --> M
    G --> H
    H --> I
    H --> J
```

## 🔐 认证方式支持

### 1. 密码认证
- 手机号 + 密码
- 邮箱 + 密码
- 密码强度验证（8-16位）

### 2. 验证码认证
- 手机号 + 短信验证码
- 邮箱 + 邮件验证码

### 3. 第三方认证
- Google OAuth 2.0
- Apple Sign-In
- Firebase Authentication（推荐）

## 📱 前端认证实现

### iOS Swift 实现

#### 1. 密码登录
```swift
import Foundation

class AuthService {
    private let baseURL = "https://api.yourapp.com"
    
    func loginWithPassword(phone: String, password: String) async throws -> LoginResponse {
        let request = LoginRequest(
            phone: phone,
            password: password,
            loginType: .password,
            passType: .password,
            timezoneName: TimeZone.current.identifier,
            timezoneOffset: TimeZone.current.offsetString
        )
        
        let response = try await APIClient.shared.post("/api/v1/user/login", body: request)
        return try JSONDecoder().decode(LoginResponse.self, from: response)
    }
    
    func loginWithVerifyCode(phone: String, code: String) async throws -> LoginResponse {
        let request = LoginRequest(
            phone: phone,
            verifyCode: code,
            loginType: .verifyCode,
            passType: .verifyCode,
            timezoneName: TimeZone.current.identifier,
            timezoneOffset: TimeZone.current.offsetString
        )
        
        let response = try await APIClient.shared.post("/api/v1/user/login", body: request)
        return try JSONDecoder().decode(LoginResponse.self, from: response)
    }
}

// Token管理
class TokenManager {
    private let keychain = Keychain(service: "com.yourapp.tokens")
    
    func saveToken(_ token: String, expireAt: Int32) {
        keychain["access_token"] = token
        keychain["expire_at"] = String(expireAt)
    }
    
    func getToken() -> String? {
        return keychain["access_token"]
    }
    
    func isTokenValid() -> Bool {
        guard let expireAtString = keychain["expire_at"],
              let expireAt = Int32(expireAtString) else {
            return false
        }
        return Int32(Date().timeIntervalSince1970) < expireAt
    }
    
    func clearToken() {
        keychain["access_token"] = nil
        keychain["expire_at"] = nil
    }
}
```

#### 2. Firebase认证集成
```swift
import FirebaseAuth

extension AuthService {
    func loginWithFirebase(email: String, password: String) async throws -> LoginResponse {
        // 1. Firebase认证
        let authResult = try await Auth.auth().signIn(withEmail: email, password: password)
        
        // 2. 获取ID Token
        let idToken = try await authResult.user.getIDToken()
        
        // 3. 发送到后端验证
        let request = LoginRequest(
            thirdPartyToken: idToken,
            loginType: .firebase,
            timezoneName: TimeZone.current.identifier,
            timezoneOffset: TimeZone.current.offsetString
        )
        
        let response = try await APIClient.shared.post("/api/v1/user/login", body: request)
        return try JSONDecoder().decode(LoginResponse.self, from: response)
    }
    
    func loginWithGoogle() async throws -> LoginResponse {
        // 使用Firebase Google登录
        guard let presentingViewController = UIApplication.shared.windows.first?.rootViewController else {
            throw AuthError.noPresentingViewController
        }
        
        let result = try await GoogleSignIn.sharedInstance.signIn(withPresenting: presentingViewController)
        let idToken = result.user.idToken?.tokenString
        
        // 发送到后端验证
        return try await loginWithThirdParty(token: idToken, type: .google)
    }
}
```

### Android Kotlin 实现

#### 1. 认证服务
```kotlin
class AuthService(private val apiClient: ApiClient) {
    
    suspend fun loginWithPassword(phone: String, password: String): LoginResponse {
        val request = LoginRequest(
            phone = phone,
            password = password,
            loginType = LoginType.PASSWORD.value,
            passType = PassType.PASSWORD.value,
            timezoneName = TimeZone.getDefault().id,
            timezoneOffset = getTimezoneOffset()
        )
        
        return apiClient.post("/api/v1/user/login", request)
    }
    
    suspend fun loginWithFirebase(email: String, password: String): LoginResponse {
        // 1. Firebase认证
        val authResult = FirebaseAuth.getInstance()
            .signInWithEmailAndPassword(email, password)
            .await()
        
        // 2. 获取ID Token
        val idToken = authResult.user?.getIdToken(true)?.await()?.token
            ?: throw Exception("Failed to get ID token")
        
        // 3. 发送到后端
        val request = LoginRequest(
            thirdPartyToken = idToken,
            loginType = LoginType.FIREBASE.value,
            timezoneName = TimeZone.getDefault().id,
            timezoneOffset = getTimezoneOffset()
        )
        
        return apiClient.post("/api/v1/user/login", request)
    }
    
    private fun getTimezoneOffset(): String {
        val offset = TimeZone.getDefault().rawOffset / 1000 / 3600
        return if (offset >= 0) "+${String.format("%02d", offset)}:00" 
               else "${String.format("%02d", offset)}:00"
    }
}

// Token管理
class TokenManager(private val context: Context) {
    private val prefs = context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
    
    fun saveToken(token: String, expireAt: Int) {
        prefs.edit()
            .putString("access_token", token)
            .putInt("expire_at", expireAt)
            .apply()
    }
    
    fun getToken(): String? = prefs.getString("access_token", null)
    
    fun isTokenValid(): Boolean {
        val expireAt = prefs.getInt("expire_at", 0)
        return System.currentTimeMillis() / 1000 < expireAt
    }
    
    fun clearToken() {
        prefs.edit()
            .remove("access_token")
            .remove("expire_at")
            .apply()
    }
}
```

### Web JavaScript 实现

#### 1. 认证服务
```javascript
class AuthService {
    constructor(baseURL) {
        this.baseURL = baseURL;
    }
    
    async loginWithPassword(phone, password) {
        const request = {
            phone,
            password,
            login_type: 0, // PASSWORD
            pass_type: 0,  // PASSWORD
            timezone_name: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezone_offset: this.getTimezoneOffset()
        };
        
        const response = await fetch(`${this.baseURL}/api/v1/user/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request)
        });
        
        return response.json();
    }
    
    async loginWithFirebase(email, password) {
        // 1. Firebase认证
        const userCredential = await signInWithEmailAndPassword(auth, email, password);
        
        // 2. 获取ID Token
        const idToken = await userCredential.user.getIdToken();
        
        // 3. 发送到后端
        const request = {
            third_party_token: idToken,
            login_type: 4, // FIREBASE
            timezone_name: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timezone_offset: this.getTimezoneOffset()
        };
        
        const response = await fetch(`${this.baseURL}/api/v1/user/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(request)
        });
        
        return response.json();
    }
    
    getTimezoneOffset() {
        const offset = -new Date().getTimezoneOffset() / 60;
        return offset >= 0 ? `+${String(offset).padStart(2, '0')}:00` 
                          : `${String(offset).padStart(3, '0')}:00`;
    }
}

// Token管理
class TokenManager {
    saveToken(token, expireAt) {
        localStorage.setItem('access_token', token);
        localStorage.setItem('expire_at', expireAt.toString());
    }
    
    getToken() {
        return localStorage.getItem('access_token');
    }
    
    isTokenValid() {
        const expireAt = parseInt(localStorage.getItem('expire_at') || '0');
        return Date.now() / 1000 < expireAt;
    }
    
    clearToken() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('expire_at');
    }
}
```

## 🔄 用户操作流程

### 1. 注册流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 后端服务
    participant D as 数据库
    participant SMS as 短信服务
    
    C->>S: 1. 发送验证码请求
    S->>SMS: 2. 发送短信验证码
    SMS-->>C: 3. 用户收到验证码
    C->>S: 4. 提交注册信息
    S->>S: 5. 验证验证码
    S->>S: 6. 密码加密
    S->>D: 7. 创建用户记录
    S->>S: 8. 生成JWT Token
    S->>D: 9. 保存Token记录
    S-->>C: 10. 返回Token和用户信息
```

### 2. 登录流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 后端服务
    participant D as 数据库
    participant T as 第三方服务
    
    alt 密码登录
        C->>S: 1. 手机号/邮箱 + 密码
        S->>D: 2. 查询用户
        S->>S: 3. 验证密码
        S->>S: 4. 生成JWT Token
        S-->>C: 5. 返回Token
    else 第三方登录
        C->>T: 1. 第三方认证
        T-->>C: 2. 返回ID Token
        C->>S: 3. 发送ID Token
        S->>T: 4. 验证ID Token
        S->>D: 5. 查找/创建用户
        S->>S: 6. 生成JWT Token
        S-->>C: 7. 返回Token
    end
```

### 3. Token刷新流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 后端服务
    participant D as 数据库
    
    C->>S: 1. 携带当前Token请求API
    S->>S: 2. 验证Token有效性
    S->>S: 3. 检查是否即将过期(7天内)
    alt Token即将过期
        S->>S: 4. 生成新Token
        S->>D: 5. 保存新Token记录
        S-->>C: 6. 返回新Token
    else Token未过期
        S-->>C: 4. 正常返回API响应
    end
```

## 🛡️ 安全考虑

### 1. 密码安全
```go
// 密码加密使用bcrypt
func Encrypt(source string) (string, error) {
    hashedBytes, err := bcrypt.GenerateFromPassword([]byte(source), bcrypt.DefaultCost)
    return string(hashedBytes), err
}

// 密码强度检查
func CheckPassword(password string) bool {
    return len(password) >= 8 && len(password) <= 16
}
```

### 2. JWT Token安全
```go
type CustomerClaims struct {
    jwtv4.RegisteredClaims
    UserID string `json:"user_id"`
}

// Token生成
func GenerateToken(userID int32, expireAt time.Time) (string, error) {
    jwtClaims := CustomerClaims{
        RegisteredClaims: jwtv4.RegisteredClaims{
            ExpiresAt: jwtv4.NewNumericDate(expireAt),
            Issuer:    Secret.Issuer,
        },
        UserID: fmt.Sprintf("%v", userID),
    }
    claims := jwtv4.NewWithClaims(jwtv4.SigningMethodHS256, jwtClaims)
    return claims.SignedString([]byte(Secret.Key))
}
```

### 3. 请求验证
```go
// HTTP中间件验证JWT
jwt.Server(func(token *jwtv4.Token) (interface{}, error) {
    return []byte(ac.SecretKey), nil
}, jwt.WithSigningMethod(jwtv4.SigningMethodHS256))
```

## 📋 API接口定义

### 1. 注册接口
```http
POST /api/v1/user/register
Content-Type: application/json

{
  "user_name": "用户名",
  "password": "密码",
  "confirm_password": "确认密码", 
  "verify_code": "验证码",
  "phone": "手机号"
}

Response:
{
  "code": 0,
  "msg": "success",
  "data": {
    "token": "jwt_token_here",
    "expire_at": 1640995200
  }
}
```

### 2. 登录接口
```http
POST /api/v1/user/login
Content-Type: application/json

{
  "phone": "手机号",
  "email": "邮箱",
  "password": "密码",
  "verify_code": "验证码",
  "login_type": 0,  // 0:密码 1:验证码 2:Google 3:Apple 4:Firebase
  "pass_type": 0,   // 0:密码 1:验证码
  "third_party_token": "第三方token",
  "timezone_name": "Asia/Shanghai",
  "timezone_offset": "+08:00"
}

Response:
{
  "code": 0,
  "msg": "success", 
  "data": {
    "token": "jwt_token_here",
    "expire_at": 1640995200,
    "user_id": 123,
    "name": "用户名",
    "avatar_url": "头像URL",
    "phone": "手机号",
    "email": "邮箱"
  }
}
```

### 3. Token刷新接口
```http
POST /api/v1/user/refresh-token
Authorization: Bearer jwt_token_here

Response:
{
  "code": 0,
  "msg": "success",
  "data": {
    "token": "new_jwt_token_here",  // 仅在即将过期时返回
    "expire_at": 1640995200,
    "user_id": 123,
    // ... 用户信息
  }
}
```

## 🚨 错误处理

### 1. 常见错误码
```go
const (
    ErrUserNotFound     = 40001  // 用户不存在
    ErrPasswordWrong    = 40002  // 密码错误
    ErrTokenInvalid     = 40003  // Token无效
    ErrTokenExpired     = 40004  // Token过期
    ErrVerifyCodeWrong  = 40005  // 验证码错误
    ErrThirdPartyFailed = 40006  // 第三方认证失败
)
```

### 2. 客户端错误处理
```swift
// iOS错误处理示例
enum AuthError: Error {
    case invalidCredentials
    case tokenExpired
    case networkError
    case unknownError
    
    var localizedDescription: String {
        switch self {
        case .invalidCredentials:
            return "用户名或密码错误"
        case .tokenExpired:
            return "登录已过期，请重新登录"
        case .networkError:
            return "网络连接失败"
        case .unknownError:
            return "未知错误"
        }
    }
}
```

## 🔧 部署和运维

### 1. 配置管理
```yaml
# configs/config.yaml
auth:
  secret_key: "your-secret-key"
  issuer: "life-log-be"
  expire_at: 2592000  # 30天

oauth:
  google:
    client_id: "google-client-id"
    client_secret: "google-client-secret"
  apple:
    client_id: "apple-client-id"
    key_id: "apple-key-id"
    team_id: "apple-team-id"
```

### 2. 监控指标
- 登录成功率
- Token刷新频率
- 第三方认证成功率
- 异常登录检测

### 3. 日志记录
```go
// 登录日志
uc.log.Infof("用户登录成功: user_id=%d, login_type=%d, ip=%s", 
    userID, loginType, clientIP)

// 安全日志
uc.log.Warnf("密码错误: phone=%s, ip=%s", phone, clientIP)
```

这个认证系统提供了完整的用户认证解决方案，支持多种认证方式，具有良好的安全性和扩展性。
