# 推送任务移除功能使用指南

## 概述

本文档介绍了分布式调度器中任务移除功能的使用方法，包括单个任务移除、批量移除和标签管理。

## 核心功能

### 1. 分布式调度器任务移除方法

#### 基础移除方法

```go
// 移除单个任务
func (ds *DistributedScheduler) RemoveTask(taskName string) error

// 通过标签移除任务
func (ds *DistributedScheduler) RemoveTaskByTags(tags ...string) error
```

#### 推送任务专用方法

```go
// 移除特定推送任务
func (ds *DistributedScheduler) RemovePushTask(taskType string, userID, relatedID int32) error

// 移除用户的所有推送任务
func (ds *DistributedScheduler) RemoveUserPushTasks(userID int32) error

// 生成推送任务标签
func GeneratePushTaskTag(taskType string, userID, relatedID int32) string

// 创建推送任务（带标签）
func (ds *DistributedScheduler) CreatePushTask(taskType string, userID, relatedID int32, interval time.Duration, taskFunc TaskFunc) error
```

### 2. 标签系统

每个推送任务都会自动添加以下标签：

- `user_{userID}` - 用户标签，用于按用户管理任务
- `type_{taskType}` - 任务类型标签，用于按类型管理任务
- `related_{relatedID}` - 关联ID标签，用于按关联对象管理任务
- `push_task` - 通用推送任务标签

### 3. 推送通知用例中的集成

#### 删除用户习惯相关任务

```go
func (u *PushNotificationUsecase) DeleteTasksByUserHabit(ctx context.Context, userID, relatedID int32) error {
    // 从调度器中移除任务
    if u.scheduler != nil {
        taskType := string(enums.PushTaskTypeHabitReminder)
        if err := u.scheduler.RemovePushTask(taskType, userID, relatedID); err != nil {
            u.log.Warnf("从调度器移除任务失败: %v", err)
        }
    }
    
    // 从数据库中删除任务记录
    return u.repo.DeleteTasksByUserHabit(ctx, userID, relatedID)
}
```

#### 删除用户所有任务

```go
func (u *PushNotificationUsecase) DeleteAllUserTasks(ctx context.Context, userID int32) error {
    // 从调度器中移除用户的所有任务
    if u.scheduler != nil {
        if err := u.scheduler.RemoveUserPushTasks(userID); err != nil {
            u.log.Warnf("从调度器移除用户任务失败: %v", err)
        }
    }
    
    return nil
}
```

## 使用场景

### 1. 用户删除习惯时

```go
// 删除习惯ID为123的用户456的习惯提醒任务
err := pushUsecase.DeleteTasksByUserHabit(ctx, 456, 123)
```

### 2. 用户注销账户时

```go
// 删除用户456的所有推送任务
err := pushUsecase.DeleteAllUserTasks(ctx, 456)
```

### 3. 批量管理特定类型任务

```go
// 删除所有习惯提醒任务
err := scheduler.RemoveTaskByTags("type_habit_reminder")

// 删除特定用户的所有任务
err := scheduler.RemoveTaskByTags("user_456")
```

### 4. 精确删除特定任务

```go
// 删除特定的习惯提醒任务
err := scheduler.RemovePushTask("habit_reminder", 456, 123)
```

## 技术实现细节

### 1. Redis状态管理

- 任务状态存储在 `task_state:{taskName}` 键中
- 分布式锁存储在 `task_lock:{taskName}` 键中
- 移除任务时会同时清理相关的Redis状态

### 2. 错误处理

- 移除不存在的任务不会报错，会记录信息日志
- Redis清理失败会记录警告日志但不中断流程
- 调度器移除失败会返回错误

### 3. 标签管理

- 任务名称自动作为默认标签
- 支持多标签查询和移除
- 标签格式统一，便于管理和查询

## 注意事项

1. **幂等性**：重复移除同一任务不会报错
2. **异步清理**：Redis状态清理失败不会阻止任务移除
3. **分布式一致性**：多实例环境下任务移除会同步到所有节点
4. **日志记录**：所有操作都有详细的日志记录，便于调试和监控

## 最佳实践

1. **优先使用专用方法**：推送任务优先使用 `RemovePushTask` 等专用方法
2. **批量操作**：需要删除多个相关任务时，使用标签批量删除更高效
3. **错误处理**：移除任务失败时应记录日志但不中断主要业务流程
4. **状态同步**：确保调度器和数据库状态保持一致
