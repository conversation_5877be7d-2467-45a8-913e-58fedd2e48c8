# 周期性推送任务系统实现文档

## 🎯 概述

基于"执行前实时状态检查"的设计理念，实现了一个智能的周期性推送任务系统，支持习惯提醒的自动续期和状态检查。

## 🏗️ 架构设计

### 核心组件

1. **TaskValidator 接口** - 任务验证器抽象接口
2. **HabitReminderValidator** - 习惯提醒任务验证器实现
3. **扩展的 PushTask 结构** - 支持周期性任务的数据模型
4. **增强的 ExecutePushTask** - 集成状态检查和任务续期

### 设计原则

- **实时状态检查**: 执行前查询最新业务状态
- **任务复用**: 更新现有任务而非创建新任务
- **降级策略**: 查询失败时的安全处理
- **扩展性**: 支持多种任务类型的验证器

## 📊 数据库变更

### 新增字段

```sql
-- tb_push_task 表新增字段
ALTER TABLE tb_push_task 
ADD COLUMN is_recurring TINYINT(1) DEFAULT 0 COMMENT '是否为周期性任务',
ADD COLUMN repeat_rule VARCHAR(20) DEFAULT '' COMMENT '重复规则：daily, weekly, monthly',
ADD COLUMN next_execution BIGINT(20) DEFAULT 0 COMMENT '下次执行时间',
ADD COLUMN reminder_time VARCHAR(10) DEFAULT '' COMMENT '原始提醒时间 HH:MM',
ADD COLUMN max_executions INT(11) DEFAULT 0 COMMENT '最大执行次数（0表示无限）',
ADD COLUMN execution_count INT(11) DEFAULT 0 COMMENT '已执行次数';
```

### 索引优化

```sql
-- 优化查询性能的索引
CREATE INDEX idx_push_task_recurring ON tb_push_task (is_recurring, status, scheduled_time);
CREATE INDEX idx_push_task_next_execution ON tb_push_task (next_execution, status);
CREATE INDEX idx_push_task_user_habit ON tb_push_task (user_id, user_habit_id, task_type);
```

## 🔧 核心实现

### 1. TaskValidator 接口

```go
type TaskValidator interface {
    // 检查任务是否应该执行
    ShouldExecute(ctx context.Context, task *PushTask) (bool, error)
    
    // 计算下次执行时间
    CalculateNextExecution(ctx context.Context, task *PushTask) (time.Time, error)
    
    // 获取任务类型
    GetTaskType() string
    
    // 更新任务内容（基于最新业务数据）
    UpdateTaskContent(ctx context.Context, task *PushTask) error
}
```

### 2. 习惯提醒验证器

#### 状态检查逻辑
- ✅ 习惯是否存在且未删除
- ✅ 习惯状态是否为正常
- ✅ 用户推送权限是否已授权
- ✅ 是否超过习惯结束日期
- ✅ 根据打卡周期判断今日是否需要提醒

#### 周期计算策略
- **固定周期 (PunchCycleFix)**: 查找下一个匹配的星期几
- **按周周期 (PunchCycleWeek)**: 智能分配本周剩余提醒
- **按月周期 (PunchCycleMonth)**: 智能分配本月剩余提醒

### 3. 任务执行流程

```
1. 获取待执行任务
2. 执行前状态检查
   ├─ 任务应该执行 → 继续执行
   └─ 任务不应执行 → 计算下次执行时间并更新
3. 更新任务内容（基于最新数据）
4. 执行推送
5. 处理执行结果
   ├─ 成功 → 任务续期（如果是周期性任务）
   └─ 失败 → 重试或标记失败
```

## 🚀 关键特性

### 1. 智能状态检查

- **实时查询**: 每次执行前查询最新的习惯状态
- **权限验证**: 检查用户推送权限设置
- **周期匹配**: 根据习惯周期类型智能判断提醒时机

### 2. 自动任务续期

- **任务复用**: 更新现有任务的执行时间，避免任务表无限增长
- **智能计算**: 根据习惯类型和打卡情况计算下次提醒时间
- **执行限制**: 支持最大执行次数限制

### 3. 降级策略

- **查询失败**: 使用缓存数据或默认策略
- **网络超时**: 延迟重试，不取消推送
- **业务异常**: 停止任务或暂停等待

### 4. 性能优化

- **批量处理**: 支持批量查询和处理
- **索引优化**: 针对查询模式优化数据库索引
- **缓存策略**: 缓存频繁查询的数据

## 📈 使用示例

### 创建周期性提醒任务

```go
// 创建习惯提醒任务
err := pushUsecase.CreateHabitReminderTasks(ctx, userID, userHabitID)
if err != nil {
    log.Errorf("创建提醒任务失败: %v", err)
}
```

### 处理待执行任务

```go
// 定期处理待执行任务（通过定时器调用）
err := pushUsecase.ProcessPendingTasks(ctx)
if err != nil {
    log.Errorf("处理推送任务失败: %v", err)
}
```

### 自定义验证器

```go
// 注册自定义验证器
customValidator := NewCustomValidator()
pushUsecase.RegisterValidator(customValidator)
```

## 🔍 监控和维护

### 关键指标

- **任务执行成功率**: 监控推送任务的成功率
- **状态检查失败率**: 监控验证器的失败情况
- **任务续期成功率**: 监控周期性任务的续期情况

### 日志记录

- **执行日志**: 记录任务执行的详细过程
- **状态检查日志**: 记录验证器的检查结果
- **错误日志**: 记录失败原因和降级策略

### 数据清理

```sql
-- 清理已完成的非周期性任务（30天前）
DELETE FROM tb_push_task 
WHERE is_recurring = 0 
AND status IN (2, 3) 
AND created_at < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY));
```

## 🎉 优势总结

1. **数据一致性**: 始终使用最新的业务状态
2. **系统简洁**: 避免复杂的任务链管理
3. **扩展性强**: 支持多种任务类型和验证策略
4. **性能优化**: 减少数据库写入和存储空间
5. **容错能力**: 完善的降级和重试机制

这个实现为 life-log-be 项目提供了一个强大、灵活且易于维护的周期性推送任务系统。
