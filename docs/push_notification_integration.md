# 多平台推送通知系统集成指南

## 概述

本文档描述了如何在 life-log-be 项目中集成多平台推送通知系统，用于用户习惯提醒功能。

## 系统架构

### 推送平台路由策略
- **中国大陆用户**（手机号+86开头）：使用极光推送（JPush）
- **海外iOS用户**：使用Apple Push Notification Service (APNs)
- **海外Android用户**：使用Firebase Cloud Messaging (FCM)
- **备选方案**：OneSignal作为所有平台的fallback推送服务

### 核心组件

1. **PushNotificationUsecase**: 推送通知业务逻辑
2. **PushScheduler**: 推送任务调度器
3. **PushService**: 推送服务接口（JPush、APNs、FCM、OneSignal）
4. **PushNotificationRepo**: 推送数据访问层

## 数据库表结构

### 1. 扩展现有表

```sql
-- 扩展用户表
ALTER TABLE `tb_user` ADD COLUMN `region` varchar(10) DEFAULT 'overseas' COMMENT '用户区域：china-中国大陆，overseas-海外';

-- 扩展用户token表
ALTER TABLE `tb_user_token` ADD COLUMN `platform` varchar(20) DEFAULT '' COMMENT '设备平台：ios, android, web';
ALTER TABLE `tb_user_token` ADD COLUMN `device_token` varchar(500) DEFAULT '' COMMENT '设备推送token';
ALTER TABLE `tb_user_token` ADD COLUMN `device_id` varchar(100) DEFAULT '' COMMENT '设备唯一标识';
```

### 2. 新增表

- `tb_push_task`: 推送任务表
- `tb_push_statistics`: 推送统计表
- `tb_user_push_setting`: 用户推送设置表
- `tb_push_template`: 推送模板表

## 配置文件扩展

需要在 `internal/conf/conf.proto` 中添加推送服务配置：

```protobuf
message Data {
  // ... 现有配置
  
  // 推送服务配置
  JPush jpush = 20;
  APNs apns = 21;
  FCM fcm = 22;
  OneSignal onesignal = 23;
}

message JPush {
  string app_key = 1;
  string app_secret = 2;
}

message APNs {
  string key_id = 1;
  string team_id = 2;
  string bundle_id = 3;
  string key_path = 4;
  bool is_product = 5;
}

message FCM {
  string server_key = 1;
}

message OneSignal {
  string app_id = 1;
  string api_key = 2;
}
```

## 集成步骤

### 1. 执行数据库迁移

```bash
mysql -u username -p database_name < sql/create_push_notification_tables.sql
```

### 2. 更新配置文件

在 `configs/config.yaml` 中添加推送服务配置：

```yaml
data:
  # ... 现有配置
  
  jpush:
    app_key: "your_jpush_app_key"
    app_secret: "your_jpush_app_secret"
    
  apns:
    key_id: "your_apns_key_id"
    team_id: "your_apns_team_id"
    bundle_id: "your_app_bundle_id"
    key_path: "/path/to/apns/key.p8"
    is_product: false
    
  fcm:
    server_key: "your_fcm_server_key"
    
  onesignal:
    app_id: "your_onesignal_app_id"
    api_key: "your_onesignal_api_key"
```

### 3. 依赖注入配置

在 `internal/data/data.go` 中添加推送服务的依赖注入：

```go
// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
    NewData,
    NewUserRepo,
    NewUserHabitRepo,
    // ... 现有providers
    
    // 推送通知相关
    NewPushNotificationRepo,
    service.NewJPushService,
    service.NewAPNsService,
    service.NewFCMService,
    service.NewOneSignalService,
    biz.NewPushNotificationUsecase,
    service.NewPushScheduler,
)
```

### 4. 启动推送调度器

在 `cmd/server/main.go` 中启动推送调度器：

```go
func main() {
    // ... 现有代码
    
    // 启动推送调度器
    pushScheduler := initPushScheduler()
    pushScheduler.Start()
    defer pushScheduler.Stop()
    
    // ... 现有代码
}
```

## API接口

### 1. 注册设备Token

```protobuf
message RegisterDeviceTokenRequest {
  string platform = 1;      // ios, android, web
  string device_token = 2;   // 设备推送token
  string device_id = 3;      // 设备唯一标识
}
```

### 2. 更新推送设置

```protobuf
message UpdatePushSettingRequest {
  bool habit_reminder_enabled = 1;  // 是否启用习惯提醒
  string quiet_start_time = 2;      // 免打扰开始时间
  string quiet_end_time = 3;        // 免打扰结束时间
  int32 max_daily_push = 4;         // 每日最大推送数量
}
```

## 使用示例

### 1. 客户端注册设备Token

```javascript
// iOS Swift
func registerDeviceToken() {
    let request = RegisterDeviceTokenRequest()
    request.platform = "ios"
    request.deviceToken = deviceToken
    request.deviceID = UIDevice.current.identifierForVendor?.uuidString ?? ""
    
    // 调用API
    apiClient.registerDeviceToken(request)
}

// Android Kotlin
fun registerDeviceToken() {
    val request = RegisterDeviceTokenRequest.newBuilder()
        .setPlatform("android")
        .setDeviceToken(deviceToken)
        .setDeviceId(Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID))
        .build()
    
    // 调用API
    apiClient.registerDeviceToken(request)
}
```

### 2. 创建习惯时自动设置提醒

```go
// 在创建习惯时，系统会自动：
// 1. 判断用户区域（中国大陆 vs 海外）
// 2. 根据用户设置的提醒时间创建推送任务
// 3. 根据用户设备平台选择合适的推送服务

config := &UserHabitConfig{
    ReminderTimes: []string{"09:00", "18:00", "21:30"},
    // ... 其他配置
}
```

## 监控和统计

### 1. 推送统计

系统会自动记录推送统计数据：
- 每日发送总数
- 成功/失败数量
- 按平台分类的统计

### 2. 健康检查

```go
// 检查推送服务健康状态
func (s *PushScheduler) HealthCheck(ctx context.Context) error {
    // 检查Redis连接
    // 检查各推送服务状态
    // 检查待处理任务数量
}
```

## 故障处理

### 1. 重试机制

- 推送失败时自动重试，最多3次
- 使用指数退避策略：2^retry_count 分钟

### 2. 降级方案

- 主推送服务失败时，自动切换到OneSignal
- 推送服务不可用时，记录日志但不影响主业务

### 3. 分布式锁

- 使用Redis分布式锁防止多Pod环境下的重复执行
- 锁定时间：60秒

## 性能优化

### 1. 批量处理

- 每30秒批量处理待执行的推送任务
- 每次最多处理100个任务

### 2. 异步执行

- 所有推送操作都是异步执行
- 不影响主业务接口的响应时间

### 3. 缓存策略

- 用户区域信息缓存在User表的region字段
- 推送模板缓存在内存中

## 安全考虑

### 1. 数据加密

- 设备Token等敏感信息需要加密存储
- API通信使用HTTPS

### 2. 权限控制

- 只有认证用户才能注册设备Token
- 推送内容不包含敏感信息

### 3. 频率限制

- 每用户每日最大推送数量限制
- 免打扰时间段不发送推送

这个推送通知系统提供了完整的多平台推送能力，支持用户习惯提醒功能，具有良好的扩展性和可维护性。
