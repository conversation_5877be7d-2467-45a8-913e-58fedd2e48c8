# 推送通知系统快速启动指南

## 🚀 快速部署步骤

### 1. 数据库迁移
```bash
# 执行数据库表创建脚本
mysql -u username -p database_name < sql/create_push_notification_tables.sql
```

### 2. 配置推送服务
```bash
# 复制配置示例
cp configs/config_push_example.yaml configs/config_push.yaml

# 编辑配置文件，填入你的推送服务密钥
vim configs/config_push.yaml
```

### 3. 更新主配置文件
将 `configs/config_push.yaml` 中的推送配置合并到你的主配置文件 `configs/config.yaml` 中。

### 4. 重新编译和启动
```bash
# 重新编译
go build ./cmd/life-log-be

# 启动服务
./life-log-be -conf configs/config.yaml
```

## 📱 客户端集成

### iOS Swift 示例
```swift
import UserNotifications

// 1. 请求推送权限
UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
    if granted {
        DispatchQueue.main.async {
            UIApplication.shared.registerForRemoteNotifications()
        }
    }
}

// 2. 获取设备Token
func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    let tokenString = deviceToken.map { String(format: "%02.2hhx", $0) }.joined()
    
    // 调用API注册设备Token
    registerDeviceToken(platform: "ios", deviceToken: tokenString, deviceID: UIDevice.current.identifierForVendor?.uuidString ?? "")
}
```

### Android Kotlin 示例
```kotlin
// 1. 获取FCM Token
FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
    if (!task.isSuccessful) {
        Log.w(TAG, "Fetching FCM registration token failed", task.exception)
        return@addOnCompleteListener
    }

    // 获取新的FCM注册令牌
    val token = task.result
    
    // 调用API注册设备Token
    registerDeviceToken("android", token, getDeviceId())
}

// 2. 获取设备ID
private fun getDeviceId(): String {
    return Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
}
```

## 🔧 API接口使用

### 注册设备Token
```http
POST /api/user/register-device-token
Content-Type: application/json
Authorization: Bearer your_jwt_token

{
  "platform": "ios",           // ios, android, web
  "device_token": "device_push_token_here",
  "device_id": "unique_device_id"
}
```

### 更新推送设置
```http
POST /api/user/update-push-setting
Content-Type: application/json
Authorization: Bearer your_jwt_token

{
  "habit_reminder_enabled": true,    // 是否启用习惯提醒
  "quiet_start_time": "22:00",       // 免打扰开始时间
  "quiet_end_time": "08:00",         // 免打扰结束时间
  "max_daily_push": 20               // 每日最大推送数量
}
```

## 🧪 测试推送功能

### 1. 创建测试习惯
```http
POST /api/user-habit/create
Content-Type: application/json
Authorization: Bearer your_jwt_token

{
  "name": "测试习惯",
  "config": {
    "reminder_times": ["09:00", "18:00"],  // 设置提醒时间
    "punch_cycle": [1, 2, 3, 4, 5],       // 周一到周五
    "punch_cycle_type": 1                  // 固定周期
  },
  "create_date": "2024-01-01T00:00:00Z"
}
```

### 2. 检查推送任务
```sql
-- 查看创建的推送任务
SELECT * FROM tb_push_task WHERE user_habit_id = your_habit_id;

-- 查看用户推送设置
SELECT * FROM tb_user_push_setting WHERE user_id = your_user_id;
```

### 3. 手动触发推送（调试用）
```bash
# 连接Redis，手动添加任务到队列
redis-cli
> RPUSH push_task_queue '{"type":"test","user_id":123,"message":"测试推送"}'
```

## 🔍 故障排查

### 常见问题

1. **推送任务未创建**
   - 检查用户是否设置了 `reminder_times`
   - 检查用户推送设置是否启用
   - 查看日志中的错误信息

2. **推送发送失败**
   - 检查推送服务配置是否正确
   - 验证设备Token是否有效
   - 查看推送统计表中的错误信息

3. **用户区域判断错误**
   - 检查用户手机号格式
   - 验证时区设置
   - 查看User表中的region字段

### 日志查看
```bash
# 查看推送相关日志
tail -f logs/app.log | grep -i push

# 查看特定用户的推送日志
tail -f logs/app.log | grep "user_id: 123"
```

### 数据库查询
```sql
-- 查看推送统计
SELECT * FROM tb_push_statistics ORDER BY date DESC LIMIT 10;

-- 查看失败的推送任务
SELECT * FROM tb_push_task WHERE status = 3 ORDER BY created_at DESC LIMIT 10;

-- 查看用户设备Token
SELECT * FROM tb_user_token WHERE user_id = your_user_id AND device_token != '';
```

## 📊 监控和维护

### 推送成功率监控
```sql
-- 每日推送成功率
SELECT 
    date,
    platform,
    total_sent,
    total_success,
    ROUND(total_success * 100.0 / total_sent, 2) as success_rate
FROM tb_push_statistics 
WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY date DESC, platform;
```

### 清理过期数据
```sql
-- 清理30天前的推送任务（已完成或失败的）
DELETE FROM tb_push_task 
WHERE status IN (2, 3) 
AND created_at < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY));

-- 清理90天前的推送统计
DELETE FROM tb_push_statistics 
WHERE date < DATE_SUB(CURDATE(), INTERVAL 90 DAY);
```

## 🔄 版本升级

当需要升级推送系统时：

1. **备份数据库**
2. **停止服务**
3. **更新代码**
4. **执行数据库迁移**
5. **更新配置文件**
6. **重启服务**
7. **验证功能**

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排查部分
2. 检查系统日志
3. 查看数据库中的错误记录
4. 参考完整的集成文档：`docs/push_notification_integration.md`

推送通知系统现在已经完全集成到你的 life-log-be 项目中，享受智能的多平台推送体验吧！🎉
