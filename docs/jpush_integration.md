# 极光推送(JPush)集成说明

## 🎯 概述

本文档说明在Firebase统一认证架构下，如何正确集成和使用极光推送(JPush)服务。

## 📋 JPush相关字段

### 保留的JPush必要字段

在LoginRequest中保留了以下三个JPush必要字段：

```protobuf
message LoginRequest {
  // JPush集成必要字段
  string push_service_type = 27;           // 推送服务类型标识符
  string jpush_registration_id = 28;       // 极光推送设备注册ID
  string recommended_push_channel = 29;    // 极光推送推荐渠道配置
  
  // 其他推送字段
  string fcm_token = 24;                   // FCM推送token
  string apns_token = 25;                  // APNs推送token
  bool push_permission_granted = 36;       // 推送权限状态
}
```

### 字段详细说明

#### 1. `push_service_type` (字段27)
**用途**：指定使用的推送服务类型
**可选值**：
- `"jpush"` - 极光推送（支持Android和iOS）
- `"fcm"` - Firebase Cloud Messaging（主要用于Android）
- `"apns"` - Apple Push Notification Service（仅iOS）
- `"auto"` - 自动选择（根据平台自动判断）

**示例**：
```json
{
  "push_service_type": "jpush"
}
```

#### 2. `jpush_registration_id` (字段28)
**用途**：极光推送的设备注册ID，这是极光推送服务的核心标识符
**格式**：极光SDK生成的唯一设备标识符
**重要性**：⭐⭐⭐⭐⭐ 必需字段，没有此ID无法进行极光推送

**示例**：
```json
{
  "jpush_registration_id": "1a0018970a8d2b3c4d5e6f7g8h9i0j1k"
}
```

#### 3. `recommended_push_channel` (字段29)
**用途**：极光推送的推荐推送渠道配置
**常用值**：
- `"developer-default"` - 开发者默认渠道
- `"production"` - 生产环境渠道
- `"test"` - 测试渠道
- 自定义渠道名称

**示例**：
```json
{
  "recommended_push_channel": "developer-default"
}
```

## 🔧 服务器端处理逻辑

### 1. 推送服务类型自动判断

```go
// determinePushService 确定推送服务类型
func (uc *EnhancedAuthUsecase) determinePushService(requestedService, platform string) string {
    // 如果明确指定了推送服务类型，使用指定的
    if requestedService != "" {
        switch requestedService {
        case "jpush", "fcm", "apns":
            return requestedService
        }
    }
    
    // 根据平台自动选择推送服务
    switch platform {
    case "ios":
        return "apns"  // iOS优先使用APNs
    case "android":
        return "fcm"   // Android优先使用FCM
    default:
        return "jpush" // 默认使用极光推送（支持多平台）
    }
}
```

### 2. JPush注册ID处理

```go
// 注册极光推送ID
if req.JpushRegistrationId != "" {
    if err := uc.pushUsecase.UpdateUserDeviceToken(ctx, userID, req.Platform, req.JpushRegistrationId, req.DeviceId); err != nil {
        uc.log.Errorf("注册极光推送ID失败: %v", err)
    }
}
```

### 3. 推送设置更新

```go
// 更新推送服务类型（支持JPush）
if req.PushServiceType != "" && req.PushServiceType != setting.PreferredPushService {
    setting.PreferredPushService = req.PushServiceType
    updated = true
}

// 更新JPush推荐渠道
if req.RecommendedPushChannel != "" && req.RecommendedPushChannel != setting.PushChannel {
    setting.PushChannel = req.RecommendedPushChannel
    updated = true
}
```

## 📱 客户端集成示例

### Android客户端（JPush）

```kotlin
// 1. 初始化JPush SDK
JPushInterface.setDebugMode(BuildConfig.DEBUG)
JPushInterface.init(this)

// 2. 获取Registration ID
val registrationId = JPushInterface.getRegistrationID(this)

// 3. Firebase认证
val authResult = Firebase.auth.signInWithEmailAndPassword(email, password).await()
val idToken = authResult.user?.getIdToken(false)?.await()?.token

// 4. 登录请求（包含JPush信息）
val loginRequest = LoginRequest(
    loginType = 2, // Firebase登录
    thirdPartyToken = idToken,
    
    // JPush相关字段
    pushServiceType = "jpush",
    jpushRegistrationId = registrationId,
    recommendedPushChannel = "developer-default",
    pushPermissionGranted = NotificationManagerCompat.from(this).areNotificationsEnabled(),
    
    // 其他必要字段
    timezoneName = TimeZone.getDefault().id,
    timezoneOffset = getTimezoneOffset(),
    countryCode = Locale.getDefault().country,
    locale = Locale.getDefault().language,
    deviceId = getDeviceId(),
    platform = "android",
    systemVersion = Build.VERSION.RELEASE,
    appVersion = getAppVersion()
)

// 5. 调用登录接口
val response = apiClient.login(loginRequest)
```

### iOS客户端（JPush + APNs）

```swift
// 1. 初始化JPush SDK
JPUSHService.setup(withOption: launchOptions, 
                   appKey: "your-jpush-app-key",
                   channel: "App Store",
                   apsForProduction: !isDebug)

// 2. 获取Registration ID
let registrationId = JPUSHService.registrationID()

// 3. Firebase认证
let authResult = try await Auth.auth().signIn(withEmail: email, password: password)
let idToken = try await authResult.user.getIDToken()

// 4. 登录请求（支持JPush和APNs双推送）
let loginRequest = LoginRequest(
    loginType: 2, // Firebase登录
    thirdPartyToken: idToken,
    
    // 推送服务配置
    pushServiceType: "jpush", // 主要使用JPush
    jpushRegistrationId: registrationId,
    recommendedPushChannel: "developer-default",
    apnsToken: UserDefaults.standard.string(forKey: "apns_token") ?? "",
    pushPermissionGranted: await UNUserNotificationCenter.current().notificationSettings().authorizationStatus == .authorized,
    
    // 其他必要字段
    timezoneName: TimeZone.current.identifier,
    timezoneOffset: TimeZone.current.offsetString,
    countryCode: Locale.current.regionCode,
    locale: Locale.current.languageCode,
    deviceId: UIDevice.current.identifierForVendor?.uuidString,
    platform: "ios",
    systemVersion: UIDevice.current.systemVersion,
    appVersion: Bundle.main.appVersion
)

// 5. 调用登录接口
let response = try await apiClient.login(loginRequest)
```

## 🔄 推送服务选择策略

### 1. 自动选择模式

```json
{
  "push_service_type": "auto",
  "platform": "android"
}
```
**结果**：自动选择FCM（Android平台）

### 2. 强制指定JPush

```json
{
  "push_service_type": "jpush",
  "jpush_registration_id": "1a0018970a8d2b3c4d5e6f7g8h9i0j1k",
  "recommended_push_channel": "developer-default"
}
```
**结果**：使用极光推送服务

### 3. 混合推送策略

```json
{
  "push_service_type": "jpush",
  "jpush_registration_id": "1a0018970a8d2b3c4d5e6f7g8h9i0j1k",
  "apns_token": "apns_token_for_ios_backup",
  "fcm_token": "fcm_token_for_android_backup"
}
```
**结果**：主要使用JPush，同时保留原生推送作为备用

## 🚨 注意事项

### 1. Registration ID获取时机
- JPush Registration ID需要在SDK初始化完成后获取
- 建议在应用启动时异步获取，登录时使用缓存值
- 如果获取失败，可以先登录，后续通过设备注册接口补充

### 2. 推送权限处理
- `push_permission_granted`字段应该反映真实的系统权限状态
- 不要依赖用户手动设置，应该通过系统API检查

### 3. 渠道配置
- `recommended_push_channel`应该与JPush控制台配置的渠道名称一致
- 不同环境（开发/测试/生产）应该使用不同的渠道

### 4. 错误处理
- JPush Registration ID获取失败时，不应该阻塞登录流程
- 可以在登录成功后，通过独立接口补充推送信息

## 📊 测试验证

使用提供的测试脚本验证JPush集成：

```bash
# 运行包含JPush测试的完整测试套件
./scripts/test_firebase_auth.sh
```

测试将验证：
- JPush Registration ID的正确处理
- 推送服务类型的自动选择
- 推送渠道配置的保存
- 多种推送服务的兼容性

通过保留这三个JPush必要字段，life-log-be项目能够完整支持极光推送服务，同时保持与Firebase统一认证架构的兼容性。
