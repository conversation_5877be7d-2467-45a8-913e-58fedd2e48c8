# 推送通知系统架构重构

## 🎯 架构变更概述

### 原架构问题
- `push_scheduler.go` 和 `push_notification.go` 职责重叠
- 缺少从数据库加载现有任务的机制
- 两层调度器设计冗余

### 新架构设计
**简化为单一核心**：直接在 `PushNotificationUsecase` 中集成分布式 gocron

## 🏗️ 新架构组件

### 1. 核心组件：PushNotificationUsecase

```go
type PushNotificationUsecase struct {
    repo          PushNotificationRepo
    userRepo      UserRepo
    userHabitRepo UserHabitRepo
    log           *log.Helper
    
    // 推送服务
    jpushService PushService
    fcmService   PushService
    
    // 任务验证器
    validators map[string]TaskValidator
    
    // 🆕 分布式调度器
    scheduler *task.DistributedScheduler
}
```

### 2. 启动流程

```go
// 1. 创建 Usecase（注入 Redis 客户端）
pushUsecase := NewPushNotificationUsecase(repo, userRepo, userHabitRepo, redisClient, logger)

// 2. 设置推送服务
pushUsecase.SetPushServices(jpushService, fcmService)

// 3. 启动调度器
err := pushUsecase.StartScheduler()
```

### 3. 调度器启动逻辑

```mermaid
graph TD
    A[StartScheduler] --> B[从数据库加载现有任务]
    B --> C[为每个周期性任务创建调度]
    C --> D[设置系统级调度任务]
    D --> E[启动分布式调度器]
    
    B --> B1[GetActiveRecurringTasks]
    C --> C1[scheduleRecurringTask]
    D --> D1[系统任务处理器]
    D --> D2[过期任务清理器]
```

## 📋 任务类型

### 1. 业务任务（从数据库加载）
- **习惯提醒任务**：`habit_reminder_{userID}_{habitID}`
- **执行间隔**：根据 RepeatRule（daily/weekly/monthly）
- **执行逻辑**：实时状态检查 + 推送发送

### 2. 系统任务（固定配置）
- **系统任务处理器**：`system_push_task_processor`（每分钟）
- **过期任务清理器**：`cleanup_expired_tasks`（每小时）

## 🔄 任务执行流程

### 周期性任务执行
```go
// 1. 任务触发（gocron）
TaskFunc: func() error {
    return u.executeSpecificTask(task.ID)
}

// 2. 实时状态检查
task := u.repo.GetPushTaskByID(ctx, taskID)
if !task.IsActive { return nil }

// 3. 业务验证
validator := u.getValidator(task.TaskType)
if !validator.ShouldExecute(ctx, task) { return nil }

// 4. 执行推送
return u.ExecutePushTask(ctx, task)
```

### 系统任务执行
```go
// 每分钟执行：处理时间窗口内的待执行任务
func() error {
    return u.ProcessPendingTasks(ctx)
}
```

## 🗄️ 数据库交互

### 新增仓库方法
```go
type PushNotificationRepo interface {
    // 🆕 获取所有激活的周期性任务
    GetActiveRecurringTasks(ctx context.Context) ([]*PushTask, error)
    
    // 🆕 根据ID获取任务
    GetPushTaskByID(ctx context.Context, taskID int64) (*PushTask, error)
    
    // 现有方法...
}
```

### 任务加载逻辑
```sql
-- 加载激活的周期性任务
SELECT * FROM tb_push_task 
WHERE is_recurring = true AND is_active = true;

-- 获取特定任务
SELECT * FROM tb_push_task WHERE id = ?;
```

## ⚙️ 配置与部署

### 1. 依赖注入更新
```go
// wire.go 中需要更新
func NewPushNotificationUsecase(
    repo biz.PushNotificationRepo,
    userRepo biz.UserRepo,
    userHabitRepo biz.UserHabitRepo,
    redisClient *redis.Client,  // 🆕 新增 Redis 依赖
    logger log.Logger,
) *biz.PushNotificationUsecase
```

### 2. 启动顺序
```go
// main.go 或 server.go 中
func main() {
    // ... 初始化其他组件
    
    // 启动推送调度器
    if err := pushUsecase.StartScheduler(); err != nil {
        log.Fatal("启动推送调度器失败", err)
    }
    
    // 优雅关闭
    defer pushUsecase.StopScheduler()
}
```

## 🔧 关键特性

### 1. 分布式调度
- **多 Pod 安全**：Redis 分布式锁确保任务不重复执行
- **故障切换**：Pod 故障时其他实例自动接管
- **负载均衡**：任务在多个 Pod 间自动分配

### 2. 动态任务管理
- **启动时加载**：从数据库加载所有激活的周期性任务
- **实时状态检查**：执行前验证任务和业务状态
- **灵活调度**：支持不同的重复规则（daily/weekly/monthly）

### 3. 可观测性
- **详细日志**：任务执行、锁竞争、错误处理
- **实例追踪**：每个 Pod 有唯一标识
- **性能监控**：执行时间统计

## 📊 性能考虑

### 1. 数据库查询优化
```sql
-- 为周期性任务查询添加索引
CREATE INDEX idx_push_task_recurring_active 
ON tb_push_task(is_recurring, is_active);

-- 为任务ID查询添加主键索引（已存在）
```

### 2. Redis 连接池
- 合理配置连接池大小
- 监控 Redis 连接使用情况
- 设置合适的超时时间

### 3. 任务执行频率
- **周期性任务**：根据业务需求（daily/weekly/monthly）
- **系统任务**：1分钟检查频率平衡及时性和性能

## 🚀 迁移步骤

### 1. 移除 push_scheduler.go
```bash
# 可以安全删除
rm internal/service/push_scheduler.go
```

### 2. 更新依赖注入
- 在 wire.go 中更新 PushNotificationUsecase 构造函数
- 添加 Redis 客户端依赖

### 3. 更新启动代码
- 在应用启动时调用 `pushUsecase.StartScheduler()`
- 在应用关闭时调用 `pushUsecase.StopScheduler()`

### 4. 数据库迁移
- 确保 `is_recurring` 和 `is_active` 字段已存在
- 添加必要的索引

## 🎯 优势总结

1. **架构简化**：移除冗余的调度器层
2. **功能完整**：支持数据库任务加载和分布式调度
3. **高可用性**：多 Pod 部署支持
4. **易维护性**：单一职责，逻辑集中
5. **可扩展性**：支持新的任务类型和调度规则

这个新架构解决了您提出的所有问题：
- ✅ 移除了不必要的 push_scheduler.go
- ✅ 集成了自定义的分布式 gocron
- ✅ 支持从数据库加载现有推送任务
- ✅ 保持了分布式调度的所有优势
