# 版本检查接口文档

## 概述

版本检查接口用于客户端检查是否有新版本可用，支持 iOS、Android 和 Web 平台。

## 接口信息

- **接口路径**: `/api.LifeLog/VersionCheck`
- **请求方法**: gRPC
- **是否需要认证**: 否

## 请求参数

```protobuf
message VersionCheckRequest {
  string current_version = 1;  // 当前版本号，如：1.0.0
  string platform = 2;         // 平台：ios, android, web
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current_version | string | 是 | 当前客户端版本号，格式：x.y.z |
| platform | string | 是 | 平台类型：ios、android、web |

## 响应参数

```protobuf
message VersionCheckReply {
  message Data {
    bool hasUpdate = 1;           // 是否有更新
    bool forceUpdate = 2;         // 是否强制更新
    string latestVersion = 3;     // 最新版本号
    string currentVersion = 4;    // 当前版本号
    string updateTitle = 5;       // 更新标题
    string updateContent = 6;     // 更新内容
    string downloadUrl = 7;       // 下载链接
    string iosAppStoreUrl = 8;    // iOS App Store链接
    string androidDirectUrl = 9;  // Android直接下载链接
    uint32 fileSize = 10;         // 文件大小（字节）
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}
```

## 使用示例

### 请求示例

```json
{
  "current_version": "1.0.0",
  "platform": "ios"
}
```

### 响应示例

#### 有更新的情况

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "hasUpdate": true,
    "forceUpdate": false,
    "latestVersion": "1.1.0",
    "currentVersion": "1.0.0",
    "updateTitle": "新功能上线",
    "updateContent": "1. 新增习惯统计功能\n2. 优化界面体验\n3. 修复已知问题",
    "downloadUrl": "https://your-domain.com/download/app-v1.1.0.apk",
    "iosAppStoreUrl": "https://apps.apple.com/app/your-app-id",
    "androidDirectUrl": "https://your-domain.com/download/app-v1.1.0.apk",
    "fileSize": 31457280
  }
}
```

#### 无更新的情况

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "hasUpdate": false,
    "forceUpdate": false,
    "latestVersion": "1.0.0",
    "currentVersion": "1.0.0",
    "updateTitle": "",
    "updateContent": "",
    "downloadUrl": "",
    "iosAppStoreUrl": "",
    "androidDirectUrl": "",
    "fileSize": 0
  }
}
```

## 数据库管理

### 版本代码计算规则

版本号 `x.y.z` 转换为版本代码的规则：
- 版本代码 = x * 10000 + y * 100 + z
- 例如：1.2.3 → 10203

### 添加新版本示例

```sql
-- iOS 新版本
INSERT INTO `tb_app_version` (
  `platform`, `version`, `version_code`, `is_force_update`, 
  `update_title`, `update_content`, `download_url`, 
  `ios_app_store_url`, `android_direct_url`, `file_size`, 
  `is_active`, `created_at`, `updated_at`
) VALUES (
  'ios', '1.1.0', 10100, 0, 
  '新功能上线', '1. 新增习惯统计功能\n2. 优化界面体验\n3. 修复已知问题', 
  '', 'https://apps.apple.com/app/your-app-id', '', 52428800, 
  1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
);

-- Android 新版本
INSERT INTO `tb_app_version` (
  `platform`, `version`, `version_code`, `is_force_update`, 
  `update_title`, `update_content`, `download_url`, 
  `ios_app_store_url`, `android_direct_url`, `file_size`, 
  `is_active`, `created_at`, `updated_at`
) VALUES (
  'android', '1.1.0', 10100, 1, 
  '重要更新', '1. 修复安全漏洞\n2. 新增习惯统计功能\n3. 优化性能', 
  'https://your-domain.com/download/app-v1.1.0.apk', 
  '', 'https://your-domain.com/download/app-v1.1.0.apk', 31457280, 
  1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
);
```

## 注意事项

1. **版本比较**: 系统通过 `version_code` 字段进行版本比较
2. **平台区分**: 不同平台的版本信息独立管理
3. **强制更新**: 当 `is_force_update=true` 且有更新时，客户端应强制用户更新
4. **启用状态**: 只有 `is_active=true` 的版本才会被返回
5. **文件大小**: 以字节为单位，客户端可以根据此信息提示用户下载大小
