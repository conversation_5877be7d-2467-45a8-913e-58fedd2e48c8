# life-log-be 推送通知优化实施方案

## 🎯 实施概述

基于推送通知功能深度分析，本方案将分三个阶段实施推送系统优化，确保与现有登录优化架构的完美集成。

## 📋 Phase 1: 高优先级优化（立即实施）

### 1.1 数据库结构增强

#### 扩展现有表结构
```sql
-- 扩展 tb_user_token 表
ALTER TABLE tb_user_token 
ADD COLUMN IF NOT EXISTS push_environment varchar(20) DEFAULT 'production' COMMENT '推送环境：development, production',
ADD COLUMN IF NOT EXISTS token_expires_at bigint(20) DEFAULT NULL COMMENT 'Token过期时间',
ADD COLUMN IF NOT EXISTS auto_refresh_enabled tinyint(1) DEFAULT 1 COMMENT '是否启用自动刷新',
ADD COLUMN IF NOT EXISTS token_priority tinyint(1) DEFAULT 1 COMMENT 'Token优先级：1-主要，2-备用';

-- 添加索引优化
CREATE INDEX IF NOT EXISTS idx_token_environment ON tb_user_token(push_environment, token_status);
CREATE INDEX IF NOT EXISTS idx_token_expires ON tb_user_token(token_expires_at, is_valid);
```

#### 新增推送服务配置表
```sql
-- 推送服务配置表
CREATE TABLE IF NOT EXISTS tb_push_service_config (
    id int(11) NOT NULL AUTO_INCREMENT,
    service_name varchar(20) NOT NULL COMMENT '服务名称：fcm, apns, jpush',
    environment varchar(20) NOT NULL COMMENT '环境：development, production',
    config_data json NOT NULL COMMENT '配置数据',
    is_active tinyint(1) DEFAULT 1 COMMENT '是否启用',
    priority tinyint(1) DEFAULT 1 COMMENT '优先级：1-主要，2-备用',
    health_check_url varchar(255) DEFAULT '' COMMENT '健康检查URL',
    last_health_check bigint(20) DEFAULT NULL COMMENT '最后健康检查时间',
    health_status tinyint(1) DEFAULT 1 COMMENT '健康状态：1-正常，0-异常',
    created_at bigint(20) NOT NULL,
    updated_at bigint(20) NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY uk_service_env (service_name, environment),
    KEY idx_active_priority (is_active, priority),
    KEY idx_health_status (health_status, last_health_check)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推送服务配置表';
```

#### 插入默认配置数据
```sql
-- 插入默认推送服务配置
INSERT INTO tb_push_service_config (service_name, environment, config_data, is_active, priority, created_at, updated_at) VALUES
('fcm', 'production', '{"project_id": "", "server_key": "", "api_endpoint": "https://fcm.googleapis.com/fcm/send"}', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('fcm', 'development', '{"project_id": "", "server_key": "", "api_endpoint": "https://fcm.googleapis.com/fcm/send"}', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('apns', 'production', '{"team_id": "", "key_id": "", "bundle_id": "", "api_endpoint": "https://api.push.apple.com"}', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('apns', 'development', '{"team_id": "", "key_id": "", "bundle_id": "", "api_endpoint": "https://api.development.push.apple.com"}', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('jpush', 'production', '{"app_key": "", "app_secret": "", "api_endpoint": "https://api.jpush.cn/v3/push"}', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());
```

### 1.2 推送Token管理器实现

#### 创建Token管理器
```go
// 创建文件：internal/biz/push_token_manager.go
package biz

import (
    "context"
    "crypto/md5"
    "fmt"
    "strings"
    "time"
    
    "github.com/go-kratos/kratos/v2/log"
    "github.com/wlnil/life-log-be/internal/pkg/cache"
    "github.com/wlnil/life-log-be/internal/pkg/enums"
    v1 "github.com/wlnil/life-log-be/api/user/v1"
)

// PushTokenManager 推送Token管理器
type PushTokenManager struct {
    userRepo   UserRepo
    multiCache *cache.MultiLevelCache
    log        *log.Helper
}

// NewPushTokenManager 创建推送Token管理器
func NewPushTokenManager(userRepo UserRepo, multiCache *cache.MultiLevelCache, logger log.Logger) *PushTokenManager {
    return &PushTokenManager{
        userRepo:   userRepo,
        multiCache: multiCache,
        log:        log.NewHelper(logger),
    }
}

// UpdateTokensFromLoginRequest 从登录请求更新推送Token
func (ptm *PushTokenManager) UpdateTokensFromLoginRequest(ctx context.Context, userID int32, req *v1.LoginRequest) error {
    var updates []TokenUpdate
    
    // FCM Token处理
    if req.FcmToken != "" {
        updates = append(updates, TokenUpdate{
            UserID:      userID,
            TokenType:   "fcm",
            DeviceToken: req.FcmToken,
            Platform:    ptm.determinePlatform(req.Platform),
            Environment: "production",
            DeviceID:    req.DeviceId,
        })
    }
    
    // APNs Token处理
    if req.ApnsToken != "" {
        updates = append(updates, TokenUpdate{
            UserID:      userID,
            TokenType:   "apns",
            DeviceToken: req.ApnsToken,
            Platform:    enums.UserTokenPlatformIOS,
            Environment: ptm.determineAPNsEnvironment(req),
            DeviceID:    req.DeviceId,
        })
    }
    
    // JPush Registration ID处理
    if req.JpushRegistrationId != "" {
        updates = append(updates, TokenUpdate{
            UserID:      userID,
            TokenType:   "jpush",
            DeviceToken: req.JpushRegistrationId,
            Platform:    ptm.determinePlatform(req.Platform),
            Environment: "production",
            DeviceID:    req.DeviceId,
            ExtraData: map[string]string{
                "channel": req.RecommendedPushChannel,
            },
        })
    }
    
    // 批量更新Token
    return ptm.batchUpdateTokens(ctx, updates)
}

// TokenUpdate Token更新结构
type TokenUpdate struct {
    UserID      int32
    TokenType   string
    DeviceToken string
    Platform    enums.UserTokenPlatformType
    Environment string
    DeviceID    string
    ExtraData   map[string]string
}

// batchUpdateTokens 批量更新Token
func (ptm *PushTokenManager) batchUpdateTokens(ctx context.Context, updates []TokenUpdate) error {
    successCount := 0
    for _, update := range updates {
        if err := ptm.updateSingleToken(ctx, update); err != nil {
            ptm.log.Errorf("更新Token失败: %v, update: %+v", err, update)
        } else {
            successCount++
        }
    }
    
    ptm.log.Infof("批量更新Token完成，成功: %d, 总数: %d", successCount, len(updates))
    return nil
}

// updateSingleToken 更新单个Token
func (ptm *PushTokenManager) updateSingleToken(ctx context.Context, update TokenUpdate) error {
    // 1. 生成Token哈希用于去重
    tokenHash := ptm.generateTokenHash(update.DeviceToken)
    
    // 2. 检查是否存在相同的Token
    existing, err := ptm.findExistingTokenByHash(ctx, update.UserID, update.TokenType, tokenHash)
    if err != nil && err.Error() != "record not found" {
        return err
    }
    
    // 3. 如果Token相同，只更新验证时间
    if existing != nil {
        return ptm.updateTokenVerificationTime(ctx, existing.ID)
    }
    
    // 4. 使同类型的旧Token失效
    if err := ptm.invalidateTokensByType(ctx, update.UserID, update.TokenType, update.DeviceID); err != nil {
        ptm.log.Warnf("使旧Token失效失败: %v", err)
    }
    
    // 5. 创建新Token
    newToken := &UserToken{
        BasicModel: BasicModel{
            CreatedAt: time.Now().Unix(),
            UpdatedAt: time.Now().Unix(),
        },
        UserID:              update.UserID,
        DeviceToken:         update.DeviceToken,
        TokenType:           update.TokenType,
        Platform:            update.Platform,
        DeviceID:            update.DeviceID,
        TokenStatus:         1,
        PushEnvironment:     update.Environment,
        LastVerifiedAt:      time.Now().Unix(),
        IsValid:             true,
        ExpireAt:            time.Now().Add(365 * 24 * time.Hour).Unix(),
        AutoRefreshEnabled:  true,
        TokenPriority:       1,
    }
    
    // 6. 保存到数据库
    if err := ptm.userRepo.CreateUserToken(ctx, newToken); err != nil {
        return err
    }
    
    // 7. 更新缓存
    ptm.invalidateUserTokenCache(ctx, update.UserID)
    
    ptm.log.Infof("成功创建新Token: userID=%d, type=%s, platform=%d", 
        update.UserID, update.TokenType, update.Platform)
    
    return nil
}

// generateTokenHash 生成Token哈希
func (ptm *PushTokenManager) generateTokenHash(token string) string {
    hash := md5.Sum([]byte(token))
    return fmt.Sprintf("%x", hash)
}

// determinePlatform 确定平台类型
func (ptm *PushTokenManager) determinePlatform(platform string) enums.UserTokenPlatformType {
    switch strings.ToLower(platform) {
    case "ios":
        return enums.UserTokenPlatformIOS
    case "android":
        return enums.UserTokenPlatformAndroid
    case "web", "website":
        return enums.UserTokenPlatformWebsite
    default:
        return enums.UserTokenPlatformUnknown
    }
}

// determineAPNsEnvironment 确定APNs环境
func (ptm *PushTokenManager) determineAPNsEnvironment(req *v1.LoginRequest) string {
    // 简化判断逻辑，实际可以根据更多因素判断
    if req.AppVersion != "" && (strings.Contains(req.AppVersion, "debug") || 
                                strings.Contains(req.AppVersion, "dev")) {
        return "development"
    }
    return "production"
}

// findExistingTokenByHash 根据哈希查找现有Token
func (ptm *PushTokenManager) findExistingTokenByHash(ctx context.Context, userID int32, tokenType, tokenHash string) (*UserToken, error) {
    // 这里需要在UserRepo中添加相应的查询方法
    // 暂时返回nil，表示未找到
    return nil, fmt.Errorf("record not found")
}

// updateTokenVerificationTime 更新Token验证时间
func (ptm *PushTokenManager) updateTokenVerificationTime(ctx context.Context, tokenID int32) error {
    // 这里需要在UserRepo中添加相应的更新方法
    return nil
}

// invalidateTokensByType 使指定类型的Token失效
func (ptm *PushTokenManager) invalidateTokensByType(ctx context.Context, userID int32, tokenType, deviceID string) error {
    // 这里需要在UserRepo中添加相应的方法
    return nil
}

// invalidateUserTokenCache 清除用户Token缓存
func (ptm *PushTokenManager) invalidateUserTokenCache(ctx context.Context, userID int32) {
    cacheKey := fmt.Sprintf("user_tokens_%d", userID)
    if ptm.multiCache != nil {
        ptm.multiCache.Delete(ctx, cacheKey)
    }
}
```

### 1.3 集成到现有登录流程

#### 修改UserUsecase构造函数
```go
// 修改 internal/biz/user.go
type UserUsecase struct {
    repo             UserRepo
    authUc           *UserAuthUsecase
    commonUseCase    *CommonUsecase
    cronTaskUsecase  *CronTaskUsecase
    auditHelper      *AuditHelper
    firebaseAuth     firebase.FirebaseAuthInterface
    multiCache       *cache.MultiLevelCache
    pushTokenManager *PushTokenManager  // 新增

    log      *log.Helper
    confData *conf.Data
}

// 更新构造函数
func NewUserUsecase(
    repo UserRepo, 
    logger log.Logger, 
    authUc *UserAuthUsecase, 
    commonUseCase *CommonUsecase, 
    cronTaskUsecase *CronTaskUsecase, 
    auditHelper *AuditHelper, 
    firebaseAuth firebase.FirebaseAuthInterface, 
    multiCache *cache.MultiLevelCache, 
    confData *conf.Data,
) *UserUsecase {
    uc := &UserUsecase{
        repo:            repo,
        log:             log.NewHelper(logger),
        confData:        confData,
        authUc:          authUc,
        commonUseCase:   commonUseCase,
        cronTaskUsecase: cronTaskUsecase,
        auditHelper:     auditHelper,
        firebaseAuth:    firebaseAuth,
        multiCache:      multiCache,
    }
    
    // 初始化推送Token管理器
    uc.pushTokenManager = NewPushTokenManager(repo, multiCache, logger)
    
    return uc
}
```

#### 更新异步操作
```go
// 修改 internal/biz/async_operations.go
func (uc *UserUsecase) updatePushSettingsAsync(ctx context.Context, userID int32, req *v1.LoginRequest) error {
    // 1. 更新推送Token（新增）
    if uc.pushTokenManager != nil {
        if err := uc.pushTokenManager.UpdateTokensFromLoginRequest(ctx, userID, req); err != nil {
            uc.log.Errorf("更新推送Token失败: %v", err)
            // 不返回错误，继续处理推送设置
        }
    }
    
    // 2. 更新推送设置
    pushSetting := &UserPushSetting{
        UserID:                userID,
        PushPermissionGranted: req.PushPermissionGranted,
        PreferredPushService:  uc.determinePushService(req.PushServiceType, req.Platform),
        PushChannel:           req.RecommendedPushChannel,
        FirebaseEnabled:       true,
        PushEnvironment:       uc.determinePushEnvironment(req),
    }
    
    return uc.repo.CreateOrUpdateUserPushSetting(ctx, pushSetting)
}

// determinePushEnvironment 确定推送环境
func (uc *UserUsecase) determinePushEnvironment(req *v1.LoginRequest) string {
    // 根据应用版本等信息判断环境
    if req.AppVersion != "" && strings.Contains(req.AppVersion, "debug") {
        return "development"
    }
    return "production"
}
```

## 📋 Phase 2: 中优先级优化（2周内实施）

### 2.1 多级缓存集成

#### 扩展多级缓存功能
```go
// 扩展 internal/pkg/cache/multi_level_cache.go
// GetPushServiceConfig 获取推送服务配置
func (mlc *MultiLevelCache) GetPushServiceConfig(ctx context.Context, serviceName, environment string) (map[string]interface{}, error) {
    cacheKey := fmt.Sprintf("push_config_%s_%s", serviceName, environment)
    
    // 1. 本地缓存查询
    if value, ok := mlc.localCache.Get(cacheKey); ok {
        mlc.log.Debugf("【推送配置】本地缓存命中: %s", cacheKey)
        return value.(map[string]interface{}), nil
    }
    
    // 2. Redis缓存查询
    if configData, err := mlc.redisClient.Get(ctx, cacheKey).Result(); err == nil {
        var config map[string]interface{}
        if err := json.Unmarshal([]byte(configData), &config); err == nil {
            mlc.localCache.Set(cacheKey, config, 30*time.Minute)
            mlc.log.Debugf("【推送配置】Redis缓存命中: %s", cacheKey)
            return config, nil
        }
    }
    
    mlc.log.Debugf("【推送配置】缓存未命中: %s", cacheKey)
    return nil, fmt.Errorf("cache miss")
}

// SetPushServiceConfig 设置推送服务配置缓存
func (mlc *MultiLevelCache) SetPushServiceConfig(ctx context.Context, serviceName, environment string, config map[string]interface{}) error {
    cacheKey := fmt.Sprintf("push_config_%s_%s", serviceName, environment)
    
    // 1. 设置本地缓存
    mlc.localCache.Set(cacheKey, config, 30*time.Minute)
    
    // 2. 设置Redis缓存
    configBytes, err := json.Marshal(config)
    if err != nil {
        return err
    }
    
    return mlc.redisClient.Set(ctx, cacheKey, configBytes, 2*time.Hour).Err()
}
```

### 2.2 智能推送路由

#### 创建智能路由器
```go
// 创建文件：internal/biz/push_router.go
package biz

import (
    "context"
    "fmt"
    "time"
    
    "github.com/go-kratos/kratos/v2/log"
    "github.com/wlnil/life-log-be/internal/pkg/cache"
)

// SmartPushRouter 智能推送路由器
type SmartPushRouter struct {
    userRepo   UserRepo
    pushRepo   PushNotificationRepo
    multiCache *cache.MultiLevelCache
    log        *log.Helper
}

// NewSmartPushRouter 创建智能推送路由器
func NewSmartPushRouter(
    userRepo UserRepo,
    pushRepo PushNotificationRepo,
    multiCache *cache.MultiLevelCache,
    logger log.Logger,
) *SmartPushRouter {
    return &SmartPushRouter{
        userRepo:   userRepo,
        pushRepo:   pushRepo,
        multiCache: multiCache,
        log:        log.NewHelper(logger),
    }
}

// RouteRequest 智能路由推送请求
func (spr *SmartPushRouter) RouteRequest(ctx context.Context, req *PushRequest) (string, error) {
    // 1. 获取用户信息
    user, err := spr.getUserInfo(ctx, req.UserID)
    if err != nil {
        return "", err
    }
    
    // 2. 获取用户推送偏好
    pushSetting, err := spr.getUserPushSetting(ctx, req.UserID)
    if err != nil {
        spr.log.Warnf("获取用户推送设置失败，使用默认设置: %v", err)
        pushSetting = spr.getDefaultPushSetting(user)
    }
    
    // 3. 确定最佳推送服务
    service := spr.determineBestService(user, pushSetting, req)
    
    // 4. 检查服务可用性
    if !spr.isServiceAvailable(ctx, service) {
        fallbackService := spr.getFallbackService(user, req)
        spr.log.Warnf("推送服务 %s 不可用，切换到备用服务 %s", service, fallbackService)
        service = fallbackService
    }
    
    return service, nil
}

// determineBestService 确定最佳推送服务
func (spr *SmartPushRouter) determineBestService(user *User, setting *UserPushSetting, req *PushRequest) string {
    // 1. 用户明确指定的服务
    if setting.PreferredPushService != "" && setting.PreferredPushService != "auto" {
        return setting.PreferredPushService
    }
    
    // 2. 基于用户区域的智能选择
    if user.Region == "china" {
        return "jpush"
    }
    
    // 3. 基于平台的默认选择
    switch req.Platform {
    case "ios", "1":
        return "apns"
    case "android", "2":
        return "fcm"
    default:
        return "jpush"
    }
}

// isServiceAvailable 检查服务可用性
func (spr *SmartPushRouter) isServiceAvailable(ctx context.Context, serviceName string) bool {
    cacheKey := fmt.Sprintf("push_service_status_%s", serviceName)
    
    // 从缓存中获取服务状态
    if statusData, err := spr.multiCache.Get(ctx, cacheKey); err == nil {
        if status, ok := statusData.(bool); ok {
            return status
        }
    }
    
    // 如果缓存中没有状态信息，默认认为服务可用
    // 实际实现中可以添加健康检查逻辑
    return true
}

// getFallbackService 获取备用服务
func (spr *SmartPushRouter) getFallbackService(user *User, req *PushRequest) string {
    // 简单的备用策略：中国用户使用极光，海外用户使用OneSignal
    if user.Region == "china" {
        return "jpush"
    }
    return "onesignal"
}

// getUserInfo 获取用户信息（带缓存）
func (spr *SmartPushRouter) getUserInfo(ctx context.Context, userID int32) (*User, error) {
    cacheKey := fmt.Sprintf("user_info_%d", userID)
    
    // 尝试从缓存获取
    if userData, err := spr.multiCache.GetUserInfo(ctx, userID); err == nil {
        // 转换为User对象
        user := &User{
            ID:     userID,
            Region: userData["region"].(string),
            // 其他必要字段...
        }
        return user, nil
    }
    
    // 从数据库获取
    user := &User{ID: userID}
    if err := spr.userRepo.FindByID(ctx, user); err != nil {
        return nil, err
    }
    
    return user, nil
}

// getUserPushSetting 获取用户推送设置（带缓存）
func (spr *SmartPushRouter) getUserPushSetting(ctx context.Context, userID int32) (*UserPushSetting, error) {
    return spr.pushRepo.GetUserPushSetting(ctx, userID)
}

// getDefaultPushSetting 获取默认推送设置
func (spr *SmartPushRouter) getDefaultPushSetting(user *User) *UserPushSetting {
    service := "jpush"
    if user.Region != "china" {
        service = "fcm"
    }
    
    return &UserPushSetting{
        UserID:               user.ID,
        PreferredPushService: service,
        PushPermissionGranted: true,
        HabitReminderEnabled: true,
        FirebaseEnabled:      true,
    }
}
```

## 🚀 部署脚本

### 创建部署脚本
```bash
#!/bin/bash
# 创建文件：scripts/deploy_push_optimization.sh

echo "🚀 开始部署推送通知优化..."

# 1. 执行数据库结构升级
echo "📊 执行数据库结构升级..."
mysql -u $DB_USER -p$DB_PASSWORD $DB_NAME < sql/push_optimization_schema.sql

# 2. 编译新版本
echo "🔨 编译新版本..."
go build -o life-log-be-push-optimized ./cmd/life-log-be

# 3. 运行测试
echo "🧪 运行推送相关测试..."
go test ./internal/biz/push* -v

# 4. 部署新版本
echo "🚀 部署新版本..."
mv life-log-be-push-optimized life-log-be
chmod +x life-log-be

echo "✅ 推送通知优化部署完成！"
```

通过这个分阶段的实施方案，life-log-be的推送通知系统将得到全面优化，与现有的登录优化架构完美集成，实现高性能、高可靠性的推送服务。
