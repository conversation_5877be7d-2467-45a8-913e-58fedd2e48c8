# Firebase认证配置指南

## 🎯 概述

本文档说明如何配置Firebase认证服务，以支持life-log-be项目的统一第三方认证功能。

## 📋 配置步骤

### 1. 环境变量配置

在服务器环境中设置以下环境变量：

```bash
# Firebase项目配置
export FIREBASE_PROJECT_ID="your-firebase-project-id"

# 开发/测试环境可以启用模拟模式
export FIREBASE_MOCK_MODE="true"  # 生产环境设置为false或不设置
```

### 2. 生产环境配置

#### 2.1 获取Firebase项目ID

1. 登录 [Firebase Console](https://console.firebase.google.com/)
2. 选择你的项目
3. 点击项目设置（齿轮图标）
4. 在"常规"标签页中找到"项目ID"

#### 2.2 配置Firebase Admin SDK（可选）

如果需要使用Firebase Admin SDK进行更严格的token验证：

1. 在Firebase Console中生成服务账号密钥
2. 下载JSON密钥文件
3. 设置环境变量：

```bash
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/firebase-service-account.json"
```

### 3. 开发环境配置

#### 3.1 使用模拟模式

开发环境可以使用模拟模式，无需真实的Firebase配置：

```bash
# 启用模拟模式
export FIREBASE_MOCK_MODE="true"
```

#### 3.2 模拟token格式

模拟模式支持以下token格式：

```javascript
// Google登录模拟token
"mock_google_" + 任意字符串

// Apple登录模拟token  
"mock_apple_" + 任意字符串

// 无效token测试
"mock_invalid_" + 任意字符串

// 默认Firebase用户
其他任意字符串
```

### 4. Docker环境配置

#### 4.1 docker-compose.yml

```yaml
version: '3.8'
services:
  life-log-be:
    image: life-log-be:latest
    environment:
      - FIREBASE_PROJECT_ID=your-firebase-project-id
      - FIREBASE_MOCK_MODE=false
    volumes:
      - ./firebase-service-account.json:/app/firebase-service-account.json
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/app/firebase-service-account.json
```

#### 4.2 Kubernetes配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: firebase-config
data:
  FIREBASE_PROJECT_ID: "your-firebase-project-id"
  FIREBASE_MOCK_MODE: "false"
---
apiVersion: v1
kind: Secret
metadata:
  name: firebase-secret
type: Opaque
data:
  service-account.json: <base64-encoded-json>
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: life-log-be
spec:
  template:
    spec:
      containers:
      - name: life-log-be
        image: life-log-be:latest
        envFrom:
        - configMapRef:
            name: firebase-config
        env:
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/etc/firebase/service-account.json"
        volumeMounts:
        - name: firebase-secret
          mountPath: "/etc/firebase"
          readOnly: true
      volumes:
      - name: firebase-secret
        secret:
          secretName: firebase-secret
```

## 🔧 配置验证

### 1. 检查配置

```bash
# 检查环境变量
echo "Firebase Project ID: $FIREBASE_PROJECT_ID"
echo "Mock Mode: $FIREBASE_MOCK_MODE"

# 检查服务账号文件（如果使用）
if [ -n "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
    echo "Service Account File: $GOOGLE_APPLICATION_CREDENTIALS"
    ls -la "$GOOGLE_APPLICATION_CREDENTIALS"
fi
```

### 2. 运行测试

```bash
# 给测试脚本执行权限
chmod +x scripts/test_firebase_auth.sh

# 运行Firebase认证测试
./scripts/test_firebase_auth.sh
```

### 3. 验证日志

启动服务后，查看日志确认Firebase配置：

```bash
# 查看服务日志
tail -f logs/app.log | grep -i firebase

# 期望看到的日志
# INFO: 使用Firebase认证客户端，项目ID: your-project-id
# 或
# INFO: 使用Firebase模拟认证客户端
```

## 🚨 故障排除

### 1. 常见问题

#### 问题1: "Firebase Project ID未配置"
```
解决方案: 设置FIREBASE_PROJECT_ID环境变量
export FIREBASE_PROJECT_ID="your-firebase-project-id"
```

#### 问题2: "Firebase token验证失败"
```
可能原因:
1. token已过期
2. 项目ID不匹配
3. 网络连接问题

解决方案:
1. 检查客户端token生成逻辑
2. 验证FIREBASE_PROJECT_ID配置
3. 检查网络连接到googleapis.com
```

#### 问题3: "模拟模式下仍然调用真实API"
```
解决方案: 确保FIREBASE_MOCK_MODE=true
export FIREBASE_MOCK_MODE="true"
```

### 2. 调试模式

启用详细日志：

```bash
# 设置日志级别
export LOG_LEVEL="debug"

# 启用Firebase调试日志
export FIREBASE_DEBUG="true"
```

### 3. 网络问题

如果服务器无法访问Google API：

```bash
# 测试网络连接
curl -I https://oauth2.googleapis.com/tokeninfo

# 配置代理（如果需要）
export HTTP_PROXY="http://proxy.company.com:8080"
export HTTPS_PROXY="http://proxy.company.com:8080"
```

## 📊 监控和指标

### 1. 关键指标

- Firebase token验证成功率
- 认证响应时间
- 错误率和错误类型

### 2. 告警配置

建议配置以下告警：

```yaml
# Prometheus告警规则示例
groups:
- name: firebase_auth
  rules:
  - alert: FirebaseAuthFailureRate
    expr: rate(firebase_auth_failures_total[5m]) > 0.1
    for: 2m
    annotations:
      summary: "Firebase认证失败率过高"
      
  - alert: FirebaseAuthLatency
    expr: histogram_quantile(0.95, firebase_auth_duration_seconds) > 2
    for: 5m
    annotations:
      summary: "Firebase认证延迟过高"
```

## 🔄 回滚方案

如果需要回滚到旧的认证方式：

1. **代码回滚**：
   ```bash
   git checkout previous-commit
   go build ./cmd/life-log-be
   ```

2. **配置回滚**：
   ```bash
   unset FIREBASE_PROJECT_ID
   export FIREBASE_MOCK_MODE="true"
   ```

3. **数据库回滚**：
   ```sql
   -- 执行回滚脚本（如果需要）
   source sql/rollback_firebase_auth.sql
   ```

## 📚 参考资料

- [Firebase Authentication文档](https://firebase.google.com/docs/auth)
- [Firebase Admin SDK文档](https://firebase.google.com/docs/admin/setup)
- [Google OAuth2 API文档](https://developers.google.com/identity/protocols/oauth2)

通过以上配置，life-log-be项目将能够正确使用Firebase统一认证服务。
