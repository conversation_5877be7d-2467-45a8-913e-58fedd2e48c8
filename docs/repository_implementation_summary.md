# Repository层事务支持实现总结

## 概述

为推送通知系统的Repository层实现了完整的事务支持，包括分布式锁、乐观锁和事务一致性保证。

## 新增接口定义

### Transaction接口
```go
// Transaction 事务接口
type Transaction interface {
    Commit() error
    Rollback() error
}
```

### PushNotificationRepo接口扩展
```go
type PushNotificationRepo interface {
    // 原有方法...
    CreatePushTask(ctx context.Context, task *PushTask) error
    UpdatePushTask(ctx context.Context, task *PushTask) error
    GetPushTaskByID(ctx context.Context, taskID int64) (*PushTask, error
    GetPendingTasks(ctx context.Context, limit int32) ([]*PushTask, error)
    GetPendingTasksPaginated(ctx context.Context, limit, offset int) ([]*PushTask, error)
    DeleteTasksByUserHabit(ctx context.Context, userID, userHabitID int32) error
    
    // 新增：事务支持
    BeginTx(ctx context.Context) (Transaction, error)
    UpdatePushTaskWithVersion(ctx context.Context, tx Transaction, task *PushTask, expectedVersion int32) error
    CreatePushTaskInTx(ctx context.Context, tx Transaction, task *PushTask) error
}
```

## Repository层实现

### 1. 事务包装器实现

```go
// gormTransaction 实现 biz.Transaction 接口
type gormTransaction struct {
    tx *gorm.DB
}

func (t *gormTransaction) Commit() error {
    return t.tx.Commit().Error
}

func (t *gormTransaction) Rollback() error {
    return t.tx.Rollback().Error
}
```

### 2. 事务开始方法

```go
// BeginTx 开始事务
func (r *pushTaskRepo) BeginTx(ctx context.Context) (biz.Transaction, error) {
    tx := r.data.DB.WithContext(ctx).Begin()
    if tx.Error != nil {
        return nil, tx.Error
    }
    return &gormTransaction{tx: tx}, nil
}
```

### 3. 乐观锁更新方法

```go
// UpdatePushTaskWithVersion 使用乐观锁更新推送任务
func (r *pushTaskRepo) UpdatePushTaskWithVersion(ctx context.Context, tx biz.Transaction, task *biz.PushTask, expectedVersion int32) error {
    var db *gorm.DB
    if tx != nil {
        gormTx, ok := tx.(*gormTransaction)
        if !ok {
            return fmt.Errorf("无效的事务类型")
        }
        db = gormTx.tx
    } else {
        db = r.data.DB.WithContext(ctx)
    }

    // 使用乐观锁更新，检查版本号
    result := db.Model(task).
        Where("id = ? AND version = ?", task.ID, expectedVersion).
        Updates(map[string]interface{}{
            "user_id":           task.UserID,
            "task_type":         task.TaskType,
            "related_id":        task.RelatedID,
            "title":             task.Title,
            "content":           task.Content,
            "scheduled_time":    task.ScheduledTime,
            "status":            task.Status,
            "is_recurring":      task.IsRecurring,
            "repeat_rule":       task.RepeatRule,
            "reminder_time":     task.ReminderTime,
            "execute_time":      task.ExecuteTime,
            "error_msg":         task.ErrorMsg,
            "failure_count":     task.FailureCount,
            "max_failures":      task.MaxFailures,
            "last_failure_time": task.LastFailureTime,
            "failure_reason":    task.FailureReason,
            "version":           task.Version,
            "updated_at":        time.Now(),
        })

    if result.Error != nil {
        return result.Error
    }

    if result.RowsAffected == 0 {
        return fmt.Errorf("乐观锁冲突：任务可能已被其他进程修改")
    }

    return nil
}
```

### 4. 事务中创建方法

```go
// CreatePushTaskInTx 在事务中创建推送任务
func (r *pushTaskRepo) CreatePushTaskInTx(ctx context.Context, tx biz.Transaction, task *biz.PushTask) error {
    var db *gorm.DB
    if tx != nil {
        gormTx, ok := tx.(*gormTransaction)
        if !ok {
            return fmt.Errorf("无效的事务类型")
        }
        db = gormTx.tx
    } else {
        db = r.data.DB.WithContext(ctx)
    }

    return db.Create(task).Error
}
```

## 核心特性

### 1. 事务一致性
- 支持数据库事务的开始、提交和回滚
- 确保多个数据库操作的原子性
- 支持事务内和事务外的操作

### 2. 乐观锁并发控制
- 使用版本号防止并发修改冲突
- 在更新时检查版本号，确保数据一致性
- 提供明确的冲突错误信息

### 3. 灵活的事务支持
- 方法支持传入事务参数，也支持不使用事务
- 自动判断是否在事务中执行
- 类型安全的事务接口

### 4. 完整的字段更新
- 乐观锁更新包含所有字段
- 自动更新时间戳
- 支持失败处理相关的新字段

## 使用示例

### 基本事务使用
```go
// 开始事务
tx, err := repo.BeginTx(ctx)
if err != nil {
    return err
}
defer tx.Rollback()

// 在事务中创建任务
err = repo.CreatePushTaskInTx(ctx, tx, newTask)
if err != nil {
    return err
}

// 使用乐观锁更新任务
err = repo.UpdatePushTaskWithVersion(ctx, tx, task, originalVersion)
if err != nil {
    return err
}

// 提交事务
return tx.Commit()
```

### 乐观锁冲突处理
```go
originalVersion := task.Version
task.Version++

err := repo.UpdatePushTaskWithVersion(ctx, tx, task, originalVersion)
if err != nil {
    if strings.Contains(err.Error(), "乐观锁冲突") {
        // 处理并发冲突
        return handleConcurrencyConflict(task)
    }
    return err
}
```

## 错误处理

### 1. 事务错误
- 事务开始失败：返回具体的数据库错误
- 事务提交失败：返回提交错误信息
- 事务回滚失败：记录错误但不影响主流程

### 2. 乐观锁错误
- 版本冲突：返回明确的冲突信息
- 无效事务类型：类型断言失败时的错误处理
- 数据库操作失败：返回底层数据库错误

### 3. 类型安全
- 事务接口类型检查
- 参数验证
- 空值检查

## 性能优化

### 1. 批量更新
- 使用map进行批量字段更新
- 避免逐个字段的单独更新
- 减少数据库交互次数

### 2. 索引优化
- 在version字段上建立索引
- 复合索引支持乐观锁查询
- 优化WHERE条件的执行效率

### 3. 连接管理
- 复用数据库连接
- 事务超时控制
- 连接池优化

## 部署注意事项

1. **数据库迁移**：确保新字段已添加到数据库
2. **索引创建**：为version字段创建索引
3. **事务超时**：配置合适的事务超时时间
4. **监控告警**：监控乐观锁冲突频率

## 总结

Repository层的事务支持实现提供了：
- ✅ 完整的事务一致性保证
- ✅ 乐观锁并发控制
- ✅ 类型安全的接口设计
- ✅ 灵活的使用方式
- ✅ 完善的错误处理
- ✅ 良好的性能优化

这个实现为推送通知系统提供了生产环境所需的数据一致性和并发安全保障。
