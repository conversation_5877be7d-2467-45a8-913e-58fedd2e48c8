package main

import (
	"context"
	"flag"
	"os"

	"github.com/getsentry/sentry-go"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/sdk/resource"
	tracesdk "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.20.0"

	"github.com/wlnil/life-log-be/internal/biz"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/infrastructure"
	"github.com/wlnil/life-log-be/internal/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"

	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, hs *http.Server, auditService *service.AsyncAuditService, pushUsecase *biz.PushNotificationUsecase, infra *infrastructure.Infrastructure) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			hs,
		),
		// 添加启动钩子来启动审计服务和推送调度器
		kratos.BeforeStart(func(ctx context.Context) error {
			logger.Log(log.LevelInfo, "msg", "启动异步审计服务")
			auditService.Start()

			logger.Log(log.LevelInfo, "msg", "启动推送通知调度器")
			if err := pushUsecase.StartScheduler(ctx); err != nil {
				logger.Log(log.LevelError, "msg", "启动推送调度器失败", "error", err)
				return err
			}
			return nil
		}),
		// 添加停止钩子来停止审计服务和推送调度器
		kratos.BeforeStop(func(ctx context.Context) error {
			logger.Log(log.LevelInfo, "msg", "停止异步审计服务")
			auditService.Stop()

			logger.Log(log.LevelInfo, "msg", "停止推送通知调度器")
			pushUsecase.StopScheduler()

			logger.Log(log.LevelInfo, "msg", "停止基础设施服务")
			infra.Cleanup()
			return nil
		}),
	)
}

// Set global trace provider
func initTracer(url string) error {
	// Create the Jaeger exporter
	exp, err := jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(url)))
	if err != nil {
		return err
	}
	tp := tracesdk.NewTracerProvider(
		// Set the sampling rate based on the parent span to 100%
		tracesdk.WithSampler(tracesdk.ParentBased(tracesdk.TraceIDRatioBased(1.0))),
		// Always be sure to batch in production.
		tracesdk.WithBatcher(exp),
		// Record information about this application in a Resource.
		tracesdk.WithResource(resource.NewSchemaless(
			semconv.ServiceNameKey.String(Name),
			attribute.String("env", "dev"),
		)),
	)
	otel.SetTracerProvider(tp)
	return nil
}

func initSentry(dsn string) error {
	return sentry.Init(sentry.ClientOptions{
		Dsn:              dsn,
		AttachStacktrace: true,
		EnableTracing:    true,
		TracesSampleRate: 1.0,
	})
}

func main() {
	flag.Parse()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	if bc.Data.JaegerUrl != "" {
		if err := initTracer(bc.Data.JaegerUrl); err != nil {
			log.Error(err)
		}
	}

	if bc.Data.SentryDsn != "" {
		log.Info("sentry init...")
		if err := initSentry(bc.Data.SentryDsn); err != nil {
			log.Error(err)
		}
	}

	app, cleanup, err := wireApp(bc.Server, bc.Data, bc.Auth, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
