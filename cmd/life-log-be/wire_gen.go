// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/data"
	"github.com/wlnil/life-log-be/internal/pkg/cache"
	"github.com/wlnil/life-log-be/internal/pkg/infrastructure"
	"github.com/wlnil/life-log-be/internal/server"
	"github.com/wlnil/life-log-be/internal/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, auth *conf.Auth, logger log.Logger) (*kratos.App, func(), error) {
	dataData, cleanup, err := data.NewData(confData, auth, logger)
	if err != nil {
		return nil, nil, err
	}
	userRepo := data.NewUserRepo(dataData, logger)
	userAuthRepo := data.NewUserAuthRepo(dataData, logger)
	userAuthUsecase := biz.NewUserAuthUsecase(userAuthRepo, logger)
	userPlanetRepo := data.NewUserPlanetRepo(dataData, logger)
	planetRepo := data.NewPlanetRepo(dataData, logger)
	userHabitRepo := data.NewUserHabitRepo(dataData, logger)
	commonUsecase := biz.NewCommonUsecase(logger, confData, userPlanetRepo, planetRepo, userRepo, userHabitRepo)
	cronTaskRepo := data.NewCronTaskRepo(dataData, logger)
	messageRepo := data.NewMessageRepo(dataData, logger)
	cronTaskUsecase := biz.NewCronTaskUsecase(cronTaskRepo, messageRepo, logger, userHabitRepo, confData, commonUsecase, userRepo)
	userDeviceRepo := data.NewUserDeviceRepo(dataData, logger)
	asyncAuditService := service.NewAsyncAuditService(userDeviceRepo, logger)
	auditHelper := biz.NewAuditHelper(asyncAuditService, logger)
	client := data.NewRedisClient(confData, logger)
	multiLevelCache := cache.NewMultiLevelCache(client, logger)
	pushNotificationRepo := data.NewPushTaskRepo(dataData, logger)
	pushNotificationUsecase := biz.NewPushNotificationUsecase(pushNotificationRepo, userHabitRepo, userRepo, client, logger)
	enhancedAuthUsecase := biz.NewEnhancedAuthUsecase(userRepo, userAuthRepo, userDeviceRepo, pushNotificationUsecase, asyncAuditService, logger)
	userUsecase := biz.NewUserUsecase(userRepo, logger, userAuthUsecase, commonUsecase, cronTaskUsecase, auditHelper, multiLevelCache, enhancedAuthUsecase, confData)
	siteSettingRepo := data.NewSiteSettingRepo(dataData, logger)
	siteSettingUsecase := biz.NewSiteSettingUsecase(siteSettingRepo, logger, confData)
	userHabitUsecase := biz.NewUserHabitUsecase(userHabitRepo, logger, userRepo, userPlanetRepo, cronTaskRepo, messageRepo, confData, commonUsecase, pushNotificationUsecase)
	planetUsecase := biz.NewPlanetUsecase(planetRepo, logger)
	userPlanetUsecase := biz.NewUserPlanetUsecase(userPlanetRepo, logger, planetRepo, userRepo, confData, commonUsecase)
	messageUsecase := biz.NewMessageUsecase(messageRepo, logger)
	userStatisticUsecase := biz.NewUserStatisticUsecase(userRepo, logger, commonUsecase, userHabitUsecase)
	orderRepo := data.NewOrderRepo(dataData, logger)
	orderUsecase := biz.NewOrderUsecase(orderRepo, logger, confData, commonUsecase, userRepo)
	lifeLogInterface := service.NewLifeLogInterface(userUsecase, siteSettingUsecase, userHabitUsecase, planetUsecase, userPlanetUsecase, cronTaskUsecase, messageUsecase, userStatisticUsecase, orderUsecase, logger)
	httpServer := server.NewHTTPServer(confServer, auth, confData, lifeLogInterface, logger)
	infrastructureInfrastructure := infrastructure.NewInfrastructure(confData, logger)
	app := newApp(logger, httpServer, asyncAuditService, pushNotificationUsecase, infrastructureInfrastructure)
	return app, func() {
		cleanup()
	}, nil
}
