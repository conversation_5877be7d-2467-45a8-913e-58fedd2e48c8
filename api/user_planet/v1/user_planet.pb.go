// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: user_planet/v1/user_planet.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type JoinPlanetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinPlanetRequest) Reset() {
	*x = JoinPlanetRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinPlanetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinPlanetRequest) ProtoMessage() {}

func (x *JoinPlanetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinPlanetRequest.ProtoReflect.Descriptor instead.
func (*JoinPlanetRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{0}
}

func (x *JoinPlanetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type JoinPlanetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinPlanetReply) Reset() {
	*x = JoinPlanetReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinPlanetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinPlanetReply) ProtoMessage() {}

func (x *JoinPlanetReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinPlanetReply.ProtoReflect.Descriptor instead.
func (*JoinPlanetReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{1}
}

func (x *JoinPlanetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *JoinPlanetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type QuitPlanetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuitPlanetRequest) Reset() {
	*x = QuitPlanetRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuitPlanetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuitPlanetRequest) ProtoMessage() {}

func (x *QuitPlanetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuitPlanetRequest.ProtoReflect.Descriptor instead.
func (*QuitPlanetRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{2}
}

func (x *QuitPlanetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type QuitPlanetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuitPlanetReply) Reset() {
	*x = QuitPlanetReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuitPlanetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuitPlanetReply) ProtoMessage() {}

func (x *QuitPlanetReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuitPlanetReply.ProtoReflect.Descriptor instead.
func (*QuitPlanetReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{3}
}

func (x *QuitPlanetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QuitPlanetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ListPlanetByUserIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetByUserIDRequest) Reset() {
	*x = ListPlanetByUserIDRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetByUserIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetByUserIDRequest) ProtoMessage() {}

func (x *ListPlanetByUserIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetByUserIDRequest.ProtoReflect.Descriptor instead.
func (*ListPlanetByUserIDRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{4}
}

type ListPlanetByUserIDReply struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Code          int32                           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          []*ListPlanetByUserIDReply_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetByUserIDReply) Reset() {
	*x = ListPlanetByUserIDReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetByUserIDReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetByUserIDReply) ProtoMessage() {}

func (x *ListPlanetByUserIDReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetByUserIDReply.ProtoReflect.Descriptor instead.
func (*ListPlanetByUserIDReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{5}
}

func (x *ListPlanetByUserIDReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListPlanetByUserIDReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListPlanetByUserIDReply) GetData() []*ListPlanetByUserIDReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type JoinPlanetTargetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	TargetId      int32                  `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinPlanetTargetRequest) Reset() {
	*x = JoinPlanetTargetRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinPlanetTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinPlanetTargetRequest) ProtoMessage() {}

func (x *JoinPlanetTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinPlanetTargetRequest.ProtoReflect.Descriptor instead.
func (*JoinPlanetTargetRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{6}
}

func (x *JoinPlanetTargetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *JoinPlanetTargetRequest) GetTargetId() int32 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

type JoinPlanetTargetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JoinPlanetTargetReply) Reset() {
	*x = JoinPlanetTargetReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JoinPlanetTargetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JoinPlanetTargetReply) ProtoMessage() {}

func (x *JoinPlanetTargetReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JoinPlanetTargetReply.ProtoReflect.Descriptor instead.
func (*JoinPlanetTargetReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{7}
}

func (x *JoinPlanetTargetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *JoinPlanetTargetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type QuitPlanetTargetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	TargetId      int32                  `protobuf:"varint,2,opt,name=target_id,json=targetId,proto3" json:"target_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuitPlanetTargetRequest) Reset() {
	*x = QuitPlanetTargetRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuitPlanetTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuitPlanetTargetRequest) ProtoMessage() {}

func (x *QuitPlanetTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuitPlanetTargetRequest.ProtoReflect.Descriptor instead.
func (*QuitPlanetTargetRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{8}
}

func (x *QuitPlanetTargetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *QuitPlanetTargetRequest) GetTargetId() int32 {
	if x != nil {
		return x.TargetId
	}
	return 0
}

type QuitPlanetTargetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuitPlanetTargetReply) Reset() {
	*x = QuitPlanetTargetReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuitPlanetTargetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuitPlanetTargetReply) ProtoMessage() {}

func (x *QuitPlanetTargetReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuitPlanetTargetReply.ProtoReflect.Descriptor instead.
func (*QuitPlanetTargetReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{9}
}

func (x *QuitPlanetTargetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QuitPlanetTargetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CreatePlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Images        []string               `protobuf:"bytes,3,rep,name=images,proto3" json:"images,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePlanetPostRequest) Reset() {
	*x = CreatePlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlanetPostRequest) ProtoMessage() {}

func (x *CreatePlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlanetPostRequest.ProtoReflect.Descriptor instead.
func (*CreatePlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{10}
}

func (x *CreatePlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *CreatePlanetPostRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreatePlanetPostRequest) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

type CreatePlanetPostReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePlanetPostReply) Reset() {
	*x = CreatePlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlanetPostReply) ProtoMessage() {}

func (x *CreatePlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlanetPostReply.ProtoReflect.Descriptor instead.
func (*CreatePlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{11}
}

func (x *CreatePlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreatePlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdatePlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Images        []string               `protobuf:"bytes,3,rep,name=images,proto3" json:"images,omitempty"`
	PostId        int32                  `protobuf:"varint,4,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePlanetPostRequest) Reset() {
	*x = UpdatePlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlanetPostRequest) ProtoMessage() {}

func (x *UpdatePlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlanetPostRequest.ProtoReflect.Descriptor instead.
func (*UpdatePlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{12}
}

func (x *UpdatePlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *UpdatePlanetPostRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UpdatePlanetPostRequest) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *UpdatePlanetPostRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type UpdatePlanetPostReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePlanetPostReply) Reset() {
	*x = UpdatePlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlanetPostReply) ProtoMessage() {}

func (x *UpdatePlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlanetPostReply.ProtoReflect.Descriptor instead.
func (*UpdatePlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{13}
}

func (x *UpdatePlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdatePlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeletePlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanetPostRequest) Reset() {
	*x = DeletePlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanetPostRequest) ProtoMessage() {}

func (x *DeletePlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanetPostRequest.ProtoReflect.Descriptor instead.
func (*DeletePlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{14}
}

func (x *DeletePlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *DeletePlanetPostRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type DeletePlanetPostReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanetPostReply) Reset() {
	*x = DeletePlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanetPostReply) ProtoMessage() {}

func (x *DeletePlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanetPostReply.ProtoReflect.Descriptor instead.
func (*DeletePlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{15}
}

func (x *DeletePlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeletePlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Nickname      string                 `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AvatarUrl     string                 `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{16}
}

func (x *UserInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfo) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type ListPlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	LabelType     int32                  `protobuf:"varint,4,opt,name=label_type,json=labelType,proto3" json:"label_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetPostRequest) Reset() {
	*x = ListPlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetPostRequest) ProtoMessage() {}

func (x *ListPlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetPostRequest.ProtoReflect.Descriptor instead.
func (*ListPlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{17}
}

func (x *ListPlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *ListPlanetPostRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPlanetPostRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPlanetPostRequest) GetLabelType() int32 {
	if x != nil {
		return x.LabelType
	}
	return 0
}

type ListPlanetPostReply struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *ListPlanetPostReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetPostReply) Reset() {
	*x = ListPlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetPostReply) ProtoMessage() {}

func (x *ListPlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetPostReply.ProtoReflect.Descriptor instead.
func (*ListPlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{18}
}

func (x *ListPlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListPlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListPlanetPostReply) GetData() *ListPlanetPostReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListPlanetTopPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetTopPostRequest) Reset() {
	*x = ListPlanetTopPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetTopPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetTopPostRequest) ProtoMessage() {}

func (x *ListPlanetTopPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetTopPostRequest.ProtoReflect.Descriptor instead.
func (*ListPlanetTopPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{19}
}

func (x *ListPlanetTopPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *ListPlanetTopPostRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPlanetTopPostRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListPlanetTopPostReply struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Code          int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *ListPlanetTopPostReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetTopPostReply) Reset() {
	*x = ListPlanetTopPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetTopPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetTopPostReply) ProtoMessage() {}

func (x *ListPlanetTopPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetTopPostReply.ProtoReflect.Descriptor instead.
func (*ListPlanetTopPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{20}
}

func (x *ListPlanetTopPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListPlanetTopPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListPlanetTopPostReply) GetData() *ListPlanetTopPostReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type LikePlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LikePlanetPostRequest) Reset() {
	*x = LikePlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LikePlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikePlanetPostRequest) ProtoMessage() {}

func (x *LikePlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikePlanetPostRequest.ProtoReflect.Descriptor instead.
func (*LikePlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{21}
}

func (x *LikePlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *LikePlanetPostRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type LikePlanetPostReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LikePlanetPostReply) Reset() {
	*x = LikePlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LikePlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LikePlanetPostReply) ProtoMessage() {}

func (x *LikePlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LikePlanetPostReply.ProtoReflect.Descriptor instead.
func (*LikePlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{22}
}

func (x *LikePlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LikePlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CancelLikePlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelLikePlanetPostRequest) Reset() {
	*x = CancelLikePlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelLikePlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLikePlanetPostRequest) ProtoMessage() {}

func (x *CancelLikePlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLikePlanetPostRequest.ProtoReflect.Descriptor instead.
func (*CancelLikePlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{23}
}

func (x *CancelLikePlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *CancelLikePlanetPostRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type CancelLikePlanetPostReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelLikePlanetPostReply) Reset() {
	*x = CancelLikePlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelLikePlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelLikePlanetPostReply) ProtoMessage() {}

func (x *CancelLikePlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelLikePlanetPostReply.ProtoReflect.Descriptor instead.
func (*CancelLikePlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{24}
}

func (x *CancelLikePlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CancelLikePlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type FavoritePlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FavoritePlanetPostRequest) Reset() {
	*x = FavoritePlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FavoritePlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FavoritePlanetPostRequest) ProtoMessage() {}

func (x *FavoritePlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FavoritePlanetPostRequest.ProtoReflect.Descriptor instead.
func (*FavoritePlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{25}
}

func (x *FavoritePlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *FavoritePlanetPostRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type FavoritePlanetPostReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FavoritePlanetPostReply) Reset() {
	*x = FavoritePlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FavoritePlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FavoritePlanetPostReply) ProtoMessage() {}

func (x *FavoritePlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FavoritePlanetPostReply.ProtoReflect.Descriptor instead.
func (*FavoritePlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{26}
}

func (x *FavoritePlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FavoritePlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CancelFavoritePlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelFavoritePlanetPostRequest) Reset() {
	*x = CancelFavoritePlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelFavoritePlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelFavoritePlanetPostRequest) ProtoMessage() {}

func (x *CancelFavoritePlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelFavoritePlanetPostRequest.ProtoReflect.Descriptor instead.
func (*CancelFavoritePlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{27}
}

func (x *CancelFavoritePlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *CancelFavoritePlanetPostRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type CancelFavoritePlanetPostReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelFavoritePlanetPostReply) Reset() {
	*x = CancelFavoritePlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelFavoritePlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelFavoritePlanetPostReply) ProtoMessage() {}

func (x *CancelFavoritePlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelFavoritePlanetPostReply.ProtoReflect.Descriptor instead.
func (*CancelFavoritePlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{28}
}

func (x *CancelFavoritePlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CancelFavoritePlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CreatePlanetPostCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommentId     int32                  `protobuf:"varint,1,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	PlanetId      int32                  `protobuf:"varint,4,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePlanetPostCommentRequest) Reset() {
	*x = CreatePlanetPostCommentRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePlanetPostCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlanetPostCommentRequest) ProtoMessage() {}

func (x *CreatePlanetPostCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlanetPostCommentRequest.ProtoReflect.Descriptor instead.
func (*CreatePlanetPostCommentRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{29}
}

func (x *CreatePlanetPostCommentRequest) GetCommentId() int32 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

func (x *CreatePlanetPostCommentRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *CreatePlanetPostCommentRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreatePlanetPostCommentRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type CreatePlanetPostCommentReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePlanetPostCommentReply) Reset() {
	*x = CreatePlanetPostCommentReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePlanetPostCommentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlanetPostCommentReply) ProtoMessage() {}

func (x *CreatePlanetPostCommentReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlanetPostCommentReply.ProtoReflect.Descriptor instead.
func (*CreatePlanetPostCommentReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{30}
}

func (x *CreatePlanetPostCommentReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreatePlanetPostCommentReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeletePlanetPostCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	CommentId     int32                  `protobuf:"varint,3,opt,name=comment_id,json=commentId,proto3" json:"comment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanetPostCommentRequest) Reset() {
	*x = DeletePlanetPostCommentRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanetPostCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanetPostCommentRequest) ProtoMessage() {}

func (x *DeletePlanetPostCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanetPostCommentRequest.ProtoReflect.Descriptor instead.
func (*DeletePlanetPostCommentRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{31}
}

func (x *DeletePlanetPostCommentRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *DeletePlanetPostCommentRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *DeletePlanetPostCommentRequest) GetCommentId() int32 {
	if x != nil {
		return x.CommentId
	}
	return 0
}

type DeletePlanetPostCommentReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanetPostCommentReply) Reset() {
	*x = DeletePlanetPostCommentReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanetPostCommentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanetPostCommentReply) ProtoMessage() {}

func (x *DeletePlanetPostCommentReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanetPostCommentReply.ProtoReflect.Descriptor instead.
func (*DeletePlanetPostCommentReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{32}
}

func (x *DeletePlanetPostCommentReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeletePlanetPostCommentReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SubCommentItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	PlanetId      int32                  `protobuf:"varint,4,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	CreatedAt     int32                  `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	User          *UserInfo              `protobuf:"bytes,6,opt,name=user,proto3" json:"user,omitempty"`
	RepliedUser   *UserInfo              `protobuf:"bytes,7,opt,name=replied_user,json=repliedUser,proto3" json:"replied_user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubCommentItem) Reset() {
	*x = SubCommentItem{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubCommentItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubCommentItem) ProtoMessage() {}

func (x *SubCommentItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubCommentItem.ProtoReflect.Descriptor instead.
func (*SubCommentItem) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{33}
}

func (x *SubCommentItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubCommentItem) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *SubCommentItem) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SubCommentItem) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *SubCommentItem) GetCreatedAt() int32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SubCommentItem) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *SubCommentItem) GetRepliedUser() *UserInfo {
	if x != nil {
		return x.RepliedUser
	}
	return nil
}

type ListPlanetPostCommentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetPostCommentRequest) Reset() {
	*x = ListPlanetPostCommentRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetPostCommentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetPostCommentRequest) ProtoMessage() {}

func (x *ListPlanetPostCommentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetPostCommentRequest.ProtoReflect.Descriptor instead.
func (*ListPlanetPostCommentRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{34}
}

func (x *ListPlanetPostCommentRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *ListPlanetPostCommentRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *ListPlanetPostCommentRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPlanetPostCommentRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListPlanetPostCommentReply struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Code          int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *ListPlanetPostCommentReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetPostCommentReply) Reset() {
	*x = ListPlanetPostCommentReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetPostCommentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetPostCommentReply) ProtoMessage() {}

func (x *ListPlanetPostCommentReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetPostCommentReply.ProtoReflect.Descriptor instead.
func (*ListPlanetPostCommentReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{35}
}

func (x *ListPlanetPostCommentReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListPlanetPostCommentReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListPlanetPostCommentReply) GetData() *ListPlanetPostCommentReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ToppedPlanetPostRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ToppedPlanetPostRequest) Reset() {
	*x = ToppedPlanetPostRequest{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ToppedPlanetPostRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToppedPlanetPostRequest) ProtoMessage() {}

func (x *ToppedPlanetPostRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToppedPlanetPostRequest.ProtoReflect.Descriptor instead.
func (*ToppedPlanetPostRequest) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{36}
}

func (x *ToppedPlanetPostRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *ToppedPlanetPostRequest) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

type ToppedPlanetPostReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ToppedPlanetPostReply) Reset() {
	*x = ToppedPlanetPostReply{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ToppedPlanetPostReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToppedPlanetPostReply) ProtoMessage() {}

func (x *ToppedPlanetPostReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToppedPlanetPostReply.ProtoReflect.Descriptor instead.
func (*ToppedPlanetPostReply) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{37}
}

func (x *ToppedPlanetPostReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ToppedPlanetPostReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ListPlanetByUserIDReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ImgUrl        string                 `protobuf:"bytes,3,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetByUserIDReply_Data) Reset() {
	*x = ListPlanetByUserIDReply_Data{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetByUserIDReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetByUserIDReply_Data) ProtoMessage() {}

func (x *ListPlanetByUserIDReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetByUserIDReply_Data.ProtoReflect.Descriptor instead.
func (*ListPlanetByUserIDReply_Data) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{5, 0}
}

func (x *ListPlanetByUserIDReply_Data) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListPlanetByUserIDReply_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListPlanetByUserIDReply_Data) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

type ListPlanetPostReply_Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	User          *UserInfo              `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	PlanetId      int32                  `protobuf:"varint,4,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	LikeCount     int32                  `protobuf:"varint,5,opt,name=like_count,json=likeCount,proto3" json:"like_count,omitempty"`
	FavoriteCount int32                  `protobuf:"varint,6,opt,name=favorite_count,json=favoriteCount,proto3" json:"favorite_count,omitempty"`
	CommentCount  int32                  `protobuf:"varint,7,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	CreatedAt     int32                  `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	IsLike        bool                   `protobuf:"varint,9,opt,name=is_like,json=isLike,proto3" json:"is_like,omitempty"`
	IsFavorite    bool                   `protobuf:"varint,10,opt,name=is_favorite,json=isFavorite,proto3" json:"is_favorite,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetPostReply_Item) Reset() {
	*x = ListPlanetPostReply_Item{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetPostReply_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetPostReply_Item) ProtoMessage() {}

func (x *ListPlanetPostReply_Item) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetPostReply_Item.ProtoReflect.Descriptor instead.
func (*ListPlanetPostReply_Item) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{18, 0}
}

func (x *ListPlanetPostReply_Item) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListPlanetPostReply_Item) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ListPlanetPostReply_Item) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ListPlanetPostReply_Item) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *ListPlanetPostReply_Item) GetLikeCount() int32 {
	if x != nil {
		return x.LikeCount
	}
	return 0
}

func (x *ListPlanetPostReply_Item) GetFavoriteCount() int32 {
	if x != nil {
		return x.FavoriteCount
	}
	return 0
}

func (x *ListPlanetPostReply_Item) GetCommentCount() int32 {
	if x != nil {
		return x.CommentCount
	}
	return 0
}

func (x *ListPlanetPostReply_Item) GetCreatedAt() int32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ListPlanetPostReply_Item) GetIsLike() bool {
	if x != nil {
		return x.IsLike
	}
	return false
}

func (x *ListPlanetPostReply_Item) GetIsFavorite() bool {
	if x != nil {
		return x.IsFavorite
	}
	return false
}

type ListPlanetPostReply_Data struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Page          int32                       `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                       `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total         int32                       `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Items         []*ListPlanetPostReply_Item `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetPostReply_Data) Reset() {
	*x = ListPlanetPostReply_Data{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetPostReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetPostReply_Data) ProtoMessage() {}

func (x *ListPlanetPostReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetPostReply_Data.ProtoReflect.Descriptor instead.
func (*ListPlanetPostReply_Data) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{18, 1}
}

func (x *ListPlanetPostReply_Data) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPlanetPostReply_Data) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPlanetPostReply_Data) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListPlanetPostReply_Data) GetItems() []*ListPlanetPostReply_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type ListPlanetTopPostReply_Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PlanetId      int32                  `protobuf:"varint,2,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetTopPostReply_Item) Reset() {
	*x = ListPlanetTopPostReply_Item{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetTopPostReply_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetTopPostReply_Item) ProtoMessage() {}

func (x *ListPlanetTopPostReply_Item) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetTopPostReply_Item.ProtoReflect.Descriptor instead.
func (*ListPlanetTopPostReply_Item) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{20, 0}
}

func (x *ListPlanetTopPostReply_Item) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListPlanetTopPostReply_Item) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *ListPlanetTopPostReply_Item) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ListPlanetTopPostReply_Data struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Page          int32                          `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                          `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total         int32                          `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Items         []*ListPlanetTopPostReply_Item `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetTopPostReply_Data) Reset() {
	*x = ListPlanetTopPostReply_Data{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetTopPostReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetTopPostReply_Data) ProtoMessage() {}

func (x *ListPlanetTopPostReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetTopPostReply_Data.ProtoReflect.Descriptor instead.
func (*ListPlanetTopPostReply_Data) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{20, 1}
}

func (x *ListPlanetTopPostReply_Data) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPlanetTopPostReply_Data) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPlanetTopPostReply_Data) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListPlanetTopPostReply_Data) GetItems() []*ListPlanetTopPostReply_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

type ListPlanetPostCommentReply_Item struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PostId        int32                  `protobuf:"varint,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	PlanetId      int32                  `protobuf:"varint,4,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	User          *UserInfo              `protobuf:"bytes,5,opt,name=user,proto3" json:"user,omitempty"`
	SubItems      []*SubCommentItem      `protobuf:"bytes,6,rep,name=sub_items,json=subItems,proto3" json:"sub_items,omitempty"`
	CreatedAt     int32                  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	SubCount      int32                  `protobuf:"varint,8,opt,name=sub_count,json=subCount,proto3" json:"sub_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetPostCommentReply_Item) Reset() {
	*x = ListPlanetPostCommentReply_Item{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetPostCommentReply_Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetPostCommentReply_Item) ProtoMessage() {}

func (x *ListPlanetPostCommentReply_Item) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetPostCommentReply_Item.ProtoReflect.Descriptor instead.
func (*ListPlanetPostCommentReply_Item) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{35, 0}
}

func (x *ListPlanetPostCommentReply_Item) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListPlanetPostCommentReply_Item) GetPostId() int32 {
	if x != nil {
		return x.PostId
	}
	return 0
}

func (x *ListPlanetPostCommentReply_Item) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ListPlanetPostCommentReply_Item) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *ListPlanetPostCommentReply_Item) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ListPlanetPostCommentReply_Item) GetSubItems() []*SubCommentItem {
	if x != nil {
		return x.SubItems
	}
	return nil
}

func (x *ListPlanetPostCommentReply_Item) GetCreatedAt() int32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ListPlanetPostCommentReply_Item) GetSubCount() int32 {
	if x != nil {
		return x.SubCount
	}
	return 0
}

type ListPlanetPostCommentReply_Data struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Page          int32                              `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                              `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total         int32                              `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Items         []*ListPlanetPostCommentReply_Item `protobuf:"bytes,4,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetPostCommentReply_Data) Reset() {
	*x = ListPlanetPostCommentReply_Data{}
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetPostCommentReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetPostCommentReply_Data) ProtoMessage() {}

func (x *ListPlanetPostCommentReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_planet_v1_user_planet_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetPostCommentReply_Data.ProtoReflect.Descriptor instead.
func (*ListPlanetPostCommentReply_Data) Descriptor() ([]byte, []int) {
	return file_user_planet_v1_user_planet_proto_rawDescGZIP(), []int{35, 1}
}

func (x *ListPlanetPostCommentReply_Data) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPlanetPostCommentReply_Data) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPlanetPostCommentReply_Data) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListPlanetPostCommentReply_Data) GetItems() []*ListPlanetPostCommentReply_Item {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_user_planet_v1_user_planet_proto protoreflect.FileDescriptor

const file_user_planet_v1_user_planet_proto_rawDesc = "" +
	"\n" +
	" user_planet/v1/user_planet.proto\x12\x0euser_planet_v1\"0\n" +
	"\x11JoinPlanetRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\"7\n" +
	"\x0fJoinPlanetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"0\n" +
	"\x11QuitPlanetRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\"7\n" +
	"\x0fQuitPlanetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x1b\n" +
	"\x19ListPlanetByUserIDRequest\"\xc6\x01\n" +
	"\x17ListPlanetByUserIDReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12@\n" +
	"\x04data\x18\x03 \x03(\v2,.user_planet_v1.ListPlanetByUserIDReply.DataR\x04data\x1aC\n" +
	"\x04Data\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x17\n" +
	"\aimg_url\x18\x03 \x01(\tR\x06imgUrl\"S\n" +
	"\x17JoinPlanetTargetRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x1b\n" +
	"\ttarget_id\x18\x02 \x01(\x05R\btargetId\"=\n" +
	"\x15JoinPlanetTargetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"S\n" +
	"\x17QuitPlanetTargetRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x1b\n" +
	"\ttarget_id\x18\x02 \x01(\x05R\btargetId\"=\n" +
	"\x15QuitPlanetTargetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"h\n" +
	"\x17CreatePlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x16\n" +
	"\x06images\x18\x03 \x03(\tR\x06images\"=\n" +
	"\x15CreatePlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x81\x01\n" +
	"\x17UpdatePlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x16\n" +
	"\x06images\x18\x03 \x03(\tR\x06images\x12\x17\n" +
	"\apost_id\x18\x04 \x01(\x05R\x06postId\"=\n" +
	"\x15UpdatePlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"O\n" +
	"\x17DeletePlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\"=\n" +
	"\x15DeletePlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"U\n" +
	"\bUserInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1a\n" +
	"\bnickname\x18\x02 \x01(\tR\bnickname\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x03 \x01(\tR\tavatarUrl\"\x84\x01\n" +
	"\x15ListPlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"label_type\x18\x04 \x01(\x05R\tlabelType\"\xcb\x04\n" +
	"\x13ListPlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12<\n" +
	"\x04data\x18\x03 \x01(\v2(.user_planet_v1.ListPlanetPostReply.DataR\x04data\x1a\xbf\x02\n" +
	"\x04Item\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12,\n" +
	"\x04user\x18\x02 \x01(\v2\x18.user_planet_v1.UserInfoR\x04user\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\x12\x1b\n" +
	"\tplanet_id\x18\x04 \x01(\x05R\bplanetId\x12\x1d\n" +
	"\n" +
	"like_count\x18\x05 \x01(\x05R\tlikeCount\x12%\n" +
	"\x0efavorite_count\x18\x06 \x01(\x05R\rfavoriteCount\x12#\n" +
	"\rcomment_count\x18\a \x01(\x05R\fcommentCount\x12\x1d\n" +
	"\n" +
	"created_at\x18\b \x01(\x05R\tcreatedAt\x12\x17\n" +
	"\ais_like\x18\t \x01(\bR\x06isLike\x12\x1f\n" +
	"\vis_favorite\x18\n" +
	" \x01(\bR\n" +
	"isFavorite\x1a\x8d\x01\n" +
	"\x04Data\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12>\n" +
	"\x05items\x18\x04 \x03(\v2(.user_planet_v1.ListPlanetPostReply.ItemR\x05items\"h\n" +
	"\x18ListPlanetTopPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\"\xe1\x02\n" +
	"\x16ListPlanetTopPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12?\n" +
	"\x04data\x18\x03 \x01(\v2+.user_planet_v1.ListPlanetTopPostReply.DataR\x04data\x1aM\n" +
	"\x04Item\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1b\n" +
	"\tplanet_id\x18\x02 \x01(\x05R\bplanetId\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\x1a\x90\x01\n" +
	"\x04Data\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12A\n" +
	"\x05items\x18\x04 \x03(\v2+.user_planet_v1.ListPlanetTopPostReply.ItemR\x05items\"M\n" +
	"\x15LikePlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\";\n" +
	"\x13LikePlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"S\n" +
	"\x1bCancelLikePlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\"A\n" +
	"\x19CancelLikePlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"Q\n" +
	"\x19FavoritePlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\"?\n" +
	"\x17FavoritePlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"W\n" +
	"\x1fCancelFavoritePlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\"E\n" +
	"\x1dCancelFavoritePlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x8f\x01\n" +
	"\x1eCreatePlanetPostCommentRequest\x12\x1d\n" +
	"\n" +
	"comment_id\x18\x01 \x01(\x05R\tcommentId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\x12\x1b\n" +
	"\tplanet_id\x18\x04 \x01(\x05R\bplanetId\"D\n" +
	"\x1cCreatePlanetPostCommentReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"u\n" +
	"\x1eDeletePlanetPostCommentRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\x12\x1d\n" +
	"\n" +
	"comment_id\x18\x03 \x01(\x05R\tcommentId\"D\n" +
	"\x1cDeletePlanetPostCommentReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xfa\x01\n" +
	"\x0eSubCommentItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\x12\x1b\n" +
	"\tplanet_id\x18\x04 \x01(\x05R\bplanetId\x12\x1d\n" +
	"\n" +
	"created_at\x18\x05 \x01(\x05R\tcreatedAt\x12,\n" +
	"\x04user\x18\x06 \x01(\v2\x18.user_planet_v1.UserInfoR\x04user\x12;\n" +
	"\freplied_user\x18\a \x01(\v2\x18.user_planet_v1.UserInfoR\vrepliedUser\"\x85\x01\n" +
	"\x1cListPlanetPostCommentRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\xae\x04\n" +
	"\x1aListPlanetPostCommentReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12C\n" +
	"\x04data\x18\x03 \x01(\v2/.user_planet_v1.ListPlanetPostCommentReply.DataR\x04data\x1a\x8d\x02\n" +
	"\x04Item\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\x12\x1b\n" +
	"\tplanet_id\x18\x04 \x01(\x05R\bplanetId\x12,\n" +
	"\x04user\x18\x05 \x01(\v2\x18.user_planet_v1.UserInfoR\x04user\x12;\n" +
	"\tsub_items\x18\x06 \x03(\v2\x1e.user_planet_v1.SubCommentItemR\bsubItems\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x05R\tcreatedAt\x12\x1b\n" +
	"\tsub_count\x18\b \x01(\x05R\bsubCount\x1a\x94\x01\n" +
	"\x04Data\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12E\n" +
	"\x05items\x18\x04 \x03(\v2/.user_planet_v1.ListPlanetPostCommentReply.ItemR\x05items\"O\n" +
	"\x17ToppedPlanetPostRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x17\n" +
	"\apost_id\x18\x02 \x01(\x05R\x06postId\"=\n" +
	"\x15ToppedPlanetPostReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msgB4Z2github.com/wlnil/life-log-be/api/user_planet/v1;v1b\x06proto3"

var (
	file_user_planet_v1_user_planet_proto_rawDescOnce sync.Once
	file_user_planet_v1_user_planet_proto_rawDescData []byte
)

func file_user_planet_v1_user_planet_proto_rawDescGZIP() []byte {
	file_user_planet_v1_user_planet_proto_rawDescOnce.Do(func() {
		file_user_planet_v1_user_planet_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_planet_v1_user_planet_proto_rawDesc), len(file_user_planet_v1_user_planet_proto_rawDesc)))
	})
	return file_user_planet_v1_user_planet_proto_rawDescData
}

var file_user_planet_v1_user_planet_proto_msgTypes = make([]protoimpl.MessageInfo, 45)
var file_user_planet_v1_user_planet_proto_goTypes = []any{
	(*JoinPlanetRequest)(nil),               // 0: user_planet_v1.JoinPlanetRequest
	(*JoinPlanetReply)(nil),                 // 1: user_planet_v1.JoinPlanetReply
	(*QuitPlanetRequest)(nil),               // 2: user_planet_v1.QuitPlanetRequest
	(*QuitPlanetReply)(nil),                 // 3: user_planet_v1.QuitPlanetReply
	(*ListPlanetByUserIDRequest)(nil),       // 4: user_planet_v1.ListPlanetByUserIDRequest
	(*ListPlanetByUserIDReply)(nil),         // 5: user_planet_v1.ListPlanetByUserIDReply
	(*JoinPlanetTargetRequest)(nil),         // 6: user_planet_v1.JoinPlanetTargetRequest
	(*JoinPlanetTargetReply)(nil),           // 7: user_planet_v1.JoinPlanetTargetReply
	(*QuitPlanetTargetRequest)(nil),         // 8: user_planet_v1.QuitPlanetTargetRequest
	(*QuitPlanetTargetReply)(nil),           // 9: user_planet_v1.QuitPlanetTargetReply
	(*CreatePlanetPostRequest)(nil),         // 10: user_planet_v1.CreatePlanetPostRequest
	(*CreatePlanetPostReply)(nil),           // 11: user_planet_v1.CreatePlanetPostReply
	(*UpdatePlanetPostRequest)(nil),         // 12: user_planet_v1.UpdatePlanetPostRequest
	(*UpdatePlanetPostReply)(nil),           // 13: user_planet_v1.UpdatePlanetPostReply
	(*DeletePlanetPostRequest)(nil),         // 14: user_planet_v1.DeletePlanetPostRequest
	(*DeletePlanetPostReply)(nil),           // 15: user_planet_v1.DeletePlanetPostReply
	(*UserInfo)(nil),                        // 16: user_planet_v1.UserInfo
	(*ListPlanetPostRequest)(nil),           // 17: user_planet_v1.ListPlanetPostRequest
	(*ListPlanetPostReply)(nil),             // 18: user_planet_v1.ListPlanetPostReply
	(*ListPlanetTopPostRequest)(nil),        // 19: user_planet_v1.ListPlanetTopPostRequest
	(*ListPlanetTopPostReply)(nil),          // 20: user_planet_v1.ListPlanetTopPostReply
	(*LikePlanetPostRequest)(nil),           // 21: user_planet_v1.LikePlanetPostRequest
	(*LikePlanetPostReply)(nil),             // 22: user_planet_v1.LikePlanetPostReply
	(*CancelLikePlanetPostRequest)(nil),     // 23: user_planet_v1.CancelLikePlanetPostRequest
	(*CancelLikePlanetPostReply)(nil),       // 24: user_planet_v1.CancelLikePlanetPostReply
	(*FavoritePlanetPostRequest)(nil),       // 25: user_planet_v1.FavoritePlanetPostRequest
	(*FavoritePlanetPostReply)(nil),         // 26: user_planet_v1.FavoritePlanetPostReply
	(*CancelFavoritePlanetPostRequest)(nil), // 27: user_planet_v1.CancelFavoritePlanetPostRequest
	(*CancelFavoritePlanetPostReply)(nil),   // 28: user_planet_v1.CancelFavoritePlanetPostReply
	(*CreatePlanetPostCommentRequest)(nil),  // 29: user_planet_v1.CreatePlanetPostCommentRequest
	(*CreatePlanetPostCommentReply)(nil),    // 30: user_planet_v1.CreatePlanetPostCommentReply
	(*DeletePlanetPostCommentRequest)(nil),  // 31: user_planet_v1.DeletePlanetPostCommentRequest
	(*DeletePlanetPostCommentReply)(nil),    // 32: user_planet_v1.DeletePlanetPostCommentReply
	(*SubCommentItem)(nil),                  // 33: user_planet_v1.SubCommentItem
	(*ListPlanetPostCommentRequest)(nil),    // 34: user_planet_v1.ListPlanetPostCommentRequest
	(*ListPlanetPostCommentReply)(nil),      // 35: user_planet_v1.ListPlanetPostCommentReply
	(*ToppedPlanetPostRequest)(nil),         // 36: user_planet_v1.ToppedPlanetPostRequest
	(*ToppedPlanetPostReply)(nil),           // 37: user_planet_v1.ToppedPlanetPostReply
	(*ListPlanetByUserIDReply_Data)(nil),    // 38: user_planet_v1.ListPlanetByUserIDReply.Data
	(*ListPlanetPostReply_Item)(nil),        // 39: user_planet_v1.ListPlanetPostReply.Item
	(*ListPlanetPostReply_Data)(nil),        // 40: user_planet_v1.ListPlanetPostReply.Data
	(*ListPlanetTopPostReply_Item)(nil),     // 41: user_planet_v1.ListPlanetTopPostReply.Item
	(*ListPlanetTopPostReply_Data)(nil),     // 42: user_planet_v1.ListPlanetTopPostReply.Data
	(*ListPlanetPostCommentReply_Item)(nil), // 43: user_planet_v1.ListPlanetPostCommentReply.Item
	(*ListPlanetPostCommentReply_Data)(nil), // 44: user_planet_v1.ListPlanetPostCommentReply.Data
}
var file_user_planet_v1_user_planet_proto_depIdxs = []int32{
	38, // 0: user_planet_v1.ListPlanetByUserIDReply.data:type_name -> user_planet_v1.ListPlanetByUserIDReply.Data
	40, // 1: user_planet_v1.ListPlanetPostReply.data:type_name -> user_planet_v1.ListPlanetPostReply.Data
	42, // 2: user_planet_v1.ListPlanetTopPostReply.data:type_name -> user_planet_v1.ListPlanetTopPostReply.Data
	16, // 3: user_planet_v1.SubCommentItem.user:type_name -> user_planet_v1.UserInfo
	16, // 4: user_planet_v1.SubCommentItem.replied_user:type_name -> user_planet_v1.UserInfo
	44, // 5: user_planet_v1.ListPlanetPostCommentReply.data:type_name -> user_planet_v1.ListPlanetPostCommentReply.Data
	16, // 6: user_planet_v1.ListPlanetPostReply.Item.user:type_name -> user_planet_v1.UserInfo
	39, // 7: user_planet_v1.ListPlanetPostReply.Data.items:type_name -> user_planet_v1.ListPlanetPostReply.Item
	41, // 8: user_planet_v1.ListPlanetTopPostReply.Data.items:type_name -> user_planet_v1.ListPlanetTopPostReply.Item
	16, // 9: user_planet_v1.ListPlanetPostCommentReply.Item.user:type_name -> user_planet_v1.UserInfo
	33, // 10: user_planet_v1.ListPlanetPostCommentReply.Item.sub_items:type_name -> user_planet_v1.SubCommentItem
	43, // 11: user_planet_v1.ListPlanetPostCommentReply.Data.items:type_name -> user_planet_v1.ListPlanetPostCommentReply.Item
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_user_planet_v1_user_planet_proto_init() }
func file_user_planet_v1_user_planet_proto_init() {
	if File_user_planet_v1_user_planet_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_planet_v1_user_planet_proto_rawDesc), len(file_user_planet_v1_user_planet_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   45,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_planet_v1_user_planet_proto_goTypes,
		DependencyIndexes: file_user_planet_v1_user_planet_proto_depIdxs,
		MessageInfos:      file_user_planet_v1_user_planet_proto_msgTypes,
	}.Build()
	File_user_planet_v1_user_planet_proto = out.File
	file_user_planet_v1_user_planet_proto_goTypes = nil
	file_user_planet_v1_user_planet_proto_depIdxs = nil
}
