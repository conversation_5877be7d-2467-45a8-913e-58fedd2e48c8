// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: user_planet/v1/user_planet.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on JoinPlanetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *JoinPlanetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JoinPlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JoinPlanetRequestMultiError, or nil if none found.
func (m *JoinPlanetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *JoinPlanetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return JoinPlanetRequestMultiError(errors)
	}

	return nil
}

// JoinPlanetRequestMultiError is an error wrapping multiple validation errors
// returned by JoinPlanetRequest.ValidateAll() if the designated constraints
// aren't met.
type JoinPlanetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JoinPlanetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JoinPlanetRequestMultiError) AllErrors() []error { return m }

// JoinPlanetRequestValidationError is the validation error returned by
// JoinPlanetRequest.Validate if the designated constraints aren't met.
type JoinPlanetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JoinPlanetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JoinPlanetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JoinPlanetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JoinPlanetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JoinPlanetRequestValidationError) ErrorName() string {
	return "JoinPlanetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e JoinPlanetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJoinPlanetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JoinPlanetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JoinPlanetRequestValidationError{}

// Validate checks the field values on JoinPlanetReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *JoinPlanetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JoinPlanetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JoinPlanetReplyMultiError, or nil if none found.
func (m *JoinPlanetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *JoinPlanetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return JoinPlanetReplyMultiError(errors)
	}

	return nil
}

// JoinPlanetReplyMultiError is an error wrapping multiple validation errors
// returned by JoinPlanetReply.ValidateAll() if the designated constraints
// aren't met.
type JoinPlanetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JoinPlanetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JoinPlanetReplyMultiError) AllErrors() []error { return m }

// JoinPlanetReplyValidationError is the validation error returned by
// JoinPlanetReply.Validate if the designated constraints aren't met.
type JoinPlanetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JoinPlanetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JoinPlanetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JoinPlanetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JoinPlanetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JoinPlanetReplyValidationError) ErrorName() string { return "JoinPlanetReplyValidationError" }

// Error satisfies the builtin error interface
func (e JoinPlanetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJoinPlanetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JoinPlanetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JoinPlanetReplyValidationError{}

// Validate checks the field values on QuitPlanetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *QuitPlanetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuitPlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuitPlanetRequestMultiError, or nil if none found.
func (m *QuitPlanetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QuitPlanetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return QuitPlanetRequestMultiError(errors)
	}

	return nil
}

// QuitPlanetRequestMultiError is an error wrapping multiple validation errors
// returned by QuitPlanetRequest.ValidateAll() if the designated constraints
// aren't met.
type QuitPlanetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuitPlanetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuitPlanetRequestMultiError) AllErrors() []error { return m }

// QuitPlanetRequestValidationError is the validation error returned by
// QuitPlanetRequest.Validate if the designated constraints aren't met.
type QuitPlanetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuitPlanetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuitPlanetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuitPlanetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuitPlanetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuitPlanetRequestValidationError) ErrorName() string {
	return "QuitPlanetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QuitPlanetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuitPlanetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuitPlanetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuitPlanetRequestValidationError{}

// Validate checks the field values on QuitPlanetReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *QuitPlanetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuitPlanetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuitPlanetReplyMultiError, or nil if none found.
func (m *QuitPlanetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QuitPlanetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return QuitPlanetReplyMultiError(errors)
	}

	return nil
}

// QuitPlanetReplyMultiError is an error wrapping multiple validation errors
// returned by QuitPlanetReply.ValidateAll() if the designated constraints
// aren't met.
type QuitPlanetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuitPlanetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuitPlanetReplyMultiError) AllErrors() []error { return m }

// QuitPlanetReplyValidationError is the validation error returned by
// QuitPlanetReply.Validate if the designated constraints aren't met.
type QuitPlanetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuitPlanetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuitPlanetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuitPlanetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuitPlanetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuitPlanetReplyValidationError) ErrorName() string { return "QuitPlanetReplyValidationError" }

// Error satisfies the builtin error interface
func (e QuitPlanetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuitPlanetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuitPlanetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuitPlanetReplyValidationError{}

// Validate checks the field values on ListPlanetByUserIDRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetByUserIDRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetByUserIDRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetByUserIDRequestMultiError, or nil if none found.
func (m *ListPlanetByUserIDRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetByUserIDRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListPlanetByUserIDRequestMultiError(errors)
	}

	return nil
}

// ListPlanetByUserIDRequestMultiError is an error wrapping multiple validation
// errors returned by ListPlanetByUserIDRequest.ValidateAll() if the
// designated constraints aren't met.
type ListPlanetByUserIDRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetByUserIDRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetByUserIDRequestMultiError) AllErrors() []error { return m }

// ListPlanetByUserIDRequestValidationError is the validation error returned by
// ListPlanetByUserIDRequest.Validate if the designated constraints aren't met.
type ListPlanetByUserIDRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetByUserIDRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetByUserIDRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetByUserIDRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetByUserIDRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetByUserIDRequestValidationError) ErrorName() string {
	return "ListPlanetByUserIDRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetByUserIDRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetByUserIDRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetByUserIDRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetByUserIDRequestValidationError{}

// Validate checks the field values on ListPlanetByUserIDReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetByUserIDReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetByUserIDReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetByUserIDReplyMultiError, or nil if none found.
func (m *ListPlanetByUserIDReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetByUserIDReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPlanetByUserIDReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPlanetByUserIDReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPlanetByUserIDReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListPlanetByUserIDReplyMultiError(errors)
	}

	return nil
}

// ListPlanetByUserIDReplyMultiError is an error wrapping multiple validation
// errors returned by ListPlanetByUserIDReply.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetByUserIDReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetByUserIDReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetByUserIDReplyMultiError) AllErrors() []error { return m }

// ListPlanetByUserIDReplyValidationError is the validation error returned by
// ListPlanetByUserIDReply.Validate if the designated constraints aren't met.
type ListPlanetByUserIDReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetByUserIDReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetByUserIDReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetByUserIDReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetByUserIDReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetByUserIDReplyValidationError) ErrorName() string {
	return "ListPlanetByUserIDReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetByUserIDReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetByUserIDReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetByUserIDReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetByUserIDReplyValidationError{}

// Validate checks the field values on JoinPlanetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *JoinPlanetTargetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JoinPlanetTargetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JoinPlanetTargetRequestMultiError, or nil if none found.
func (m *JoinPlanetTargetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *JoinPlanetTargetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for TargetId

	if len(errors) > 0 {
		return JoinPlanetTargetRequestMultiError(errors)
	}

	return nil
}

// JoinPlanetTargetRequestMultiError is an error wrapping multiple validation
// errors returned by JoinPlanetTargetRequest.ValidateAll() if the designated
// constraints aren't met.
type JoinPlanetTargetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JoinPlanetTargetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JoinPlanetTargetRequestMultiError) AllErrors() []error { return m }

// JoinPlanetTargetRequestValidationError is the validation error returned by
// JoinPlanetTargetRequest.Validate if the designated constraints aren't met.
type JoinPlanetTargetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JoinPlanetTargetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JoinPlanetTargetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JoinPlanetTargetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JoinPlanetTargetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JoinPlanetTargetRequestValidationError) ErrorName() string {
	return "JoinPlanetTargetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e JoinPlanetTargetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJoinPlanetTargetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JoinPlanetTargetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JoinPlanetTargetRequestValidationError{}

// Validate checks the field values on JoinPlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *JoinPlanetTargetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JoinPlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JoinPlanetTargetReplyMultiError, or nil if none found.
func (m *JoinPlanetTargetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *JoinPlanetTargetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return JoinPlanetTargetReplyMultiError(errors)
	}

	return nil
}

// JoinPlanetTargetReplyMultiError is an error wrapping multiple validation
// errors returned by JoinPlanetTargetReply.ValidateAll() if the designated
// constraints aren't met.
type JoinPlanetTargetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JoinPlanetTargetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JoinPlanetTargetReplyMultiError) AllErrors() []error { return m }

// JoinPlanetTargetReplyValidationError is the validation error returned by
// JoinPlanetTargetReply.Validate if the designated constraints aren't met.
type JoinPlanetTargetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JoinPlanetTargetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JoinPlanetTargetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JoinPlanetTargetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JoinPlanetTargetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JoinPlanetTargetReplyValidationError) ErrorName() string {
	return "JoinPlanetTargetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e JoinPlanetTargetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJoinPlanetTargetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JoinPlanetTargetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JoinPlanetTargetReplyValidationError{}

// Validate checks the field values on QuitPlanetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuitPlanetTargetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuitPlanetTargetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuitPlanetTargetRequestMultiError, or nil if none found.
func (m *QuitPlanetTargetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QuitPlanetTargetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for TargetId

	if len(errors) > 0 {
		return QuitPlanetTargetRequestMultiError(errors)
	}

	return nil
}

// QuitPlanetTargetRequestMultiError is an error wrapping multiple validation
// errors returned by QuitPlanetTargetRequest.ValidateAll() if the designated
// constraints aren't met.
type QuitPlanetTargetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuitPlanetTargetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuitPlanetTargetRequestMultiError) AllErrors() []error { return m }

// QuitPlanetTargetRequestValidationError is the validation error returned by
// QuitPlanetTargetRequest.Validate if the designated constraints aren't met.
type QuitPlanetTargetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuitPlanetTargetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuitPlanetTargetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuitPlanetTargetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuitPlanetTargetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuitPlanetTargetRequestValidationError) ErrorName() string {
	return "QuitPlanetTargetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QuitPlanetTargetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuitPlanetTargetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuitPlanetTargetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuitPlanetTargetRequestValidationError{}

// Validate checks the field values on QuitPlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuitPlanetTargetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuitPlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuitPlanetTargetReplyMultiError, or nil if none found.
func (m *QuitPlanetTargetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QuitPlanetTargetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return QuitPlanetTargetReplyMultiError(errors)
	}

	return nil
}

// QuitPlanetTargetReplyMultiError is an error wrapping multiple validation
// errors returned by QuitPlanetTargetReply.ValidateAll() if the designated
// constraints aren't met.
type QuitPlanetTargetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuitPlanetTargetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuitPlanetTargetReplyMultiError) AllErrors() []error { return m }

// QuitPlanetTargetReplyValidationError is the validation error returned by
// QuitPlanetTargetReply.Validate if the designated constraints aren't met.
type QuitPlanetTargetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuitPlanetTargetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuitPlanetTargetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuitPlanetTargetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuitPlanetTargetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuitPlanetTargetReplyValidationError) ErrorName() string {
	return "QuitPlanetTargetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QuitPlanetTargetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuitPlanetTargetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuitPlanetTargetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuitPlanetTargetReplyValidationError{}

// Validate checks the field values on CreatePlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePlanetPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePlanetPostRequestMultiError, or nil if none found.
func (m *CreatePlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for Content

	if len(errors) > 0 {
		return CreatePlanetPostRequestMultiError(errors)
	}

	return nil
}

// CreatePlanetPostRequestMultiError is an error wrapping multiple validation
// errors returned by CreatePlanetPostRequest.ValidateAll() if the designated
// constraints aren't met.
type CreatePlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePlanetPostRequestMultiError) AllErrors() []error { return m }

// CreatePlanetPostRequestValidationError is the validation error returned by
// CreatePlanetPostRequest.Validate if the designated constraints aren't met.
type CreatePlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePlanetPostRequestValidationError) ErrorName() string {
	return "CreatePlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePlanetPostRequestValidationError{}

// Validate checks the field values on CreatePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePlanetPostReplyMultiError, or nil if none found.
func (m *CreatePlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreatePlanetPostReplyMultiError(errors)
	}

	return nil
}

// CreatePlanetPostReplyMultiError is an error wrapping multiple validation
// errors returned by CreatePlanetPostReply.ValidateAll() if the designated
// constraints aren't met.
type CreatePlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePlanetPostReplyMultiError) AllErrors() []error { return m }

// CreatePlanetPostReplyValidationError is the validation error returned by
// CreatePlanetPostReply.Validate if the designated constraints aren't met.
type CreatePlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePlanetPostReplyValidationError) ErrorName() string {
	return "CreatePlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePlanetPostReplyValidationError{}

// Validate checks the field values on UpdatePlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePlanetPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePlanetPostRequestMultiError, or nil if none found.
func (m *UpdatePlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for Content

	// no validation rules for PostId

	if len(errors) > 0 {
		return UpdatePlanetPostRequestMultiError(errors)
	}

	return nil
}

// UpdatePlanetPostRequestMultiError is an error wrapping multiple validation
// errors returned by UpdatePlanetPostRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdatePlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePlanetPostRequestMultiError) AllErrors() []error { return m }

// UpdatePlanetPostRequestValidationError is the validation error returned by
// UpdatePlanetPostRequest.Validate if the designated constraints aren't met.
type UpdatePlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePlanetPostRequestValidationError) ErrorName() string {
	return "UpdatePlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePlanetPostRequestValidationError{}

// Validate checks the field values on UpdatePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePlanetPostReplyMultiError, or nil if none found.
func (m *UpdatePlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdatePlanetPostReplyMultiError(errors)
	}

	return nil
}

// UpdatePlanetPostReplyMultiError is an error wrapping multiple validation
// errors returned by UpdatePlanetPostReply.ValidateAll() if the designated
// constraints aren't met.
type UpdatePlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePlanetPostReplyMultiError) AllErrors() []error { return m }

// UpdatePlanetPostReplyValidationError is the validation error returned by
// UpdatePlanetPostReply.Validate if the designated constraints aren't met.
type UpdatePlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePlanetPostReplyValidationError) ErrorName() string {
	return "UpdatePlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePlanetPostReplyValidationError{}

// Validate checks the field values on DeletePlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePlanetPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePlanetPostRequestMultiError, or nil if none found.
func (m *DeletePlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for PostId

	if len(errors) > 0 {
		return DeletePlanetPostRequestMultiError(errors)
	}

	return nil
}

// DeletePlanetPostRequestMultiError is an error wrapping multiple validation
// errors returned by DeletePlanetPostRequest.ValidateAll() if the designated
// constraints aren't met.
type DeletePlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePlanetPostRequestMultiError) AllErrors() []error { return m }

// DeletePlanetPostRequestValidationError is the validation error returned by
// DeletePlanetPostRequest.Validate if the designated constraints aren't met.
type DeletePlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePlanetPostRequestValidationError) ErrorName() string {
	return "DeletePlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePlanetPostRequestValidationError{}

// Validate checks the field values on DeletePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePlanetPostReplyMultiError, or nil if none found.
func (m *DeletePlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeletePlanetPostReplyMultiError(errors)
	}

	return nil
}

// DeletePlanetPostReplyMultiError is an error wrapping multiple validation
// errors returned by DeletePlanetPostReply.ValidateAll() if the designated
// constraints aren't met.
type DeletePlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePlanetPostReplyMultiError) AllErrors() []error { return m }

// DeletePlanetPostReplyValidationError is the validation error returned by
// DeletePlanetPostReply.Validate if the designated constraints aren't met.
type DeletePlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePlanetPostReplyValidationError) ErrorName() string {
	return "DeletePlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePlanetPostReplyValidationError{}

// Validate checks the field values on UserInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserInfoMultiError, or nil
// if none found.
func (m *UserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Nickname

	// no validation rules for AvatarUrl

	if len(errors) > 0 {
		return UserInfoMultiError(errors)
	}

	return nil
}

// UserInfoMultiError is an error wrapping multiple validation errors returned
// by UserInfo.ValidateAll() if the designated constraints aren't met.
type UserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInfoMultiError) AllErrors() []error { return m }

// UserInfoValidationError is the validation error returned by
// UserInfo.Validate if the designated constraints aren't met.
type UserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInfoValidationError) ErrorName() string { return "UserInfoValidationError" }

// Error satisfies the builtin error interface
func (e UserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInfoValidationError{}

// Validate checks the field values on ListPlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetPostRequestMultiError, or nil if none found.
func (m *ListPlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for LabelType

	if len(errors) > 0 {
		return ListPlanetPostRequestMultiError(errors)
	}

	return nil
}

// ListPlanetPostRequestMultiError is an error wrapping multiple validation
// errors returned by ListPlanetPostRequest.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetPostRequestMultiError) AllErrors() []error { return m }

// ListPlanetPostRequestValidationError is the validation error returned by
// ListPlanetPostRequest.Validate if the designated constraints aren't met.
type ListPlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetPostRequestValidationError) ErrorName() string {
	return "ListPlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetPostRequestValidationError{}

// Validate checks the field values on ListPlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetPostReplyMultiError, or nil if none found.
func (m *ListPlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPlanetPostReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPlanetPostReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPlanetPostReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListPlanetPostReplyMultiError(errors)
	}

	return nil
}

// ListPlanetPostReplyMultiError is an error wrapping multiple validation
// errors returned by ListPlanetPostReply.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetPostReplyMultiError) AllErrors() []error { return m }

// ListPlanetPostReplyValidationError is the validation error returned by
// ListPlanetPostReply.Validate if the designated constraints aren't met.
type ListPlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetPostReplyValidationError) ErrorName() string {
	return "ListPlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetPostReplyValidationError{}

// Validate checks the field values on ListPlanetTopPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetTopPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetTopPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetTopPostRequestMultiError, or nil if none found.
func (m *ListPlanetTopPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetTopPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ListPlanetTopPostRequestMultiError(errors)
	}

	return nil
}

// ListPlanetTopPostRequestMultiError is an error wrapping multiple validation
// errors returned by ListPlanetTopPostRequest.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetTopPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetTopPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetTopPostRequestMultiError) AllErrors() []error { return m }

// ListPlanetTopPostRequestValidationError is the validation error returned by
// ListPlanetTopPostRequest.Validate if the designated constraints aren't met.
type ListPlanetTopPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetTopPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetTopPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetTopPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetTopPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetTopPostRequestValidationError) ErrorName() string {
	return "ListPlanetTopPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetTopPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetTopPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetTopPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetTopPostRequestValidationError{}

// Validate checks the field values on ListPlanetTopPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetTopPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetTopPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetTopPostReplyMultiError, or nil if none found.
func (m *ListPlanetTopPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetTopPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPlanetTopPostReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPlanetTopPostReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPlanetTopPostReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListPlanetTopPostReplyMultiError(errors)
	}

	return nil
}

// ListPlanetTopPostReplyMultiError is an error wrapping multiple validation
// errors returned by ListPlanetTopPostReply.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetTopPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetTopPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetTopPostReplyMultiError) AllErrors() []error { return m }

// ListPlanetTopPostReplyValidationError is the validation error returned by
// ListPlanetTopPostReply.Validate if the designated constraints aren't met.
type ListPlanetTopPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetTopPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetTopPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetTopPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetTopPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetTopPostReplyValidationError) ErrorName() string {
	return "ListPlanetTopPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetTopPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetTopPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetTopPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetTopPostReplyValidationError{}

// Validate checks the field values on LikePlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LikePlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LikePlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LikePlanetPostRequestMultiError, or nil if none found.
func (m *LikePlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LikePlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for PostId

	if len(errors) > 0 {
		return LikePlanetPostRequestMultiError(errors)
	}

	return nil
}

// LikePlanetPostRequestMultiError is an error wrapping multiple validation
// errors returned by LikePlanetPostRequest.ValidateAll() if the designated
// constraints aren't met.
type LikePlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LikePlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LikePlanetPostRequestMultiError) AllErrors() []error { return m }

// LikePlanetPostRequestValidationError is the validation error returned by
// LikePlanetPostRequest.Validate if the designated constraints aren't met.
type LikePlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LikePlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LikePlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LikePlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LikePlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LikePlanetPostRequestValidationError) ErrorName() string {
	return "LikePlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LikePlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLikePlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LikePlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LikePlanetPostRequestValidationError{}

// Validate checks the field values on LikePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LikePlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LikePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LikePlanetPostReplyMultiError, or nil if none found.
func (m *LikePlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LikePlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return LikePlanetPostReplyMultiError(errors)
	}

	return nil
}

// LikePlanetPostReplyMultiError is an error wrapping multiple validation
// errors returned by LikePlanetPostReply.ValidateAll() if the designated
// constraints aren't met.
type LikePlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LikePlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LikePlanetPostReplyMultiError) AllErrors() []error { return m }

// LikePlanetPostReplyValidationError is the validation error returned by
// LikePlanetPostReply.Validate if the designated constraints aren't met.
type LikePlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LikePlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LikePlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LikePlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LikePlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LikePlanetPostReplyValidationError) ErrorName() string {
	return "LikePlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e LikePlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLikePlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LikePlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LikePlanetPostReplyValidationError{}

// Validate checks the field values on CancelLikePlanetPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelLikePlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLikePlanetPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLikePlanetPostRequestMultiError, or nil if none found.
func (m *CancelLikePlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLikePlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for PostId

	if len(errors) > 0 {
		return CancelLikePlanetPostRequestMultiError(errors)
	}

	return nil
}

// CancelLikePlanetPostRequestMultiError is an error wrapping multiple
// validation errors returned by CancelLikePlanetPostRequest.ValidateAll() if
// the designated constraints aren't met.
type CancelLikePlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLikePlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLikePlanetPostRequestMultiError) AllErrors() []error { return m }

// CancelLikePlanetPostRequestValidationError is the validation error returned
// by CancelLikePlanetPostRequest.Validate if the designated constraints
// aren't met.
type CancelLikePlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLikePlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLikePlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLikePlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLikePlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLikePlanetPostRequestValidationError) ErrorName() string {
	return "CancelLikePlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLikePlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLikePlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLikePlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLikePlanetPostRequestValidationError{}

// Validate checks the field values on CancelLikePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelLikePlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelLikePlanetPostReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelLikePlanetPostReplyMultiError, or nil if none found.
func (m *CancelLikePlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelLikePlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CancelLikePlanetPostReplyMultiError(errors)
	}

	return nil
}

// CancelLikePlanetPostReplyMultiError is an error wrapping multiple validation
// errors returned by CancelLikePlanetPostReply.ValidateAll() if the
// designated constraints aren't met.
type CancelLikePlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelLikePlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelLikePlanetPostReplyMultiError) AllErrors() []error { return m }

// CancelLikePlanetPostReplyValidationError is the validation error returned by
// CancelLikePlanetPostReply.Validate if the designated constraints aren't met.
type CancelLikePlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelLikePlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelLikePlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelLikePlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelLikePlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelLikePlanetPostReplyValidationError) ErrorName() string {
	return "CancelLikePlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CancelLikePlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelLikePlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelLikePlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelLikePlanetPostReplyValidationError{}

// Validate checks the field values on FavoritePlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FavoritePlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FavoritePlanetPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FavoritePlanetPostRequestMultiError, or nil if none found.
func (m *FavoritePlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FavoritePlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for PostId

	if len(errors) > 0 {
		return FavoritePlanetPostRequestMultiError(errors)
	}

	return nil
}

// FavoritePlanetPostRequestMultiError is an error wrapping multiple validation
// errors returned by FavoritePlanetPostRequest.ValidateAll() if the
// designated constraints aren't met.
type FavoritePlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FavoritePlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FavoritePlanetPostRequestMultiError) AllErrors() []error { return m }

// FavoritePlanetPostRequestValidationError is the validation error returned by
// FavoritePlanetPostRequest.Validate if the designated constraints aren't met.
type FavoritePlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FavoritePlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FavoritePlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FavoritePlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FavoritePlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FavoritePlanetPostRequestValidationError) ErrorName() string {
	return "FavoritePlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FavoritePlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFavoritePlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FavoritePlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FavoritePlanetPostRequestValidationError{}

// Validate checks the field values on FavoritePlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FavoritePlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FavoritePlanetPostReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FavoritePlanetPostReplyMultiError, or nil if none found.
func (m *FavoritePlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FavoritePlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return FavoritePlanetPostReplyMultiError(errors)
	}

	return nil
}

// FavoritePlanetPostReplyMultiError is an error wrapping multiple validation
// errors returned by FavoritePlanetPostReply.ValidateAll() if the designated
// constraints aren't met.
type FavoritePlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FavoritePlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FavoritePlanetPostReplyMultiError) AllErrors() []error { return m }

// FavoritePlanetPostReplyValidationError is the validation error returned by
// FavoritePlanetPostReply.Validate if the designated constraints aren't met.
type FavoritePlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FavoritePlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FavoritePlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FavoritePlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FavoritePlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FavoritePlanetPostReplyValidationError) ErrorName() string {
	return "FavoritePlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FavoritePlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFavoritePlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FavoritePlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FavoritePlanetPostReplyValidationError{}

// Validate checks the field values on CancelFavoritePlanetPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelFavoritePlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelFavoritePlanetPostRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CancelFavoritePlanetPostRequestMultiError, or nil if none found.
func (m *CancelFavoritePlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelFavoritePlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for PostId

	if len(errors) > 0 {
		return CancelFavoritePlanetPostRequestMultiError(errors)
	}

	return nil
}

// CancelFavoritePlanetPostRequestMultiError is an error wrapping multiple
// validation errors returned by CancelFavoritePlanetPostRequest.ValidateAll()
// if the designated constraints aren't met.
type CancelFavoritePlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelFavoritePlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelFavoritePlanetPostRequestMultiError) AllErrors() []error { return m }

// CancelFavoritePlanetPostRequestValidationError is the validation error
// returned by CancelFavoritePlanetPostRequest.Validate if the designated
// constraints aren't met.
type CancelFavoritePlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelFavoritePlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelFavoritePlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelFavoritePlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelFavoritePlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelFavoritePlanetPostRequestValidationError) ErrorName() string {
	return "CancelFavoritePlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelFavoritePlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelFavoritePlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelFavoritePlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelFavoritePlanetPostRequestValidationError{}

// Validate checks the field values on CancelFavoritePlanetPostReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelFavoritePlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelFavoritePlanetPostReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CancelFavoritePlanetPostReplyMultiError, or nil if none found.
func (m *CancelFavoritePlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelFavoritePlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CancelFavoritePlanetPostReplyMultiError(errors)
	}

	return nil
}

// CancelFavoritePlanetPostReplyMultiError is an error wrapping multiple
// validation errors returned by CancelFavoritePlanetPostReply.ValidateAll()
// if the designated constraints aren't met.
type CancelFavoritePlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelFavoritePlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelFavoritePlanetPostReplyMultiError) AllErrors() []error { return m }

// CancelFavoritePlanetPostReplyValidationError is the validation error
// returned by CancelFavoritePlanetPostReply.Validate if the designated
// constraints aren't met.
type CancelFavoritePlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelFavoritePlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelFavoritePlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelFavoritePlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelFavoritePlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelFavoritePlanetPostReplyValidationError) ErrorName() string {
	return "CancelFavoritePlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CancelFavoritePlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelFavoritePlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelFavoritePlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelFavoritePlanetPostReplyValidationError{}

// Validate checks the field values on CreatePlanetPostCommentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePlanetPostCommentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePlanetPostCommentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreatePlanetPostCommentRequestMultiError, or nil if none found.
func (m *CreatePlanetPostCommentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePlanetPostCommentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CommentId

	// no validation rules for PostId

	// no validation rules for Content

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return CreatePlanetPostCommentRequestMultiError(errors)
	}

	return nil
}

// CreatePlanetPostCommentRequestMultiError is an error wrapping multiple
// validation errors returned by CreatePlanetPostCommentRequest.ValidateAll()
// if the designated constraints aren't met.
type CreatePlanetPostCommentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePlanetPostCommentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePlanetPostCommentRequestMultiError) AllErrors() []error { return m }

// CreatePlanetPostCommentRequestValidationError is the validation error
// returned by CreatePlanetPostCommentRequest.Validate if the designated
// constraints aren't met.
type CreatePlanetPostCommentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePlanetPostCommentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePlanetPostCommentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePlanetPostCommentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePlanetPostCommentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePlanetPostCommentRequestValidationError) ErrorName() string {
	return "CreatePlanetPostCommentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePlanetPostCommentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePlanetPostCommentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePlanetPostCommentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePlanetPostCommentRequestValidationError{}

// Validate checks the field values on CreatePlanetPostCommentReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePlanetPostCommentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePlanetPostCommentReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePlanetPostCommentReplyMultiError, or nil if none found.
func (m *CreatePlanetPostCommentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePlanetPostCommentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreatePlanetPostCommentReplyMultiError(errors)
	}

	return nil
}

// CreatePlanetPostCommentReplyMultiError is an error wrapping multiple
// validation errors returned by CreatePlanetPostCommentReply.ValidateAll() if
// the designated constraints aren't met.
type CreatePlanetPostCommentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePlanetPostCommentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePlanetPostCommentReplyMultiError) AllErrors() []error { return m }

// CreatePlanetPostCommentReplyValidationError is the validation error returned
// by CreatePlanetPostCommentReply.Validate if the designated constraints
// aren't met.
type CreatePlanetPostCommentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePlanetPostCommentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePlanetPostCommentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePlanetPostCommentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePlanetPostCommentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePlanetPostCommentReplyValidationError) ErrorName() string {
	return "CreatePlanetPostCommentReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePlanetPostCommentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePlanetPostCommentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePlanetPostCommentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePlanetPostCommentReplyValidationError{}

// Validate checks the field values on DeletePlanetPostCommentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePlanetPostCommentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePlanetPostCommentRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeletePlanetPostCommentRequestMultiError, or nil if none found.
func (m *DeletePlanetPostCommentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePlanetPostCommentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for PostId

	// no validation rules for CommentId

	if len(errors) > 0 {
		return DeletePlanetPostCommentRequestMultiError(errors)
	}

	return nil
}

// DeletePlanetPostCommentRequestMultiError is an error wrapping multiple
// validation errors returned by DeletePlanetPostCommentRequest.ValidateAll()
// if the designated constraints aren't met.
type DeletePlanetPostCommentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePlanetPostCommentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePlanetPostCommentRequestMultiError) AllErrors() []error { return m }

// DeletePlanetPostCommentRequestValidationError is the validation error
// returned by DeletePlanetPostCommentRequest.Validate if the designated
// constraints aren't met.
type DeletePlanetPostCommentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePlanetPostCommentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePlanetPostCommentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePlanetPostCommentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePlanetPostCommentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePlanetPostCommentRequestValidationError) ErrorName() string {
	return "DeletePlanetPostCommentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePlanetPostCommentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePlanetPostCommentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePlanetPostCommentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePlanetPostCommentRequestValidationError{}

// Validate checks the field values on DeletePlanetPostCommentReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePlanetPostCommentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePlanetPostCommentReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePlanetPostCommentReplyMultiError, or nil if none found.
func (m *DeletePlanetPostCommentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePlanetPostCommentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeletePlanetPostCommentReplyMultiError(errors)
	}

	return nil
}

// DeletePlanetPostCommentReplyMultiError is an error wrapping multiple
// validation errors returned by DeletePlanetPostCommentReply.ValidateAll() if
// the designated constraints aren't met.
type DeletePlanetPostCommentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePlanetPostCommentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePlanetPostCommentReplyMultiError) AllErrors() []error { return m }

// DeletePlanetPostCommentReplyValidationError is the validation error returned
// by DeletePlanetPostCommentReply.Validate if the designated constraints
// aren't met.
type DeletePlanetPostCommentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePlanetPostCommentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePlanetPostCommentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePlanetPostCommentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePlanetPostCommentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePlanetPostCommentReplyValidationError) ErrorName() string {
	return "DeletePlanetPostCommentReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePlanetPostCommentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePlanetPostCommentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePlanetPostCommentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePlanetPostCommentReplyValidationError{}

// Validate checks the field values on SubCommentItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubCommentItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubCommentItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SubCommentItemMultiError,
// or nil if none found.
func (m *SubCommentItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SubCommentItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for PostId

	// no validation rules for Content

	// no validation rules for PlanetId

	// no validation rules for CreatedAt

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubCommentItemValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubCommentItemValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubCommentItemValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRepliedUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubCommentItemValidationError{
					field:  "RepliedUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubCommentItemValidationError{
					field:  "RepliedUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRepliedUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubCommentItemValidationError{
				field:  "RepliedUser",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubCommentItemMultiError(errors)
	}

	return nil
}

// SubCommentItemMultiError is an error wrapping multiple validation errors
// returned by SubCommentItem.ValidateAll() if the designated constraints
// aren't met.
type SubCommentItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubCommentItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubCommentItemMultiError) AllErrors() []error { return m }

// SubCommentItemValidationError is the validation error returned by
// SubCommentItem.Validate if the designated constraints aren't met.
type SubCommentItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubCommentItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubCommentItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubCommentItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubCommentItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubCommentItemValidationError) ErrorName() string { return "SubCommentItemValidationError" }

// Error satisfies the builtin error interface
func (e SubCommentItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubCommentItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubCommentItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubCommentItemValidationError{}

// Validate checks the field values on ListPlanetPostCommentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetPostCommentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetPostCommentRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetPostCommentRequestMultiError, or nil if none found.
func (m *ListPlanetPostCommentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetPostCommentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for PostId

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return ListPlanetPostCommentRequestMultiError(errors)
	}

	return nil
}

// ListPlanetPostCommentRequestMultiError is an error wrapping multiple
// validation errors returned by ListPlanetPostCommentRequest.ValidateAll() if
// the designated constraints aren't met.
type ListPlanetPostCommentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetPostCommentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetPostCommentRequestMultiError) AllErrors() []error { return m }

// ListPlanetPostCommentRequestValidationError is the validation error returned
// by ListPlanetPostCommentRequest.Validate if the designated constraints
// aren't met.
type ListPlanetPostCommentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetPostCommentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetPostCommentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetPostCommentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetPostCommentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetPostCommentRequestValidationError) ErrorName() string {
	return "ListPlanetPostCommentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetPostCommentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetPostCommentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetPostCommentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetPostCommentRequestValidationError{}

// Validate checks the field values on ListPlanetPostCommentReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetPostCommentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetPostCommentReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetPostCommentReplyMultiError, or nil if none found.
func (m *ListPlanetPostCommentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetPostCommentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPlanetPostCommentReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPlanetPostCommentReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPlanetPostCommentReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListPlanetPostCommentReplyMultiError(errors)
	}

	return nil
}

// ListPlanetPostCommentReplyMultiError is an error wrapping multiple
// validation errors returned by ListPlanetPostCommentReply.ValidateAll() if
// the designated constraints aren't met.
type ListPlanetPostCommentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetPostCommentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetPostCommentReplyMultiError) AllErrors() []error { return m }

// ListPlanetPostCommentReplyValidationError is the validation error returned
// by ListPlanetPostCommentReply.Validate if the designated constraints aren't met.
type ListPlanetPostCommentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetPostCommentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetPostCommentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetPostCommentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetPostCommentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetPostCommentReplyValidationError) ErrorName() string {
	return "ListPlanetPostCommentReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetPostCommentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetPostCommentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetPostCommentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetPostCommentReplyValidationError{}

// Validate checks the field values on ToppedPlanetPostRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ToppedPlanetPostRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ToppedPlanetPostRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ToppedPlanetPostRequestMultiError, or nil if none found.
func (m *ToppedPlanetPostRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ToppedPlanetPostRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for PostId

	if len(errors) > 0 {
		return ToppedPlanetPostRequestMultiError(errors)
	}

	return nil
}

// ToppedPlanetPostRequestMultiError is an error wrapping multiple validation
// errors returned by ToppedPlanetPostRequest.ValidateAll() if the designated
// constraints aren't met.
type ToppedPlanetPostRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ToppedPlanetPostRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ToppedPlanetPostRequestMultiError) AllErrors() []error { return m }

// ToppedPlanetPostRequestValidationError is the validation error returned by
// ToppedPlanetPostRequest.Validate if the designated constraints aren't met.
type ToppedPlanetPostRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ToppedPlanetPostRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ToppedPlanetPostRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ToppedPlanetPostRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ToppedPlanetPostRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ToppedPlanetPostRequestValidationError) ErrorName() string {
	return "ToppedPlanetPostRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ToppedPlanetPostRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sToppedPlanetPostRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ToppedPlanetPostRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ToppedPlanetPostRequestValidationError{}

// Validate checks the field values on ToppedPlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ToppedPlanetPostReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ToppedPlanetPostReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ToppedPlanetPostReplyMultiError, or nil if none found.
func (m *ToppedPlanetPostReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ToppedPlanetPostReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ToppedPlanetPostReplyMultiError(errors)
	}

	return nil
}

// ToppedPlanetPostReplyMultiError is an error wrapping multiple validation
// errors returned by ToppedPlanetPostReply.ValidateAll() if the designated
// constraints aren't met.
type ToppedPlanetPostReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ToppedPlanetPostReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ToppedPlanetPostReplyMultiError) AllErrors() []error { return m }

// ToppedPlanetPostReplyValidationError is the validation error returned by
// ToppedPlanetPostReply.Validate if the designated constraints aren't met.
type ToppedPlanetPostReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ToppedPlanetPostReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ToppedPlanetPostReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ToppedPlanetPostReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ToppedPlanetPostReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ToppedPlanetPostReplyValidationError) ErrorName() string {
	return "ToppedPlanetPostReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ToppedPlanetPostReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sToppedPlanetPostReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ToppedPlanetPostReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ToppedPlanetPostReplyValidationError{}

// Validate checks the field values on ListPlanetByUserIDReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetByUserIDReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetByUserIDReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetByUserIDReply_DataMultiError, or nil if none found.
func (m *ListPlanetByUserIDReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetByUserIDReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for ImgUrl

	if len(errors) > 0 {
		return ListPlanetByUserIDReply_DataMultiError(errors)
	}

	return nil
}

// ListPlanetByUserIDReply_DataMultiError is an error wrapping multiple
// validation errors returned by ListPlanetByUserIDReply_Data.ValidateAll() if
// the designated constraints aren't met.
type ListPlanetByUserIDReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetByUserIDReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetByUserIDReply_DataMultiError) AllErrors() []error { return m }

// ListPlanetByUserIDReply_DataValidationError is the validation error returned
// by ListPlanetByUserIDReply_Data.Validate if the designated constraints
// aren't met.
type ListPlanetByUserIDReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetByUserIDReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetByUserIDReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetByUserIDReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetByUserIDReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetByUserIDReply_DataValidationError) ErrorName() string {
	return "ListPlanetByUserIDReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetByUserIDReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetByUserIDReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetByUserIDReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetByUserIDReply_DataValidationError{}

// Validate checks the field values on ListPlanetPostReply_Item with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetPostReply_Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetPostReply_Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetPostReply_ItemMultiError, or nil if none found.
func (m *ListPlanetPostReply_Item) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetPostReply_Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPlanetPostReply_ItemValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPlanetPostReply_ItemValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPlanetPostReply_ItemValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Content

	// no validation rules for PlanetId

	// no validation rules for LikeCount

	// no validation rules for FavoriteCount

	// no validation rules for CommentCount

	// no validation rules for CreatedAt

	// no validation rules for IsLike

	// no validation rules for IsFavorite

	if len(errors) > 0 {
		return ListPlanetPostReply_ItemMultiError(errors)
	}

	return nil
}

// ListPlanetPostReply_ItemMultiError is an error wrapping multiple validation
// errors returned by ListPlanetPostReply_Item.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetPostReply_ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetPostReply_ItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetPostReply_ItemMultiError) AllErrors() []error { return m }

// ListPlanetPostReply_ItemValidationError is the validation error returned by
// ListPlanetPostReply_Item.Validate if the designated constraints aren't met.
type ListPlanetPostReply_ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetPostReply_ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetPostReply_ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetPostReply_ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetPostReply_ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetPostReply_ItemValidationError) ErrorName() string {
	return "ListPlanetPostReply_ItemValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetPostReply_ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetPostReply_Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetPostReply_ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetPostReply_ItemValidationError{}

// Validate checks the field values on ListPlanetPostReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetPostReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetPostReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetPostReply_DataMultiError, or nil if none found.
func (m *ListPlanetPostReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetPostReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Total

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPlanetPostReply_DataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPlanetPostReply_DataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPlanetPostReply_DataValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListPlanetPostReply_DataMultiError(errors)
	}

	return nil
}

// ListPlanetPostReply_DataMultiError is an error wrapping multiple validation
// errors returned by ListPlanetPostReply_Data.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetPostReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetPostReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetPostReply_DataMultiError) AllErrors() []error { return m }

// ListPlanetPostReply_DataValidationError is the validation error returned by
// ListPlanetPostReply_Data.Validate if the designated constraints aren't met.
type ListPlanetPostReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetPostReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetPostReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetPostReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetPostReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetPostReply_DataValidationError) ErrorName() string {
	return "ListPlanetPostReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetPostReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetPostReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetPostReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetPostReply_DataValidationError{}

// Validate checks the field values on ListPlanetTopPostReply_Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetTopPostReply_Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetTopPostReply_Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetTopPostReply_ItemMultiError, or nil if none found.
func (m *ListPlanetTopPostReply_Item) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetTopPostReply_Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for PlanetId

	// no validation rules for Content

	if len(errors) > 0 {
		return ListPlanetTopPostReply_ItemMultiError(errors)
	}

	return nil
}

// ListPlanetTopPostReply_ItemMultiError is an error wrapping multiple
// validation errors returned by ListPlanetTopPostReply_Item.ValidateAll() if
// the designated constraints aren't met.
type ListPlanetTopPostReply_ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetTopPostReply_ItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetTopPostReply_ItemMultiError) AllErrors() []error { return m }

// ListPlanetTopPostReply_ItemValidationError is the validation error returned
// by ListPlanetTopPostReply_Item.Validate if the designated constraints
// aren't met.
type ListPlanetTopPostReply_ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetTopPostReply_ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetTopPostReply_ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetTopPostReply_ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetTopPostReply_ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetTopPostReply_ItemValidationError) ErrorName() string {
	return "ListPlanetTopPostReply_ItemValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetTopPostReply_ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetTopPostReply_Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetTopPostReply_ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetTopPostReply_ItemValidationError{}

// Validate checks the field values on ListPlanetTopPostReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetTopPostReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetTopPostReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetTopPostReply_DataMultiError, or nil if none found.
func (m *ListPlanetTopPostReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetTopPostReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Total

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPlanetTopPostReply_DataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPlanetTopPostReply_DataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPlanetTopPostReply_DataValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListPlanetTopPostReply_DataMultiError(errors)
	}

	return nil
}

// ListPlanetTopPostReply_DataMultiError is an error wrapping multiple
// validation errors returned by ListPlanetTopPostReply_Data.ValidateAll() if
// the designated constraints aren't met.
type ListPlanetTopPostReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetTopPostReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetTopPostReply_DataMultiError) AllErrors() []error { return m }

// ListPlanetTopPostReply_DataValidationError is the validation error returned
// by ListPlanetTopPostReply_Data.Validate if the designated constraints
// aren't met.
type ListPlanetTopPostReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetTopPostReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetTopPostReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetTopPostReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetTopPostReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetTopPostReply_DataValidationError) ErrorName() string {
	return "ListPlanetTopPostReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetTopPostReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetTopPostReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetTopPostReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetTopPostReply_DataValidationError{}

// Validate checks the field values on ListPlanetPostCommentReply_Item with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetPostCommentReply_Item) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetPostCommentReply_Item with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListPlanetPostCommentReply_ItemMultiError, or nil if none found.
func (m *ListPlanetPostCommentReply_Item) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetPostCommentReply_Item) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for PostId

	// no validation rules for Content

	// no validation rules for PlanetId

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListPlanetPostCommentReply_ItemValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListPlanetPostCommentReply_ItemValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListPlanetPostCommentReply_ItemValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSubItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPlanetPostCommentReply_ItemValidationError{
						field:  fmt.Sprintf("SubItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPlanetPostCommentReply_ItemValidationError{
						field:  fmt.Sprintf("SubItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPlanetPostCommentReply_ItemValidationError{
					field:  fmt.Sprintf("SubItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CreatedAt

	// no validation rules for SubCount

	if len(errors) > 0 {
		return ListPlanetPostCommentReply_ItemMultiError(errors)
	}

	return nil
}

// ListPlanetPostCommentReply_ItemMultiError is an error wrapping multiple
// validation errors returned by ListPlanetPostCommentReply_Item.ValidateAll()
// if the designated constraints aren't met.
type ListPlanetPostCommentReply_ItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetPostCommentReply_ItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetPostCommentReply_ItemMultiError) AllErrors() []error { return m }

// ListPlanetPostCommentReply_ItemValidationError is the validation error
// returned by ListPlanetPostCommentReply_Item.Validate if the designated
// constraints aren't met.
type ListPlanetPostCommentReply_ItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetPostCommentReply_ItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetPostCommentReply_ItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetPostCommentReply_ItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetPostCommentReply_ItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetPostCommentReply_ItemValidationError) ErrorName() string {
	return "ListPlanetPostCommentReply_ItemValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetPostCommentReply_ItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetPostCommentReply_Item.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetPostCommentReply_ItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetPostCommentReply_ItemValidationError{}

// Validate checks the field values on ListPlanetPostCommentReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetPostCommentReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetPostCommentReply_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListPlanetPostCommentReply_DataMultiError, or nil if none found.
func (m *ListPlanetPostCommentReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetPostCommentReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Total

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPlanetPostCommentReply_DataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPlanetPostCommentReply_DataValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPlanetPostCommentReply_DataValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListPlanetPostCommentReply_DataMultiError(errors)
	}

	return nil
}

// ListPlanetPostCommentReply_DataMultiError is an error wrapping multiple
// validation errors returned by ListPlanetPostCommentReply_Data.ValidateAll()
// if the designated constraints aren't met.
type ListPlanetPostCommentReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetPostCommentReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetPostCommentReply_DataMultiError) AllErrors() []error { return m }

// ListPlanetPostCommentReply_DataValidationError is the validation error
// returned by ListPlanetPostCommentReply_Data.Validate if the designated
// constraints aren't met.
type ListPlanetPostCommentReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetPostCommentReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetPostCommentReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetPostCommentReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetPostCommentReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetPostCommentReply_DataValidationError) ErrorName() string {
	return "ListPlanetPostCommentReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetPostCommentReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetPostCommentReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetPostCommentReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetPostCommentReply_DataValidationError{}
