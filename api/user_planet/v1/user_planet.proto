syntax = "proto3";

package user_planet_v1;

option go_package = "github.com/wlnil/life-log-be/api/user_planet/v1;v1";

message JoinPlanetRequest {
	int32 planet_id = 1;
}
message JoinPlanetReply {
	int32 code = 1;
	string msg = 2;
}

message QuitPlanetRequest {
	int32 planet_id = 1;
}
message QuitPlanetReply {
	int32 code = 1;
	string msg = 2;
}

message ListPlanetByUserIDRequest {}
message ListPlanetByUserIDReply {
	message Data {
		int32 id = 1;
		string name = 2;
		string img_url = 3;
	}

	int32 code = 1;
	string msg = 2;
	repeated Data data = 3;
}

message JoinPlanetTargetRequest {
	int32 planet_id = 1;
	int32 target_id = 2;
}
message JoinPlanetTargetReply {
	int32 code = 1;
	string msg = 2;
}

message QuitPlanetTargetRequest {
	int32 planet_id = 1;
	int32 target_id = 2;
}
message QuitPlanetTargetReply {
	int32 code = 1;
	string msg = 2;
}

message CreatePlanetPostRequest {
	int32 planet_id = 1;
	string content = 2;
	repeated string images = 3;
}
message CreatePlanetPostReply {
	int32 code = 1;
	string msg = 2;
}

message UpdatePlanetPostRequest {
	int32 planet_id = 1;
	string content = 2;
	repeated string images = 3;
	int32 post_id = 4;
}
message UpdatePlanetPostReply {
	int32 code = 1;
	string msg = 2;
}

message DeletePlanetPostRequest {
	int32 planet_id = 1;
	int32 post_id = 2;
}
message DeletePlanetPostReply {
	int32 code = 1;
	string msg = 2;
}

message UserInfo {
	int32 id = 1;
	string nickname = 2;
	string avatar_url = 3;
}

message ListPlanetPostRequest {
	int32 planet_id = 1;
	int32 page = 2;
	int32 page_size = 3;
	int32 label_type = 4;
}
message ListPlanetPostReply {
	message Item {
		int32 id = 1;
		UserInfo user = 2;
		string content = 3;
		int32 planet_id = 4;
		int32 like_count = 5;
		int32 favorite_count = 6;
		int32 comment_count = 7;
		int32 created_at = 8;
		bool is_like = 9;
		bool is_favorite = 10;
	}

	message Data {
		int32 page = 1;
		int32 page_size = 2;
		int32 total = 3;
		repeated Item items = 4;
	}

	int32 code = 1;
	string msg = 2;
	Data data = 3;
}

message ListPlanetTopPostRequest {
	int32 planet_id = 1;
	int32 page = 2;
	int32 page_size = 3;
}
message ListPlanetTopPostReply {
	message Item {
		int32 id = 1;
		int32 planet_id = 2;
		string content = 3;
	}

	message Data {
		int32 page = 1;
		int32 page_size = 2;
		int32 total = 3;
		repeated Item items = 4;
	}

	int32 code = 1;
	string msg = 2;
	Data data = 3;
}

message LikePlanetPostRequest {
	int32 planet_id = 1;
	int32 post_id = 2;
}
message LikePlanetPostReply {
	int32 code = 1;
	string msg = 2;
}

message CancelLikePlanetPostRequest {
	int32 planet_id = 1;
	int32 post_id = 2;
}
message CancelLikePlanetPostReply {
	int32 code = 1;
	string msg = 2;
}

message FavoritePlanetPostRequest {
	int32 planet_id = 1;
	int32 post_id = 2;
}
message FavoritePlanetPostReply {
	int32 code = 1;
	string msg = 2;
}

message CancelFavoritePlanetPostRequest {
	int32 planet_id = 1;
	int32 post_id = 2;
}
message CancelFavoritePlanetPostReply {
	int32 code = 1;
	string msg = 2;
}

message CreatePlanetPostCommentRequest {
	int32 comment_id = 1;
	int32 post_id = 2;
	string content = 3;
	int32 planet_id = 4;
}
message CreatePlanetPostCommentReply {
	int32 code = 1;
	string msg = 2;
}

message DeletePlanetPostCommentRequest {
	int32 planet_id = 1;
	int32 post_id = 2;
	int32 comment_id = 3;
}
message DeletePlanetPostCommentReply {
	int32 code = 1;
	string msg = 2;
}

message SubCommentItem {
	int32 id = 1;
	int32 post_id = 2;
	string content = 3;
	int32 planet_id = 4;
	int32 created_at = 5;
	UserInfo user = 6;
	UserInfo replied_user = 7;
}

message ListPlanetPostCommentRequest {
	int32 planet_id = 1;
	int32 post_id = 2;
	int32 page = 3;
	int32 page_size = 4;
}
message ListPlanetPostCommentReply {
	message Item {
		int32 id = 1;
		int32 post_id = 2;
		string content = 3;
		int32 planet_id = 4;
		UserInfo user = 5;
		repeated SubCommentItem sub_items = 6;
		int32 created_at = 7;
		int32 sub_count = 8;
	}

	message Data {
		int32 page = 1;
		int32 page_size = 2;
		int32 total = 3;
		repeated Item items = 4;
	}

	int32 code = 1;
	string msg = 2;
	Data data = 3;
}

message ToppedPlanetPostRequest {
	int32 planet_id = 1;
	int32 post_id = 2;
}
message ToppedPlanetPostReply {
	int32 code = 1;
	string msg = 2;
}
