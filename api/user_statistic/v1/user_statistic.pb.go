// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: user_statistic/v1/user_statistic.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TodayStatisticFromHomeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurrentDate   string                 `protobuf:"bytes,1,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"` // eg: 2023-11-26T07:00:00+08:00
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TodayStatisticFromHomeRequest) Reset() {
	*x = TodayStatisticFromHomeRequest{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TodayStatisticFromHomeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TodayStatisticFromHomeRequest) ProtoMessage() {}

func (x *TodayStatisticFromHomeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TodayStatisticFromHomeRequest.ProtoReflect.Descriptor instead.
func (*TodayStatisticFromHomeRequest) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{0}
}

func (x *TodayStatisticFromHomeRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type TodayStatisticFromHomeReply struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Code          int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *TodayStatisticFromHomeReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TodayStatisticFromHomeReply) Reset() {
	*x = TodayStatisticFromHomeReply{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TodayStatisticFromHomeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TodayStatisticFromHomeReply) ProtoMessage() {}

func (x *TodayStatisticFromHomeReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TodayStatisticFromHomeReply.ProtoReflect.Descriptor instead.
func (*TodayStatisticFromHomeReply) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{1}
}

func (x *TodayStatisticFromHomeReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TodayStatisticFromHomeReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TodayStatisticFromHomeReply) GetData() *TodayStatisticFromHomeReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserHabitStatisticFromDetailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	CurrentDate   string                 `protobuf:"bytes,2,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`
	LabelType     string                 `protobuf:"bytes,3,opt,name=label_type,json=labelType,proto3" json:"label_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserHabitStatisticFromDetailRequest) Reset() {
	*x = UserHabitStatisticFromDetailRequest{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserHabitStatisticFromDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHabitStatisticFromDetailRequest) ProtoMessage() {}

func (x *UserHabitStatisticFromDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHabitStatisticFromDetailRequest.ProtoReflect.Descriptor instead.
func (*UserHabitStatisticFromDetailRequest) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{2}
}

func (x *UserHabitStatisticFromDetailRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *UserHabitStatisticFromDetailRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

func (x *UserHabitStatisticFromDetailRequest) GetLabelType() string {
	if x != nil {
		return x.LabelType
	}
	return ""
}

type UserHabitStatisticFromDetailReply struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	Code          int32                                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *UserHabitStatisticFromDetailReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserHabitStatisticFromDetailReply) Reset() {
	*x = UserHabitStatisticFromDetailReply{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserHabitStatisticFromDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHabitStatisticFromDetailReply) ProtoMessage() {}

func (x *UserHabitStatisticFromDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHabitStatisticFromDetailReply.ProtoReflect.Descriptor instead.
func (*UserHabitStatisticFromDetailReply) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{3}
}

func (x *UserHabitStatisticFromDetailReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserHabitStatisticFromDetailReply) GetData() *UserHabitStatisticFromDetailReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type TodayStatisticFromHomeReply_Data struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	SmallHabit    *TodayStatisticFromHomeReply_HabitDetail `protobuf:"bytes,1,opt,name=small_habit,json=smallHabit,proto3" json:"small_habit,omitempty"`
	NormalHabit   *TodayStatisticFromHomeReply_HabitDetail `protobuf:"bytes,2,opt,name=normal_habit,json=normalHabit,proto3" json:"normal_habit,omitempty"`
	OverComplete  string                                   `protobuf:"bytes,3,opt,name=over_complete,json=overComplete,proto3" json:"over_complete,omitempty"`
	UnComplete    string                                   `protobuf:"bytes,4,opt,name=un_complete,json=unComplete,proto3" json:"un_complete,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TodayStatisticFromHomeReply_Data) Reset() {
	*x = TodayStatisticFromHomeReply_Data{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TodayStatisticFromHomeReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TodayStatisticFromHomeReply_Data) ProtoMessage() {}

func (x *TodayStatisticFromHomeReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TodayStatisticFromHomeReply_Data.ProtoReflect.Descriptor instead.
func (*TodayStatisticFromHomeReply_Data) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{1, 0}
}

func (x *TodayStatisticFromHomeReply_Data) GetSmallHabit() *TodayStatisticFromHomeReply_HabitDetail {
	if x != nil {
		return x.SmallHabit
	}
	return nil
}

func (x *TodayStatisticFromHomeReply_Data) GetNormalHabit() *TodayStatisticFromHomeReply_HabitDetail {
	if x != nil {
		return x.NormalHabit
	}
	return nil
}

func (x *TodayStatisticFromHomeReply_Data) GetOverComplete() string {
	if x != nil {
		return x.OverComplete
	}
	return ""
}

func (x *TodayStatisticFromHomeReply_Data) GetUnComplete() string {
	if x != nil {
		return x.UnComplete
	}
	return ""
}

type TodayStatisticFromHomeReply_HabitDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Per           int32                  `protobuf:"varint,1,opt,name=per,proto3" json:"per,omitempty"`
	DoneCount     int32                  `protobuf:"varint,2,opt,name=done_count,json=doneCount,proto3" json:"done_count,omitempty"`
	AllCount      int32                  `protobuf:"varint,3,opt,name=all_count,json=allCount,proto3" json:"all_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TodayStatisticFromHomeReply_HabitDetail) Reset() {
	*x = TodayStatisticFromHomeReply_HabitDetail{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TodayStatisticFromHomeReply_HabitDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TodayStatisticFromHomeReply_HabitDetail) ProtoMessage() {}

func (x *TodayStatisticFromHomeReply_HabitDetail) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TodayStatisticFromHomeReply_HabitDetail.ProtoReflect.Descriptor instead.
func (*TodayStatisticFromHomeReply_HabitDetail) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{1, 1}
}

func (x *TodayStatisticFromHomeReply_HabitDetail) GetPer() int32 {
	if x != nil {
		return x.Per
	}
	return 0
}

func (x *TodayStatisticFromHomeReply_HabitDetail) GetDoneCount() int32 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *TodayStatisticFromHomeReply_HabitDetail) GetAllCount() int32 {
	if x != nil {
		return x.AllCount
	}
	return 0
}

type UserHabitStatisticFromDetailReply_Data struct {
	state          protoimpl.MessageState                            `protogen:"open.v1"`
	AllStatistic   *UserHabitStatisticFromDetailReply_AllStatistic   `protobuf:"bytes,1,opt,name=all_statistic,json=allStatistic,proto3" json:"all_statistic,omitempty"`
	WeekStatistic  *UserHabitStatisticFromDetailReply_WeekStatistic  `protobuf:"bytes,2,opt,name=week_statistic,json=weekStatistic,proto3" json:"week_statistic,omitempty"`
	MonthStatistic *UserHabitStatisticFromDetailReply_MonthStatistic `protobuf:"bytes,3,opt,name=month_statistic,json=monthStatistic,proto3" json:"month_statistic,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserHabitStatisticFromDetailReply_Data) Reset() {
	*x = UserHabitStatisticFromDetailReply_Data{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserHabitStatisticFromDetailReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHabitStatisticFromDetailReply_Data) ProtoMessage() {}

func (x *UserHabitStatisticFromDetailReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHabitStatisticFromDetailReply_Data.ProtoReflect.Descriptor instead.
func (*UserHabitStatisticFromDetailReply_Data) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{3, 0}
}

func (x *UserHabitStatisticFromDetailReply_Data) GetAllStatistic() *UserHabitStatisticFromDetailReply_AllStatistic {
	if x != nil {
		return x.AllStatistic
	}
	return nil
}

func (x *UserHabitStatisticFromDetailReply_Data) GetWeekStatistic() *UserHabitStatisticFromDetailReply_WeekStatistic {
	if x != nil {
		return x.WeekStatistic
	}
	return nil
}

func (x *UserHabitStatisticFromDetailReply_Data) GetMonthStatistic() *UserHabitStatisticFromDetailReply_MonthStatistic {
	if x != nil {
		return x.MonthStatistic
	}
	return nil
}

type UserHabitStatisticFromDetailReply_AllStatistic struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Per              int32                  `protobuf:"varint,1,opt,name=per,proto3" json:"per,omitempty"`
	DoneCount        int32                  `protobuf:"varint,2,opt,name=done_count,json=doneCount,proto3" json:"done_count,omitempty"`
	AllCount         int32                  `protobuf:"varint,3,opt,name=all_count,json=allCount,proto3" json:"all_count,omitempty"`
	PersistLongDays  int32                  `protobuf:"varint,4,opt,name=persist_long_days,json=persistLongDays,proto3" json:"persist_long_days,omitempty"`
	AvgData          string                 `protobuf:"bytes,6,opt,name=avg_data,json=avgData,proto3" json:"avg_data,omitempty"`
	MaxData          string                 `protobuf:"bytes,7,opt,name=max_data,json=maxData,proto3" json:"max_data,omitempty"`
	MinData          string                 `protobuf:"bytes,8,opt,name=min_data,json=minData,proto3" json:"min_data,omitempty"`
	PersistStartDate string                 `protobuf:"bytes,5,opt,name=persist_start_date,json=persistStartDate,proto3" json:"persist_start_date,omitempty"`
	PersistEndDate   string                 `protobuf:"bytes,9,opt,name=persist_end_date,json=persistEndDate,proto3" json:"persist_end_date,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) Reset() {
	*x = UserHabitStatisticFromDetailReply_AllStatistic{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHabitStatisticFromDetailReply_AllStatistic) ProtoMessage() {}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHabitStatisticFromDetailReply_AllStatistic.ProtoReflect.Descriptor instead.
func (*UserHabitStatisticFromDetailReply_AllStatistic) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{3, 1}
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetPer() int32 {
	if x != nil {
		return x.Per
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetDoneCount() int32 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetAllCount() int32 {
	if x != nil {
		return x.AllCount
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetPersistLongDays() int32 {
	if x != nil {
		return x.PersistLongDays
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetAvgData() string {
	if x != nil {
		return x.AvgData
	}
	return ""
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetMaxData() string {
	if x != nil {
		return x.MaxData
	}
	return ""
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetMinData() string {
	if x != nil {
		return x.MinData
	}
	return ""
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetPersistStartDate() string {
	if x != nil {
		return x.PersistStartDate
	}
	return ""
}

func (x *UserHabitStatisticFromDetailReply_AllStatistic) GetPersistEndDate() string {
	if x != nil {
		return x.PersistEndDate
	}
	return ""
}

type UserHabitStatisticFromDetailReply_WeekStatistic struct {
	state          protoimpl.MessageState                      `protogen:"open.v1"`
	DoneCount      int32                                       `protobuf:"varint,1,opt,name=done_count,json=doneCount,proto3" json:"done_count,omitempty"`
	AllCount       int32                                       `protobuf:"varint,2,opt,name=all_count,json=allCount,proto3" json:"all_count,omitempty"`
	Stages         []int32                                     `protobuf:"varint,3,rep,packed,name=stages,proto3" json:"stages,omitempty"`
	Detail         []*UserHabitStatisticFromDetailReply_Detail `protobuf:"bytes,4,rep,name=detail,proto3" json:"detail,omitempty"`
	ChartLeftCount []int32                                     `protobuf:"varint,5,rep,packed,name=chartLeftCount,proto3" json:"chartLeftCount,omitempty"`
	AvgData        string                                      `protobuf:"bytes,6,opt,name=avg_data,json=avgData,proto3" json:"avg_data,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) Reset() {
	*x = UserHabitStatisticFromDetailReply_WeekStatistic{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHabitStatisticFromDetailReply_WeekStatistic) ProtoMessage() {}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHabitStatisticFromDetailReply_WeekStatistic.ProtoReflect.Descriptor instead.
func (*UserHabitStatisticFromDetailReply_WeekStatistic) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{3, 2}
}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) GetDoneCount() int32 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) GetAllCount() int32 {
	if x != nil {
		return x.AllCount
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) GetStages() []int32 {
	if x != nil {
		return x.Stages
	}
	return nil
}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) GetDetail() []*UserHabitStatisticFromDetailReply_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) GetChartLeftCount() []int32 {
	if x != nil {
		return x.ChartLeftCount
	}
	return nil
}

func (x *UserHabitStatisticFromDetailReply_WeekStatistic) GetAvgData() string {
	if x != nil {
		return x.AvgData
	}
	return ""
}

type UserHabitStatisticFromDetailReply_MonthStatistic struct {
	state          protoimpl.MessageState                      `protogen:"open.v1"`
	DoneCount      int32                                       `protobuf:"varint,1,opt,name=done_count,json=doneCount,proto3" json:"done_count,omitempty"`
	AllCount       int32                                       `protobuf:"varint,2,opt,name=all_count,json=allCount,proto3" json:"all_count,omitempty"`
	Stages         []int32                                     `protobuf:"varint,3,rep,packed,name=stages,proto3" json:"stages,omitempty"`
	Detail         []*UserHabitStatisticFromDetailReply_Detail `protobuf:"bytes,4,rep,name=detail,proto3" json:"detail,omitempty"`
	ChartLeftCount []int32                                     `protobuf:"varint,5,rep,packed,name=chartLeftCount,proto3" json:"chartLeftCount,omitempty"`
	AvgData        string                                      `protobuf:"bytes,6,opt,name=avg_data,json=avgData,proto3" json:"avg_data,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) Reset() {
	*x = UserHabitStatisticFromDetailReply_MonthStatistic{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHabitStatisticFromDetailReply_MonthStatistic) ProtoMessage() {}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHabitStatisticFromDetailReply_MonthStatistic.ProtoReflect.Descriptor instead.
func (*UserHabitStatisticFromDetailReply_MonthStatistic) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{3, 3}
}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) GetDoneCount() int32 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) GetAllCount() int32 {
	if x != nil {
		return x.AllCount
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) GetStages() []int32 {
	if x != nil {
		return x.Stages
	}
	return nil
}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) GetDetail() []*UserHabitStatisticFromDetailReply_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) GetChartLeftCount() []int32 {
	if x != nil {
		return x.ChartLeftCount
	}
	return nil
}

func (x *UserHabitStatisticFromDetailReply_MonthStatistic) GetAvgData() string {
	if x != nil {
		return x.AvgData
	}
	return ""
}

type UserHabitStatisticFromDetailReply_Detail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Day           int32                  `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
	DoneCount     int32                  `protobuf:"varint,2,opt,name=done_count,json=doneCount,proto3" json:"done_count,omitempty"`
	Stages        []int32                `protobuf:"varint,3,rep,packed,name=stages,proto3" json:"stages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserHabitStatisticFromDetailReply_Detail) Reset() {
	*x = UserHabitStatisticFromDetailReply_Detail{}
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserHabitStatisticFromDetailReply_Detail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHabitStatisticFromDetailReply_Detail) ProtoMessage() {}

func (x *UserHabitStatisticFromDetailReply_Detail) ProtoReflect() protoreflect.Message {
	mi := &file_user_statistic_v1_user_statistic_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHabitStatisticFromDetailReply_Detail.ProtoReflect.Descriptor instead.
func (*UserHabitStatisticFromDetailReply_Detail) Descriptor() ([]byte, []int) {
	return file_user_statistic_v1_user_statistic_proto_rawDescGZIP(), []int{3, 4}
}

func (x *UserHabitStatisticFromDetailReply_Detail) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_Detail) GetDoneCount() int32 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *UserHabitStatisticFromDetailReply_Detail) GetStages() []int32 {
	if x != nil {
		return x.Stages
	}
	return nil
}

var File_user_statistic_v1_user_statistic_proto protoreflect.FileDescriptor

const file_user_statistic_v1_user_statistic_proto_rawDesc = "" +
	"\n" +
	"&user_statistic/v1/user_statistic.proto\x12\x15api.user_statistic_v1\"B\n" +
	"\x1dTodayStatisticFromHomeRequest\x12!\n" +
	"\fcurrent_date\x18\x01 \x01(\tR\vcurrentDate\"\x80\x04\n" +
	"\x1bTodayStatisticFromHomeReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12K\n" +
	"\x04data\x18\x03 \x01(\v27.api.user_statistic_v1.TodayStatisticFromHomeReply.DataR\x04data\x1a\x90\x02\n" +
	"\x04Data\x12_\n" +
	"\vsmall_habit\x18\x01 \x01(\v2>.api.user_statistic_v1.TodayStatisticFromHomeReply.HabitDetailR\n" +
	"smallHabit\x12a\n" +
	"\fnormal_habit\x18\x02 \x01(\v2>.api.user_statistic_v1.TodayStatisticFromHomeReply.HabitDetailR\vnormalHabit\x12#\n" +
	"\rover_complete\x18\x03 \x01(\tR\foverComplete\x12\x1f\n" +
	"\vun_complete\x18\x04 \x01(\tR\n" +
	"unComplete\x1a[\n" +
	"\vHabitDetail\x12\x10\n" +
	"\x03per\x18\x01 \x01(\x05R\x03per\x12\x1d\n" +
	"\n" +
	"done_count\x18\x02 \x01(\x05R\tdoneCount\x12\x1b\n" +
	"\tall_count\x18\x03 \x01(\x05R\ballCount\"\x8b\x01\n" +
	"#UserHabitStatisticFromDetailRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12!\n" +
	"\fcurrent_date\x18\x02 \x01(\tR\vcurrentDate\x12\x1d\n" +
	"\n" +
	"label_type\x18\x03 \x01(\tR\tlabelType\"\xfe\n" +
	"\n" +
	"!UserHabitStatisticFromDetailReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12Q\n" +
	"\x04data\x18\x03 \x01(\v2=.api.user_statistic_v1.UserHabitStatisticFromDetailReply.DataR\x04data\x1a\xd3\x02\n" +
	"\x04Data\x12j\n" +
	"\rall_statistic\x18\x01 \x01(\v2E.api.user_statistic_v1.UserHabitStatisticFromDetailReply.AllStatisticR\fallStatistic\x12m\n" +
	"\x0eweek_statistic\x18\x02 \x01(\v2F.api.user_statistic_v1.UserHabitStatisticFromDetailReply.WeekStatisticR\rweekStatistic\x12p\n" +
	"\x0fmonth_statistic\x18\x03 \x01(\v2G.api.user_statistic_v1.UserHabitStatisticFromDetailReply.MonthStatisticR\x0emonthStatistic\x1a\xb1\x02\n" +
	"\fAllStatistic\x12\x10\n" +
	"\x03per\x18\x01 \x01(\x05R\x03per\x12\x1d\n" +
	"\n" +
	"done_count\x18\x02 \x01(\x05R\tdoneCount\x12\x1b\n" +
	"\tall_count\x18\x03 \x01(\x05R\ballCount\x12*\n" +
	"\x11persist_long_days\x18\x04 \x01(\x05R\x0fpersistLongDays\x12\x19\n" +
	"\bavg_data\x18\x06 \x01(\tR\aavgData\x12\x19\n" +
	"\bmax_data\x18\a \x01(\tR\amaxData\x12\x19\n" +
	"\bmin_data\x18\b \x01(\tR\aminData\x12,\n" +
	"\x12persist_start_date\x18\x05 \x01(\tR\x10persistStartDate\x12(\n" +
	"\x10persist_end_date\x18\t \x01(\tR\x0epersistEndDate\x1a\xff\x01\n" +
	"\rWeekStatistic\x12\x1d\n" +
	"\n" +
	"done_count\x18\x01 \x01(\x05R\tdoneCount\x12\x1b\n" +
	"\tall_count\x18\x02 \x01(\x05R\ballCount\x12\x16\n" +
	"\x06stages\x18\x03 \x03(\x05R\x06stages\x12W\n" +
	"\x06detail\x18\x04 \x03(\v2?.api.user_statistic_v1.UserHabitStatisticFromDetailReply.DetailR\x06detail\x12&\n" +
	"\x0echartLeftCount\x18\x05 \x03(\x05R\x0echartLeftCount\x12\x19\n" +
	"\bavg_data\x18\x06 \x01(\tR\aavgData\x1a\x80\x02\n" +
	"\x0eMonthStatistic\x12\x1d\n" +
	"\n" +
	"done_count\x18\x01 \x01(\x05R\tdoneCount\x12\x1b\n" +
	"\tall_count\x18\x02 \x01(\x05R\ballCount\x12\x16\n" +
	"\x06stages\x18\x03 \x03(\x05R\x06stages\x12W\n" +
	"\x06detail\x18\x04 \x03(\v2?.api.user_statistic_v1.UserHabitStatisticFromDetailReply.DetailR\x06detail\x12&\n" +
	"\x0echartLeftCount\x18\x05 \x03(\x05R\x0echartLeftCount\x12\x19\n" +
	"\bavg_data\x18\x06 \x01(\tR\aavgData\x1aQ\n" +
	"\x06Detail\x12\x10\n" +
	"\x03day\x18\x01 \x01(\x05R\x03day\x12\x1d\n" +
	"\n" +
	"done_count\x18\x02 \x01(\x05R\tdoneCount\x12\x16\n" +
	"\x06stages\x18\x03 \x03(\x05R\x06stagesB7Z5github.com/wlnil/life-log-be/api/user_statistic/v1;v1b\x06proto3"

var (
	file_user_statistic_v1_user_statistic_proto_rawDescOnce sync.Once
	file_user_statistic_v1_user_statistic_proto_rawDescData []byte
)

func file_user_statistic_v1_user_statistic_proto_rawDescGZIP() []byte {
	file_user_statistic_v1_user_statistic_proto_rawDescOnce.Do(func() {
		file_user_statistic_v1_user_statistic_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_statistic_v1_user_statistic_proto_rawDesc), len(file_user_statistic_v1_user_statistic_proto_rawDesc)))
	})
	return file_user_statistic_v1_user_statistic_proto_rawDescData
}

var file_user_statistic_v1_user_statistic_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_user_statistic_v1_user_statistic_proto_goTypes = []any{
	(*TodayStatisticFromHomeRequest)(nil),                    // 0: api.user_statistic_v1.TodayStatisticFromHomeRequest
	(*TodayStatisticFromHomeReply)(nil),                      // 1: api.user_statistic_v1.TodayStatisticFromHomeReply
	(*UserHabitStatisticFromDetailRequest)(nil),              // 2: api.user_statistic_v1.UserHabitStatisticFromDetailRequest
	(*UserHabitStatisticFromDetailReply)(nil),                // 3: api.user_statistic_v1.UserHabitStatisticFromDetailReply
	(*TodayStatisticFromHomeReply_Data)(nil),                 // 4: api.user_statistic_v1.TodayStatisticFromHomeReply.Data
	(*TodayStatisticFromHomeReply_HabitDetail)(nil),          // 5: api.user_statistic_v1.TodayStatisticFromHomeReply.HabitDetail
	(*UserHabitStatisticFromDetailReply_Data)(nil),           // 6: api.user_statistic_v1.UserHabitStatisticFromDetailReply.Data
	(*UserHabitStatisticFromDetailReply_AllStatistic)(nil),   // 7: api.user_statistic_v1.UserHabitStatisticFromDetailReply.AllStatistic
	(*UserHabitStatisticFromDetailReply_WeekStatistic)(nil),  // 8: api.user_statistic_v1.UserHabitStatisticFromDetailReply.WeekStatistic
	(*UserHabitStatisticFromDetailReply_MonthStatistic)(nil), // 9: api.user_statistic_v1.UserHabitStatisticFromDetailReply.MonthStatistic
	(*UserHabitStatisticFromDetailReply_Detail)(nil),         // 10: api.user_statistic_v1.UserHabitStatisticFromDetailReply.Detail
}
var file_user_statistic_v1_user_statistic_proto_depIdxs = []int32{
	4,  // 0: api.user_statistic_v1.TodayStatisticFromHomeReply.data:type_name -> api.user_statistic_v1.TodayStatisticFromHomeReply.Data
	6,  // 1: api.user_statistic_v1.UserHabitStatisticFromDetailReply.data:type_name -> api.user_statistic_v1.UserHabitStatisticFromDetailReply.Data
	5,  // 2: api.user_statistic_v1.TodayStatisticFromHomeReply.Data.small_habit:type_name -> api.user_statistic_v1.TodayStatisticFromHomeReply.HabitDetail
	5,  // 3: api.user_statistic_v1.TodayStatisticFromHomeReply.Data.normal_habit:type_name -> api.user_statistic_v1.TodayStatisticFromHomeReply.HabitDetail
	7,  // 4: api.user_statistic_v1.UserHabitStatisticFromDetailReply.Data.all_statistic:type_name -> api.user_statistic_v1.UserHabitStatisticFromDetailReply.AllStatistic
	8,  // 5: api.user_statistic_v1.UserHabitStatisticFromDetailReply.Data.week_statistic:type_name -> api.user_statistic_v1.UserHabitStatisticFromDetailReply.WeekStatistic
	9,  // 6: api.user_statistic_v1.UserHabitStatisticFromDetailReply.Data.month_statistic:type_name -> api.user_statistic_v1.UserHabitStatisticFromDetailReply.MonthStatistic
	10, // 7: api.user_statistic_v1.UserHabitStatisticFromDetailReply.WeekStatistic.detail:type_name -> api.user_statistic_v1.UserHabitStatisticFromDetailReply.Detail
	10, // 8: api.user_statistic_v1.UserHabitStatisticFromDetailReply.MonthStatistic.detail:type_name -> api.user_statistic_v1.UserHabitStatisticFromDetailReply.Detail
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_user_statistic_v1_user_statistic_proto_init() }
func file_user_statistic_v1_user_statistic_proto_init() {
	if File_user_statistic_v1_user_statistic_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_statistic_v1_user_statistic_proto_rawDesc), len(file_user_statistic_v1_user_statistic_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_statistic_v1_user_statistic_proto_goTypes,
		DependencyIndexes: file_user_statistic_v1_user_statistic_proto_depIdxs,
		MessageInfos:      file_user_statistic_v1_user_statistic_proto_msgTypes,
	}.Build()
	File_user_statistic_v1_user_statistic_proto = out.File
	file_user_statistic_v1_user_statistic_proto_goTypes = nil
	file_user_statistic_v1_user_statistic_proto_depIdxs = nil
}
