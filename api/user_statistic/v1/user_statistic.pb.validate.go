// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: user_statistic/v1/user_statistic.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TodayStatisticFromHomeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TodayStatisticFromHomeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TodayStatisticFromHomeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TodayStatisticFromHomeRequestMultiError, or nil if none found.
func (m *TodayStatisticFromHomeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TodayStatisticFromHomeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return TodayStatisticFromHomeRequestMultiError(errors)
	}

	return nil
}

// TodayStatisticFromHomeRequestMultiError is an error wrapping multiple
// validation errors returned by TodayStatisticFromHomeRequest.ValidateAll()
// if the designated constraints aren't met.
type TodayStatisticFromHomeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TodayStatisticFromHomeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TodayStatisticFromHomeRequestMultiError) AllErrors() []error { return m }

// TodayStatisticFromHomeRequestValidationError is the validation error
// returned by TodayStatisticFromHomeRequest.Validate if the designated
// constraints aren't met.
type TodayStatisticFromHomeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TodayStatisticFromHomeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TodayStatisticFromHomeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TodayStatisticFromHomeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TodayStatisticFromHomeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TodayStatisticFromHomeRequestValidationError) ErrorName() string {
	return "TodayStatisticFromHomeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TodayStatisticFromHomeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTodayStatisticFromHomeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TodayStatisticFromHomeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TodayStatisticFromHomeRequestValidationError{}

// Validate checks the field values on TodayStatisticFromHomeReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TodayStatisticFromHomeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TodayStatisticFromHomeReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TodayStatisticFromHomeReplyMultiError, or nil if none found.
func (m *TodayStatisticFromHomeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *TodayStatisticFromHomeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TodayStatisticFromHomeReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TodayStatisticFromHomeReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TodayStatisticFromHomeReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TodayStatisticFromHomeReplyMultiError(errors)
	}

	return nil
}

// TodayStatisticFromHomeReplyMultiError is an error wrapping multiple
// validation errors returned by TodayStatisticFromHomeReply.ValidateAll() if
// the designated constraints aren't met.
type TodayStatisticFromHomeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TodayStatisticFromHomeReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TodayStatisticFromHomeReplyMultiError) AllErrors() []error { return m }

// TodayStatisticFromHomeReplyValidationError is the validation error returned
// by TodayStatisticFromHomeReply.Validate if the designated constraints
// aren't met.
type TodayStatisticFromHomeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TodayStatisticFromHomeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TodayStatisticFromHomeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TodayStatisticFromHomeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TodayStatisticFromHomeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TodayStatisticFromHomeReplyValidationError) ErrorName() string {
	return "TodayStatisticFromHomeReplyValidationError"
}

// Error satisfies the builtin error interface
func (e TodayStatisticFromHomeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTodayStatisticFromHomeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TodayStatisticFromHomeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TodayStatisticFromHomeReplyValidationError{}

// Validate checks the field values on UserHabitStatisticFromDetailRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UserHabitStatisticFromDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserHabitStatisticFromDetailRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UserHabitStatisticFromDetailRequestMultiError, or nil if none found.
func (m *UserHabitStatisticFromDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UserHabitStatisticFromDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for CurrentDate

	// no validation rules for LabelType

	if len(errors) > 0 {
		return UserHabitStatisticFromDetailRequestMultiError(errors)
	}

	return nil
}

// UserHabitStatisticFromDetailRequestMultiError is an error wrapping multiple
// validation errors returned by
// UserHabitStatisticFromDetailRequest.ValidateAll() if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserHabitStatisticFromDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserHabitStatisticFromDetailRequestMultiError) AllErrors() []error { return m }

// UserHabitStatisticFromDetailRequestValidationError is the validation error
// returned by UserHabitStatisticFromDetailRequest.Validate if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserHabitStatisticFromDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserHabitStatisticFromDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserHabitStatisticFromDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserHabitStatisticFromDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserHabitStatisticFromDetailRequestValidationError) ErrorName() string {
	return "UserHabitStatisticFromDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UserHabitStatisticFromDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserHabitStatisticFromDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserHabitStatisticFromDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserHabitStatisticFromDetailRequestValidationError{}

// Validate checks the field values on UserHabitStatisticFromDetailReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UserHabitStatisticFromDetailReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserHabitStatisticFromDetailReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UserHabitStatisticFromDetailReplyMultiError, or nil if none found.
func (m *UserHabitStatisticFromDetailReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UserHabitStatisticFromDetailReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserHabitStatisticFromDetailReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserHabitStatisticFromDetailReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserHabitStatisticFromDetailReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserHabitStatisticFromDetailReplyMultiError(errors)
	}

	return nil
}

// UserHabitStatisticFromDetailReplyMultiError is an error wrapping multiple
// validation errors returned by
// UserHabitStatisticFromDetailReply.ValidateAll() if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserHabitStatisticFromDetailReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserHabitStatisticFromDetailReplyMultiError) AllErrors() []error { return m }

// UserHabitStatisticFromDetailReplyValidationError is the validation error
// returned by UserHabitStatisticFromDetailReply.Validate if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserHabitStatisticFromDetailReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserHabitStatisticFromDetailReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserHabitStatisticFromDetailReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserHabitStatisticFromDetailReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserHabitStatisticFromDetailReplyValidationError) ErrorName() string {
	return "UserHabitStatisticFromDetailReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UserHabitStatisticFromDetailReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserHabitStatisticFromDetailReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserHabitStatisticFromDetailReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserHabitStatisticFromDetailReplyValidationError{}

// Validate checks the field values on TodayStatisticFromHomeReply_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TodayStatisticFromHomeReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TodayStatisticFromHomeReply_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TodayStatisticFromHomeReply_DataMultiError, or nil if none found.
func (m *TodayStatisticFromHomeReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *TodayStatisticFromHomeReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSmallHabit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TodayStatisticFromHomeReply_DataValidationError{
					field:  "SmallHabit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TodayStatisticFromHomeReply_DataValidationError{
					field:  "SmallHabit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmallHabit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TodayStatisticFromHomeReply_DataValidationError{
				field:  "SmallHabit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNormalHabit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TodayStatisticFromHomeReply_DataValidationError{
					field:  "NormalHabit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TodayStatisticFromHomeReply_DataValidationError{
					field:  "NormalHabit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNormalHabit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TodayStatisticFromHomeReply_DataValidationError{
				field:  "NormalHabit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OverComplete

	// no validation rules for UnComplete

	if len(errors) > 0 {
		return TodayStatisticFromHomeReply_DataMultiError(errors)
	}

	return nil
}

// TodayStatisticFromHomeReply_DataMultiError is an error wrapping multiple
// validation errors returned by
// TodayStatisticFromHomeReply_Data.ValidateAll() if the designated
// constraints aren't met.
type TodayStatisticFromHomeReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TodayStatisticFromHomeReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TodayStatisticFromHomeReply_DataMultiError) AllErrors() []error { return m }

// TodayStatisticFromHomeReply_DataValidationError is the validation error
// returned by TodayStatisticFromHomeReply_Data.Validate if the designated
// constraints aren't met.
type TodayStatisticFromHomeReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TodayStatisticFromHomeReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TodayStatisticFromHomeReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TodayStatisticFromHomeReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TodayStatisticFromHomeReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TodayStatisticFromHomeReply_DataValidationError) ErrorName() string {
	return "TodayStatisticFromHomeReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e TodayStatisticFromHomeReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTodayStatisticFromHomeReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TodayStatisticFromHomeReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TodayStatisticFromHomeReply_DataValidationError{}

// Validate checks the field values on TodayStatisticFromHomeReply_HabitDetail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *TodayStatisticFromHomeReply_HabitDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TodayStatisticFromHomeReply_HabitDetail with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// TodayStatisticFromHomeReply_HabitDetailMultiError, or nil if none found.
func (m *TodayStatisticFromHomeReply_HabitDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *TodayStatisticFromHomeReply_HabitDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Per

	// no validation rules for DoneCount

	// no validation rules for AllCount

	if len(errors) > 0 {
		return TodayStatisticFromHomeReply_HabitDetailMultiError(errors)
	}

	return nil
}

// TodayStatisticFromHomeReply_HabitDetailMultiError is an error wrapping
// multiple validation errors returned by
// TodayStatisticFromHomeReply_HabitDetail.ValidateAll() if the designated
// constraints aren't met.
type TodayStatisticFromHomeReply_HabitDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TodayStatisticFromHomeReply_HabitDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TodayStatisticFromHomeReply_HabitDetailMultiError) AllErrors() []error { return m }

// TodayStatisticFromHomeReply_HabitDetailValidationError is the validation
// error returned by TodayStatisticFromHomeReply_HabitDetail.Validate if the
// designated constraints aren't met.
type TodayStatisticFromHomeReply_HabitDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TodayStatisticFromHomeReply_HabitDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TodayStatisticFromHomeReply_HabitDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TodayStatisticFromHomeReply_HabitDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TodayStatisticFromHomeReply_HabitDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TodayStatisticFromHomeReply_HabitDetailValidationError) ErrorName() string {
	return "TodayStatisticFromHomeReply_HabitDetailValidationError"
}

// Error satisfies the builtin error interface
func (e TodayStatisticFromHomeReply_HabitDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTodayStatisticFromHomeReply_HabitDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TodayStatisticFromHomeReply_HabitDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TodayStatisticFromHomeReply_HabitDetailValidationError{}

// Validate checks the field values on UserHabitStatisticFromDetailReply_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UserHabitStatisticFromDetailReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UserHabitStatisticFromDetailReply_Data with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UserHabitStatisticFromDetailReply_DataMultiError, or nil if none found.
func (m *UserHabitStatisticFromDetailReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *UserHabitStatisticFromDetailReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAllStatistic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserHabitStatisticFromDetailReply_DataValidationError{
					field:  "AllStatistic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserHabitStatisticFromDetailReply_DataValidationError{
					field:  "AllStatistic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAllStatistic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserHabitStatisticFromDetailReply_DataValidationError{
				field:  "AllStatistic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWeekStatistic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserHabitStatisticFromDetailReply_DataValidationError{
					field:  "WeekStatistic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserHabitStatisticFromDetailReply_DataValidationError{
					field:  "WeekStatistic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWeekStatistic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserHabitStatisticFromDetailReply_DataValidationError{
				field:  "WeekStatistic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMonthStatistic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UserHabitStatisticFromDetailReply_DataValidationError{
					field:  "MonthStatistic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UserHabitStatisticFromDetailReply_DataValidationError{
					field:  "MonthStatistic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthStatistic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UserHabitStatisticFromDetailReply_DataValidationError{
				field:  "MonthStatistic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UserHabitStatisticFromDetailReply_DataMultiError(errors)
	}

	return nil
}

// UserHabitStatisticFromDetailReply_DataMultiError is an error wrapping
// multiple validation errors returned by
// UserHabitStatisticFromDetailReply_Data.ValidateAll() if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserHabitStatisticFromDetailReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserHabitStatisticFromDetailReply_DataMultiError) AllErrors() []error { return m }

// UserHabitStatisticFromDetailReply_DataValidationError is the validation
// error returned by UserHabitStatisticFromDetailReply_Data.Validate if the
// designated constraints aren't met.
type UserHabitStatisticFromDetailReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserHabitStatisticFromDetailReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserHabitStatisticFromDetailReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserHabitStatisticFromDetailReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserHabitStatisticFromDetailReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserHabitStatisticFromDetailReply_DataValidationError) ErrorName() string {
	return "UserHabitStatisticFromDetailReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e UserHabitStatisticFromDetailReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserHabitStatisticFromDetailReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserHabitStatisticFromDetailReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserHabitStatisticFromDetailReply_DataValidationError{}

// Validate checks the field values on
// UserHabitStatisticFromDetailReply_AllStatistic with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserHabitStatisticFromDetailReply_AllStatistic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UserHabitStatisticFromDetailReply_AllStatistic with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// UserHabitStatisticFromDetailReply_AllStatisticMultiError, or nil if none found.
func (m *UserHabitStatisticFromDetailReply_AllStatistic) ValidateAll() error {
	return m.validate(true)
}

func (m *UserHabitStatisticFromDetailReply_AllStatistic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Per

	// no validation rules for DoneCount

	// no validation rules for AllCount

	// no validation rules for PersistLongDays

	// no validation rules for AvgData

	// no validation rules for MaxData

	// no validation rules for MinData

	// no validation rules for PersistStartDate

	// no validation rules for PersistEndDate

	if len(errors) > 0 {
		return UserHabitStatisticFromDetailReply_AllStatisticMultiError(errors)
	}

	return nil
}

// UserHabitStatisticFromDetailReply_AllStatisticMultiError is an error
// wrapping multiple validation errors returned by
// UserHabitStatisticFromDetailReply_AllStatistic.ValidateAll() if the
// designated constraints aren't met.
type UserHabitStatisticFromDetailReply_AllStatisticMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserHabitStatisticFromDetailReply_AllStatisticMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserHabitStatisticFromDetailReply_AllStatisticMultiError) AllErrors() []error { return m }

// UserHabitStatisticFromDetailReply_AllStatisticValidationError is the
// validation error returned by
// UserHabitStatisticFromDetailReply_AllStatistic.Validate if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailReply_AllStatisticValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserHabitStatisticFromDetailReply_AllStatisticValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserHabitStatisticFromDetailReply_AllStatisticValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UserHabitStatisticFromDetailReply_AllStatisticValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserHabitStatisticFromDetailReply_AllStatisticValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserHabitStatisticFromDetailReply_AllStatisticValidationError) ErrorName() string {
	return "UserHabitStatisticFromDetailReply_AllStatisticValidationError"
}

// Error satisfies the builtin error interface
func (e UserHabitStatisticFromDetailReply_AllStatisticValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserHabitStatisticFromDetailReply_AllStatistic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserHabitStatisticFromDetailReply_AllStatisticValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserHabitStatisticFromDetailReply_AllStatisticValidationError{}

// Validate checks the field values on
// UserHabitStatisticFromDetailReply_WeekStatistic with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserHabitStatisticFromDetailReply_WeekStatistic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UserHabitStatisticFromDetailReply_WeekStatistic with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// UserHabitStatisticFromDetailReply_WeekStatisticMultiError, or nil if none found.
func (m *UserHabitStatisticFromDetailReply_WeekStatistic) ValidateAll() error {
	return m.validate(true)
}

func (m *UserHabitStatisticFromDetailReply_WeekStatistic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DoneCount

	// no validation rules for AllCount

	for idx, item := range m.GetDetail() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserHabitStatisticFromDetailReply_WeekStatisticValidationError{
						field:  fmt.Sprintf("Detail[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserHabitStatisticFromDetailReply_WeekStatisticValidationError{
						field:  fmt.Sprintf("Detail[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserHabitStatisticFromDetailReply_WeekStatisticValidationError{
					field:  fmt.Sprintf("Detail[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AvgData

	if len(errors) > 0 {
		return UserHabitStatisticFromDetailReply_WeekStatisticMultiError(errors)
	}

	return nil
}

// UserHabitStatisticFromDetailReply_WeekStatisticMultiError is an error
// wrapping multiple validation errors returned by
// UserHabitStatisticFromDetailReply_WeekStatistic.ValidateAll() if the
// designated constraints aren't met.
type UserHabitStatisticFromDetailReply_WeekStatisticMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserHabitStatisticFromDetailReply_WeekStatisticMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserHabitStatisticFromDetailReply_WeekStatisticMultiError) AllErrors() []error { return m }

// UserHabitStatisticFromDetailReply_WeekStatisticValidationError is the
// validation error returned by
// UserHabitStatisticFromDetailReply_WeekStatistic.Validate if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailReply_WeekStatisticValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserHabitStatisticFromDetailReply_WeekStatisticValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UserHabitStatisticFromDetailReply_WeekStatisticValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UserHabitStatisticFromDetailReply_WeekStatisticValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserHabitStatisticFromDetailReply_WeekStatisticValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserHabitStatisticFromDetailReply_WeekStatisticValidationError) ErrorName() string {
	return "UserHabitStatisticFromDetailReply_WeekStatisticValidationError"
}

// Error satisfies the builtin error interface
func (e UserHabitStatisticFromDetailReply_WeekStatisticValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserHabitStatisticFromDetailReply_WeekStatistic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserHabitStatisticFromDetailReply_WeekStatisticValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserHabitStatisticFromDetailReply_WeekStatisticValidationError{}

// Validate checks the field values on
// UserHabitStatisticFromDetailReply_MonthStatistic with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserHabitStatisticFromDetailReply_MonthStatistic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UserHabitStatisticFromDetailReply_MonthStatistic with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// UserHabitStatisticFromDetailReply_MonthStatisticMultiError, or nil if none found.
func (m *UserHabitStatisticFromDetailReply_MonthStatistic) ValidateAll() error {
	return m.validate(true)
}

func (m *UserHabitStatisticFromDetailReply_MonthStatistic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DoneCount

	// no validation rules for AllCount

	for idx, item := range m.GetDetail() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UserHabitStatisticFromDetailReply_MonthStatisticValidationError{
						field:  fmt.Sprintf("Detail[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UserHabitStatisticFromDetailReply_MonthStatisticValidationError{
						field:  fmt.Sprintf("Detail[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UserHabitStatisticFromDetailReply_MonthStatisticValidationError{
					field:  fmt.Sprintf("Detail[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AvgData

	if len(errors) > 0 {
		return UserHabitStatisticFromDetailReply_MonthStatisticMultiError(errors)
	}

	return nil
}

// UserHabitStatisticFromDetailReply_MonthStatisticMultiError is an error
// wrapping multiple validation errors returned by
// UserHabitStatisticFromDetailReply_MonthStatistic.ValidateAll() if the
// designated constraints aren't met.
type UserHabitStatisticFromDetailReply_MonthStatisticMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserHabitStatisticFromDetailReply_MonthStatisticMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserHabitStatisticFromDetailReply_MonthStatisticMultiError) AllErrors() []error { return m }

// UserHabitStatisticFromDetailReply_MonthStatisticValidationError is the
// validation error returned by
// UserHabitStatisticFromDetailReply_MonthStatistic.Validate if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailReply_MonthStatisticValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserHabitStatisticFromDetailReply_MonthStatisticValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e UserHabitStatisticFromDetailReply_MonthStatisticValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e UserHabitStatisticFromDetailReply_MonthStatisticValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e UserHabitStatisticFromDetailReply_MonthStatisticValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserHabitStatisticFromDetailReply_MonthStatisticValidationError) ErrorName() string {
	return "UserHabitStatisticFromDetailReply_MonthStatisticValidationError"
}

// Error satisfies the builtin error interface
func (e UserHabitStatisticFromDetailReply_MonthStatisticValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserHabitStatisticFromDetailReply_MonthStatistic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserHabitStatisticFromDetailReply_MonthStatisticValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserHabitStatisticFromDetailReply_MonthStatisticValidationError{}

// Validate checks the field values on UserHabitStatisticFromDetailReply_Detail
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UserHabitStatisticFromDetailReply_Detail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UserHabitStatisticFromDetailReply_Detail with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UserHabitStatisticFromDetailReply_DetailMultiError, or nil if none found.
func (m *UserHabitStatisticFromDetailReply_Detail) ValidateAll() error {
	return m.validate(true)
}

func (m *UserHabitStatisticFromDetailReply_Detail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Day

	// no validation rules for DoneCount

	if len(errors) > 0 {
		return UserHabitStatisticFromDetailReply_DetailMultiError(errors)
	}

	return nil
}

// UserHabitStatisticFromDetailReply_DetailMultiError is an error wrapping
// multiple validation errors returned by
// UserHabitStatisticFromDetailReply_Detail.ValidateAll() if the designated
// constraints aren't met.
type UserHabitStatisticFromDetailReply_DetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserHabitStatisticFromDetailReply_DetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserHabitStatisticFromDetailReply_DetailMultiError) AllErrors() []error { return m }

// UserHabitStatisticFromDetailReply_DetailValidationError is the validation
// error returned by UserHabitStatisticFromDetailReply_Detail.Validate if the
// designated constraints aren't met.
type UserHabitStatisticFromDetailReply_DetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserHabitStatisticFromDetailReply_DetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserHabitStatisticFromDetailReply_DetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserHabitStatisticFromDetailReply_DetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserHabitStatisticFromDetailReply_DetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserHabitStatisticFromDetailReply_DetailValidationError) ErrorName() string {
	return "UserHabitStatisticFromDetailReply_DetailValidationError"
}

// Error satisfies the builtin error interface
func (e UserHabitStatisticFromDetailReply_DetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserHabitStatisticFromDetailReply_Detail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserHabitStatisticFromDetailReply_DetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserHabitStatisticFromDetailReply_DetailValidationError{}
