syntax = "proto3";

package api.user_statistic_v1;

option go_package = "github.com/wlnil/life-log-be/api/user_statistic/v1;v1";

message TodayStatisticFromHomeRequest {
	string current_date = 1; // eg: 2023-11-26T07:00:00+08:00
}
message TodayStatisticFromHomeReply {
	message Data {
		HabitDetail small_habit = 1;
		HabitDetail normal_habit = 2;
		string over_complete = 3;
		string un_complete = 4;
	}

	message HabitDetail {
		int32 per = 1;
		int32 done_count = 2;
		int32 all_count = 3;
	}

	int32 code = 1;
	string msg = 2;
	Data data = 3;
}

message UserHabitStatisticFromDetailRequest {
	int32 user_habit_id = 1;
	string current_date = 2;
	string label_type = 3;
}
message UserHabitStatisticFromDetailReply {
	message Data {
		AllStatistic all_statistic = 1;
		WeekStatistic week_statistic = 2;
		MonthStatistic month_statistic = 3;
	}

	message AllStatistic {
		int32 per = 1;
		int32 done_count = 2;
		int32 all_count = 3;
		int32 persist_long_days = 4;
		string avg_data = 6;
		string max_data = 7;
		string min_data = 8;
		string persist_start_date = 5;
		string persist_end_date = 9;
	}

	message WeekStatistic {
		int32 done_count = 1;
		int32 all_count = 2;
		repeated int32 stages = 3;
		repeated Detail detail = 4;
		repeated int32 chartLeftCount = 5;
		string avg_data = 6;
	}

	message MonthStatistic {
		int32 done_count = 1;
		int32 all_count = 2;
		repeated int32 stages = 3;
		repeated Detail detail = 4;
		repeated int32 chartLeftCount = 5;
		string avg_data = 6;
	}

	message Detail {
		int32 day = 1;
		int32 done_count = 2;
		repeated int32 stages = 3;
	}

	int32 code = 1;
	string msg = 2;
	Data data = 3;
}
