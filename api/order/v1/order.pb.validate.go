// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: order/v1/order.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateUserOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserOrderRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserOrderRequestMultiError, or nil if none found.
func (m *CreateUserOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PaymentMethod

	// no validation rules for PurchaseType

	// no validation rules for Category

	if len(errors) > 0 {
		return CreateUserOrderRequestMultiError(errors)
	}

	return nil
}

// CreateUserOrderRequestMultiError is an error wrapping multiple validation
// errors returned by CreateUserOrderRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateUserOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserOrderRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserOrderRequestMultiError) AllErrors() []error { return m }

// CreateUserOrderRequestValidationError is the validation error returned by
// CreateUserOrderRequest.Validate if the designated constraints aren't met.
type CreateUserOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserOrderRequestValidationError) ErrorName() string {
	return "CreateUserOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserOrderRequestValidationError{}

// Validate checks the field values on CreateUserOrderReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserOrderReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserOrderReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserOrderReplyMultiError, or nil if none found.
func (m *CreateUserOrderReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserOrderReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserOrderReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserOrderReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserOrderReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateUserOrderReplyMultiError(errors)
	}

	return nil
}

// CreateUserOrderReplyMultiError is an error wrapping multiple validation
// errors returned by CreateUserOrderReply.ValidateAll() if the designated
// constraints aren't met.
type CreateUserOrderReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserOrderReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserOrderReplyMultiError) AllErrors() []error { return m }

// CreateUserOrderReplyValidationError is the validation error returned by
// CreateUserOrderReply.Validate if the designated constraints aren't met.
type CreateUserOrderReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserOrderReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserOrderReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserOrderReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserOrderReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserOrderReplyValidationError) ErrorName() string {
	return "CreateUserOrderReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserOrderReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserOrderReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserOrderReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserOrderReplyValidationError{}

// Validate checks the field values on PayCallbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PayCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayCallbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PayCallbackRequestMultiError, or nil if none found.
func (m *PayCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PayCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PayCallbackRequestMultiError(errors)
	}

	return nil
}

// PayCallbackRequestMultiError is an error wrapping multiple validation errors
// returned by PayCallbackRequest.ValidateAll() if the designated constraints
// aren't met.
type PayCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayCallbackRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayCallbackRequestMultiError) AllErrors() []error { return m }

// PayCallbackRequestValidationError is the validation error returned by
// PayCallbackRequest.Validate if the designated constraints aren't met.
type PayCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayCallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayCallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayCallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayCallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayCallbackRequestValidationError) ErrorName() string {
	return "PayCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PayCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayCallbackRequestValidationError{}

// Validate checks the field values on PayCallbackReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PayCallbackReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayCallbackReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PayCallbackReplyMultiError, or nil if none found.
func (m *PayCallbackReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PayCallbackReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PayCallbackReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PayCallbackReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PayCallbackReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PayCallbackReplyMultiError(errors)
	}

	return nil
}

// PayCallbackReplyMultiError is an error wrapping multiple validation errors
// returned by PayCallbackReply.ValidateAll() if the designated constraints
// aren't met.
type PayCallbackReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayCallbackReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayCallbackReplyMultiError) AllErrors() []error { return m }

// PayCallbackReplyValidationError is the validation error returned by
// PayCallbackReply.Validate if the designated constraints aren't met.
type PayCallbackReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayCallbackReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayCallbackReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayCallbackReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayCallbackReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayCallbackReplyValidationError) ErrorName() string { return "PayCallbackReplyValidationError" }

// Error satisfies the builtin error interface
func (e PayCallbackReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayCallbackReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayCallbackReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayCallbackReplyValidationError{}

// Validate checks the field values on PayReturnRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PayReturnRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayReturnRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PayReturnRequestMultiError, or nil if none found.
func (m *PayReturnRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PayReturnRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PayReturnRequestMultiError(errors)
	}

	return nil
}

// PayReturnRequestMultiError is an error wrapping multiple validation errors
// returned by PayReturnRequest.ValidateAll() if the designated constraints
// aren't met.
type PayReturnRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayReturnRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayReturnRequestMultiError) AllErrors() []error { return m }

// PayReturnRequestValidationError is the validation error returned by
// PayReturnRequest.Validate if the designated constraints aren't met.
type PayReturnRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayReturnRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayReturnRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayReturnRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayReturnRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayReturnRequestValidationError) ErrorName() string { return "PayReturnRequestValidationError" }

// Error satisfies the builtin error interface
func (e PayReturnRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayReturnRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayReturnRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayReturnRequestValidationError{}

// Validate checks the field values on PayReturnReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PayReturnReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayReturnReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PayReturnReplyMultiError,
// or nil if none found.
func (m *PayReturnReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PayReturnReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return PayReturnReplyMultiError(errors)
	}

	return nil
}

// PayReturnReplyMultiError is an error wrapping multiple validation errors
// returned by PayReturnReply.ValidateAll() if the designated constraints
// aren't met.
type PayReturnReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayReturnReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayReturnReplyMultiError) AllErrors() []error { return m }

// PayReturnReplyValidationError is the validation error returned by
// PayReturnReply.Validate if the designated constraints aren't met.
type PayReturnReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayReturnReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayReturnReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayReturnReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayReturnReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayReturnReplyValidationError) ErrorName() string { return "PayReturnReplyValidationError" }

// Error satisfies the builtin error interface
func (e PayReturnReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayReturnReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayReturnReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayReturnReplyValidationError{}

// Validate checks the field values on CreateUserOrderReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserOrderReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserOrderReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserOrderReply_DataMultiError, or nil if none found.
func (m *CreateUserOrderReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserOrderReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return CreateUserOrderReply_DataMultiError(errors)
	}

	return nil
}

// CreateUserOrderReply_DataMultiError is an error wrapping multiple validation
// errors returned by CreateUserOrderReply_Data.ValidateAll() if the
// designated constraints aren't met.
type CreateUserOrderReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserOrderReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserOrderReply_DataMultiError) AllErrors() []error { return m }

// CreateUserOrderReply_DataValidationError is the validation error returned by
// CreateUserOrderReply_Data.Validate if the designated constraints aren't met.
type CreateUserOrderReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserOrderReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserOrderReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserOrderReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserOrderReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserOrderReply_DataValidationError) ErrorName() string {
	return "CreateUserOrderReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserOrderReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserOrderReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserOrderReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserOrderReply_DataValidationError{}

// Validate checks the field values on PayCallbackReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PayCallbackReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PayCallbackReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PayCallbackReply_DataMultiError, or nil if none found.
func (m *PayCallbackReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *PayCallbackReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return PayCallbackReply_DataMultiError(errors)
	}

	return nil
}

// PayCallbackReply_DataMultiError is an error wrapping multiple validation
// errors returned by PayCallbackReply_Data.ValidateAll() if the designated
// constraints aren't met.
type PayCallbackReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PayCallbackReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PayCallbackReply_DataMultiError) AllErrors() []error { return m }

// PayCallbackReply_DataValidationError is the validation error returned by
// PayCallbackReply_Data.Validate if the designated constraints aren't met.
type PayCallbackReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PayCallbackReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PayCallbackReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PayCallbackReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PayCallbackReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PayCallbackReply_DataValidationError) ErrorName() string {
	return "PayCallbackReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e PayCallbackReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPayCallbackReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PayCallbackReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PayCallbackReply_DataValidationError{}
