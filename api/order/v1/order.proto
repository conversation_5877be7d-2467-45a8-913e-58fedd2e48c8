syntax = "proto3";

package order_v1;

option go_package = "github.com/wlnil/life-log-be/api/order/v1;v1";

message CreateUserOrderRequest {
  string payment_method = 1; // 支付方式
  string purchase_type = 2; // 购买服务类型
  int32 category = 3; // 服务类别
}
message CreateUserOrderReply {
  message Data {
    string url = 1;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message PayCallbackRequest {}
message PayCallbackReply {
  message Data {
    string url = 1;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message PayReturnRequest {}
message PayReturnReply {
  int32 code = 1;
  string msg = 2;
}
