// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: order/v1/order.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateUserOrderRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PaymentMethod string                 `protobuf:"bytes,1,opt,name=payment_method,json=paymentMethod,proto3" json:"payment_method,omitempty"` // 支付方式
	PurchaseType  string                 `protobuf:"bytes,2,opt,name=purchase_type,json=purchaseType,proto3" json:"purchase_type,omitempty"`    // 购买服务类型
	Category      int32                  `protobuf:"varint,3,opt,name=category,proto3" json:"category,omitempty"`                               // 服务类别
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserOrderRequest) Reset() {
	*x = CreateUserOrderRequest{}
	mi := &file_order_v1_order_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserOrderRequest) ProtoMessage() {}

func (x *CreateUserOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateUserOrderRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_proto_rawDescGZIP(), []int{0}
}

func (x *CreateUserOrderRequest) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *CreateUserOrderRequest) GetPurchaseType() string {
	if x != nil {
		return x.PurchaseType
	}
	return ""
}

func (x *CreateUserOrderRequest) GetCategory() int32 {
	if x != nil {
		return x.Category
	}
	return 0
}

type CreateUserOrderReply struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *CreateUserOrderReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserOrderReply) Reset() {
	*x = CreateUserOrderReply{}
	mi := &file_order_v1_order_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserOrderReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserOrderReply) ProtoMessage() {}

func (x *CreateUserOrderReply) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserOrderReply.ProtoReflect.Descriptor instead.
func (*CreateUserOrderReply) Descriptor() ([]byte, []int) {
	return file_order_v1_order_proto_rawDescGZIP(), []int{1}
}

func (x *CreateUserOrderReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateUserOrderReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateUserOrderReply) GetData() *CreateUserOrderReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type PayCallbackRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayCallbackRequest) Reset() {
	*x = PayCallbackRequest{}
	mi := &file_order_v1_order_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayCallbackRequest) ProtoMessage() {}

func (x *PayCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayCallbackRequest.ProtoReflect.Descriptor instead.
func (*PayCallbackRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_proto_rawDescGZIP(), []int{2}
}

type PayCallbackReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *PayCallbackReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayCallbackReply) Reset() {
	*x = PayCallbackReply{}
	mi := &file_order_v1_order_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayCallbackReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayCallbackReply) ProtoMessage() {}

func (x *PayCallbackReply) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayCallbackReply.ProtoReflect.Descriptor instead.
func (*PayCallbackReply) Descriptor() ([]byte, []int) {
	return file_order_v1_order_proto_rawDescGZIP(), []int{3}
}

func (x *PayCallbackReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PayCallbackReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PayCallbackReply) GetData() *PayCallbackReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type PayReturnRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayReturnRequest) Reset() {
	*x = PayReturnRequest{}
	mi := &file_order_v1_order_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayReturnRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayReturnRequest) ProtoMessage() {}

func (x *PayReturnRequest) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayReturnRequest.ProtoReflect.Descriptor instead.
func (*PayReturnRequest) Descriptor() ([]byte, []int) {
	return file_order_v1_order_proto_rawDescGZIP(), []int{4}
}

type PayReturnReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayReturnReply) Reset() {
	*x = PayReturnReply{}
	mi := &file_order_v1_order_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayReturnReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayReturnReply) ProtoMessage() {}

func (x *PayReturnReply) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayReturnReply.ProtoReflect.Descriptor instead.
func (*PayReturnReply) Descriptor() ([]byte, []int) {
	return file_order_v1_order_proto_rawDescGZIP(), []int{5}
}

func (x *PayReturnReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PayReturnReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CreateUserOrderReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserOrderReply_Data) Reset() {
	*x = CreateUserOrderReply_Data{}
	mi := &file_order_v1_order_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserOrderReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserOrderReply_Data) ProtoMessage() {}

func (x *CreateUserOrderReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserOrderReply_Data.ProtoReflect.Descriptor instead.
func (*CreateUserOrderReply_Data) Descriptor() ([]byte, []int) {
	return file_order_v1_order_proto_rawDescGZIP(), []int{1, 0}
}

func (x *CreateUserOrderReply_Data) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type PayCallbackReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayCallbackReply_Data) Reset() {
	*x = PayCallbackReply_Data{}
	mi := &file_order_v1_order_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayCallbackReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayCallbackReply_Data) ProtoMessage() {}

func (x *PayCallbackReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_order_v1_order_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayCallbackReply_Data.ProtoReflect.Descriptor instead.
func (*PayCallbackReply_Data) Descriptor() ([]byte, []int) {
	return file_order_v1_order_proto_rawDescGZIP(), []int{3, 0}
}

func (x *PayCallbackReply_Data) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_order_v1_order_proto protoreflect.FileDescriptor

const file_order_v1_order_proto_rawDesc = "" +
	"\n" +
	"\x14order/v1/order.proto\x12\border_v1\"\x80\x01\n" +
	"\x16CreateUserOrderRequest\x12%\n" +
	"\x0epayment_method\x18\x01 \x01(\tR\rpaymentMethod\x12#\n" +
	"\rpurchase_type\x18\x02 \x01(\tR\fpurchaseType\x12\x1a\n" +
	"\bcategory\x18\x03 \x01(\x05R\bcategory\"\x8f\x01\n" +
	"\x14CreateUserOrderReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x127\n" +
	"\x04data\x18\x03 \x01(\v2#.order_v1.CreateUserOrderReply.DataR\x04data\x1a\x18\n" +
	"\x04Data\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\"\x14\n" +
	"\x12PayCallbackRequest\"\x87\x01\n" +
	"\x10PayCallbackReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x123\n" +
	"\x04data\x18\x03 \x01(\v2\x1f.order_v1.PayCallbackReply.DataR\x04data\x1a\x18\n" +
	"\x04Data\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\"\x12\n" +
	"\x10PayReturnRequest\"6\n" +
	"\x0ePayReturnReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msgB.Z,github.com/wlnil/life-log-be/api/order/v1;v1b\x06proto3"

var (
	file_order_v1_order_proto_rawDescOnce sync.Once
	file_order_v1_order_proto_rawDescData []byte
)

func file_order_v1_order_proto_rawDescGZIP() []byte {
	file_order_v1_order_proto_rawDescOnce.Do(func() {
		file_order_v1_order_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_order_v1_order_proto_rawDesc), len(file_order_v1_order_proto_rawDesc)))
	})
	return file_order_v1_order_proto_rawDescData
}

var file_order_v1_order_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_order_v1_order_proto_goTypes = []any{
	(*CreateUserOrderRequest)(nil),    // 0: order_v1.CreateUserOrderRequest
	(*CreateUserOrderReply)(nil),      // 1: order_v1.CreateUserOrderReply
	(*PayCallbackRequest)(nil),        // 2: order_v1.PayCallbackRequest
	(*PayCallbackReply)(nil),          // 3: order_v1.PayCallbackReply
	(*PayReturnRequest)(nil),          // 4: order_v1.PayReturnRequest
	(*PayReturnReply)(nil),            // 5: order_v1.PayReturnReply
	(*CreateUserOrderReply_Data)(nil), // 6: order_v1.CreateUserOrderReply.Data
	(*PayCallbackReply_Data)(nil),     // 7: order_v1.PayCallbackReply.Data
}
var file_order_v1_order_proto_depIdxs = []int32{
	6, // 0: order_v1.CreateUserOrderReply.data:type_name -> order_v1.CreateUserOrderReply.Data
	7, // 1: order_v1.PayCallbackReply.data:type_name -> order_v1.PayCallbackReply.Data
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_order_v1_order_proto_init() }
func file_order_v1_order_proto_init() {
	if File_order_v1_order_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_order_v1_order_proto_rawDesc), len(file_order_v1_order_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_order_v1_order_proto_goTypes,
		DependencyIndexes: file_order_v1_order_proto_depIdxs,
		MessageInfos:      file_order_v1_order_proto_msgTypes,
	}.Build()
	File_order_v1_order_proto = out.File
	file_order_v1_order_proto_goTypes = nil
	file_order_v1_order_proto_depIdxs = nil
}
