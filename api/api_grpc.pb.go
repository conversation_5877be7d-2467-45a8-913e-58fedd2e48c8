// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api.proto

package api

import (
	context "context"
	v13 "github.com/wlnil/life-log-be/api/planet/v1"
	v11 "github.com/wlnil/life-log-be/api/site/v1"
	v1 "github.com/wlnil/life-log-be/api/user/v1"
	v12 "github.com/wlnil/life-log-be/api/user_habit/v1"
	v16 "github.com/wlnil/life-log-be/api/user_message/v1"
	v14 "github.com/wlnil/life-log-be/api/user_planet/v1"
	v15 "github.com/wlnil/life-log-be/api/user_statistic/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	LifeLog_Register_FullMethodName                     = "/api.LifeLog/Register"
	LifeLog_Login_FullMethodName                        = "/api.LifeLog/Login"
	LifeLog_Logout_FullMethodName                       = "/api.LifeLog/Logout"
	LifeLog_PushToken_FullMethodName                    = "/api.LifeLog/PushToken"
	LifeLog_ForgetPassword_FullMethodName               = "/api.LifeLog/ForgetPassword"
	LifeLog_ChangePassword_FullMethodName               = "/api.LifeLog/ChangePassword"
	LifeLog_DeleteUser_FullMethodName                   = "/api.LifeLog/DeleteUser"
	LifeLog_ChangePhone_FullMethodName                  = "/api.LifeLog/ChangePhone"
	LifeLog_ChangeEmail_FullMethodName                  = "/api.LifeLog/ChangeEmail"
	LifeLog_GetUserProfile_FullMethodName               = "/api.LifeLog/GetUserProfile"
	LifeLog_UpdateUserProfile_FullMethodName            = "/api.LifeLog/UpdateUserProfile"
	LifeLog_FollowUser_FullMethodName                   = "/api.LifeLog/FollowUser"
	LifeLog_UnfollowUser_FullMethodName                 = "/api.LifeLog/UnfollowUser"
	LifeLog_RefreshToken_FullMethodName                 = "/api.LifeLog/RefreshToken"
	LifeLog_GetUserSetting_FullMethodName               = "/api.LifeLog/GetUserSetting"
	LifeLog_UpdateUserAward_FullMethodName              = "/api.LifeLog/UpdateUserAward"
	LifeLog_ChangeUserPrivacyPassword_FullMethodName    = "/api.LifeLog/ChangeUserPrivacyPassword"
	LifeLog_CheckUserPrivacyPassword_FullMethodName     = "/api.LifeLog/CheckUserPrivacyPassword"
	LifeLog_GetUserVipInfo_FullMethodName               = "/api.LifeLog/GetUserVipInfo"
	LifeLog_BuddySearch_FullMethodName                  = "/api.LifeLog/BuddySearch"
	LifeLog_BuddyInvitation_FullMethodName              = "/api.LifeLog/BuddyInvitation"
	LifeLog_GetSiteInfo_FullMethodName                  = "/api.LifeLog/GetSiteInfo"
	LifeLog_HealthCheck_FullMethodName                  = "/api.LifeLog/HealthCheck"
	LifeLog_CreateUpToken_FullMethodName                = "/api.LifeLog/CreateUpToken"
	LifeLog_CreateDownURL_FullMethodName                = "/api.LifeLog/CreateDownURL"
	LifeLog_SendVerifyCode_FullMethodName               = "/api.LifeLog/SendVerifyCode"
	LifeLog_ListMotiveMemo_FullMethodName               = "/api.LifeLog/ListMotiveMemo"
	LifeLog_CreateFeedback_FullMethodName               = "/api.LifeLog/CreateFeedback"
	LifeLog_VersionCheck_FullMethodName                 = "/api.LifeLog/VersionCheck"
	LifeLog_CreateUserHabit_FullMethodName              = "/api.LifeLog/CreateUserHabit"
	LifeLog_ListUserHabit_FullMethodName                = "/api.LifeLog/ListUserHabit"
	LifeLog_GetUserHabit_FullMethodName                 = "/api.LifeLog/GetUserHabit"
	LifeLog_UpdateUserHabit_FullMethodName              = "/api.LifeLog/UpdateUserHabit"
	LifeLog_DeleteUserHabit_FullMethodName              = "/api.LifeLog/DeleteUserHabit"
	LifeLog_PauseUserHabit_FullMethodName               = "/api.LifeLog/PauseUserHabit"
	LifeLog_RecoverUserHabit_FullMethodName             = "/api.LifeLog/RecoverUserHabit"
	LifeLog_ArchiveUserHabit_FullMethodName             = "/api.LifeLog/ArchiveUserHabit"
	LifeLog_ListUserHabitSnapshot_FullMethodName        = "/api.LifeLog/ListUserHabitSnapshot"
	LifeLog_CreateUserHabitMemo_FullMethodName          = "/api.LifeLog/CreateUserHabitMemo"
	LifeLog_UpdateUserHabitMemo_FullMethodName          = "/api.LifeLog/UpdateUserHabitMemo"
	LifeLog_DeleteUserHabitMemo_FullMethodName          = "/api.LifeLog/DeleteUserHabitMemo"
	LifeLog_PunchUserHabit_FullMethodName               = "/api.LifeLog/PunchUserHabit"
	LifeLog_CancelPunchUserHabit_FullMethodName         = "/api.LifeLog/CancelPunchUserHabit"
	LifeLog_UpdatePunchUserHabit_FullMethodName         = "/api.LifeLog/UpdatePunchUserHabit"
	LifeLog_ReckonUserHabit_FullMethodName              = "/api.LifeLog/ReckonUserHabit"
	LifeLog_CancelReckonUserHabit_FullMethodName        = "/api.LifeLog/CancelReckonUserHabit"
	LifeLog_CreateUserHabitReckon_FullMethodName        = "/api.LifeLog/CreateUserHabitReckon"
	LifeLog_UpdateUserHabitReckon_FullMethodName        = "/api.LifeLog/UpdateUserHabitReckon"
	LifeLog_DeleteUserHabitReckon_FullMethodName        = "/api.LifeLog/DeleteUserHabitReckon"
	LifeLog_CreatePlanet_FullMethodName                 = "/api.LifeLog/CreatePlanet"
	LifeLog_GetPlanet_FullMethodName                    = "/api.LifeLog/GetPlanet"
	LifeLog_UpdatePlanet_FullMethodName                 = "/api.LifeLog/UpdatePlanet"
	LifeLog_DeletePlanet_FullMethodName                 = "/api.LifeLog/DeletePlanet"
	LifeLog_CreatePlanetTarget_FullMethodName           = "/api.LifeLog/CreatePlanetTarget"
	LifeLog_GetPlanetTarget_FullMethodName              = "/api.LifeLog/GetPlanetTarget"
	LifeLog_UpdatePlanetTarget_FullMethodName           = "/api.LifeLog/UpdatePlanetTarget"
	LifeLog_DeletePlanetTarget_FullMethodName           = "/api.LifeLog/DeletePlanetTarget"
	LifeLog_ListPlanetTarget_FullMethodName             = "/api.LifeLog/ListPlanetTarget"
	LifeLog_ListPlanetByUserID_FullMethodName           = "/api.LifeLog/ListPlanetByUserID"
	LifeLog_JoinPlanet_FullMethodName                   = "/api.LifeLog/JoinPlanet"
	LifeLog_QuitPlanet_FullMethodName                   = "/api.LifeLog/QuitPlanet"
	LifeLog_JoinPlanetTarget_FullMethodName             = "/api.LifeLog/JoinPlanetTarget"
	LifeLog_QuitPlanetTarget_FullMethodName             = "/api.LifeLog/QuitPlanetTarget"
	LifeLog_LikePlanetPost_FullMethodName               = "/api.LifeLog/LikePlanetPost"
	LifeLog_CancelLikePlanetPost_FullMethodName         = "/api.LifeLog/CancelLikePlanetPost"
	LifeLog_FavoritePlanetPost_FullMethodName           = "/api.LifeLog/FavoritePlanetPost"
	LifeLog_CancelFavoritePlanetPost_FullMethodName     = "/api.LifeLog/CancelFavoritePlanetPost"
	LifeLog_CreatePlanetPost_FullMethodName             = "/api.LifeLog/CreatePlanetPost"
	LifeLog_UpdatePlanetPost_FullMethodName             = "/api.LifeLog/UpdatePlanetPost"
	LifeLog_DeletePlanetPost_FullMethodName             = "/api.LifeLog/DeletePlanetPost"
	LifeLog_ToppedPlanetPost_FullMethodName             = "/api.LifeLog/ToppedPlanetPost"
	LifeLog_ListPlanetPost_FullMethodName               = "/api.LifeLog/ListPlanetPost"
	LifeLog_ListPlanetTopPost_FullMethodName            = "/api.LifeLog/ListPlanetTopPost"
	LifeLog_CreatePlanetPostComment_FullMethodName      = "/api.LifeLog/CreatePlanetPostComment"
	LifeLog_DeletePlanetPostComment_FullMethodName      = "/api.LifeLog/DeletePlanetPostComment"
	LifeLog_ListPlanetPostComment_FullMethodName        = "/api.LifeLog/ListPlanetPostComment"
	LifeLog_RunCronTask_FullMethodName                  = "/api.LifeLog/RunCronTask"
	LifeLog_TodayStatisticFromHome_FullMethodName       = "/api.LifeLog/TodayStatisticFromHome"
	LifeLog_UserHabitStatisticFromDetail_FullMethodName = "/api.LifeLog/UserHabitStatisticFromDetail"
	LifeLog_UnreadCount_FullMethodName                  = "/api.LifeLog/UnreadCount"
)

// LifeLogClient is the client API for LifeLog service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LifeLogClient interface {
	// 注册用户
	Register(ctx context.Context, in *v1.RegisterRequest, opts ...grpc.CallOption) (*v1.RegisterReply, error)
	// 登录
	Login(ctx context.Context, in *v1.LoginRequest, opts ...grpc.CallOption) (*v1.LoginReply, error)
	// 登出
	Logout(ctx context.Context, in *v1.LogoutRequest, opts ...grpc.CallOption) (*v1.LogoutReply, error)
	// 推送 token 和设备信息
	PushToken(ctx context.Context, in *v1.PushTokenRequest, opts ...grpc.CallOption) (*v1.PushTokenReply, error)
	// 忘记密码
	ForgetPassword(ctx context.Context, in *v1.ForgetPasswordRequest, opts ...grpc.CallOption) (*v1.ForgetPasswordReply, error)
	// 修改密码
	ChangePassword(ctx context.Context, in *v1.ChangePasswordRequest, opts ...grpc.CallOption) (*v1.ChangePasswordReply, error)
	// 删除用户
	DeleteUser(ctx context.Context, in *v1.DeleteUserRequest, opts ...grpc.CallOption) (*v1.DeleteUserReply, error)
	// 修改手机号
	ChangePhone(ctx context.Context, in *v1.ChangePhoneRequest, opts ...grpc.CallOption) (*v1.ChangePhoneReply, error)
	// 修改邮箱
	ChangeEmail(ctx context.Context, in *v1.ChangeEmailRequest, opts ...grpc.CallOption) (*v1.ChangeEmailReply, error)
	// 获取用户资料
	GetUserProfile(ctx context.Context, in *v1.GetUserProfileRequest, opts ...grpc.CallOption) (*v1.GetUserProfileReply, error)
	// 修改用户资料
	UpdateUserProfile(ctx context.Context, in *v1.UpdateUserProfileRequest, opts ...grpc.CallOption) (*v1.UpdateUserProfileReply, error)
	// 关注
	FollowUser(ctx context.Context, in *v1.FollowUserRequest, opts ...grpc.CallOption) (*v1.FollowUserReply, error)
	// 取消关注
	UnfollowUser(ctx context.Context, in *v1.UnfollowUserRequest, opts ...grpc.CallOption) (*v1.UnfollowUserReply, error)
	// 刷新 token
	RefreshToken(ctx context.Context, in *v1.RefreshTokenRequest, opts ...grpc.CallOption) (*v1.LoginReply, error)
	// 获取用户配置信息
	GetUserSetting(ctx context.Context, in *v1.GetUserSettingRequest, opts ...grpc.CallOption) (*v1.GetUserSettingReply, error)
	// 修改用户配置信息
	UpdateUserAward(ctx context.Context, in *v1.UpdateUserAwardRequest, opts ...grpc.CallOption) (*v1.UpdateUserAwardReply, error)
	// 修改用户隐私密码
	ChangeUserPrivacyPassword(ctx context.Context, in *v1.ChangeUserPrivacyPasswordRequest, opts ...grpc.CallOption) (*v1.ChangeUserPrivacyPasswordReply, error)
	// 检查用户隐私密码
	CheckUserPrivacyPassword(ctx context.Context, in *v1.CheckUserPrivacyPasswordRequest, opts ...grpc.CallOption) (*v1.CheckUserPrivacyPasswordReply, error)
	// 获取用户会员详情
	GetUserVipInfo(ctx context.Context, in *v1.GetUserVipInfoRequest, opts ...grpc.CallOption) (*v1.GetUserVipInfoReply, error)
	// 搭子搜索
	BuddySearch(ctx context.Context, in *v1.BuddySearchRequest, opts ...grpc.CallOption) (*v1.BuddySearchReply, error)
	// 发送搭子邀请
	BuddyInvitation(ctx context.Context, in *v1.BuddyInvitationRequest, opts ...grpc.CallOption) (*v1.BuddyInvitationReply, error)
	// 获取网站信息
	GetSiteInfo(ctx context.Context, in *v11.GetSiteInfoRequest, opts ...grpc.CallOption) (*v11.GetSiteInfoReply, error)
	// 健康检查接口
	HealthCheck(ctx context.Context, in *v11.HealthCheckRequest, opts ...grpc.CallOption) (*v11.HealthCheckReply, error)
	// 获取七牛上传凭证
	CreateUpToken(ctx context.Context, in *v11.CreateUpTokenRequest, opts ...grpc.CallOption) (*v11.CreateUpTokenReply, error)
	// 获取七牛下载地址
	CreateDownURL(ctx context.Context, in *v11.CreateDownURLRequest, opts ...grpc.CallOption) (*v11.CreateDownURLReply, error)
	// 发送验证码
	SendVerifyCode(ctx context.Context, in *v11.SendVerifyCodeRequest, opts ...grpc.CallOption) (*v11.SendVerifyCodeReply, error)
	// 获取每日随想
	ListMotiveMemo(ctx context.Context, in *v11.ListMotiveMemoRequest, opts ...grpc.CallOption) (*v11.ListMotiveMemoReply, error)
	// 添加意见反馈
	CreateFeedback(ctx context.Context, in *v11.CreateFeedbackRequest, opts ...grpc.CallOption) (*v11.CreateFeedbackReply, error)
	// 获取版本更新
	VersionCheck(ctx context.Context, in *v11.VersionCheckRequest, opts ...grpc.CallOption) (*v11.VersionCheckReply, error)
	// 创建用户习惯
	CreateUserHabit(ctx context.Context, in *v12.CreateUserHabitRequest, opts ...grpc.CallOption) (*v12.CreateUserHabitReply, error)
	// 获取用户习惯列表
	ListUserHabit(ctx context.Context, in *v12.ListUserHabitRequest, opts ...grpc.CallOption) (*v12.ListUserHabitReply, error)
	// 获取用户习惯详情
	GetUserHabit(ctx context.Context, in *v12.GetUserHabitRequest, opts ...grpc.CallOption) (*v12.GetUserHabitReply, error)
	// 更新用户习惯
	UpdateUserHabit(ctx context.Context, in *v12.UpdateUserHabitRequest, opts ...grpc.CallOption) (*v12.UpdateUserHabitReply, error)
	// 删除用户习惯
	DeleteUserHabit(ctx context.Context, in *v12.DeleteUserHabitRequest, opts ...grpc.CallOption) (*v12.DeleteUserHabitReply, error)
	// 暂停用户习惯
	PauseUserHabit(ctx context.Context, in *v12.PauseUserHabitRequest, opts ...grpc.CallOption) (*v12.PauseUserHabitReply, error)
	// 恢复用户习惯
	RecoverUserHabit(ctx context.Context, in *v12.RecoverUserHabitRequest, opts ...grpc.CallOption) (*v12.RecoverUserHabitReply, error)
	// 归档用户习惯
	ArchiveUserHabit(ctx context.Context, in *v12.ArchiveUserHabitRequest, opts ...grpc.CallOption) (*v12.ArchiveUserHabitReply, error)
	// 获取用户每日习惯列表
	ListUserHabitSnapshot(ctx context.Context, in *v12.ListUserHabitSnapshotRequest, opts ...grpc.CallOption) (*v12.ListUserHabitSnapshotReply, error)
	// 创建用户习惯想法
	CreateUserHabitMemo(ctx context.Context, in *v12.CreateUserHabitMemoRequest, opts ...grpc.CallOption) (*v12.CreateUserHabitMemoReply, error)
	// 更新用户习惯想法
	UpdateUserHabitMemo(ctx context.Context, in *v12.UpdateUserHabitMemoRequest, opts ...grpc.CallOption) (*v12.UpdateUserHabitMemoReply, error)
	// 删除用户习惯想法
	DeleteUserHabitMemo(ctx context.Context, in *v12.DeleteUserHabitMemoRequest, opts ...grpc.CallOption) (*v12.DeleteUserHabitMemoReply, error)
	// 习惯打卡
	PunchUserHabit(ctx context.Context, in *v12.PunchUserHabitRequest, opts ...grpc.CallOption) (*v12.PunchUserHabitReply, error)
	// 取消习惯打卡
	CancelPunchUserHabit(ctx context.Context, in *v12.CancelPunchUserHabitRequest, opts ...grpc.CallOption) (*v12.CancelPunchUserHabitReply, error)
	// 更新习惯打卡
	UpdatePunchUserHabit(ctx context.Context, in *v12.UpdatePunchUserHabitRequest, opts ...grpc.CallOption) (*v12.UpdatePunchUserHabitReply, error)
	// 习惯计时
	ReckonUserHabit(ctx context.Context, in *v12.ReckonUserHabitRequest, opts ...grpc.CallOption) (*v12.ReckonUserHabitReply, error)
	// 取消习惯计时
	CancelReckonUserHabit(ctx context.Context, in *v12.CancelReckonUserHabitRequest, opts ...grpc.CallOption) (*v12.CancelReckonUserHabitReply, error)
	// 创建用户习惯计时
	CreateUserHabitReckon(ctx context.Context, in *v12.CreateUserHabitReckonRequest, opts ...grpc.CallOption) (*v12.CreateUserHabitReckonReply, error)
	// 更新用户习惯计时
	UpdateUserHabitReckon(ctx context.Context, in *v12.UpdateUserHabitReckonRequest, opts ...grpc.CallOption) (*v12.UpdateUserHabitReckonReply, error)
	// 删除用户习惯计时
	DeleteUserHabitReckon(ctx context.Context, in *v12.DeleteUserHabitReckonRequest, opts ...grpc.CallOption) (*v12.DeleteUserHabitReckonReply, error)
	// 创建星球
	CreatePlanet(ctx context.Context, in *v13.CreatePlanetRequest, opts ...grpc.CallOption) (*v13.CreatePlanetReply, error)
	// 获取星球详情
	GetPlanet(ctx context.Context, in *v13.GetPlanetRequest, opts ...grpc.CallOption) (*v13.GetPlanetReply, error)
	// 更新星球
	UpdatePlanet(ctx context.Context, in *v13.UpdatePlanetRequest, opts ...grpc.CallOption) (*v13.UpdatePlanetReply, error)
	// 删除星球
	DeletePlanet(ctx context.Context, in *v13.DeletePlanetRequest, opts ...grpc.CallOption) (*v13.DeletePlanetReply, error)
	// 创建星球目标
	CreatePlanetTarget(ctx context.Context, in *v13.CreatePlanetTargetRequest, opts ...grpc.CallOption) (*v13.CreatePlanetTargetReply, error)
	// 获取星球目标详情
	GetPlanetTarget(ctx context.Context, in *v13.GetPlanetTargetRequest, opts ...grpc.CallOption) (*v13.GetPlanetTargetReply, error)
	// 更新星球目标
	UpdatePlanetTarget(ctx context.Context, in *v13.UpdatePlanetTargetRequest, opts ...grpc.CallOption) (*v13.UpdatePlanetTargetReply, error)
	// 删除星球目标
	DeletePlanetTarget(ctx context.Context, in *v13.DeletePlanetTargetRequest, opts ...grpc.CallOption) (*v13.DeletePlanetTargetReply, error)
	// 获取星球目标列表
	ListPlanetTarget(ctx context.Context, in *v13.ListPlanetTargetRequest, opts ...grpc.CallOption) (*v13.ListPlanetTargetReply, error)
	// 获取用户加入星球列表
	ListPlanetByUserID(ctx context.Context, in *v14.ListPlanetByUserIDRequest, opts ...grpc.CallOption) (*v14.ListPlanetByUserIDReply, error)
	// 加入星球
	JoinPlanet(ctx context.Context, in *v14.JoinPlanetRequest, opts ...grpc.CallOption) (*v14.JoinPlanetReply, error)
	// 退出星球
	QuitPlanet(ctx context.Context, in *v14.QuitPlanetRequest, opts ...grpc.CallOption) (*v14.QuitPlanetReply, error)
	// 加入星球目标
	JoinPlanetTarget(ctx context.Context, in *v14.JoinPlanetTargetRequest, opts ...grpc.CallOption) (*v14.JoinPlanetTargetReply, error)
	// 退出星球目标
	QuitPlanetTarget(ctx context.Context, in *v14.QuitPlanetTargetRequest, opts ...grpc.CallOption) (*v14.QuitPlanetTargetReply, error)
	// 点赞星球动态
	LikePlanetPost(ctx context.Context, in *v14.LikePlanetPostRequest, opts ...grpc.CallOption) (*v14.LikePlanetPostReply, error)
	// 取消点赞星球动态
	CancelLikePlanetPost(ctx context.Context, in *v14.CancelLikePlanetPostRequest, opts ...grpc.CallOption) (*v14.CancelLikePlanetPostReply, error)
	// 收藏星球动态
	FavoritePlanetPost(ctx context.Context, in *v14.FavoritePlanetPostRequest, opts ...grpc.CallOption) (*v14.FavoritePlanetPostReply, error)
	// 取消收藏星球动态
	CancelFavoritePlanetPost(ctx context.Context, in *v14.CancelFavoritePlanetPostRequest, opts ...grpc.CallOption) (*v14.CancelFavoritePlanetPostReply, error)
	// 创建星球动态
	CreatePlanetPost(ctx context.Context, in *v14.CreatePlanetPostRequest, opts ...grpc.CallOption) (*v14.CreatePlanetPostReply, error)
	// 修改星球动态
	UpdatePlanetPost(ctx context.Context, in *v14.UpdatePlanetPostRequest, opts ...grpc.CallOption) (*v14.UpdatePlanetPostReply, error)
	// 删除星球动态
	DeletePlanetPost(ctx context.Context, in *v14.DeletePlanetPostRequest, opts ...grpc.CallOption) (*v14.DeletePlanetPostReply, error)
	// 置顶星球动态
	ToppedPlanetPost(ctx context.Context, in *v14.ToppedPlanetPostRequest, opts ...grpc.CallOption) (*v14.ToppedPlanetPostReply, error)
	// 获取星球动态列表
	ListPlanetPost(ctx context.Context, in *v14.ListPlanetPostRequest, opts ...grpc.CallOption) (*v14.ListPlanetPostReply, error)
	// 获取星球热点动态列表
	ListPlanetTopPost(ctx context.Context, in *v14.ListPlanetTopPostRequest, opts ...grpc.CallOption) (*v14.ListPlanetTopPostReply, error)
	// 创建动态评论
	CreatePlanetPostComment(ctx context.Context, in *v14.CreatePlanetPostCommentRequest, opts ...grpc.CallOption) (*v14.CreatePlanetPostCommentReply, error)
	// 删除动态评论
	DeletePlanetPostComment(ctx context.Context, in *v14.DeletePlanetPostCommentRequest, opts ...grpc.CallOption) (*v14.DeletePlanetPostCommentReply, error)
	// 获取动态评论列表
	ListPlanetPostComment(ctx context.Context, in *v14.ListPlanetPostCommentRequest, opts ...grpc.CallOption) (*v14.ListPlanetPostCommentReply, error)
	// 运行定时任务
	RunCronTask(ctx context.Context, in *v12.RunCronTaskRequest, opts ...grpc.CallOption) (*v12.RunCronTaskReply, error)
	// 数据统计
	// 获取首页每日数据
	TodayStatisticFromHome(ctx context.Context, in *v15.TodayStatisticFromHomeRequest, opts ...grpc.CallOption) (*v15.TodayStatisticFromHomeReply, error)
	// 获取用户习惯详情统计数据
	UserHabitStatisticFromDetail(ctx context.Context, in *v15.UserHabitStatisticFromDetailRequest, opts ...grpc.CallOption) (*v15.UserHabitStatisticFromDetailReply, error)
	UnreadCount(ctx context.Context, in *v16.UnreadCountRequest, opts ...grpc.CallOption) (*v16.UnreadCountReply, error)
}

type lifeLogClient struct {
	cc grpc.ClientConnInterface
}

func NewLifeLogClient(cc grpc.ClientConnInterface) LifeLogClient {
	return &lifeLogClient{cc}
}

func (c *lifeLogClient) Register(ctx context.Context, in *v1.RegisterRequest, opts ...grpc.CallOption) (*v1.RegisterReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.RegisterReply)
	err := c.cc.Invoke(ctx, LifeLog_Register_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) Login(ctx context.Context, in *v1.LoginRequest, opts ...grpc.CallOption) (*v1.LoginReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.LoginReply)
	err := c.cc.Invoke(ctx, LifeLog_Login_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) Logout(ctx context.Context, in *v1.LogoutRequest, opts ...grpc.CallOption) (*v1.LogoutReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.LogoutReply)
	err := c.cc.Invoke(ctx, LifeLog_Logout_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) PushToken(ctx context.Context, in *v1.PushTokenRequest, opts ...grpc.CallOption) (*v1.PushTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.PushTokenReply)
	err := c.cc.Invoke(ctx, LifeLog_PushToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ForgetPassword(ctx context.Context, in *v1.ForgetPasswordRequest, opts ...grpc.CallOption) (*v1.ForgetPasswordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.ForgetPasswordReply)
	err := c.cc.Invoke(ctx, LifeLog_ForgetPassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ChangePassword(ctx context.Context, in *v1.ChangePasswordRequest, opts ...grpc.CallOption) (*v1.ChangePasswordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.ChangePasswordReply)
	err := c.cc.Invoke(ctx, LifeLog_ChangePassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) DeleteUser(ctx context.Context, in *v1.DeleteUserRequest, opts ...grpc.CallOption) (*v1.DeleteUserReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.DeleteUserReply)
	err := c.cc.Invoke(ctx, LifeLog_DeleteUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ChangePhone(ctx context.Context, in *v1.ChangePhoneRequest, opts ...grpc.CallOption) (*v1.ChangePhoneReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.ChangePhoneReply)
	err := c.cc.Invoke(ctx, LifeLog_ChangePhone_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ChangeEmail(ctx context.Context, in *v1.ChangeEmailRequest, opts ...grpc.CallOption) (*v1.ChangeEmailReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.ChangeEmailReply)
	err := c.cc.Invoke(ctx, LifeLog_ChangeEmail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) GetUserProfile(ctx context.Context, in *v1.GetUserProfileRequest, opts ...grpc.CallOption) (*v1.GetUserProfileReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GetUserProfileReply)
	err := c.cc.Invoke(ctx, LifeLog_GetUserProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdateUserProfile(ctx context.Context, in *v1.UpdateUserProfileRequest, opts ...grpc.CallOption) (*v1.UpdateUserProfileReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.UpdateUserProfileReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdateUserProfile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) FollowUser(ctx context.Context, in *v1.FollowUserRequest, opts ...grpc.CallOption) (*v1.FollowUserReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.FollowUserReply)
	err := c.cc.Invoke(ctx, LifeLog_FollowUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UnfollowUser(ctx context.Context, in *v1.UnfollowUserRequest, opts ...grpc.CallOption) (*v1.UnfollowUserReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.UnfollowUserReply)
	err := c.cc.Invoke(ctx, LifeLog_UnfollowUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) RefreshToken(ctx context.Context, in *v1.RefreshTokenRequest, opts ...grpc.CallOption) (*v1.LoginReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.LoginReply)
	err := c.cc.Invoke(ctx, LifeLog_RefreshToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) GetUserSetting(ctx context.Context, in *v1.GetUserSettingRequest, opts ...grpc.CallOption) (*v1.GetUserSettingReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GetUserSettingReply)
	err := c.cc.Invoke(ctx, LifeLog_GetUserSetting_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdateUserAward(ctx context.Context, in *v1.UpdateUserAwardRequest, opts ...grpc.CallOption) (*v1.UpdateUserAwardReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.UpdateUserAwardReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdateUserAward_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ChangeUserPrivacyPassword(ctx context.Context, in *v1.ChangeUserPrivacyPasswordRequest, opts ...grpc.CallOption) (*v1.ChangeUserPrivacyPasswordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.ChangeUserPrivacyPasswordReply)
	err := c.cc.Invoke(ctx, LifeLog_ChangeUserPrivacyPassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CheckUserPrivacyPassword(ctx context.Context, in *v1.CheckUserPrivacyPasswordRequest, opts ...grpc.CallOption) (*v1.CheckUserPrivacyPasswordReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.CheckUserPrivacyPasswordReply)
	err := c.cc.Invoke(ctx, LifeLog_CheckUserPrivacyPassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) GetUserVipInfo(ctx context.Context, in *v1.GetUserVipInfoRequest, opts ...grpc.CallOption) (*v1.GetUserVipInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.GetUserVipInfoReply)
	err := c.cc.Invoke(ctx, LifeLog_GetUserVipInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) BuddySearch(ctx context.Context, in *v1.BuddySearchRequest, opts ...grpc.CallOption) (*v1.BuddySearchReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.BuddySearchReply)
	err := c.cc.Invoke(ctx, LifeLog_BuddySearch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) BuddyInvitation(ctx context.Context, in *v1.BuddyInvitationRequest, opts ...grpc.CallOption) (*v1.BuddyInvitationReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.BuddyInvitationReply)
	err := c.cc.Invoke(ctx, LifeLog_BuddyInvitation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) GetSiteInfo(ctx context.Context, in *v11.GetSiteInfoRequest, opts ...grpc.CallOption) (*v11.GetSiteInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.GetSiteInfoReply)
	err := c.cc.Invoke(ctx, LifeLog_GetSiteInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) HealthCheck(ctx context.Context, in *v11.HealthCheckRequest, opts ...grpc.CallOption) (*v11.HealthCheckReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.HealthCheckReply)
	err := c.cc.Invoke(ctx, LifeLog_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreateUpToken(ctx context.Context, in *v11.CreateUpTokenRequest, opts ...grpc.CallOption) (*v11.CreateUpTokenReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.CreateUpTokenReply)
	err := c.cc.Invoke(ctx, LifeLog_CreateUpToken_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreateDownURL(ctx context.Context, in *v11.CreateDownURLRequest, opts ...grpc.CallOption) (*v11.CreateDownURLReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.CreateDownURLReply)
	err := c.cc.Invoke(ctx, LifeLog_CreateDownURL_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) SendVerifyCode(ctx context.Context, in *v11.SendVerifyCodeRequest, opts ...grpc.CallOption) (*v11.SendVerifyCodeReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.SendVerifyCodeReply)
	err := c.cc.Invoke(ctx, LifeLog_SendVerifyCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ListMotiveMemo(ctx context.Context, in *v11.ListMotiveMemoRequest, opts ...grpc.CallOption) (*v11.ListMotiveMemoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.ListMotiveMemoReply)
	err := c.cc.Invoke(ctx, LifeLog_ListMotiveMemo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreateFeedback(ctx context.Context, in *v11.CreateFeedbackRequest, opts ...grpc.CallOption) (*v11.CreateFeedbackReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.CreateFeedbackReply)
	err := c.cc.Invoke(ctx, LifeLog_CreateFeedback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) VersionCheck(ctx context.Context, in *v11.VersionCheckRequest, opts ...grpc.CallOption) (*v11.VersionCheckReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.VersionCheckReply)
	err := c.cc.Invoke(ctx, LifeLog_VersionCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreateUserHabit(ctx context.Context, in *v12.CreateUserHabitRequest, opts ...grpc.CallOption) (*v12.CreateUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.CreateUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_CreateUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ListUserHabit(ctx context.Context, in *v12.ListUserHabitRequest, opts ...grpc.CallOption) (*v12.ListUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.ListUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_ListUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) GetUserHabit(ctx context.Context, in *v12.GetUserHabitRequest, opts ...grpc.CallOption) (*v12.GetUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.GetUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_GetUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdateUserHabit(ctx context.Context, in *v12.UpdateUserHabitRequest, opts ...grpc.CallOption) (*v12.UpdateUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.UpdateUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdateUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) DeleteUserHabit(ctx context.Context, in *v12.DeleteUserHabitRequest, opts ...grpc.CallOption) (*v12.DeleteUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.DeleteUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_DeleteUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) PauseUserHabit(ctx context.Context, in *v12.PauseUserHabitRequest, opts ...grpc.CallOption) (*v12.PauseUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.PauseUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_PauseUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) RecoverUserHabit(ctx context.Context, in *v12.RecoverUserHabitRequest, opts ...grpc.CallOption) (*v12.RecoverUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.RecoverUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_RecoverUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ArchiveUserHabit(ctx context.Context, in *v12.ArchiveUserHabitRequest, opts ...grpc.CallOption) (*v12.ArchiveUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.ArchiveUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_ArchiveUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ListUserHabitSnapshot(ctx context.Context, in *v12.ListUserHabitSnapshotRequest, opts ...grpc.CallOption) (*v12.ListUserHabitSnapshotReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.ListUserHabitSnapshotReply)
	err := c.cc.Invoke(ctx, LifeLog_ListUserHabitSnapshot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreateUserHabitMemo(ctx context.Context, in *v12.CreateUserHabitMemoRequest, opts ...grpc.CallOption) (*v12.CreateUserHabitMemoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.CreateUserHabitMemoReply)
	err := c.cc.Invoke(ctx, LifeLog_CreateUserHabitMemo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdateUserHabitMemo(ctx context.Context, in *v12.UpdateUserHabitMemoRequest, opts ...grpc.CallOption) (*v12.UpdateUserHabitMemoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.UpdateUserHabitMemoReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdateUserHabitMemo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) DeleteUserHabitMemo(ctx context.Context, in *v12.DeleteUserHabitMemoRequest, opts ...grpc.CallOption) (*v12.DeleteUserHabitMemoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.DeleteUserHabitMemoReply)
	err := c.cc.Invoke(ctx, LifeLog_DeleteUserHabitMemo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) PunchUserHabit(ctx context.Context, in *v12.PunchUserHabitRequest, opts ...grpc.CallOption) (*v12.PunchUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.PunchUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_PunchUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CancelPunchUserHabit(ctx context.Context, in *v12.CancelPunchUserHabitRequest, opts ...grpc.CallOption) (*v12.CancelPunchUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.CancelPunchUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_CancelPunchUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdatePunchUserHabit(ctx context.Context, in *v12.UpdatePunchUserHabitRequest, opts ...grpc.CallOption) (*v12.UpdatePunchUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.UpdatePunchUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdatePunchUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ReckonUserHabit(ctx context.Context, in *v12.ReckonUserHabitRequest, opts ...grpc.CallOption) (*v12.ReckonUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.ReckonUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_ReckonUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CancelReckonUserHabit(ctx context.Context, in *v12.CancelReckonUserHabitRequest, opts ...grpc.CallOption) (*v12.CancelReckonUserHabitReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.CancelReckonUserHabitReply)
	err := c.cc.Invoke(ctx, LifeLog_CancelReckonUserHabit_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreateUserHabitReckon(ctx context.Context, in *v12.CreateUserHabitReckonRequest, opts ...grpc.CallOption) (*v12.CreateUserHabitReckonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.CreateUserHabitReckonReply)
	err := c.cc.Invoke(ctx, LifeLog_CreateUserHabitReckon_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdateUserHabitReckon(ctx context.Context, in *v12.UpdateUserHabitReckonRequest, opts ...grpc.CallOption) (*v12.UpdateUserHabitReckonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.UpdateUserHabitReckonReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdateUserHabitReckon_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) DeleteUserHabitReckon(ctx context.Context, in *v12.DeleteUserHabitReckonRequest, opts ...grpc.CallOption) (*v12.DeleteUserHabitReckonReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.DeleteUserHabitReckonReply)
	err := c.cc.Invoke(ctx, LifeLog_DeleteUserHabitReckon_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreatePlanet(ctx context.Context, in *v13.CreatePlanetRequest, opts ...grpc.CallOption) (*v13.CreatePlanetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.CreatePlanetReply)
	err := c.cc.Invoke(ctx, LifeLog_CreatePlanet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) GetPlanet(ctx context.Context, in *v13.GetPlanetRequest, opts ...grpc.CallOption) (*v13.GetPlanetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.GetPlanetReply)
	err := c.cc.Invoke(ctx, LifeLog_GetPlanet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdatePlanet(ctx context.Context, in *v13.UpdatePlanetRequest, opts ...grpc.CallOption) (*v13.UpdatePlanetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.UpdatePlanetReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdatePlanet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) DeletePlanet(ctx context.Context, in *v13.DeletePlanetRequest, opts ...grpc.CallOption) (*v13.DeletePlanetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.DeletePlanetReply)
	err := c.cc.Invoke(ctx, LifeLog_DeletePlanet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreatePlanetTarget(ctx context.Context, in *v13.CreatePlanetTargetRequest, opts ...grpc.CallOption) (*v13.CreatePlanetTargetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.CreatePlanetTargetReply)
	err := c.cc.Invoke(ctx, LifeLog_CreatePlanetTarget_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) GetPlanetTarget(ctx context.Context, in *v13.GetPlanetTargetRequest, opts ...grpc.CallOption) (*v13.GetPlanetTargetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.GetPlanetTargetReply)
	err := c.cc.Invoke(ctx, LifeLog_GetPlanetTarget_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdatePlanetTarget(ctx context.Context, in *v13.UpdatePlanetTargetRequest, opts ...grpc.CallOption) (*v13.UpdatePlanetTargetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.UpdatePlanetTargetReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdatePlanetTarget_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) DeletePlanetTarget(ctx context.Context, in *v13.DeletePlanetTargetRequest, opts ...grpc.CallOption) (*v13.DeletePlanetTargetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.DeletePlanetTargetReply)
	err := c.cc.Invoke(ctx, LifeLog_DeletePlanetTarget_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ListPlanetTarget(ctx context.Context, in *v13.ListPlanetTargetRequest, opts ...grpc.CallOption) (*v13.ListPlanetTargetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.ListPlanetTargetReply)
	err := c.cc.Invoke(ctx, LifeLog_ListPlanetTarget_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ListPlanetByUserID(ctx context.Context, in *v14.ListPlanetByUserIDRequest, opts ...grpc.CallOption) (*v14.ListPlanetByUserIDReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListPlanetByUserIDReply)
	err := c.cc.Invoke(ctx, LifeLog_ListPlanetByUserID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) JoinPlanet(ctx context.Context, in *v14.JoinPlanetRequest, opts ...grpc.CallOption) (*v14.JoinPlanetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.JoinPlanetReply)
	err := c.cc.Invoke(ctx, LifeLog_JoinPlanet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) QuitPlanet(ctx context.Context, in *v14.QuitPlanetRequest, opts ...grpc.CallOption) (*v14.QuitPlanetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.QuitPlanetReply)
	err := c.cc.Invoke(ctx, LifeLog_QuitPlanet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) JoinPlanetTarget(ctx context.Context, in *v14.JoinPlanetTargetRequest, opts ...grpc.CallOption) (*v14.JoinPlanetTargetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.JoinPlanetTargetReply)
	err := c.cc.Invoke(ctx, LifeLog_JoinPlanetTarget_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) QuitPlanetTarget(ctx context.Context, in *v14.QuitPlanetTargetRequest, opts ...grpc.CallOption) (*v14.QuitPlanetTargetReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.QuitPlanetTargetReply)
	err := c.cc.Invoke(ctx, LifeLog_QuitPlanetTarget_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) LikePlanetPost(ctx context.Context, in *v14.LikePlanetPostRequest, opts ...grpc.CallOption) (*v14.LikePlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.LikePlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_LikePlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CancelLikePlanetPost(ctx context.Context, in *v14.CancelLikePlanetPostRequest, opts ...grpc.CallOption) (*v14.CancelLikePlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CancelLikePlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_CancelLikePlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) FavoritePlanetPost(ctx context.Context, in *v14.FavoritePlanetPostRequest, opts ...grpc.CallOption) (*v14.FavoritePlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.FavoritePlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_FavoritePlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CancelFavoritePlanetPost(ctx context.Context, in *v14.CancelFavoritePlanetPostRequest, opts ...grpc.CallOption) (*v14.CancelFavoritePlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CancelFavoritePlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_CancelFavoritePlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreatePlanetPost(ctx context.Context, in *v14.CreatePlanetPostRequest, opts ...grpc.CallOption) (*v14.CreatePlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreatePlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_CreatePlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UpdatePlanetPost(ctx context.Context, in *v14.UpdatePlanetPostRequest, opts ...grpc.CallOption) (*v14.UpdatePlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.UpdatePlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_UpdatePlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) DeletePlanetPost(ctx context.Context, in *v14.DeletePlanetPostRequest, opts ...grpc.CallOption) (*v14.DeletePlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.DeletePlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_DeletePlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ToppedPlanetPost(ctx context.Context, in *v14.ToppedPlanetPostRequest, opts ...grpc.CallOption) (*v14.ToppedPlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ToppedPlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_ToppedPlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ListPlanetPost(ctx context.Context, in *v14.ListPlanetPostRequest, opts ...grpc.CallOption) (*v14.ListPlanetPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListPlanetPostReply)
	err := c.cc.Invoke(ctx, LifeLog_ListPlanetPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ListPlanetTopPost(ctx context.Context, in *v14.ListPlanetTopPostRequest, opts ...grpc.CallOption) (*v14.ListPlanetTopPostReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListPlanetTopPostReply)
	err := c.cc.Invoke(ctx, LifeLog_ListPlanetTopPost_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) CreatePlanetPostComment(ctx context.Context, in *v14.CreatePlanetPostCommentRequest, opts ...grpc.CallOption) (*v14.CreatePlanetPostCommentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreatePlanetPostCommentReply)
	err := c.cc.Invoke(ctx, LifeLog_CreatePlanetPostComment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) DeletePlanetPostComment(ctx context.Context, in *v14.DeletePlanetPostCommentRequest, opts ...grpc.CallOption) (*v14.DeletePlanetPostCommentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.DeletePlanetPostCommentReply)
	err := c.cc.Invoke(ctx, LifeLog_DeletePlanetPostComment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) ListPlanetPostComment(ctx context.Context, in *v14.ListPlanetPostCommentRequest, opts ...grpc.CallOption) (*v14.ListPlanetPostCommentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListPlanetPostCommentReply)
	err := c.cc.Invoke(ctx, LifeLog_ListPlanetPostComment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) RunCronTask(ctx context.Context, in *v12.RunCronTaskRequest, opts ...grpc.CallOption) (*v12.RunCronTaskReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.RunCronTaskReply)
	err := c.cc.Invoke(ctx, LifeLog_RunCronTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) TodayStatisticFromHome(ctx context.Context, in *v15.TodayStatisticFromHomeRequest, opts ...grpc.CallOption) (*v15.TodayStatisticFromHomeReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v15.TodayStatisticFromHomeReply)
	err := c.cc.Invoke(ctx, LifeLog_TodayStatisticFromHome_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UserHabitStatisticFromDetail(ctx context.Context, in *v15.UserHabitStatisticFromDetailRequest, opts ...grpc.CallOption) (*v15.UserHabitStatisticFromDetailReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v15.UserHabitStatisticFromDetailReply)
	err := c.cc.Invoke(ctx, LifeLog_UserHabitStatisticFromDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lifeLogClient) UnreadCount(ctx context.Context, in *v16.UnreadCountRequest, opts ...grpc.CallOption) (*v16.UnreadCountReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v16.UnreadCountReply)
	err := c.cc.Invoke(ctx, LifeLog_UnreadCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LifeLogServer is the server API for LifeLog service.
// All implementations must embed UnimplementedLifeLogServer
// for forward compatibility.
type LifeLogServer interface {
	// 注册用户
	Register(context.Context, *v1.RegisterRequest) (*v1.RegisterReply, error)
	// 登录
	Login(context.Context, *v1.LoginRequest) (*v1.LoginReply, error)
	// 登出
	Logout(context.Context, *v1.LogoutRequest) (*v1.LogoutReply, error)
	// 推送 token 和设备信息
	PushToken(context.Context, *v1.PushTokenRequest) (*v1.PushTokenReply, error)
	// 忘记密码
	ForgetPassword(context.Context, *v1.ForgetPasswordRequest) (*v1.ForgetPasswordReply, error)
	// 修改密码
	ChangePassword(context.Context, *v1.ChangePasswordRequest) (*v1.ChangePasswordReply, error)
	// 删除用户
	DeleteUser(context.Context, *v1.DeleteUserRequest) (*v1.DeleteUserReply, error)
	// 修改手机号
	ChangePhone(context.Context, *v1.ChangePhoneRequest) (*v1.ChangePhoneReply, error)
	// 修改邮箱
	ChangeEmail(context.Context, *v1.ChangeEmailRequest) (*v1.ChangeEmailReply, error)
	// 获取用户资料
	GetUserProfile(context.Context, *v1.GetUserProfileRequest) (*v1.GetUserProfileReply, error)
	// 修改用户资料
	UpdateUserProfile(context.Context, *v1.UpdateUserProfileRequest) (*v1.UpdateUserProfileReply, error)
	// 关注
	FollowUser(context.Context, *v1.FollowUserRequest) (*v1.FollowUserReply, error)
	// 取消关注
	UnfollowUser(context.Context, *v1.UnfollowUserRequest) (*v1.UnfollowUserReply, error)
	// 刷新 token
	RefreshToken(context.Context, *v1.RefreshTokenRequest) (*v1.LoginReply, error)
	// 获取用户配置信息
	GetUserSetting(context.Context, *v1.GetUserSettingRequest) (*v1.GetUserSettingReply, error)
	// 修改用户配置信息
	UpdateUserAward(context.Context, *v1.UpdateUserAwardRequest) (*v1.UpdateUserAwardReply, error)
	// 修改用户隐私密码
	ChangeUserPrivacyPassword(context.Context, *v1.ChangeUserPrivacyPasswordRequest) (*v1.ChangeUserPrivacyPasswordReply, error)
	// 检查用户隐私密码
	CheckUserPrivacyPassword(context.Context, *v1.CheckUserPrivacyPasswordRequest) (*v1.CheckUserPrivacyPasswordReply, error)
	// 获取用户会员详情
	GetUserVipInfo(context.Context, *v1.GetUserVipInfoRequest) (*v1.GetUserVipInfoReply, error)
	// 搭子搜索
	BuddySearch(context.Context, *v1.BuddySearchRequest) (*v1.BuddySearchReply, error)
	// 发送搭子邀请
	BuddyInvitation(context.Context, *v1.BuddyInvitationRequest) (*v1.BuddyInvitationReply, error)
	// 获取网站信息
	GetSiteInfo(context.Context, *v11.GetSiteInfoRequest) (*v11.GetSiteInfoReply, error)
	// 健康检查接口
	HealthCheck(context.Context, *v11.HealthCheckRequest) (*v11.HealthCheckReply, error)
	// 获取七牛上传凭证
	CreateUpToken(context.Context, *v11.CreateUpTokenRequest) (*v11.CreateUpTokenReply, error)
	// 获取七牛下载地址
	CreateDownURL(context.Context, *v11.CreateDownURLRequest) (*v11.CreateDownURLReply, error)
	// 发送验证码
	SendVerifyCode(context.Context, *v11.SendVerifyCodeRequest) (*v11.SendVerifyCodeReply, error)
	// 获取每日随想
	ListMotiveMemo(context.Context, *v11.ListMotiveMemoRequest) (*v11.ListMotiveMemoReply, error)
	// 添加意见反馈
	CreateFeedback(context.Context, *v11.CreateFeedbackRequest) (*v11.CreateFeedbackReply, error)
	// 获取版本更新
	VersionCheck(context.Context, *v11.VersionCheckRequest) (*v11.VersionCheckReply, error)
	// 创建用户习惯
	CreateUserHabit(context.Context, *v12.CreateUserHabitRequest) (*v12.CreateUserHabitReply, error)
	// 获取用户习惯列表
	ListUserHabit(context.Context, *v12.ListUserHabitRequest) (*v12.ListUserHabitReply, error)
	// 获取用户习惯详情
	GetUserHabit(context.Context, *v12.GetUserHabitRequest) (*v12.GetUserHabitReply, error)
	// 更新用户习惯
	UpdateUserHabit(context.Context, *v12.UpdateUserHabitRequest) (*v12.UpdateUserHabitReply, error)
	// 删除用户习惯
	DeleteUserHabit(context.Context, *v12.DeleteUserHabitRequest) (*v12.DeleteUserHabitReply, error)
	// 暂停用户习惯
	PauseUserHabit(context.Context, *v12.PauseUserHabitRequest) (*v12.PauseUserHabitReply, error)
	// 恢复用户习惯
	RecoverUserHabit(context.Context, *v12.RecoverUserHabitRequest) (*v12.RecoverUserHabitReply, error)
	// 归档用户习惯
	ArchiveUserHabit(context.Context, *v12.ArchiveUserHabitRequest) (*v12.ArchiveUserHabitReply, error)
	// 获取用户每日习惯列表
	ListUserHabitSnapshot(context.Context, *v12.ListUserHabitSnapshotRequest) (*v12.ListUserHabitSnapshotReply, error)
	// 创建用户习惯想法
	CreateUserHabitMemo(context.Context, *v12.CreateUserHabitMemoRequest) (*v12.CreateUserHabitMemoReply, error)
	// 更新用户习惯想法
	UpdateUserHabitMemo(context.Context, *v12.UpdateUserHabitMemoRequest) (*v12.UpdateUserHabitMemoReply, error)
	// 删除用户习惯想法
	DeleteUserHabitMemo(context.Context, *v12.DeleteUserHabitMemoRequest) (*v12.DeleteUserHabitMemoReply, error)
	// 习惯打卡
	PunchUserHabit(context.Context, *v12.PunchUserHabitRequest) (*v12.PunchUserHabitReply, error)
	// 取消习惯打卡
	CancelPunchUserHabit(context.Context, *v12.CancelPunchUserHabitRequest) (*v12.CancelPunchUserHabitReply, error)
	// 更新习惯打卡
	UpdatePunchUserHabit(context.Context, *v12.UpdatePunchUserHabitRequest) (*v12.UpdatePunchUserHabitReply, error)
	// 习惯计时
	ReckonUserHabit(context.Context, *v12.ReckonUserHabitRequest) (*v12.ReckonUserHabitReply, error)
	// 取消习惯计时
	CancelReckonUserHabit(context.Context, *v12.CancelReckonUserHabitRequest) (*v12.CancelReckonUserHabitReply, error)
	// 创建用户习惯计时
	CreateUserHabitReckon(context.Context, *v12.CreateUserHabitReckonRequest) (*v12.CreateUserHabitReckonReply, error)
	// 更新用户习惯计时
	UpdateUserHabitReckon(context.Context, *v12.UpdateUserHabitReckonRequest) (*v12.UpdateUserHabitReckonReply, error)
	// 删除用户习惯计时
	DeleteUserHabitReckon(context.Context, *v12.DeleteUserHabitReckonRequest) (*v12.DeleteUserHabitReckonReply, error)
	// 创建星球
	CreatePlanet(context.Context, *v13.CreatePlanetRequest) (*v13.CreatePlanetReply, error)
	// 获取星球详情
	GetPlanet(context.Context, *v13.GetPlanetRequest) (*v13.GetPlanetReply, error)
	// 更新星球
	UpdatePlanet(context.Context, *v13.UpdatePlanetRequest) (*v13.UpdatePlanetReply, error)
	// 删除星球
	DeletePlanet(context.Context, *v13.DeletePlanetRequest) (*v13.DeletePlanetReply, error)
	// 创建星球目标
	CreatePlanetTarget(context.Context, *v13.CreatePlanetTargetRequest) (*v13.CreatePlanetTargetReply, error)
	// 获取星球目标详情
	GetPlanetTarget(context.Context, *v13.GetPlanetTargetRequest) (*v13.GetPlanetTargetReply, error)
	// 更新星球目标
	UpdatePlanetTarget(context.Context, *v13.UpdatePlanetTargetRequest) (*v13.UpdatePlanetTargetReply, error)
	// 删除星球目标
	DeletePlanetTarget(context.Context, *v13.DeletePlanetTargetRequest) (*v13.DeletePlanetTargetReply, error)
	// 获取星球目标列表
	ListPlanetTarget(context.Context, *v13.ListPlanetTargetRequest) (*v13.ListPlanetTargetReply, error)
	// 获取用户加入星球列表
	ListPlanetByUserID(context.Context, *v14.ListPlanetByUserIDRequest) (*v14.ListPlanetByUserIDReply, error)
	// 加入星球
	JoinPlanet(context.Context, *v14.JoinPlanetRequest) (*v14.JoinPlanetReply, error)
	// 退出星球
	QuitPlanet(context.Context, *v14.QuitPlanetRequest) (*v14.QuitPlanetReply, error)
	// 加入星球目标
	JoinPlanetTarget(context.Context, *v14.JoinPlanetTargetRequest) (*v14.JoinPlanetTargetReply, error)
	// 退出星球目标
	QuitPlanetTarget(context.Context, *v14.QuitPlanetTargetRequest) (*v14.QuitPlanetTargetReply, error)
	// 点赞星球动态
	LikePlanetPost(context.Context, *v14.LikePlanetPostRequest) (*v14.LikePlanetPostReply, error)
	// 取消点赞星球动态
	CancelLikePlanetPost(context.Context, *v14.CancelLikePlanetPostRequest) (*v14.CancelLikePlanetPostReply, error)
	// 收藏星球动态
	FavoritePlanetPost(context.Context, *v14.FavoritePlanetPostRequest) (*v14.FavoritePlanetPostReply, error)
	// 取消收藏星球动态
	CancelFavoritePlanetPost(context.Context, *v14.CancelFavoritePlanetPostRequest) (*v14.CancelFavoritePlanetPostReply, error)
	// 创建星球动态
	CreatePlanetPost(context.Context, *v14.CreatePlanetPostRequest) (*v14.CreatePlanetPostReply, error)
	// 修改星球动态
	UpdatePlanetPost(context.Context, *v14.UpdatePlanetPostRequest) (*v14.UpdatePlanetPostReply, error)
	// 删除星球动态
	DeletePlanetPost(context.Context, *v14.DeletePlanetPostRequest) (*v14.DeletePlanetPostReply, error)
	// 置顶星球动态
	ToppedPlanetPost(context.Context, *v14.ToppedPlanetPostRequest) (*v14.ToppedPlanetPostReply, error)
	// 获取星球动态列表
	ListPlanetPost(context.Context, *v14.ListPlanetPostRequest) (*v14.ListPlanetPostReply, error)
	// 获取星球热点动态列表
	ListPlanetTopPost(context.Context, *v14.ListPlanetTopPostRequest) (*v14.ListPlanetTopPostReply, error)
	// 创建动态评论
	CreatePlanetPostComment(context.Context, *v14.CreatePlanetPostCommentRequest) (*v14.CreatePlanetPostCommentReply, error)
	// 删除动态评论
	DeletePlanetPostComment(context.Context, *v14.DeletePlanetPostCommentRequest) (*v14.DeletePlanetPostCommentReply, error)
	// 获取动态评论列表
	ListPlanetPostComment(context.Context, *v14.ListPlanetPostCommentRequest) (*v14.ListPlanetPostCommentReply, error)
	// 运行定时任务
	RunCronTask(context.Context, *v12.RunCronTaskRequest) (*v12.RunCronTaskReply, error)
	// 数据统计
	// 获取首页每日数据
	TodayStatisticFromHome(context.Context, *v15.TodayStatisticFromHomeRequest) (*v15.TodayStatisticFromHomeReply, error)
	// 获取用户习惯详情统计数据
	UserHabitStatisticFromDetail(context.Context, *v15.UserHabitStatisticFromDetailRequest) (*v15.UserHabitStatisticFromDetailReply, error)
	UnreadCount(context.Context, *v16.UnreadCountRequest) (*v16.UnreadCountReply, error)
	mustEmbedUnimplementedLifeLogServer()
}

// UnimplementedLifeLogServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLifeLogServer struct{}

func (UnimplementedLifeLogServer) Register(context.Context, *v1.RegisterRequest) (*v1.RegisterReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Register not implemented")
}
func (UnimplementedLifeLogServer) Login(context.Context, *v1.LoginRequest) (*v1.LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedLifeLogServer) Logout(context.Context, *v1.LogoutRequest) (*v1.LogoutReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedLifeLogServer) PushToken(context.Context, *v1.PushTokenRequest) (*v1.PushTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushToken not implemented")
}
func (UnimplementedLifeLogServer) ForgetPassword(context.Context, *v1.ForgetPasswordRequest) (*v1.ForgetPasswordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForgetPassword not implemented")
}
func (UnimplementedLifeLogServer) ChangePassword(context.Context, *v1.ChangePasswordRequest) (*v1.ChangePasswordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePassword not implemented")
}
func (UnimplementedLifeLogServer) DeleteUser(context.Context, *v1.DeleteUserRequest) (*v1.DeleteUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUser not implemented")
}
func (UnimplementedLifeLogServer) ChangePhone(context.Context, *v1.ChangePhoneRequest) (*v1.ChangePhoneReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePhone not implemented")
}
func (UnimplementedLifeLogServer) ChangeEmail(context.Context, *v1.ChangeEmailRequest) (*v1.ChangeEmailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeEmail not implemented")
}
func (UnimplementedLifeLogServer) GetUserProfile(context.Context, *v1.GetUserProfileRequest) (*v1.GetUserProfileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserProfile not implemented")
}
func (UnimplementedLifeLogServer) UpdateUserProfile(context.Context, *v1.UpdateUserProfileRequest) (*v1.UpdateUserProfileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserProfile not implemented")
}
func (UnimplementedLifeLogServer) FollowUser(context.Context, *v1.FollowUserRequest) (*v1.FollowUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FollowUser not implemented")
}
func (UnimplementedLifeLogServer) UnfollowUser(context.Context, *v1.UnfollowUserRequest) (*v1.UnfollowUserReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnfollowUser not implemented")
}
func (UnimplementedLifeLogServer) RefreshToken(context.Context, *v1.RefreshTokenRequest) (*v1.LoginReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedLifeLogServer) GetUserSetting(context.Context, *v1.GetUserSettingRequest) (*v1.GetUserSettingReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserSetting not implemented")
}
func (UnimplementedLifeLogServer) UpdateUserAward(context.Context, *v1.UpdateUserAwardRequest) (*v1.UpdateUserAwardReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserAward not implemented")
}
func (UnimplementedLifeLogServer) ChangeUserPrivacyPassword(context.Context, *v1.ChangeUserPrivacyPasswordRequest) (*v1.ChangeUserPrivacyPasswordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeUserPrivacyPassword not implemented")
}
func (UnimplementedLifeLogServer) CheckUserPrivacyPassword(context.Context, *v1.CheckUserPrivacyPasswordRequest) (*v1.CheckUserPrivacyPasswordReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserPrivacyPassword not implemented")
}
func (UnimplementedLifeLogServer) GetUserVipInfo(context.Context, *v1.GetUserVipInfoRequest) (*v1.GetUserVipInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserVipInfo not implemented")
}
func (UnimplementedLifeLogServer) BuddySearch(context.Context, *v1.BuddySearchRequest) (*v1.BuddySearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuddySearch not implemented")
}
func (UnimplementedLifeLogServer) BuddyInvitation(context.Context, *v1.BuddyInvitationRequest) (*v1.BuddyInvitationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BuddyInvitation not implemented")
}
func (UnimplementedLifeLogServer) GetSiteInfo(context.Context, *v11.GetSiteInfoRequest) (*v11.GetSiteInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSiteInfo not implemented")
}
func (UnimplementedLifeLogServer) HealthCheck(context.Context, *v11.HealthCheckRequest) (*v11.HealthCheckReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedLifeLogServer) CreateUpToken(context.Context, *v11.CreateUpTokenRequest) (*v11.CreateUpTokenReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUpToken not implemented")
}
func (UnimplementedLifeLogServer) CreateDownURL(context.Context, *v11.CreateDownURLRequest) (*v11.CreateDownURLReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDownURL not implemented")
}
func (UnimplementedLifeLogServer) SendVerifyCode(context.Context, *v11.SendVerifyCodeRequest) (*v11.SendVerifyCodeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVerifyCode not implemented")
}
func (UnimplementedLifeLogServer) ListMotiveMemo(context.Context, *v11.ListMotiveMemoRequest) (*v11.ListMotiveMemoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMotiveMemo not implemented")
}
func (UnimplementedLifeLogServer) CreateFeedback(context.Context, *v11.CreateFeedbackRequest) (*v11.CreateFeedbackReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFeedback not implemented")
}
func (UnimplementedLifeLogServer) VersionCheck(context.Context, *v11.VersionCheckRequest) (*v11.VersionCheckReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VersionCheck not implemented")
}
func (UnimplementedLifeLogServer) CreateUserHabit(context.Context, *v12.CreateUserHabitRequest) (*v12.CreateUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserHabit not implemented")
}
func (UnimplementedLifeLogServer) ListUserHabit(context.Context, *v12.ListUserHabitRequest) (*v12.ListUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserHabit not implemented")
}
func (UnimplementedLifeLogServer) GetUserHabit(context.Context, *v12.GetUserHabitRequest) (*v12.GetUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserHabit not implemented")
}
func (UnimplementedLifeLogServer) UpdateUserHabit(context.Context, *v12.UpdateUserHabitRequest) (*v12.UpdateUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserHabit not implemented")
}
func (UnimplementedLifeLogServer) DeleteUserHabit(context.Context, *v12.DeleteUserHabitRequest) (*v12.DeleteUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserHabit not implemented")
}
func (UnimplementedLifeLogServer) PauseUserHabit(context.Context, *v12.PauseUserHabitRequest) (*v12.PauseUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PauseUserHabit not implemented")
}
func (UnimplementedLifeLogServer) RecoverUserHabit(context.Context, *v12.RecoverUserHabitRequest) (*v12.RecoverUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecoverUserHabit not implemented")
}
func (UnimplementedLifeLogServer) ArchiveUserHabit(context.Context, *v12.ArchiveUserHabitRequest) (*v12.ArchiveUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ArchiveUserHabit not implemented")
}
func (UnimplementedLifeLogServer) ListUserHabitSnapshot(context.Context, *v12.ListUserHabitSnapshotRequest) (*v12.ListUserHabitSnapshotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserHabitSnapshot not implemented")
}
func (UnimplementedLifeLogServer) CreateUserHabitMemo(context.Context, *v12.CreateUserHabitMemoRequest) (*v12.CreateUserHabitMemoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserHabitMemo not implemented")
}
func (UnimplementedLifeLogServer) UpdateUserHabitMemo(context.Context, *v12.UpdateUserHabitMemoRequest) (*v12.UpdateUserHabitMemoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserHabitMemo not implemented")
}
func (UnimplementedLifeLogServer) DeleteUserHabitMemo(context.Context, *v12.DeleteUserHabitMemoRequest) (*v12.DeleteUserHabitMemoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserHabitMemo not implemented")
}
func (UnimplementedLifeLogServer) PunchUserHabit(context.Context, *v12.PunchUserHabitRequest) (*v12.PunchUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PunchUserHabit not implemented")
}
func (UnimplementedLifeLogServer) CancelPunchUserHabit(context.Context, *v12.CancelPunchUserHabitRequest) (*v12.CancelPunchUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPunchUserHabit not implemented")
}
func (UnimplementedLifeLogServer) UpdatePunchUserHabit(context.Context, *v12.UpdatePunchUserHabitRequest) (*v12.UpdatePunchUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePunchUserHabit not implemented")
}
func (UnimplementedLifeLogServer) ReckonUserHabit(context.Context, *v12.ReckonUserHabitRequest) (*v12.ReckonUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReckonUserHabit not implemented")
}
func (UnimplementedLifeLogServer) CancelReckonUserHabit(context.Context, *v12.CancelReckonUserHabitRequest) (*v12.CancelReckonUserHabitReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelReckonUserHabit not implemented")
}
func (UnimplementedLifeLogServer) CreateUserHabitReckon(context.Context, *v12.CreateUserHabitReckonRequest) (*v12.CreateUserHabitReckonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserHabitReckon not implemented")
}
func (UnimplementedLifeLogServer) UpdateUserHabitReckon(context.Context, *v12.UpdateUserHabitReckonRequest) (*v12.UpdateUserHabitReckonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserHabitReckon not implemented")
}
func (UnimplementedLifeLogServer) DeleteUserHabitReckon(context.Context, *v12.DeleteUserHabitReckonRequest) (*v12.DeleteUserHabitReckonReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserHabitReckon not implemented")
}
func (UnimplementedLifeLogServer) CreatePlanet(context.Context, *v13.CreatePlanetRequest) (*v13.CreatePlanetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlanet not implemented")
}
func (UnimplementedLifeLogServer) GetPlanet(context.Context, *v13.GetPlanetRequest) (*v13.GetPlanetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanet not implemented")
}
func (UnimplementedLifeLogServer) UpdatePlanet(context.Context, *v13.UpdatePlanetRequest) (*v13.UpdatePlanetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlanet not implemented")
}
func (UnimplementedLifeLogServer) DeletePlanet(context.Context, *v13.DeletePlanetRequest) (*v13.DeletePlanetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePlanet not implemented")
}
func (UnimplementedLifeLogServer) CreatePlanetTarget(context.Context, *v13.CreatePlanetTargetRequest) (*v13.CreatePlanetTargetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlanetTarget not implemented")
}
func (UnimplementedLifeLogServer) GetPlanetTarget(context.Context, *v13.GetPlanetTargetRequest) (*v13.GetPlanetTargetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlanetTarget not implemented")
}
func (UnimplementedLifeLogServer) UpdatePlanetTarget(context.Context, *v13.UpdatePlanetTargetRequest) (*v13.UpdatePlanetTargetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlanetTarget not implemented")
}
func (UnimplementedLifeLogServer) DeletePlanetTarget(context.Context, *v13.DeletePlanetTargetRequest) (*v13.DeletePlanetTargetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePlanetTarget not implemented")
}
func (UnimplementedLifeLogServer) ListPlanetTarget(context.Context, *v13.ListPlanetTargetRequest) (*v13.ListPlanetTargetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlanetTarget not implemented")
}
func (UnimplementedLifeLogServer) ListPlanetByUserID(context.Context, *v14.ListPlanetByUserIDRequest) (*v14.ListPlanetByUserIDReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlanetByUserID not implemented")
}
func (UnimplementedLifeLogServer) JoinPlanet(context.Context, *v14.JoinPlanetRequest) (*v14.JoinPlanetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JoinPlanet not implemented")
}
func (UnimplementedLifeLogServer) QuitPlanet(context.Context, *v14.QuitPlanetRequest) (*v14.QuitPlanetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuitPlanet not implemented")
}
func (UnimplementedLifeLogServer) JoinPlanetTarget(context.Context, *v14.JoinPlanetTargetRequest) (*v14.JoinPlanetTargetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JoinPlanetTarget not implemented")
}
func (UnimplementedLifeLogServer) QuitPlanetTarget(context.Context, *v14.QuitPlanetTargetRequest) (*v14.QuitPlanetTargetReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuitPlanetTarget not implemented")
}
func (UnimplementedLifeLogServer) LikePlanetPost(context.Context, *v14.LikePlanetPostRequest) (*v14.LikePlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LikePlanetPost not implemented")
}
func (UnimplementedLifeLogServer) CancelLikePlanetPost(context.Context, *v14.CancelLikePlanetPostRequest) (*v14.CancelLikePlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelLikePlanetPost not implemented")
}
func (UnimplementedLifeLogServer) FavoritePlanetPost(context.Context, *v14.FavoritePlanetPostRequest) (*v14.FavoritePlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavoritePlanetPost not implemented")
}
func (UnimplementedLifeLogServer) CancelFavoritePlanetPost(context.Context, *v14.CancelFavoritePlanetPostRequest) (*v14.CancelFavoritePlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelFavoritePlanetPost not implemented")
}
func (UnimplementedLifeLogServer) CreatePlanetPost(context.Context, *v14.CreatePlanetPostRequest) (*v14.CreatePlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlanetPost not implemented")
}
func (UnimplementedLifeLogServer) UpdatePlanetPost(context.Context, *v14.UpdatePlanetPostRequest) (*v14.UpdatePlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePlanetPost not implemented")
}
func (UnimplementedLifeLogServer) DeletePlanetPost(context.Context, *v14.DeletePlanetPostRequest) (*v14.DeletePlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePlanetPost not implemented")
}
func (UnimplementedLifeLogServer) ToppedPlanetPost(context.Context, *v14.ToppedPlanetPostRequest) (*v14.ToppedPlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ToppedPlanetPost not implemented")
}
func (UnimplementedLifeLogServer) ListPlanetPost(context.Context, *v14.ListPlanetPostRequest) (*v14.ListPlanetPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlanetPost not implemented")
}
func (UnimplementedLifeLogServer) ListPlanetTopPost(context.Context, *v14.ListPlanetTopPostRequest) (*v14.ListPlanetTopPostReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlanetTopPost not implemented")
}
func (UnimplementedLifeLogServer) CreatePlanetPostComment(context.Context, *v14.CreatePlanetPostCommentRequest) (*v14.CreatePlanetPostCommentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePlanetPostComment not implemented")
}
func (UnimplementedLifeLogServer) DeletePlanetPostComment(context.Context, *v14.DeletePlanetPostCommentRequest) (*v14.DeletePlanetPostCommentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePlanetPostComment not implemented")
}
func (UnimplementedLifeLogServer) ListPlanetPostComment(context.Context, *v14.ListPlanetPostCommentRequest) (*v14.ListPlanetPostCommentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPlanetPostComment not implemented")
}
func (UnimplementedLifeLogServer) RunCronTask(context.Context, *v12.RunCronTaskRequest) (*v12.RunCronTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunCronTask not implemented")
}
func (UnimplementedLifeLogServer) TodayStatisticFromHome(context.Context, *v15.TodayStatisticFromHomeRequest) (*v15.TodayStatisticFromHomeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TodayStatisticFromHome not implemented")
}
func (UnimplementedLifeLogServer) UserHabitStatisticFromDetail(context.Context, *v15.UserHabitStatisticFromDetailRequest) (*v15.UserHabitStatisticFromDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserHabitStatisticFromDetail not implemented")
}
func (UnimplementedLifeLogServer) UnreadCount(context.Context, *v16.UnreadCountRequest) (*v16.UnreadCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnreadCount not implemented")
}
func (UnimplementedLifeLogServer) mustEmbedUnimplementedLifeLogServer() {}
func (UnimplementedLifeLogServer) testEmbeddedByValue()                 {}

// UnsafeLifeLogServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LifeLogServer will
// result in compilation errors.
type UnsafeLifeLogServer interface {
	mustEmbedUnimplementedLifeLogServer()
}

func RegisterLifeLogServer(s grpc.ServiceRegistrar, srv LifeLogServer) {
	// If the following call pancis, it indicates UnimplementedLifeLogServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&LifeLog_ServiceDesc, srv)
}

func _LifeLog_Register_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.RegisterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).Register(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_Register_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).Register(ctx, req.(*v1.RegisterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.LoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).Login(ctx, req.(*v1.LoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.LogoutRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_Logout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).Logout(ctx, req.(*v1.LogoutRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_PushToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.PushTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).PushToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_PushToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).PushToken(ctx, req.(*v1.PushTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ForgetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.ForgetPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ForgetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ForgetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ForgetPassword(ctx, req.(*v1.ForgetPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ChangePassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.ChangePasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ChangePassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ChangePassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ChangePassword(ctx, req.(*v1.ChangePasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_DeleteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.DeleteUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).DeleteUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_DeleteUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).DeleteUser(ctx, req.(*v1.DeleteUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ChangePhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.ChangePhoneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ChangePhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ChangePhone_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ChangePhone(ctx, req.(*v1.ChangePhoneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ChangeEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.ChangeEmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ChangeEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ChangeEmail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ChangeEmail(ctx, req.(*v1.ChangeEmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_GetUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GetUserProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).GetUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_GetUserProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).GetUserProfile(ctx, req.(*v1.GetUserProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdateUserProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.UpdateUserProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdateUserProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdateUserProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdateUserProfile(ctx, req.(*v1.UpdateUserProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_FollowUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.FollowUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).FollowUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_FollowUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).FollowUser(ctx, req.(*v1.FollowUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UnfollowUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.UnfollowUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UnfollowUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UnfollowUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UnfollowUser(ctx, req.(*v1.UnfollowUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.RefreshTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_RefreshToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).RefreshToken(ctx, req.(*v1.RefreshTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_GetUserSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GetUserSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).GetUserSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_GetUserSetting_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).GetUserSetting(ctx, req.(*v1.GetUserSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdateUserAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.UpdateUserAwardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdateUserAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdateUserAward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdateUserAward(ctx, req.(*v1.UpdateUserAwardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ChangeUserPrivacyPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.ChangeUserPrivacyPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ChangeUserPrivacyPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ChangeUserPrivacyPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ChangeUserPrivacyPassword(ctx, req.(*v1.ChangeUserPrivacyPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CheckUserPrivacyPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.CheckUserPrivacyPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CheckUserPrivacyPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CheckUserPrivacyPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CheckUserPrivacyPassword(ctx, req.(*v1.CheckUserPrivacyPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_GetUserVipInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.GetUserVipInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).GetUserVipInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_GetUserVipInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).GetUserVipInfo(ctx, req.(*v1.GetUserVipInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_BuddySearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.BuddySearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).BuddySearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_BuddySearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).BuddySearch(ctx, req.(*v1.BuddySearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_BuddyInvitation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.BuddyInvitationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).BuddyInvitation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_BuddyInvitation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).BuddyInvitation(ctx, req.(*v1.BuddyInvitationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_GetSiteInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.GetSiteInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).GetSiteInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_GetSiteInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).GetSiteInfo(ctx, req.(*v11.GetSiteInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).HealthCheck(ctx, req.(*v11.HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreateUpToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.CreateUpTokenRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreateUpToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreateUpToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreateUpToken(ctx, req.(*v11.CreateUpTokenRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreateDownURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.CreateDownURLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreateDownURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreateDownURL_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreateDownURL(ctx, req.(*v11.CreateDownURLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_SendVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.SendVerifyCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).SendVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_SendVerifyCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).SendVerifyCode(ctx, req.(*v11.SendVerifyCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ListMotiveMemo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.ListMotiveMemoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ListMotiveMemo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ListMotiveMemo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ListMotiveMemo(ctx, req.(*v11.ListMotiveMemoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreateFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.CreateFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreateFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreateFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreateFeedback(ctx, req.(*v11.CreateFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_VersionCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.VersionCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).VersionCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_VersionCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).VersionCheck(ctx, req.(*v11.VersionCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreateUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.CreateUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreateUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreateUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreateUserHabit(ctx, req.(*v12.CreateUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ListUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.ListUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ListUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ListUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ListUserHabit(ctx, req.(*v12.ListUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_GetUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.GetUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).GetUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_GetUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).GetUserHabit(ctx, req.(*v12.GetUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdateUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.UpdateUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdateUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdateUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdateUserHabit(ctx, req.(*v12.UpdateUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_DeleteUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.DeleteUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).DeleteUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_DeleteUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).DeleteUserHabit(ctx, req.(*v12.DeleteUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_PauseUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.PauseUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).PauseUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_PauseUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).PauseUserHabit(ctx, req.(*v12.PauseUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_RecoverUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.RecoverUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).RecoverUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_RecoverUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).RecoverUserHabit(ctx, req.(*v12.RecoverUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ArchiveUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.ArchiveUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ArchiveUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ArchiveUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ArchiveUserHabit(ctx, req.(*v12.ArchiveUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ListUserHabitSnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.ListUserHabitSnapshotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ListUserHabitSnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ListUserHabitSnapshot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ListUserHabitSnapshot(ctx, req.(*v12.ListUserHabitSnapshotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreateUserHabitMemo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.CreateUserHabitMemoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreateUserHabitMemo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreateUserHabitMemo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreateUserHabitMemo(ctx, req.(*v12.CreateUserHabitMemoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdateUserHabitMemo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.UpdateUserHabitMemoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdateUserHabitMemo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdateUserHabitMemo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdateUserHabitMemo(ctx, req.(*v12.UpdateUserHabitMemoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_DeleteUserHabitMemo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.DeleteUserHabitMemoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).DeleteUserHabitMemo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_DeleteUserHabitMemo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).DeleteUserHabitMemo(ctx, req.(*v12.DeleteUserHabitMemoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_PunchUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.PunchUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).PunchUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_PunchUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).PunchUserHabit(ctx, req.(*v12.PunchUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CancelPunchUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.CancelPunchUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CancelPunchUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CancelPunchUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CancelPunchUserHabit(ctx, req.(*v12.CancelPunchUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdatePunchUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.UpdatePunchUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdatePunchUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdatePunchUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdatePunchUserHabit(ctx, req.(*v12.UpdatePunchUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ReckonUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.ReckonUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ReckonUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ReckonUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ReckonUserHabit(ctx, req.(*v12.ReckonUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CancelReckonUserHabit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.CancelReckonUserHabitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CancelReckonUserHabit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CancelReckonUserHabit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CancelReckonUserHabit(ctx, req.(*v12.CancelReckonUserHabitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreateUserHabitReckon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.CreateUserHabitReckonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreateUserHabitReckon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreateUserHabitReckon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreateUserHabitReckon(ctx, req.(*v12.CreateUserHabitReckonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdateUserHabitReckon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.UpdateUserHabitReckonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdateUserHabitReckon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdateUserHabitReckon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdateUserHabitReckon(ctx, req.(*v12.UpdateUserHabitReckonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_DeleteUserHabitReckon_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.DeleteUserHabitReckonRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).DeleteUserHabitReckon(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_DeleteUserHabitReckon_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).DeleteUserHabitReckon(ctx, req.(*v12.DeleteUserHabitReckonRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreatePlanet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.CreatePlanetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreatePlanet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreatePlanet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreatePlanet(ctx, req.(*v13.CreatePlanetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_GetPlanet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.GetPlanetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).GetPlanet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_GetPlanet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).GetPlanet(ctx, req.(*v13.GetPlanetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdatePlanet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.UpdatePlanetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdatePlanet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdatePlanet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdatePlanet(ctx, req.(*v13.UpdatePlanetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_DeletePlanet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.DeletePlanetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).DeletePlanet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_DeletePlanet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).DeletePlanet(ctx, req.(*v13.DeletePlanetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreatePlanetTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.CreatePlanetTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreatePlanetTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreatePlanetTarget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreatePlanetTarget(ctx, req.(*v13.CreatePlanetTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_GetPlanetTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.GetPlanetTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).GetPlanetTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_GetPlanetTarget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).GetPlanetTarget(ctx, req.(*v13.GetPlanetTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdatePlanetTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.UpdatePlanetTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdatePlanetTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdatePlanetTarget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdatePlanetTarget(ctx, req.(*v13.UpdatePlanetTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_DeletePlanetTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.DeletePlanetTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).DeletePlanetTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_DeletePlanetTarget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).DeletePlanetTarget(ctx, req.(*v13.DeletePlanetTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ListPlanetTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.ListPlanetTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ListPlanetTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ListPlanetTarget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ListPlanetTarget(ctx, req.(*v13.ListPlanetTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ListPlanetByUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListPlanetByUserIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ListPlanetByUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ListPlanetByUserID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ListPlanetByUserID(ctx, req.(*v14.ListPlanetByUserIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_JoinPlanet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.JoinPlanetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).JoinPlanet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_JoinPlanet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).JoinPlanet(ctx, req.(*v14.JoinPlanetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_QuitPlanet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.QuitPlanetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).QuitPlanet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_QuitPlanet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).QuitPlanet(ctx, req.(*v14.QuitPlanetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_JoinPlanetTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.JoinPlanetTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).JoinPlanetTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_JoinPlanetTarget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).JoinPlanetTarget(ctx, req.(*v14.JoinPlanetTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_QuitPlanetTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.QuitPlanetTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).QuitPlanetTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_QuitPlanetTarget_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).QuitPlanetTarget(ctx, req.(*v14.QuitPlanetTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_LikePlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.LikePlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).LikePlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_LikePlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).LikePlanetPost(ctx, req.(*v14.LikePlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CancelLikePlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CancelLikePlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CancelLikePlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CancelLikePlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CancelLikePlanetPost(ctx, req.(*v14.CancelLikePlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_FavoritePlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.FavoritePlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).FavoritePlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_FavoritePlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).FavoritePlanetPost(ctx, req.(*v14.FavoritePlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CancelFavoritePlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CancelFavoritePlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CancelFavoritePlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CancelFavoritePlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CancelFavoritePlanetPost(ctx, req.(*v14.CancelFavoritePlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreatePlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreatePlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreatePlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreatePlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreatePlanetPost(ctx, req.(*v14.CreatePlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UpdatePlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.UpdatePlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UpdatePlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UpdatePlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UpdatePlanetPost(ctx, req.(*v14.UpdatePlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_DeletePlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.DeletePlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).DeletePlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_DeletePlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).DeletePlanetPost(ctx, req.(*v14.DeletePlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ToppedPlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ToppedPlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ToppedPlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ToppedPlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ToppedPlanetPost(ctx, req.(*v14.ToppedPlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ListPlanetPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListPlanetPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ListPlanetPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ListPlanetPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ListPlanetPost(ctx, req.(*v14.ListPlanetPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ListPlanetTopPost_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListPlanetTopPostRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ListPlanetTopPost(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ListPlanetTopPost_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ListPlanetTopPost(ctx, req.(*v14.ListPlanetTopPostRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_CreatePlanetPostComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreatePlanetPostCommentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).CreatePlanetPostComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_CreatePlanetPostComment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).CreatePlanetPostComment(ctx, req.(*v14.CreatePlanetPostCommentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_DeletePlanetPostComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.DeletePlanetPostCommentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).DeletePlanetPostComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_DeletePlanetPostComment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).DeletePlanetPostComment(ctx, req.(*v14.DeletePlanetPostCommentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_ListPlanetPostComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListPlanetPostCommentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).ListPlanetPostComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_ListPlanetPostComment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).ListPlanetPostComment(ctx, req.(*v14.ListPlanetPostCommentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_RunCronTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.RunCronTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).RunCronTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_RunCronTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).RunCronTask(ctx, req.(*v12.RunCronTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_TodayStatisticFromHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v15.TodayStatisticFromHomeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).TodayStatisticFromHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_TodayStatisticFromHome_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).TodayStatisticFromHome(ctx, req.(*v15.TodayStatisticFromHomeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UserHabitStatisticFromDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v15.UserHabitStatisticFromDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UserHabitStatisticFromDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UserHabitStatisticFromDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UserHabitStatisticFromDetail(ctx, req.(*v15.UserHabitStatisticFromDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LifeLog_UnreadCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v16.UnreadCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LifeLogServer).UnreadCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LifeLog_UnreadCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LifeLogServer).UnreadCount(ctx, req.(*v16.UnreadCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// LifeLog_ServiceDesc is the grpc.ServiceDesc for LifeLog service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LifeLog_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.LifeLog",
	HandlerType: (*LifeLogServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Register",
			Handler:    _LifeLog_Register_Handler,
		},
		{
			MethodName: "Login",
			Handler:    _LifeLog_Login_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _LifeLog_Logout_Handler,
		},
		{
			MethodName: "PushToken",
			Handler:    _LifeLog_PushToken_Handler,
		},
		{
			MethodName: "ForgetPassword",
			Handler:    _LifeLog_ForgetPassword_Handler,
		},
		{
			MethodName: "ChangePassword",
			Handler:    _LifeLog_ChangePassword_Handler,
		},
		{
			MethodName: "DeleteUser",
			Handler:    _LifeLog_DeleteUser_Handler,
		},
		{
			MethodName: "ChangePhone",
			Handler:    _LifeLog_ChangePhone_Handler,
		},
		{
			MethodName: "ChangeEmail",
			Handler:    _LifeLog_ChangeEmail_Handler,
		},
		{
			MethodName: "GetUserProfile",
			Handler:    _LifeLog_GetUserProfile_Handler,
		},
		{
			MethodName: "UpdateUserProfile",
			Handler:    _LifeLog_UpdateUserProfile_Handler,
		},
		{
			MethodName: "FollowUser",
			Handler:    _LifeLog_FollowUser_Handler,
		},
		{
			MethodName: "UnfollowUser",
			Handler:    _LifeLog_UnfollowUser_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _LifeLog_RefreshToken_Handler,
		},
		{
			MethodName: "GetUserSetting",
			Handler:    _LifeLog_GetUserSetting_Handler,
		},
		{
			MethodName: "UpdateUserAward",
			Handler:    _LifeLog_UpdateUserAward_Handler,
		},
		{
			MethodName: "ChangeUserPrivacyPassword",
			Handler:    _LifeLog_ChangeUserPrivacyPassword_Handler,
		},
		{
			MethodName: "CheckUserPrivacyPassword",
			Handler:    _LifeLog_CheckUserPrivacyPassword_Handler,
		},
		{
			MethodName: "GetUserVipInfo",
			Handler:    _LifeLog_GetUserVipInfo_Handler,
		},
		{
			MethodName: "BuddySearch",
			Handler:    _LifeLog_BuddySearch_Handler,
		},
		{
			MethodName: "BuddyInvitation",
			Handler:    _LifeLog_BuddyInvitation_Handler,
		},
		{
			MethodName: "GetSiteInfo",
			Handler:    _LifeLog_GetSiteInfo_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _LifeLog_HealthCheck_Handler,
		},
		{
			MethodName: "CreateUpToken",
			Handler:    _LifeLog_CreateUpToken_Handler,
		},
		{
			MethodName: "CreateDownURL",
			Handler:    _LifeLog_CreateDownURL_Handler,
		},
		{
			MethodName: "SendVerifyCode",
			Handler:    _LifeLog_SendVerifyCode_Handler,
		},
		{
			MethodName: "ListMotiveMemo",
			Handler:    _LifeLog_ListMotiveMemo_Handler,
		},
		{
			MethodName: "CreateFeedback",
			Handler:    _LifeLog_CreateFeedback_Handler,
		},
		{
			MethodName: "VersionCheck",
			Handler:    _LifeLog_VersionCheck_Handler,
		},
		{
			MethodName: "CreateUserHabit",
			Handler:    _LifeLog_CreateUserHabit_Handler,
		},
		{
			MethodName: "ListUserHabit",
			Handler:    _LifeLog_ListUserHabit_Handler,
		},
		{
			MethodName: "GetUserHabit",
			Handler:    _LifeLog_GetUserHabit_Handler,
		},
		{
			MethodName: "UpdateUserHabit",
			Handler:    _LifeLog_UpdateUserHabit_Handler,
		},
		{
			MethodName: "DeleteUserHabit",
			Handler:    _LifeLog_DeleteUserHabit_Handler,
		},
		{
			MethodName: "PauseUserHabit",
			Handler:    _LifeLog_PauseUserHabit_Handler,
		},
		{
			MethodName: "RecoverUserHabit",
			Handler:    _LifeLog_RecoverUserHabit_Handler,
		},
		{
			MethodName: "ArchiveUserHabit",
			Handler:    _LifeLog_ArchiveUserHabit_Handler,
		},
		{
			MethodName: "ListUserHabitSnapshot",
			Handler:    _LifeLog_ListUserHabitSnapshot_Handler,
		},
		{
			MethodName: "CreateUserHabitMemo",
			Handler:    _LifeLog_CreateUserHabitMemo_Handler,
		},
		{
			MethodName: "UpdateUserHabitMemo",
			Handler:    _LifeLog_UpdateUserHabitMemo_Handler,
		},
		{
			MethodName: "DeleteUserHabitMemo",
			Handler:    _LifeLog_DeleteUserHabitMemo_Handler,
		},
		{
			MethodName: "PunchUserHabit",
			Handler:    _LifeLog_PunchUserHabit_Handler,
		},
		{
			MethodName: "CancelPunchUserHabit",
			Handler:    _LifeLog_CancelPunchUserHabit_Handler,
		},
		{
			MethodName: "UpdatePunchUserHabit",
			Handler:    _LifeLog_UpdatePunchUserHabit_Handler,
		},
		{
			MethodName: "ReckonUserHabit",
			Handler:    _LifeLog_ReckonUserHabit_Handler,
		},
		{
			MethodName: "CancelReckonUserHabit",
			Handler:    _LifeLog_CancelReckonUserHabit_Handler,
		},
		{
			MethodName: "CreateUserHabitReckon",
			Handler:    _LifeLog_CreateUserHabitReckon_Handler,
		},
		{
			MethodName: "UpdateUserHabitReckon",
			Handler:    _LifeLog_UpdateUserHabitReckon_Handler,
		},
		{
			MethodName: "DeleteUserHabitReckon",
			Handler:    _LifeLog_DeleteUserHabitReckon_Handler,
		},
		{
			MethodName: "CreatePlanet",
			Handler:    _LifeLog_CreatePlanet_Handler,
		},
		{
			MethodName: "GetPlanet",
			Handler:    _LifeLog_GetPlanet_Handler,
		},
		{
			MethodName: "UpdatePlanet",
			Handler:    _LifeLog_UpdatePlanet_Handler,
		},
		{
			MethodName: "DeletePlanet",
			Handler:    _LifeLog_DeletePlanet_Handler,
		},
		{
			MethodName: "CreatePlanetTarget",
			Handler:    _LifeLog_CreatePlanetTarget_Handler,
		},
		{
			MethodName: "GetPlanetTarget",
			Handler:    _LifeLog_GetPlanetTarget_Handler,
		},
		{
			MethodName: "UpdatePlanetTarget",
			Handler:    _LifeLog_UpdatePlanetTarget_Handler,
		},
		{
			MethodName: "DeletePlanetTarget",
			Handler:    _LifeLog_DeletePlanetTarget_Handler,
		},
		{
			MethodName: "ListPlanetTarget",
			Handler:    _LifeLog_ListPlanetTarget_Handler,
		},
		{
			MethodName: "ListPlanetByUserID",
			Handler:    _LifeLog_ListPlanetByUserID_Handler,
		},
		{
			MethodName: "JoinPlanet",
			Handler:    _LifeLog_JoinPlanet_Handler,
		},
		{
			MethodName: "QuitPlanet",
			Handler:    _LifeLog_QuitPlanet_Handler,
		},
		{
			MethodName: "JoinPlanetTarget",
			Handler:    _LifeLog_JoinPlanetTarget_Handler,
		},
		{
			MethodName: "QuitPlanetTarget",
			Handler:    _LifeLog_QuitPlanetTarget_Handler,
		},
		{
			MethodName: "LikePlanetPost",
			Handler:    _LifeLog_LikePlanetPost_Handler,
		},
		{
			MethodName: "CancelLikePlanetPost",
			Handler:    _LifeLog_CancelLikePlanetPost_Handler,
		},
		{
			MethodName: "FavoritePlanetPost",
			Handler:    _LifeLog_FavoritePlanetPost_Handler,
		},
		{
			MethodName: "CancelFavoritePlanetPost",
			Handler:    _LifeLog_CancelFavoritePlanetPost_Handler,
		},
		{
			MethodName: "CreatePlanetPost",
			Handler:    _LifeLog_CreatePlanetPost_Handler,
		},
		{
			MethodName: "UpdatePlanetPost",
			Handler:    _LifeLog_UpdatePlanetPost_Handler,
		},
		{
			MethodName: "DeletePlanetPost",
			Handler:    _LifeLog_DeletePlanetPost_Handler,
		},
		{
			MethodName: "ToppedPlanetPost",
			Handler:    _LifeLog_ToppedPlanetPost_Handler,
		},
		{
			MethodName: "ListPlanetPost",
			Handler:    _LifeLog_ListPlanetPost_Handler,
		},
		{
			MethodName: "ListPlanetTopPost",
			Handler:    _LifeLog_ListPlanetTopPost_Handler,
		},
		{
			MethodName: "CreatePlanetPostComment",
			Handler:    _LifeLog_CreatePlanetPostComment_Handler,
		},
		{
			MethodName: "DeletePlanetPostComment",
			Handler:    _LifeLog_DeletePlanetPostComment_Handler,
		},
		{
			MethodName: "ListPlanetPostComment",
			Handler:    _LifeLog_ListPlanetPostComment_Handler,
		},
		{
			MethodName: "RunCronTask",
			Handler:    _LifeLog_RunCronTask_Handler,
		},
		{
			MethodName: "TodayStatisticFromHome",
			Handler:    _LifeLog_TodayStatisticFromHome_Handler,
		},
		{
			MethodName: "UserHabitStatisticFromDetail",
			Handler:    _LifeLog_UserHabitStatisticFromDetail_Handler,
		},
		{
			MethodName: "UnreadCount",
			Handler:    _LifeLog_UnreadCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api.proto",
}
