// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: user_message/v1/user_message.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UnreadCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnreadCountRequest) Reset() {
	*x = UnreadCountRequest{}
	mi := &file_user_message_v1_user_message_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnreadCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnreadCountRequest) ProtoMessage() {}

func (x *UnreadCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_message_v1_user_message_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnreadCountRequest.ProtoReflect.Descriptor instead.
func (*UnreadCountRequest) Descriptor() ([]byte, []int) {
	return file_user_message_v1_user_message_proto_rawDescGZIP(), []int{0}
}

type UnreadCountReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *UnreadCountReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnreadCountReply) Reset() {
	*x = UnreadCountReply{}
	mi := &file_user_message_v1_user_message_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnreadCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnreadCountReply) ProtoMessage() {}

func (x *UnreadCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_message_v1_user_message_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnreadCountReply.ProtoReflect.Descriptor instead.
func (*UnreadCountReply) Descriptor() ([]byte, []int) {
	return file_user_message_v1_user_message_proto_rawDescGZIP(), []int{1}
}

func (x *UnreadCountReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UnreadCountReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UnreadCountReply) GetData() *UnreadCountReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type UnreadCountReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UnreadCount   int32                  `protobuf:"varint,1,opt,name=unread_count,json=unreadCount,proto3" json:"unread_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnreadCountReply_Data) Reset() {
	*x = UnreadCountReply_Data{}
	mi := &file_user_message_v1_user_message_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnreadCountReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnreadCountReply_Data) ProtoMessage() {}

func (x *UnreadCountReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_message_v1_user_message_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnreadCountReply_Data.ProtoReflect.Descriptor instead.
func (*UnreadCountReply_Data) Descriptor() ([]byte, []int) {
	return file_user_message_v1_user_message_proto_rawDescGZIP(), []int{1, 0}
}

func (x *UnreadCountReply_Data) GetUnreadCount() int32 {
	if x != nil {
		return x.UnreadCount
	}
	return 0
}

var File_user_message_v1_user_message_proto protoreflect.FileDescriptor

const file_user_message_v1_user_message_proto_rawDesc = "" +
	"\n" +
	"\"user_message/v1/user_message.proto\x12\x0fuser_message_v1\"\x14\n" +
	"\x12UnreadCountRequest\"\x9f\x01\n" +
	"\x10UnreadCountReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12:\n" +
	"\x04data\x18\x03 \x01(\v2&.user_message_v1.UnreadCountReply.DataR\x04data\x1a)\n" +
	"\x04Data\x12!\n" +
	"\funread_count\x18\x01 \x01(\x05R\vunreadCountB5Z3github.com/wlnil/life-log-be/api/user_message/v1;v1b\x06proto3"

var (
	file_user_message_v1_user_message_proto_rawDescOnce sync.Once
	file_user_message_v1_user_message_proto_rawDescData []byte
)

func file_user_message_v1_user_message_proto_rawDescGZIP() []byte {
	file_user_message_v1_user_message_proto_rawDescOnce.Do(func() {
		file_user_message_v1_user_message_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_message_v1_user_message_proto_rawDesc), len(file_user_message_v1_user_message_proto_rawDesc)))
	})
	return file_user_message_v1_user_message_proto_rawDescData
}

var file_user_message_v1_user_message_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_user_message_v1_user_message_proto_goTypes = []any{
	(*UnreadCountRequest)(nil),    // 0: user_message_v1.UnreadCountRequest
	(*UnreadCountReply)(nil),      // 1: user_message_v1.UnreadCountReply
	(*UnreadCountReply_Data)(nil), // 2: user_message_v1.UnreadCountReply.Data
}
var file_user_message_v1_user_message_proto_depIdxs = []int32{
	2, // 0: user_message_v1.UnreadCountReply.data:type_name -> user_message_v1.UnreadCountReply.Data
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_user_message_v1_user_message_proto_init() }
func file_user_message_v1_user_message_proto_init() {
	if File_user_message_v1_user_message_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_message_v1_user_message_proto_rawDesc), len(file_user_message_v1_user_message_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_message_v1_user_message_proto_goTypes,
		DependencyIndexes: file_user_message_v1_user_message_proto_depIdxs,
		MessageInfos:      file_user_message_v1_user_message_proto_msgTypes,
	}.Build()
	File_user_message_v1_user_message_proto = out.File
	file_user_message_v1_user_message_proto_goTypes = nil
	file_user_message_v1_user_message_proto_depIdxs = nil
}
