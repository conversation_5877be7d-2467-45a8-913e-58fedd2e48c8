// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: user_message/v1/user_message.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UnreadCountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnreadCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnreadCountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnreadCountRequestMultiError, or nil if none found.
func (m *UnreadCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnreadCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UnreadCountRequestMultiError(errors)
	}

	return nil
}

// UnreadCountRequestMultiError is an error wrapping multiple validation errors
// returned by UnreadCountRequest.ValidateAll() if the designated constraints
// aren't met.
type UnreadCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnreadCountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnreadCountRequestMultiError) AllErrors() []error { return m }

// UnreadCountRequestValidationError is the validation error returned by
// UnreadCountRequest.Validate if the designated constraints aren't met.
type UnreadCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnreadCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnreadCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnreadCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnreadCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnreadCountRequestValidationError) ErrorName() string {
	return "UnreadCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UnreadCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnreadCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnreadCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnreadCountRequestValidationError{}

// Validate checks the field values on UnreadCountReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnreadCountReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnreadCountReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnreadCountReplyMultiError, or nil if none found.
func (m *UnreadCountReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UnreadCountReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnreadCountReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnreadCountReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnreadCountReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnreadCountReplyMultiError(errors)
	}

	return nil
}

// UnreadCountReplyMultiError is an error wrapping multiple validation errors
// returned by UnreadCountReply.ValidateAll() if the designated constraints
// aren't met.
type UnreadCountReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnreadCountReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnreadCountReplyMultiError) AllErrors() []error { return m }

// UnreadCountReplyValidationError is the validation error returned by
// UnreadCountReply.Validate if the designated constraints aren't met.
type UnreadCountReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnreadCountReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnreadCountReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnreadCountReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnreadCountReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnreadCountReplyValidationError) ErrorName() string { return "UnreadCountReplyValidationError" }

// Error satisfies the builtin error interface
func (e UnreadCountReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnreadCountReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnreadCountReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnreadCountReplyValidationError{}

// Validate checks the field values on UnreadCountReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnreadCountReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnreadCountReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnreadCountReply_DataMultiError, or nil if none found.
func (m *UnreadCountReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *UnreadCountReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UnreadCount

	if len(errors) > 0 {
		return UnreadCountReply_DataMultiError(errors)
	}

	return nil
}

// UnreadCountReply_DataMultiError is an error wrapping multiple validation
// errors returned by UnreadCountReply_Data.ValidateAll() if the designated
// constraints aren't met.
type UnreadCountReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnreadCountReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnreadCountReply_DataMultiError) AllErrors() []error { return m }

// UnreadCountReply_DataValidationError is the validation error returned by
// UnreadCountReply_Data.Validate if the designated constraints aren't met.
type UnreadCountReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnreadCountReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnreadCountReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnreadCountReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnreadCountReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnreadCountReply_DataValidationError) ErrorName() string {
	return "UnreadCountReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e UnreadCountReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnreadCountReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnreadCountReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnreadCountReply_DataValidationError{}
