// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: site/v1/health.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 健康检查请求
type HealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckRequest) Reset() {
	*x = HealthCheckRequest{}
	mi := &file_site_v1_health_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckRequest) ProtoMessage() {}

func (x *HealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_health_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckRequest.ProtoReflect.Descriptor instead.
func (*HealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_site_v1_health_proto_rawDescGZIP(), []int{0}
}

// 健康检查响应
type HealthCheckReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *HealthCheckReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReply) Reset() {
	*x = HealthCheckReply{}
	mi := &file_site_v1_health_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReply) ProtoMessage() {}

func (x *HealthCheckReply) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_health_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReply.ProtoReflect.Descriptor instead.
func (*HealthCheckReply) Descriptor() ([]byte, []int) {
	return file_site_v1_health_proto_rawDescGZIP(), []int{1}
}

func (x *HealthCheckReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HealthCheckReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *HealthCheckReply) GetData() *HealthCheckReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type HealthCheckReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timestamp     int64                  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                             // 当前时间戳（秒）
	FormattedTime string                 `protobuf:"bytes,2,opt,name=formatted_time,json=formattedTime,proto3" json:"formatted_time,omitempty"` // 格式化的时间字符串
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HealthCheckReply_Data) Reset() {
	*x = HealthCheckReply_Data{}
	mi := &file_site_v1_health_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HealthCheckReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheckReply_Data) ProtoMessage() {}

func (x *HealthCheckReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_health_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheckReply_Data.ProtoReflect.Descriptor instead.
func (*HealthCheckReply_Data) Descriptor() ([]byte, []int) {
	return file_site_v1_health_proto_rawDescGZIP(), []int{1, 0}
}

func (x *HealthCheckReply_Data) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *HealthCheckReply_Data) GetFormattedTime() string {
	if x != nil {
		return x.FormattedTime
	}
	return ""
}

var File_site_v1_health_proto protoreflect.FileDescriptor

const file_site_v1_health_proto_rawDesc = "" +
	"\n" +
	"\x14site/v1/health.proto\x12\asite_v1\"\x14\n" +
	"\x12HealthCheckRequest\"\xb9\x01\n" +
	"\x10HealthCheckReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x122\n" +
	"\x04data\x18\x03 \x01(\v2\x1e.site_v1.HealthCheckReply.DataR\x04data\x1aK\n" +
	"\x04Data\x12\x1c\n" +
	"\ttimestamp\x18\x01 \x01(\x03R\ttimestamp\x12%\n" +
	"\x0eformatted_time\x18\x02 \x01(\tR\rformattedTimeB-Z+github.com/wlnil/life-log-be/api/site/v1;v1b\x06proto3"

var (
	file_site_v1_health_proto_rawDescOnce sync.Once
	file_site_v1_health_proto_rawDescData []byte
)

func file_site_v1_health_proto_rawDescGZIP() []byte {
	file_site_v1_health_proto_rawDescOnce.Do(func() {
		file_site_v1_health_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_site_v1_health_proto_rawDesc), len(file_site_v1_health_proto_rawDesc)))
	})
	return file_site_v1_health_proto_rawDescData
}

var file_site_v1_health_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_site_v1_health_proto_goTypes = []any{
	(*HealthCheckRequest)(nil),    // 0: site_v1.HealthCheckRequest
	(*HealthCheckReply)(nil),      // 1: site_v1.HealthCheckReply
	(*HealthCheckReply_Data)(nil), // 2: site_v1.HealthCheckReply.Data
}
var file_site_v1_health_proto_depIdxs = []int32{
	2, // 0: site_v1.HealthCheckReply.data:type_name -> site_v1.HealthCheckReply.Data
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_site_v1_health_proto_init() }
func file_site_v1_health_proto_init() {
	if File_site_v1_health_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_site_v1_health_proto_rawDesc), len(file_site_v1_health_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_site_v1_health_proto_goTypes,
		DependencyIndexes: file_site_v1_health_proto_depIdxs,
		MessageInfos:      file_site_v1_health_proto_msgTypes,
	}.Build()
	File_site_v1_health_proto = out.File
	file_site_v1_health_proto_goTypes = nil
	file_site_v1_health_proto_depIdxs = nil
}
