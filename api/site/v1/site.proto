syntax = "proto3";

package site_v1;

option go_package = "github.com/wlnil/life-log-be/api/site/v1;v1";

message GetSiteInfoRequest {}
message GetSiteInfoReply {
  message Data {
    string version = 1;
    string agreement = 2;
    string privacy_policy = 3;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message CreateUpTokenRequest {
  string scene = 1; // 场景
}
message CreateUpTokenReply {
  message Data {
    string token = 1; // 上传凭证
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message CreateDownURLRequest {
  string key = 1; // 文件名
  string scene = 2; // 场景
}
message CreateDownURLReply {
  message Data {
    string url = 1; // 下载 URL
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message SendVerifyCodeRequest {
  string email = 1;
  string phone = 2;
  string scene = 3;
}
message SendVerifyCodeReply {
  int32 code = 1;
  string msg = 2;
}

message ListMotiveMemoRequest {}
message ListMotiveMemoReply {
  message Data {
    repeated Motive motives = 1;
  }

  message Motive {
    int32 id = 1;
    string content = 2;
    string from = 3;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message CreateFeedbackRequest {
  string content = 1;
  string email = 2;
  repeated string imgs = 3;
  string nickname = 4;
}
message CreateFeedbackReply {
  int32 code = 1;
  string msg = 2;
}

message VersionCheckRequest {
  string current_version = 1;
  string platform = 2;
}
message VersionCheckReply {
  message Data {
    bool hasUpdate = 1;
    bool forceUpdate = 2;
    string latestVersion = 3;
    string currentVersion = 4;
    string updateTitle = 5;
    string updateContent = 6;
    string downloadUrl = 7;
    string iosAppStoreUrl = 8;
    string androidDirectUrl = 9;
    uint32 fileSize = 10;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}
