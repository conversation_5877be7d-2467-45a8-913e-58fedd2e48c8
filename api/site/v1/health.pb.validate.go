// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: site/v1/health.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on HealthCheckRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HealthCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HealthCheckRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HealthCheckRequestMultiError, or nil if none found.
func (m *HealthCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HealthCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HealthCheckRequestMultiError(errors)
	}

	return nil
}

// HealthCheckRequestMultiError is an error wrapping multiple validation errors
// returned by HealthCheckRequest.ValidateAll() if the designated constraints
// aren't met.
type HealthCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HealthCheckRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HealthCheckRequestMultiError) AllErrors() []error { return m }

// HealthCheckRequestValidationError is the validation error returned by
// HealthCheckRequest.Validate if the designated constraints aren't met.
type HealthCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HealthCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HealthCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HealthCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HealthCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HealthCheckRequestValidationError) ErrorName() string {
	return "HealthCheckRequestValidationError"
}

// Error satisfies the builtin error interface
func (e HealthCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHealthCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HealthCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HealthCheckRequestValidationError{}

// Validate checks the field values on HealthCheckReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HealthCheckReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HealthCheckReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HealthCheckReplyMultiError, or nil if none found.
func (m *HealthCheckReply) ValidateAll() error {
	return m.validate(true)
}

func (m *HealthCheckReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HealthCheckReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HealthCheckReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HealthCheckReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HealthCheckReplyMultiError(errors)
	}

	return nil
}

// HealthCheckReplyMultiError is an error wrapping multiple validation errors
// returned by HealthCheckReply.ValidateAll() if the designated constraints
// aren't met.
type HealthCheckReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HealthCheckReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HealthCheckReplyMultiError) AllErrors() []error { return m }

// HealthCheckReplyValidationError is the validation error returned by
// HealthCheckReply.Validate if the designated constraints aren't met.
type HealthCheckReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HealthCheckReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HealthCheckReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HealthCheckReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HealthCheckReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HealthCheckReplyValidationError) ErrorName() string { return "HealthCheckReplyValidationError" }

// Error satisfies the builtin error interface
func (e HealthCheckReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHealthCheckReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HealthCheckReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HealthCheckReplyValidationError{}

// Validate checks the field values on HealthCheckReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HealthCheckReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HealthCheckReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HealthCheckReply_DataMultiError, or nil if none found.
func (m *HealthCheckReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *HealthCheckReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Timestamp

	// no validation rules for FormattedTime

	if len(errors) > 0 {
		return HealthCheckReply_DataMultiError(errors)
	}

	return nil
}

// HealthCheckReply_DataMultiError is an error wrapping multiple validation
// errors returned by HealthCheckReply_Data.ValidateAll() if the designated
// constraints aren't met.
type HealthCheckReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HealthCheckReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HealthCheckReply_DataMultiError) AllErrors() []error { return m }

// HealthCheckReply_DataValidationError is the validation error returned by
// HealthCheckReply_Data.Validate if the designated constraints aren't met.
type HealthCheckReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HealthCheckReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HealthCheckReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HealthCheckReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HealthCheckReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HealthCheckReply_DataValidationError) ErrorName() string {
	return "HealthCheckReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e HealthCheckReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHealthCheckReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HealthCheckReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HealthCheckReply_DataValidationError{}
