// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: site/v1/site.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetSiteInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSiteInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSiteInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSiteInfoRequestMultiError, or nil if none found.
func (m *GetSiteInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSiteInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetSiteInfoRequestMultiError(errors)
	}

	return nil
}

// GetSiteInfoRequestMultiError is an error wrapping multiple validation errors
// returned by GetSiteInfoRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSiteInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSiteInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSiteInfoRequestMultiError) AllErrors() []error { return m }

// GetSiteInfoRequestValidationError is the validation error returned by
// GetSiteInfoRequest.Validate if the designated constraints aren't met.
type GetSiteInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSiteInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSiteInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSiteInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSiteInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSiteInfoRequestValidationError) ErrorName() string {
	return "GetSiteInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSiteInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSiteInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSiteInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSiteInfoRequestValidationError{}

// Validate checks the field values on GetSiteInfoReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSiteInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSiteInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSiteInfoReplyMultiError, or nil if none found.
func (m *GetSiteInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSiteInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSiteInfoReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSiteInfoReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSiteInfoReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSiteInfoReplyMultiError(errors)
	}

	return nil
}

// GetSiteInfoReplyMultiError is an error wrapping multiple validation errors
// returned by GetSiteInfoReply.ValidateAll() if the designated constraints
// aren't met.
type GetSiteInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSiteInfoReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSiteInfoReplyMultiError) AllErrors() []error { return m }

// GetSiteInfoReplyValidationError is the validation error returned by
// GetSiteInfoReply.Validate if the designated constraints aren't met.
type GetSiteInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSiteInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSiteInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSiteInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSiteInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSiteInfoReplyValidationError) ErrorName() string { return "GetSiteInfoReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetSiteInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSiteInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSiteInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSiteInfoReplyValidationError{}

// Validate checks the field values on CreateUpTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUpTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUpTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUpTokenRequestMultiError, or nil if none found.
func (m *CreateUpTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Scene

	if len(errors) > 0 {
		return CreateUpTokenRequestMultiError(errors)
	}

	return nil
}

// CreateUpTokenRequestMultiError is an error wrapping multiple validation
// errors returned by CreateUpTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateUpTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpTokenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpTokenRequestMultiError) AllErrors() []error { return m }

// CreateUpTokenRequestValidationError is the validation error returned by
// CreateUpTokenRequest.Validate if the designated constraints aren't met.
type CreateUpTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUpTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpTokenRequestValidationError) ErrorName() string {
	return "CreateUpTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpTokenRequestValidationError{}

// Validate checks the field values on CreateUpTokenReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUpTokenReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUpTokenReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUpTokenReplyMultiError, or nil if none found.
func (m *CreateUpTokenReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpTokenReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUpTokenReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUpTokenReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUpTokenReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateUpTokenReplyMultiError(errors)
	}

	return nil
}

// CreateUpTokenReplyMultiError is an error wrapping multiple validation errors
// returned by CreateUpTokenReply.ValidateAll() if the designated constraints
// aren't met.
type CreateUpTokenReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpTokenReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpTokenReplyMultiError) AllErrors() []error { return m }

// CreateUpTokenReplyValidationError is the validation error returned by
// CreateUpTokenReply.Validate if the designated constraints aren't met.
type CreateUpTokenReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpTokenReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpTokenReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUpTokenReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpTokenReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpTokenReplyValidationError) ErrorName() string {
	return "CreateUpTokenReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpTokenReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpTokenReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpTokenReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpTokenReplyValidationError{}

// Validate checks the field values on CreateDownURLRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDownURLRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDownURLRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDownURLRequestMultiError, or nil if none found.
func (m *CreateDownURLRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDownURLRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Key

	// no validation rules for Scene

	if len(errors) > 0 {
		return CreateDownURLRequestMultiError(errors)
	}

	return nil
}

// CreateDownURLRequestMultiError is an error wrapping multiple validation
// errors returned by CreateDownURLRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateDownURLRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDownURLRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDownURLRequestMultiError) AllErrors() []error { return m }

// CreateDownURLRequestValidationError is the validation error returned by
// CreateDownURLRequest.Validate if the designated constraints aren't met.
type CreateDownURLRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDownURLRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDownURLRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDownURLRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDownURLRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDownURLRequestValidationError) ErrorName() string {
	return "CreateDownURLRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDownURLRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDownURLRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDownURLRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDownURLRequestValidationError{}

// Validate checks the field values on CreateDownURLReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDownURLReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDownURLReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDownURLReplyMultiError, or nil if none found.
func (m *CreateDownURLReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDownURLReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDownURLReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDownURLReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDownURLReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDownURLReplyMultiError(errors)
	}

	return nil
}

// CreateDownURLReplyMultiError is an error wrapping multiple validation errors
// returned by CreateDownURLReply.ValidateAll() if the designated constraints
// aren't met.
type CreateDownURLReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDownURLReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDownURLReplyMultiError) AllErrors() []error { return m }

// CreateDownURLReplyValidationError is the validation error returned by
// CreateDownURLReply.Validate if the designated constraints aren't met.
type CreateDownURLReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDownURLReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDownURLReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDownURLReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDownURLReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDownURLReplyValidationError) ErrorName() string {
	return "CreateDownURLReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDownURLReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDownURLReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDownURLReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDownURLReplyValidationError{}

// Validate checks the field values on SendVerifyCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendVerifyCodeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendVerifyCodeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendVerifyCodeRequestMultiError, or nil if none found.
func (m *SendVerifyCodeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendVerifyCodeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Phone

	// no validation rules for Scene

	if len(errors) > 0 {
		return SendVerifyCodeRequestMultiError(errors)
	}

	return nil
}

// SendVerifyCodeRequestMultiError is an error wrapping multiple validation
// errors returned by SendVerifyCodeRequest.ValidateAll() if the designated
// constraints aren't met.
type SendVerifyCodeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendVerifyCodeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendVerifyCodeRequestMultiError) AllErrors() []error { return m }

// SendVerifyCodeRequestValidationError is the validation error returned by
// SendVerifyCodeRequest.Validate if the designated constraints aren't met.
type SendVerifyCodeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendVerifyCodeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendVerifyCodeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendVerifyCodeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendVerifyCodeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendVerifyCodeRequestValidationError) ErrorName() string {
	return "SendVerifyCodeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendVerifyCodeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendVerifyCodeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendVerifyCodeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendVerifyCodeRequestValidationError{}

// Validate checks the field values on SendVerifyCodeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendVerifyCodeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendVerifyCodeReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendVerifyCodeReplyMultiError, or nil if none found.
func (m *SendVerifyCodeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SendVerifyCodeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return SendVerifyCodeReplyMultiError(errors)
	}

	return nil
}

// SendVerifyCodeReplyMultiError is an error wrapping multiple validation
// errors returned by SendVerifyCodeReply.ValidateAll() if the designated
// constraints aren't met.
type SendVerifyCodeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendVerifyCodeReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendVerifyCodeReplyMultiError) AllErrors() []error { return m }

// SendVerifyCodeReplyValidationError is the validation error returned by
// SendVerifyCodeReply.Validate if the designated constraints aren't met.
type SendVerifyCodeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendVerifyCodeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendVerifyCodeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendVerifyCodeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendVerifyCodeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendVerifyCodeReplyValidationError) ErrorName() string {
	return "SendVerifyCodeReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SendVerifyCodeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendVerifyCodeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendVerifyCodeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendVerifyCodeReplyValidationError{}

// Validate checks the field values on ListMotiveMemoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMotiveMemoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMotiveMemoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMotiveMemoRequestMultiError, or nil if none found.
func (m *ListMotiveMemoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMotiveMemoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListMotiveMemoRequestMultiError(errors)
	}

	return nil
}

// ListMotiveMemoRequestMultiError is an error wrapping multiple validation
// errors returned by ListMotiveMemoRequest.ValidateAll() if the designated
// constraints aren't met.
type ListMotiveMemoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMotiveMemoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMotiveMemoRequestMultiError) AllErrors() []error { return m }

// ListMotiveMemoRequestValidationError is the validation error returned by
// ListMotiveMemoRequest.Validate if the designated constraints aren't met.
type ListMotiveMemoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMotiveMemoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMotiveMemoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMotiveMemoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMotiveMemoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMotiveMemoRequestValidationError) ErrorName() string {
	return "ListMotiveMemoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMotiveMemoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMotiveMemoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMotiveMemoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMotiveMemoRequestValidationError{}

// Validate checks the field values on ListMotiveMemoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMotiveMemoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMotiveMemoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMotiveMemoReplyMultiError, or nil if none found.
func (m *ListMotiveMemoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMotiveMemoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMotiveMemoReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMotiveMemoReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMotiveMemoReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMotiveMemoReplyMultiError(errors)
	}

	return nil
}

// ListMotiveMemoReplyMultiError is an error wrapping multiple validation
// errors returned by ListMotiveMemoReply.ValidateAll() if the designated
// constraints aren't met.
type ListMotiveMemoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMotiveMemoReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMotiveMemoReplyMultiError) AllErrors() []error { return m }

// ListMotiveMemoReplyValidationError is the validation error returned by
// ListMotiveMemoReply.Validate if the designated constraints aren't met.
type ListMotiveMemoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMotiveMemoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMotiveMemoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMotiveMemoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMotiveMemoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMotiveMemoReplyValidationError) ErrorName() string {
	return "ListMotiveMemoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListMotiveMemoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMotiveMemoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMotiveMemoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMotiveMemoReplyValidationError{}

// Validate checks the field values on CreateFeedbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFeedbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFeedbackRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFeedbackRequestMultiError, or nil if none found.
func (m *CreateFeedbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFeedbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	// no validation rules for Email

	// no validation rules for Nickname

	if len(errors) > 0 {
		return CreateFeedbackRequestMultiError(errors)
	}

	return nil
}

// CreateFeedbackRequestMultiError is an error wrapping multiple validation
// errors returned by CreateFeedbackRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateFeedbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFeedbackRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFeedbackRequestMultiError) AllErrors() []error { return m }

// CreateFeedbackRequestValidationError is the validation error returned by
// CreateFeedbackRequest.Validate if the designated constraints aren't met.
type CreateFeedbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFeedbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFeedbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFeedbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFeedbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFeedbackRequestValidationError) ErrorName() string {
	return "CreateFeedbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFeedbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFeedbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFeedbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFeedbackRequestValidationError{}

// Validate checks the field values on CreateFeedbackReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFeedbackReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFeedbackReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFeedbackReplyMultiError, or nil if none found.
func (m *CreateFeedbackReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFeedbackReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreateFeedbackReplyMultiError(errors)
	}

	return nil
}

// CreateFeedbackReplyMultiError is an error wrapping multiple validation
// errors returned by CreateFeedbackReply.ValidateAll() if the designated
// constraints aren't met.
type CreateFeedbackReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFeedbackReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFeedbackReplyMultiError) AllErrors() []error { return m }

// CreateFeedbackReplyValidationError is the validation error returned by
// CreateFeedbackReply.Validate if the designated constraints aren't met.
type CreateFeedbackReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFeedbackReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFeedbackReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFeedbackReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFeedbackReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFeedbackReplyValidationError) ErrorName() string {
	return "CreateFeedbackReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFeedbackReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFeedbackReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFeedbackReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFeedbackReplyValidationError{}

// Validate checks the field values on VersionCheckRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VersionCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VersionCheckRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VersionCheckRequestMultiError, or nil if none found.
func (m *VersionCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VersionCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentVersion

	// no validation rules for Platform

	if len(errors) > 0 {
		return VersionCheckRequestMultiError(errors)
	}

	return nil
}

// VersionCheckRequestMultiError is an error wrapping multiple validation
// errors returned by VersionCheckRequest.ValidateAll() if the designated
// constraints aren't met.
type VersionCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VersionCheckRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VersionCheckRequestMultiError) AllErrors() []error { return m }

// VersionCheckRequestValidationError is the validation error returned by
// VersionCheckRequest.Validate if the designated constraints aren't met.
type VersionCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VersionCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VersionCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VersionCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VersionCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VersionCheckRequestValidationError) ErrorName() string {
	return "VersionCheckRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VersionCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVersionCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VersionCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VersionCheckRequestValidationError{}

// Validate checks the field values on VersionCheckReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VersionCheckReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VersionCheckReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VersionCheckReplyMultiError, or nil if none found.
func (m *VersionCheckReply) ValidateAll() error {
	return m.validate(true)
}

func (m *VersionCheckReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VersionCheckReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VersionCheckReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VersionCheckReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VersionCheckReplyMultiError(errors)
	}

	return nil
}

// VersionCheckReplyMultiError is an error wrapping multiple validation errors
// returned by VersionCheckReply.ValidateAll() if the designated constraints
// aren't met.
type VersionCheckReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VersionCheckReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VersionCheckReplyMultiError) AllErrors() []error { return m }

// VersionCheckReplyValidationError is the validation error returned by
// VersionCheckReply.Validate if the designated constraints aren't met.
type VersionCheckReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VersionCheckReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VersionCheckReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VersionCheckReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VersionCheckReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VersionCheckReplyValidationError) ErrorName() string {
	return "VersionCheckReplyValidationError"
}

// Error satisfies the builtin error interface
func (e VersionCheckReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVersionCheckReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VersionCheckReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VersionCheckReplyValidationError{}

// Validate checks the field values on GetSiteInfoReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSiteInfoReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSiteInfoReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSiteInfoReply_DataMultiError, or nil if none found.
func (m *GetSiteInfoReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSiteInfoReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for Agreement

	// no validation rules for PrivacyPolicy

	if len(errors) > 0 {
		return GetSiteInfoReply_DataMultiError(errors)
	}

	return nil
}

// GetSiteInfoReply_DataMultiError is an error wrapping multiple validation
// errors returned by GetSiteInfoReply_Data.ValidateAll() if the designated
// constraints aren't met.
type GetSiteInfoReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSiteInfoReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSiteInfoReply_DataMultiError) AllErrors() []error { return m }

// GetSiteInfoReply_DataValidationError is the validation error returned by
// GetSiteInfoReply_Data.Validate if the designated constraints aren't met.
type GetSiteInfoReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSiteInfoReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSiteInfoReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSiteInfoReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSiteInfoReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSiteInfoReply_DataValidationError) ErrorName() string {
	return "GetSiteInfoReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetSiteInfoReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSiteInfoReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSiteInfoReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSiteInfoReply_DataValidationError{}

// Validate checks the field values on CreateUpTokenReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUpTokenReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUpTokenReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUpTokenReply_DataMultiError, or nil if none found.
func (m *CreateUpTokenReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUpTokenReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	if len(errors) > 0 {
		return CreateUpTokenReply_DataMultiError(errors)
	}

	return nil
}

// CreateUpTokenReply_DataMultiError is an error wrapping multiple validation
// errors returned by CreateUpTokenReply_Data.ValidateAll() if the designated
// constraints aren't met.
type CreateUpTokenReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUpTokenReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUpTokenReply_DataMultiError) AllErrors() []error { return m }

// CreateUpTokenReply_DataValidationError is the validation error returned by
// CreateUpTokenReply_Data.Validate if the designated constraints aren't met.
type CreateUpTokenReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUpTokenReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUpTokenReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUpTokenReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUpTokenReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUpTokenReply_DataValidationError) ErrorName() string {
	return "CreateUpTokenReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUpTokenReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUpTokenReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUpTokenReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUpTokenReply_DataValidationError{}

// Validate checks the field values on CreateDownURLReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDownURLReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDownURLReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDownURLReply_DataMultiError, or nil if none found.
func (m *CreateDownURLReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDownURLReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return CreateDownURLReply_DataMultiError(errors)
	}

	return nil
}

// CreateDownURLReply_DataMultiError is an error wrapping multiple validation
// errors returned by CreateDownURLReply_Data.ValidateAll() if the designated
// constraints aren't met.
type CreateDownURLReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDownURLReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDownURLReply_DataMultiError) AllErrors() []error { return m }

// CreateDownURLReply_DataValidationError is the validation error returned by
// CreateDownURLReply_Data.Validate if the designated constraints aren't met.
type CreateDownURLReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDownURLReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDownURLReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDownURLReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDownURLReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDownURLReply_DataValidationError) ErrorName() string {
	return "CreateDownURLReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDownURLReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDownURLReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDownURLReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDownURLReply_DataValidationError{}

// Validate checks the field values on ListMotiveMemoReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMotiveMemoReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMotiveMemoReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMotiveMemoReply_DataMultiError, or nil if none found.
func (m *ListMotiveMemoReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMotiveMemoReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMotives() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMotiveMemoReply_DataValidationError{
						field:  fmt.Sprintf("Motives[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMotiveMemoReply_DataValidationError{
						field:  fmt.Sprintf("Motives[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMotiveMemoReply_DataValidationError{
					field:  fmt.Sprintf("Motives[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListMotiveMemoReply_DataMultiError(errors)
	}

	return nil
}

// ListMotiveMemoReply_DataMultiError is an error wrapping multiple validation
// errors returned by ListMotiveMemoReply_Data.ValidateAll() if the designated
// constraints aren't met.
type ListMotiveMemoReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMotiveMemoReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMotiveMemoReply_DataMultiError) AllErrors() []error { return m }

// ListMotiveMemoReply_DataValidationError is the validation error returned by
// ListMotiveMemoReply_Data.Validate if the designated constraints aren't met.
type ListMotiveMemoReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMotiveMemoReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMotiveMemoReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMotiveMemoReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMotiveMemoReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMotiveMemoReply_DataValidationError) ErrorName() string {
	return "ListMotiveMemoReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ListMotiveMemoReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMotiveMemoReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMotiveMemoReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMotiveMemoReply_DataValidationError{}

// Validate checks the field values on ListMotiveMemoReply_Motive with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMotiveMemoReply_Motive) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMotiveMemoReply_Motive with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMotiveMemoReply_MotiveMultiError, or nil if none found.
func (m *ListMotiveMemoReply_Motive) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMotiveMemoReply_Motive) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Content

	// no validation rules for From

	if len(errors) > 0 {
		return ListMotiveMemoReply_MotiveMultiError(errors)
	}

	return nil
}

// ListMotiveMemoReply_MotiveMultiError is an error wrapping multiple
// validation errors returned by ListMotiveMemoReply_Motive.ValidateAll() if
// the designated constraints aren't met.
type ListMotiveMemoReply_MotiveMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMotiveMemoReply_MotiveMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMotiveMemoReply_MotiveMultiError) AllErrors() []error { return m }

// ListMotiveMemoReply_MotiveValidationError is the validation error returned
// by ListMotiveMemoReply_Motive.Validate if the designated constraints aren't met.
type ListMotiveMemoReply_MotiveValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMotiveMemoReply_MotiveValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMotiveMemoReply_MotiveValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMotiveMemoReply_MotiveValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMotiveMemoReply_MotiveValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMotiveMemoReply_MotiveValidationError) ErrorName() string {
	return "ListMotiveMemoReply_MotiveValidationError"
}

// Error satisfies the builtin error interface
func (e ListMotiveMemoReply_MotiveValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMotiveMemoReply_Motive.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMotiveMemoReply_MotiveValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMotiveMemoReply_MotiveValidationError{}

// Validate checks the field values on VersionCheckReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VersionCheckReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VersionCheckReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VersionCheckReply_DataMultiError, or nil if none found.
func (m *VersionCheckReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *VersionCheckReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HasUpdate

	// no validation rules for ForceUpdate

	// no validation rules for LatestVersion

	// no validation rules for CurrentVersion

	// no validation rules for UpdateTitle

	// no validation rules for UpdateContent

	// no validation rules for DownloadUrl

	// no validation rules for IosAppStoreUrl

	// no validation rules for AndroidDirectUrl

	// no validation rules for FileSize

	if len(errors) > 0 {
		return VersionCheckReply_DataMultiError(errors)
	}

	return nil
}

// VersionCheckReply_DataMultiError is an error wrapping multiple validation
// errors returned by VersionCheckReply_Data.ValidateAll() if the designated
// constraints aren't met.
type VersionCheckReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VersionCheckReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VersionCheckReply_DataMultiError) AllErrors() []error { return m }

// VersionCheckReply_DataValidationError is the validation error returned by
// VersionCheckReply_Data.Validate if the designated constraints aren't met.
type VersionCheckReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VersionCheckReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VersionCheckReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VersionCheckReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VersionCheckReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VersionCheckReply_DataValidationError) ErrorName() string {
	return "VersionCheckReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e VersionCheckReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVersionCheckReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VersionCheckReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VersionCheckReply_DataValidationError{}
