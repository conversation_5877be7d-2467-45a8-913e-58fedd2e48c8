// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: site/v1/site.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSiteInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSiteInfoRequest) Reset() {
	*x = GetSiteInfoRequest{}
	mi := &file_site_v1_site_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSiteInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSiteInfoRequest) ProtoMessage() {}

func (x *GetSiteInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSiteInfoRequest.ProtoReflect.Descriptor instead.
func (*GetSiteInfoRequest) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{0}
}

type GetSiteInfoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetSiteInfoReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSiteInfoReply) Reset() {
	*x = GetSiteInfoReply{}
	mi := &file_site_v1_site_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSiteInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSiteInfoReply) ProtoMessage() {}

func (x *GetSiteInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSiteInfoReply.ProtoReflect.Descriptor instead.
func (*GetSiteInfoReply) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{1}
}

func (x *GetSiteInfoReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetSiteInfoReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetSiteInfoReply) GetData() *GetSiteInfoReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateUpTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Scene         string                 `protobuf:"bytes,1,opt,name=scene,proto3" json:"scene,omitempty"` // 场景
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUpTokenRequest) Reset() {
	*x = CreateUpTokenRequest{}
	mi := &file_site_v1_site_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUpTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpTokenRequest) ProtoMessage() {}

func (x *CreateUpTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpTokenRequest.ProtoReflect.Descriptor instead.
func (*CreateUpTokenRequest) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{2}
}

func (x *CreateUpTokenRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type CreateUpTokenReply struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *CreateUpTokenReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUpTokenReply) Reset() {
	*x = CreateUpTokenReply{}
	mi := &file_site_v1_site_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUpTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpTokenReply) ProtoMessage() {}

func (x *CreateUpTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpTokenReply.ProtoReflect.Descriptor instead.
func (*CreateUpTokenReply) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{3}
}

func (x *CreateUpTokenReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateUpTokenReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateUpTokenReply) GetData() *CreateUpTokenReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateDownURLRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`     // 文件名
	Scene         string                 `protobuf:"bytes,2,opt,name=scene,proto3" json:"scene,omitempty"` // 场景
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDownURLRequest) Reset() {
	*x = CreateDownURLRequest{}
	mi := &file_site_v1_site_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDownURLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDownURLRequest) ProtoMessage() {}

func (x *CreateDownURLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDownURLRequest.ProtoReflect.Descriptor instead.
func (*CreateDownURLRequest) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{4}
}

func (x *CreateDownURLRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *CreateDownURLRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type CreateDownURLReply struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *CreateDownURLReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDownURLReply) Reset() {
	*x = CreateDownURLReply{}
	mi := &file_site_v1_site_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDownURLReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDownURLReply) ProtoMessage() {}

func (x *CreateDownURLReply) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDownURLReply.ProtoReflect.Descriptor instead.
func (*CreateDownURLReply) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{5}
}

func (x *CreateDownURLReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateDownURLReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CreateDownURLReply) GetData() *CreateDownURLReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type SendVerifyCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Phone         string                 `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	Scene         string                 `protobuf:"bytes,3,opt,name=scene,proto3" json:"scene,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyCodeRequest) Reset() {
	*x = SendVerifyCodeRequest{}
	mi := &file_site_v1_site_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeRequest) ProtoMessage() {}

func (x *SendVerifyCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeRequest.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeRequest) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{6}
}

func (x *SendVerifyCodeRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SendVerifyCodeRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *SendVerifyCodeRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type SendVerifyCodeReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVerifyCodeReply) Reset() {
	*x = SendVerifyCodeReply{}
	mi := &file_site_v1_site_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVerifyCodeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeReply) ProtoMessage() {}

func (x *SendVerifyCodeReply) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeReply.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeReply) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{7}
}

func (x *SendVerifyCodeReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendVerifyCodeReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ListMotiveMemoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMotiveMemoRequest) Reset() {
	*x = ListMotiveMemoRequest{}
	mi := &file_site_v1_site_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMotiveMemoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMotiveMemoRequest) ProtoMessage() {}

func (x *ListMotiveMemoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMotiveMemoRequest.ProtoReflect.Descriptor instead.
func (*ListMotiveMemoRequest) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{8}
}

type ListMotiveMemoReply struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *ListMotiveMemoReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMotiveMemoReply) Reset() {
	*x = ListMotiveMemoReply{}
	mi := &file_site_v1_site_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMotiveMemoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMotiveMemoReply) ProtoMessage() {}

func (x *ListMotiveMemoReply) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMotiveMemoReply.ProtoReflect.Descriptor instead.
func (*ListMotiveMemoReply) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{9}
}

func (x *ListMotiveMemoReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListMotiveMemoReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListMotiveMemoReply) GetData() *ListMotiveMemoReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateFeedbackRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Imgs          []string               `protobuf:"bytes,3,rep,name=imgs,proto3" json:"imgs,omitempty"`
	Nickname      string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateFeedbackRequest) Reset() {
	*x = CreateFeedbackRequest{}
	mi := &file_site_v1_site_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFeedbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFeedbackRequest) ProtoMessage() {}

func (x *CreateFeedbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFeedbackRequest.ProtoReflect.Descriptor instead.
func (*CreateFeedbackRequest) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{10}
}

func (x *CreateFeedbackRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateFeedbackRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateFeedbackRequest) GetImgs() []string {
	if x != nil {
		return x.Imgs
	}
	return nil
}

func (x *CreateFeedbackRequest) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

type CreateFeedbackReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateFeedbackReply) Reset() {
	*x = CreateFeedbackReply{}
	mi := &file_site_v1_site_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFeedbackReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFeedbackReply) ProtoMessage() {}

func (x *CreateFeedbackReply) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFeedbackReply.ProtoReflect.Descriptor instead.
func (*CreateFeedbackReply) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{11}
}

func (x *CreateFeedbackReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateFeedbackReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type VersionCheckRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CurrentVersion string                 `protobuf:"bytes,1,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty"`
	Platform       string                 `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *VersionCheckRequest) Reset() {
	*x = VersionCheckRequest{}
	mi := &file_site_v1_site_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionCheckRequest) ProtoMessage() {}

func (x *VersionCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionCheckRequest.ProtoReflect.Descriptor instead.
func (*VersionCheckRequest) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{12}
}

func (x *VersionCheckRequest) GetCurrentVersion() string {
	if x != nil {
		return x.CurrentVersion
	}
	return ""
}

func (x *VersionCheckRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type VersionCheckReply struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *VersionCheckReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VersionCheckReply) Reset() {
	*x = VersionCheckReply{}
	mi := &file_site_v1_site_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionCheckReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionCheckReply) ProtoMessage() {}

func (x *VersionCheckReply) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionCheckReply.ProtoReflect.Descriptor instead.
func (*VersionCheckReply) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{13}
}

func (x *VersionCheckReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VersionCheckReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VersionCheckReply) GetData() *VersionCheckReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetSiteInfoReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       string                 `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Agreement     string                 `protobuf:"bytes,2,opt,name=agreement,proto3" json:"agreement,omitempty"`
	PrivacyPolicy string                 `protobuf:"bytes,3,opt,name=privacy_policy,json=privacyPolicy,proto3" json:"privacy_policy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSiteInfoReply_Data) Reset() {
	*x = GetSiteInfoReply_Data{}
	mi := &file_site_v1_site_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSiteInfoReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSiteInfoReply_Data) ProtoMessage() {}

func (x *GetSiteInfoReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSiteInfoReply_Data.ProtoReflect.Descriptor instead.
func (*GetSiteInfoReply_Data) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetSiteInfoReply_Data) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *GetSiteInfoReply_Data) GetAgreement() string {
	if x != nil {
		return x.Agreement
	}
	return ""
}

func (x *GetSiteInfoReply_Data) GetPrivacyPolicy() string {
	if x != nil {
		return x.PrivacyPolicy
	}
	return ""
}

type CreateUpTokenReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"` // 上传凭证
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUpTokenReply_Data) Reset() {
	*x = CreateUpTokenReply_Data{}
	mi := &file_site_v1_site_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUpTokenReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUpTokenReply_Data) ProtoMessage() {}

func (x *CreateUpTokenReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUpTokenReply_Data.ProtoReflect.Descriptor instead.
func (*CreateUpTokenReply_Data) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{3, 0}
}

func (x *CreateUpTokenReply_Data) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type CreateDownURLReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"` // 下载 URL
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDownURLReply_Data) Reset() {
	*x = CreateDownURLReply_Data{}
	mi := &file_site_v1_site_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDownURLReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDownURLReply_Data) ProtoMessage() {}

func (x *CreateDownURLReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDownURLReply_Data.ProtoReflect.Descriptor instead.
func (*CreateDownURLReply_Data) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{5, 0}
}

func (x *CreateDownURLReply_Data) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ListMotiveMemoReply_Data struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Motives       []*ListMotiveMemoReply_Motive `protobuf:"bytes,1,rep,name=motives,proto3" json:"motives,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMotiveMemoReply_Data) Reset() {
	*x = ListMotiveMemoReply_Data{}
	mi := &file_site_v1_site_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMotiveMemoReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMotiveMemoReply_Data) ProtoMessage() {}

func (x *ListMotiveMemoReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMotiveMemoReply_Data.ProtoReflect.Descriptor instead.
func (*ListMotiveMemoReply_Data) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListMotiveMemoReply_Data) GetMotives() []*ListMotiveMemoReply_Motive {
	if x != nil {
		return x.Motives
	}
	return nil
}

type ListMotiveMemoReply_Motive struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	From          string                 `protobuf:"bytes,3,opt,name=from,proto3" json:"from,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMotiveMemoReply_Motive) Reset() {
	*x = ListMotiveMemoReply_Motive{}
	mi := &file_site_v1_site_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMotiveMemoReply_Motive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMotiveMemoReply_Motive) ProtoMessage() {}

func (x *ListMotiveMemoReply_Motive) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMotiveMemoReply_Motive.ProtoReflect.Descriptor instead.
func (*ListMotiveMemoReply_Motive) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{9, 1}
}

func (x *ListMotiveMemoReply_Motive) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListMotiveMemoReply_Motive) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ListMotiveMemoReply_Motive) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

type VersionCheckReply_Data struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	HasUpdate        bool                   `protobuf:"varint,1,opt,name=hasUpdate,proto3" json:"hasUpdate,omitempty"`
	ForceUpdate      bool                   `protobuf:"varint,2,opt,name=forceUpdate,proto3" json:"forceUpdate,omitempty"`
	LatestVersion    string                 `protobuf:"bytes,3,opt,name=latestVersion,proto3" json:"latestVersion,omitempty"`
	CurrentVersion   string                 `protobuf:"bytes,4,opt,name=currentVersion,proto3" json:"currentVersion,omitempty"`
	UpdateTitle      string                 `protobuf:"bytes,5,opt,name=updateTitle,proto3" json:"updateTitle,omitempty"`
	UpdateContent    string                 `protobuf:"bytes,6,opt,name=updateContent,proto3" json:"updateContent,omitempty"`
	DownloadUrl      string                 `protobuf:"bytes,7,opt,name=downloadUrl,proto3" json:"downloadUrl,omitempty"`
	IosAppStoreUrl   string                 `protobuf:"bytes,8,opt,name=iosAppStoreUrl,proto3" json:"iosAppStoreUrl,omitempty"`
	AndroidDirectUrl string                 `protobuf:"bytes,9,opt,name=androidDirectUrl,proto3" json:"androidDirectUrl,omitempty"`
	FileSize         uint32                 `protobuf:"varint,10,opt,name=fileSize,proto3" json:"fileSize,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *VersionCheckReply_Data) Reset() {
	*x = VersionCheckReply_Data{}
	mi := &file_site_v1_site_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionCheckReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionCheckReply_Data) ProtoMessage() {}

func (x *VersionCheckReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_site_v1_site_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionCheckReply_Data.ProtoReflect.Descriptor instead.
func (*VersionCheckReply_Data) Descriptor() ([]byte, []int) {
	return file_site_v1_site_proto_rawDescGZIP(), []int{13, 0}
}

func (x *VersionCheckReply_Data) GetHasUpdate() bool {
	if x != nil {
		return x.HasUpdate
	}
	return false
}

func (x *VersionCheckReply_Data) GetForceUpdate() bool {
	if x != nil {
		return x.ForceUpdate
	}
	return false
}

func (x *VersionCheckReply_Data) GetLatestVersion() string {
	if x != nil {
		return x.LatestVersion
	}
	return ""
}

func (x *VersionCheckReply_Data) GetCurrentVersion() string {
	if x != nil {
		return x.CurrentVersion
	}
	return ""
}

func (x *VersionCheckReply_Data) GetUpdateTitle() string {
	if x != nil {
		return x.UpdateTitle
	}
	return ""
}

func (x *VersionCheckReply_Data) GetUpdateContent() string {
	if x != nil {
		return x.UpdateContent
	}
	return ""
}

func (x *VersionCheckReply_Data) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *VersionCheckReply_Data) GetIosAppStoreUrl() string {
	if x != nil {
		return x.IosAppStoreUrl
	}
	return ""
}

func (x *VersionCheckReply_Data) GetAndroidDirectUrl() string {
	if x != nil {
		return x.AndroidDirectUrl
	}
	return ""
}

func (x *VersionCheckReply_Data) GetFileSize() uint32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

var File_site_v1_site_proto protoreflect.FileDescriptor

const file_site_v1_site_proto_rawDesc = "" +
	"\n" +
	"\x12site/v1/site.proto\x12\asite_v1\"\x14\n" +
	"\x12GetSiteInfoRequest\"\xd3\x01\n" +
	"\x10GetSiteInfoReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x122\n" +
	"\x04data\x18\x03 \x01(\v2\x1e.site_v1.GetSiteInfoReply.DataR\x04data\x1ae\n" +
	"\x04Data\x12\x18\n" +
	"\aversion\x18\x01 \x01(\tR\aversion\x12\x1c\n" +
	"\tagreement\x18\x02 \x01(\tR\tagreement\x12%\n" +
	"\x0eprivacy_policy\x18\x03 \x01(\tR\rprivacyPolicy\",\n" +
	"\x14CreateUpTokenRequest\x12\x14\n" +
	"\x05scene\x18\x01 \x01(\tR\x05scene\"\x8e\x01\n" +
	"\x12CreateUpTokenReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x124\n" +
	"\x04data\x18\x03 \x01(\v2 .site_v1.CreateUpTokenReply.DataR\x04data\x1a\x1c\n" +
	"\x04Data\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\">\n" +
	"\x14CreateDownURLRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05scene\x18\x02 \x01(\tR\x05scene\"\x8a\x01\n" +
	"\x12CreateDownURLReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x124\n" +
	"\x04data\x18\x03 \x01(\v2 .site_v1.CreateDownURLReply.DataR\x04data\x1a\x18\n" +
	"\x04Data\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\"Y\n" +
	"\x15SendVerifyCodeRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x14\n" +
	"\x05phone\x18\x02 \x01(\tR\x05phone\x12\x14\n" +
	"\x05scene\x18\x03 \x01(\tR\x05scene\";\n" +
	"\x13SendVerifyCodeReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x17\n" +
	"\x15ListMotiveMemoRequest\"\x81\x02\n" +
	"\x13ListMotiveMemoReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x125\n" +
	"\x04data\x18\x03 \x01(\v2!.site_v1.ListMotiveMemoReply.DataR\x04data\x1aE\n" +
	"\x04Data\x12=\n" +
	"\amotives\x18\x01 \x03(\v2#.site_v1.ListMotiveMemoReply.MotiveR\amotives\x1aF\n" +
	"\x06Motive\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x12\n" +
	"\x04from\x18\x03 \x01(\tR\x04from\"w\n" +
	"\x15CreateFeedbackRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x12\n" +
	"\x04imgs\x18\x03 \x03(\tR\x04imgs\x12\x1a\n" +
	"\bnickname\x18\x04 \x01(\tR\bnickname\";\n" +
	"\x13CreateFeedbackReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"Z\n" +
	"\x13VersionCheckRequest\x12'\n" +
	"\x0fcurrent_version\x18\x01 \x01(\tR\x0ecurrentVersion\x12\x1a\n" +
	"\bplatform\x18\x02 \x01(\tR\bplatform\"\xdf\x03\n" +
	"\x11VersionCheckReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x123\n" +
	"\x04data\x18\x03 \x01(\v2\x1f.site_v1.VersionCheckReply.DataR\x04data\x1a\xee\x02\n" +
	"\x04Data\x12\x1c\n" +
	"\thasUpdate\x18\x01 \x01(\bR\thasUpdate\x12 \n" +
	"\vforceUpdate\x18\x02 \x01(\bR\vforceUpdate\x12$\n" +
	"\rlatestVersion\x18\x03 \x01(\tR\rlatestVersion\x12&\n" +
	"\x0ecurrentVersion\x18\x04 \x01(\tR\x0ecurrentVersion\x12 \n" +
	"\vupdateTitle\x18\x05 \x01(\tR\vupdateTitle\x12$\n" +
	"\rupdateContent\x18\x06 \x01(\tR\rupdateContent\x12 \n" +
	"\vdownloadUrl\x18\a \x01(\tR\vdownloadUrl\x12&\n" +
	"\x0eiosAppStoreUrl\x18\b \x01(\tR\x0eiosAppStoreUrl\x12*\n" +
	"\x10androidDirectUrl\x18\t \x01(\tR\x10androidDirectUrl\x12\x1a\n" +
	"\bfileSize\x18\n" +
	" \x01(\rR\bfileSizeB-Z+github.com/wlnil/life-log-be/api/site/v1;v1b\x06proto3"

var (
	file_site_v1_site_proto_rawDescOnce sync.Once
	file_site_v1_site_proto_rawDescData []byte
)

func file_site_v1_site_proto_rawDescGZIP() []byte {
	file_site_v1_site_proto_rawDescOnce.Do(func() {
		file_site_v1_site_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_site_v1_site_proto_rawDesc), len(file_site_v1_site_proto_rawDesc)))
	})
	return file_site_v1_site_proto_rawDescData
}

var file_site_v1_site_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_site_v1_site_proto_goTypes = []any{
	(*GetSiteInfoRequest)(nil),         // 0: site_v1.GetSiteInfoRequest
	(*GetSiteInfoReply)(nil),           // 1: site_v1.GetSiteInfoReply
	(*CreateUpTokenRequest)(nil),       // 2: site_v1.CreateUpTokenRequest
	(*CreateUpTokenReply)(nil),         // 3: site_v1.CreateUpTokenReply
	(*CreateDownURLRequest)(nil),       // 4: site_v1.CreateDownURLRequest
	(*CreateDownURLReply)(nil),         // 5: site_v1.CreateDownURLReply
	(*SendVerifyCodeRequest)(nil),      // 6: site_v1.SendVerifyCodeRequest
	(*SendVerifyCodeReply)(nil),        // 7: site_v1.SendVerifyCodeReply
	(*ListMotiveMemoRequest)(nil),      // 8: site_v1.ListMotiveMemoRequest
	(*ListMotiveMemoReply)(nil),        // 9: site_v1.ListMotiveMemoReply
	(*CreateFeedbackRequest)(nil),      // 10: site_v1.CreateFeedbackRequest
	(*CreateFeedbackReply)(nil),        // 11: site_v1.CreateFeedbackReply
	(*VersionCheckRequest)(nil),        // 12: site_v1.VersionCheckRequest
	(*VersionCheckReply)(nil),          // 13: site_v1.VersionCheckReply
	(*GetSiteInfoReply_Data)(nil),      // 14: site_v1.GetSiteInfoReply.Data
	(*CreateUpTokenReply_Data)(nil),    // 15: site_v1.CreateUpTokenReply.Data
	(*CreateDownURLReply_Data)(nil),    // 16: site_v1.CreateDownURLReply.Data
	(*ListMotiveMemoReply_Data)(nil),   // 17: site_v1.ListMotiveMemoReply.Data
	(*ListMotiveMemoReply_Motive)(nil), // 18: site_v1.ListMotiveMemoReply.Motive
	(*VersionCheckReply_Data)(nil),     // 19: site_v1.VersionCheckReply.Data
}
var file_site_v1_site_proto_depIdxs = []int32{
	14, // 0: site_v1.GetSiteInfoReply.data:type_name -> site_v1.GetSiteInfoReply.Data
	15, // 1: site_v1.CreateUpTokenReply.data:type_name -> site_v1.CreateUpTokenReply.Data
	16, // 2: site_v1.CreateDownURLReply.data:type_name -> site_v1.CreateDownURLReply.Data
	17, // 3: site_v1.ListMotiveMemoReply.data:type_name -> site_v1.ListMotiveMemoReply.Data
	19, // 4: site_v1.VersionCheckReply.data:type_name -> site_v1.VersionCheckReply.Data
	18, // 5: site_v1.ListMotiveMemoReply.Data.motives:type_name -> site_v1.ListMotiveMemoReply.Motive
	6,  // [6:6] is the sub-list for method output_type
	6,  // [6:6] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_site_v1_site_proto_init() }
func file_site_v1_site_proto_init() {
	if File_site_v1_site_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_site_v1_site_proto_rawDesc), len(file_site_v1_site_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_site_v1_site_proto_goTypes,
		DependencyIndexes: file_site_v1_site_proto_depIdxs,
		MessageInfos:      file_site_v1_site_proto_msgTypes,
	}.Build()
	File_site_v1_site_proto = out.File
	file_site_v1_site_proto_goTypes = nil
	file_site_v1_site_proto_depIdxs = nil
}
