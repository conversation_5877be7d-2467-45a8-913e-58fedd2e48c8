syntax = "proto3";

package api;

option go_package = "github.com/wlnil/life-log-be/api;api";

import "google/api/annotations.proto";
import "user/v1/user.proto";
import "site/v1/site.proto";
import "site/v1/health.proto";
import "user_habit/v1/user_habit.proto";
import "planet/v1/planet.proto";
import "user_planet/v1/user_planet.proto";
import "user_statistic/v1/user_statistic.proto";
import "user_message/v1/user_message.proto";

service LifeLog {
	// 注册用户
	rpc Register (user_v1.RegisterRequest) returns (user_v1.RegisterReply) {
		option (google.api.http) = {
			post: "/api/v1/user/register",
			body: "*",
		};
	}
	// 登录
	rpc Login (user_v1.LoginRequest) returns (user_v1.LoginReply) {
		option (google.api.http) = {
			post: "/api/v1/user/login",
			body: "*",
		};
	}
	// 登出
	rpc Logout (user_v1.LogoutRequest) returns (user_v1.LogoutReply) {
		option (google.api.http) = {
			post: "/api/v1/user/logout",
			body: "*",
		};
	}
	// 推送 token 和设备信息
	rpc PushToken (user_v1.PushTokenRequest) returns (user_v1.PushTokenReply) {
		option (google.api.http) = {
			post: "/api/v1/user/push-token",
			body: "*",
		};
	}
	// 忘记密码
	rpc ForgetPassword (user_v1.ForgetPasswordRequest) returns (user_v1.ForgetPasswordReply) {
		option (google.api.http) = {
			post: "/api/v1/user/forget-password",
			body: "*",
		};
	}
	// 修改密码
	rpc ChangePassword (user_v1.ChangePasswordRequest) returns (user_v1.ChangePasswordReply) {
		option (google.api.http) = {
			post: "/api/v1/user/change-password",
			body: "*",
		};
	}
	// 删除用户
	rpc DeleteUser (user_v1.DeleteUserRequest) returns (user_v1.DeleteUserReply) {
		option (google.api.http) = {
			post: "/api/v1/user",
			body: "*",
		};
	}
	// 修改手机号
	rpc ChangePhone (user_v1.ChangePhoneRequest) returns (user_v1.ChangePhoneReply) {
		option (google.api.http) = {
			post: "/api/v1/user/change-phone",
			body: "*",
		};
	}
	// 修改邮箱
	rpc ChangeEmail (user_v1.ChangeEmailRequest) returns (user_v1.ChangeEmailReply) {
		option (google.api.http) = {
			post: "/api/v1/user/change-email",
			body: "*",
		};
	}
	// 获取用户资料
	rpc GetUserProfile (user_v1.GetUserProfileRequest) returns (user_v1.GetUserProfileReply) {
		option (google.api.http) = {
			get: "/api/v1/user/profile",
		};
	}
	// 修改用户资料
	rpc UpdateUserProfile (user_v1.UpdateUserProfileRequest) returns (user_v1.UpdateUserProfileReply) {
		option (google.api.http) = {
			put: "/api/v1/user/profile",
			body: "*",
		};
	}
	// 关注
	rpc FollowUser (user_v1.FollowUserRequest) returns (user_v1.FollowUserReply) {
		option (google.api.http) = {
			post: "/api/v1/user/{user_id}/follow",
			body: "*",
		};
	}
	// 取消关注
	rpc UnfollowUser (user_v1.UnfollowUserRequest) returns (user_v1.UnfollowUserReply) {
		option (google.api.http) = {
			post: "/api/v1/user/{user_id}/unfollow",
			body: "*",
		};
	}
	// 刷新 token
	rpc RefreshToken (user_v1.RefreshTokenRequest) returns (user_v1.LoginReply) {
		option (google.api.http) = {
			post: "/api/v1/user/refresh-token",
			body: "*",
		};
	}
	// 获取用户配置信息
	rpc GetUserSetting (user_v1.GetUserSettingRequest) returns (user_v1.GetUserSettingReply) {
		option (google.api.http) = {
			get: "/api/v1/user/setting",
		};
	}
	// 修改用户配置信息
	rpc UpdateUserAward (user_v1.UpdateUserAwardRequest) returns (user_v1.UpdateUserAwardReply) {
		option (google.api.http) = {
			post: "/api/v1/user/award",
			body: "*",
		};
	}
	// 修改用户隐私密码
	rpc ChangeUserPrivacyPassword (user_v1.ChangeUserPrivacyPasswordRequest) returns (user_v1.ChangeUserPrivacyPasswordReply) {
		option (google.api.http) = {
			post: "/api/v1/user/privacy-password",
			body: "*",
		};
	}
	// 检查用户隐私密码
	rpc CheckUserPrivacyPassword (user_v1.CheckUserPrivacyPasswordRequest) returns (user_v1.CheckUserPrivacyPasswordReply) {
		option (google.api.http) = {
			post: "/api/v1/user/privacy-password/check",
			body: "*",
		};
	}
	// 获取用户会员详情
	rpc GetUserVipInfo (user_v1.GetUserVipInfoRequest) returns (user_v1.GetUserVipInfoReply) {
		option (google.api.http) = {
			get: "/api/v1/user/vip",
		};
	}

	// 获取网站信息
	rpc GetSiteInfo (site_v1.GetSiteInfoRequest) returns (site_v1.GetSiteInfoReply) {
		option (google.api.http) = {
			get: "/api/v1/site-info",
		};
	}
	// 健康检查接口
	rpc HealthCheck (site_v1.HealthCheckRequest) returns (site_v1.HealthCheckReply) {
		option (google.api.http) = {
			get: "/api/v1/health",
		};
	}
	// 获取七牛上传凭证
	rpc CreateUpToken (site_v1.CreateUpTokenRequest) returns (site_v1.CreateUpTokenReply) {
		option (google.api.http) = {
			get: "/api/v1/storage/upload-token",
		};
	}
	// 获取七牛下载地址
	rpc CreateDownURL (site_v1.CreateDownURLRequest) returns (site_v1.CreateDownURLReply) {
		option (google.api.http) = {
			post: "/api/v1/storage/download-url",
			body: "*",
		};
	}
	// 发送验证码
	rpc SendVerifyCode (site_v1.SendVerifyCodeRequest) returns (site_v1.SendVerifyCodeReply) {
		option (google.api.http) = {
			post: "/api/v1/sms/verify-code/send",
			body: "*",
		};
	}
	// 获取每日随想
	rpc ListMotiveMemo (site_v1.ListMotiveMemoRequest) returns (site_v1.ListMotiveMemoReply) {
		option (google.api.http) = {
			get: "/api/v1/motive-memo",
		};
	}
	// 添加意见反馈
	rpc CreateFeedback (site_v1.CreateFeedbackRequest) returns (site_v1.CreateFeedbackReply) {
		option (google.api.http) = {
			post: "/api/v1/feedback",
			body: "*",
		};
	}
	// 获取版本更新
	rpc VersionCheck (site_v1.VersionCheckRequest) returns (site_v1.VersionCheckReply) {
		option (google.api.http) = {
			get: "/api/v1/version/check",
		};
	}

	// 创建用户习惯
	rpc CreateUserHabit (user_habit_v1.CreateUserHabitRequest) returns (user_habit_v1.CreateUserHabitReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit",
			body: "*",
		};
	}
	// 获取用户习惯列表
	rpc ListUserHabit (user_habit_v1.ListUserHabitRequest) returns (user_habit_v1.ListUserHabitReply) {
		option (google.api.http) = {
			get: "/api/v1/user/habit",
		};
	}
	// 获取用户习惯详情
	rpc GetUserHabit (user_habit_v1.GetUserHabitRequest) returns (user_habit_v1.GetUserHabitReply) {
		option (google.api.http) = {
			get: "/api/v1/user/habit/{id}",
		};
	}
	// 更新用户习惯
	rpc UpdateUserHabit (user_habit_v1.UpdateUserHabitRequest) returns (user_habit_v1.UpdateUserHabitReply) {
		option (google.api.http) = {
			put: "/api/v1/user/habit/{id}",
			body: "*",
		};
	}
	// 删除用户习惯
	rpc DeleteUserHabit (user_habit_v1.DeleteUserHabitRequest) returns (user_habit_v1.DeleteUserHabitReply) {
		option (google.api.http) = {
			delete: "/api/v1/user/habit/{id}",
		};
	}
	// 暂停用户习惯
	rpc PauseUserHabit (user_habit_v1.PauseUserHabitRequest) returns (user_habit_v1.PauseUserHabitReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{id}/pause",
			body: "*",
		};
	}
	// 恢复用户习惯
	rpc RecoverUserHabit (user_habit_v1.RecoverUserHabitRequest) returns (user_habit_v1.RecoverUserHabitReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{id}/recover",
			body: "*",
		};
	}
	// 归档用户习惯
	rpc ArchiveUserHabit (user_habit_v1.ArchiveUserHabitRequest) returns (user_habit_v1.ArchiveUserHabitReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{id}/archive",
			body: "*",
		};
	}
	// 获取用户每日习惯列表
	rpc ListUserHabitSnapshot (user_habit_v1.ListUserHabitSnapshotRequest) returns (user_habit_v1.ListUserHabitSnapshotReply) {
		option (google.api.http) = {
			get: "/api/v1/user/habit_snapshot",
		};
	}
	// 创建用户习惯想法
	rpc CreateUserHabitMemo (user_habit_v1.CreateUserHabitMemoRequest) returns (user_habit_v1.CreateUserHabitMemoReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{user_habit_id}/memo",
			body: "*",
		};
	}
	// 更新用户习惯想法
	rpc UpdateUserHabitMemo (user_habit_v1.UpdateUserHabitMemoRequest) returns (user_habit_v1.UpdateUserHabitMemoReply) {
		option (google.api.http) = {
			put: "/api/v1/user/habit/{user_habit_id}/memo/{memo_id}",
			body: "*",
		};
	}
	// 删除用户习惯想法
	rpc DeleteUserHabitMemo (user_habit_v1.DeleteUserHabitMemoRequest) returns (user_habit_v1.DeleteUserHabitMemoReply) {
		option (google.api.http) = {
			delete: "/api/v1/user/habit/{user_habit_id}/memo/{memo_id}",
		};
	}
	// 习惯打卡
	rpc PunchUserHabit (user_habit_v1.PunchUserHabitRequest) returns (user_habit_v1.PunchUserHabitReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{user_habit_id}/punch",
			body: "*",
		};
	}
	// 取消习惯打卡
	rpc CancelPunchUserHabit (user_habit_v1.CancelPunchUserHabitRequest) returns (user_habit_v1.CancelPunchUserHabitReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{user_habit_id}/cancel-punch",
			body: "*",
		};
	}
	// 更新习惯打卡
	rpc UpdatePunchUserHabit (user_habit_v1.UpdatePunchUserHabitRequest) returns (user_habit_v1.UpdatePunchUserHabitReply) {
		option (google.api.http) = {
			put: "/api/v1/user/habit/{user_habit_id}/punch/{punch_id}",
			body: "*",
		};
	}
	// 习惯计时
	rpc ReckonUserHabit (user_habit_v1.ReckonUserHabitRequest) returns (user_habit_v1.ReckonUserHabitReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{user_habit_id}/reckon",
			body: "*",
		};
	}
	// 取消习惯计时
	rpc CancelReckonUserHabit (user_habit_v1.CancelReckonUserHabitRequest) returns (user_habit_v1.CancelReckonUserHabitReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{user_habit_id}/cancel-reckon",
			body: "*",
		};
	}
	// 创建用户习惯计时
	rpc CreateUserHabitReckon (user_habit_v1.CreateUserHabitReckonRequest) returns (user_habit_v1.CreateUserHabitReckonReply) {
		option (google.api.http) = {
			post: "/api/v1/user/habit/{user_habit_id}/reckon/save",
			body: "*",
		};
	}
	// 更新用户习惯计时
	rpc UpdateUserHabitReckon (user_habit_v1.UpdateUserHabitReckonRequest) returns (user_habit_v1.UpdateUserHabitReckonReply) {
		option (google.api.http) = {
			put: "/api/v1/user/habit/{user_habit_id}/reckon/{reckon_id}",
			body: "*",
		};
	}
	// 删除用户习惯计时
	rpc DeleteUserHabitReckon (user_habit_v1.DeleteUserHabitReckonRequest) returns (user_habit_v1.DeleteUserHabitReckonReply) {
		option (google.api.http) = {
			delete: "/api/v1/user/habit/{user_habit_id}/reckon/{reckon_id}",
		};
	}

	// 创建星球
	rpc CreatePlanet (planet_v1.CreatePlanetRequest) returns (planet_v1.CreatePlanetReply) {
		option (google.api.http) = {
			post: "/api/v1/planet",
			body: "*",
		};
	}
	// 获取星球详情
	rpc GetPlanet (planet_v1.GetPlanetRequest) returns (planet_v1.GetPlanetReply) {
		option (google.api.http) = {
			get: "/api/v1/planet/{id}",
		};
	}
	// 更新星球
	rpc UpdatePlanet (planet_v1.UpdatePlanetRequest) returns (planet_v1.UpdatePlanetReply) {
		option (google.api.http) = {
			put: "/api/v1/planet/{id}",
			body: "*",
		};
	}
	// 删除星球
	rpc DeletePlanet (planet_v1.DeletePlanetRequest) returns (planet_v1.DeletePlanetReply) {
		option (google.api.http) = {
			delete: "/api/v1/planet/{id}",
		};
	}
	// 创建星球目标
	rpc CreatePlanetTarget (planet_v1.CreatePlanetTargetRequest) returns (planet_v1.CreatePlanetTargetReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/target",
			body: "*",
		};
	}
	// 获取星球目标详情
	rpc GetPlanetTarget (planet_v1.GetPlanetTargetRequest) returns (planet_v1.GetPlanetTargetReply) {
		option (google.api.http) = {
			get: "/api/v1/planet/{planet_id}/target/{id}",
		};
	}
	// 更新星球目标
	rpc UpdatePlanetTarget (planet_v1.UpdatePlanetTargetRequest) returns (planet_v1.UpdatePlanetTargetReply) {
		option (google.api.http) = {
			put: "/api/v1/planet/{planet_id}/target/{id}",
			body: "*",
		};
	}
	// 删除星球目标
	rpc DeletePlanetTarget (planet_v1.DeletePlanetTargetRequest) returns (planet_v1.DeletePlanetTargetReply) {
		option (google.api.http) = {
			delete: "/api/v1/planet/{planet_id}/target/{id}",
		};
	}
	// 获取星球目标列表
	rpc ListPlanetTarget (planet_v1.ListPlanetTargetRequest) returns (planet_v1.ListPlanetTargetReply) {
		option (google.api.http) = {
			get: "/api/v1/planet/{planet_id}/target",
		};
	}

	// 获取用户加入星球列表
	rpc ListPlanetByUserID (user_planet_v1.ListPlanetByUserIDRequest) returns (user_planet_v1.ListPlanetByUserIDReply) {
		option (google.api.http) = {
			get: "/api/v1/planet",
		};
	}
	// 加入星球
	rpc JoinPlanet (user_planet_v1.JoinPlanetRequest) returns (user_planet_v1.JoinPlanetReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/join",
			body: "*",
		};
	}
	// 退出星球
	rpc QuitPlanet (user_planet_v1.QuitPlanetRequest) returns (user_planet_v1.QuitPlanetReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/quit",
			body: "*",
		};
	}
	// 加入星球目标
	rpc JoinPlanetTarget (user_planet_v1.JoinPlanetTargetRequest) returns (user_planet_v1.JoinPlanetTargetReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/target/{target_id}/join",
			body: "*",
		};
	}
	// 退出星球目标
	rpc QuitPlanetTarget (user_planet_v1.QuitPlanetTargetRequest) returns (user_planet_v1.QuitPlanetTargetReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/target/{target_id}/quit",
			body: "*",
		};
	}
	// 点赞星球动态
	rpc LikePlanetPost (user_planet_v1.LikePlanetPostRequest) returns (user_planet_v1.LikePlanetPostReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/post/{post_id}/like",
			body: "*",
		};
	}
	// 取消点赞星球动态
	rpc CancelLikePlanetPost (user_planet_v1.CancelLikePlanetPostRequest) returns (user_planet_v1.CancelLikePlanetPostReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/post/{post_id}/cancel-like",
			body: "*",
		};
	}
	// 收藏星球动态
	rpc FavoritePlanetPost (user_planet_v1.FavoritePlanetPostRequest) returns (user_planet_v1.FavoritePlanetPostReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/post/{post_id}/favorite",
			body: "*",
		};
	}
	// 取消收藏星球动态
	rpc CancelFavoritePlanetPost (user_planet_v1.CancelFavoritePlanetPostRequest) returns (user_planet_v1.CancelFavoritePlanetPostReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/post/{post_id}/cancel-favorite",
			body: "*",
		};
	}
	// 创建星球动态
	rpc CreatePlanetPost (user_planet_v1.CreatePlanetPostRequest) returns (user_planet_v1.CreatePlanetPostReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/post",
			body: "*",
		};
	}
	// 修改星球动态
	rpc UpdatePlanetPost (user_planet_v1.UpdatePlanetPostRequest) returns (user_planet_v1.UpdatePlanetPostReply) {
		option (google.api.http) = {
			put: "/api/v1/planet/{planet_id}/post/{post_id}",
			body: "*",
		};
	}
	// 删除星球动态
	rpc DeletePlanetPost (user_planet_v1.DeletePlanetPostRequest) returns (user_planet_v1.DeletePlanetPostReply) {
		option (google.api.http) = {
			delete: "/api/v1/planet/{planet_id}/post/{post_id}",
		};
	}
	// 置顶星球动态
	rpc ToppedPlanetPost (user_planet_v1.ToppedPlanetPostRequest) returns (user_planet_v1.ToppedPlanetPostReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/post/{post_id}/topped",
			body: "*",
		};
	}
	// 获取星球动态列表
	rpc ListPlanetPost (user_planet_v1.ListPlanetPostRequest) returns (user_planet_v1.ListPlanetPostReply) {
		option (google.api.http) = {
			get: "/api/v1/planet/{planet_id}/post",
		};
	}
	// 获取星球热点动态列表
	rpc ListPlanetTopPost (user_planet_v1.ListPlanetTopPostRequest) returns (user_planet_v1.ListPlanetTopPostReply) {
		option (google.api.http) = {
			get: "/api/v1/planet/{planet_id}/top-post",
		};
	}
	// 创建动态评论
	rpc CreatePlanetPostComment (user_planet_v1.CreatePlanetPostCommentRequest) returns (user_planet_v1.CreatePlanetPostCommentReply) {
		option (google.api.http) = {
			post: "/api/v1/planet/{planet_id}/post/{post_id}/comment",
			body: "*",
		};
	}
	// 删除动态评论
	rpc DeletePlanetPostComment (user_planet_v1.DeletePlanetPostCommentRequest) returns (user_planet_v1.DeletePlanetPostCommentReply) {
		option (google.api.http) = {
			delete: "/api/v1/planet/{planet_id}/post/{post_id}/comment/{comment_id}",
		};
	}
	// 获取动态评论列表
	rpc ListPlanetPostComment (user_planet_v1.ListPlanetPostCommentRequest) returns (user_planet_v1.ListPlanetPostCommentReply) {
		option (google.api.http) = {
			get: "/api/v1/planet/{planet_id}/post/{post_id}/comment",
		};
	}

	// 运行定时任务
	rpc RunCronTask (user_habit_v1.RunCronTaskRequest) returns (user_habit_v1.RunCronTaskReply) {
		option (google.api.http) = {
			post: "/api/v1/cron/run",
			body: "*",
		};
	}

	// 数据统计
	// 获取首页每日数据
	rpc TodayStatisticFromHome (user_statistic_v1.TodayStatisticFromHomeRequest) returns (user_statistic_v1.TodayStatisticFromHomeReply) {
		option (google.api.http) = {
			get: "/api/v1/user_statistic/today",
		};
	}
	// 获取用户习惯详情统计数据
	rpc UserHabitStatisticFromDetail (user_statistic_v1.UserHabitStatisticFromDetailRequest) returns (user_statistic_v1.UserHabitStatisticFromDetailReply) {
		option (google.api.http) = {
			get: "/api/v1/user_statistic/habit/{user_habit_id}",
		};
	}

	rpc UnreadCount (user_message_v1.UnreadCountRequest) returns (user_message_v1.UnreadCountReply) {
		option (google.api.http) = {
			get: "/api/v1/message/unread-count",
		};
	}
}
