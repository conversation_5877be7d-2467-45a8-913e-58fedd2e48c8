syntax = "proto3";

package user_habit_v1;

import "validate/validate.proto";

option go_package = "github.com/wlnil/life-log-be/api/user_habit/v1;v1";

message UserHabitConfig {
  int32 punch_max_count = 1;
  int32 punch_cycle_type = 4;
  repeated int32 punch_cycle = 5;
  repeated string small_stages = 6;
  repeated int32 stage_punches = 11;
  bool is_allow_cancel_punch = 12; // 是否允许取消打卡
  bool is_join_award = 13; // 是否参与激励
  bool is_set_privacy = 14; //  是否设置隐私
  string privacy_display_mode = 17; // 隐私显示模式
  string privacy_display_content = 18; // 隐私显示内容
  int32 record_type = 15; // 统计方式
  string end_date = 16; // 结束日期
  repeated string reminder_times = 19; // 提醒时间列表，格式：["09:00", "18:00", "21:30"]
  repeated int32 buddy_list = 20; // 伙伴列表
}

message CreateUserHabitRequest {
  string name = 1;
  UserHabitConfig config = 4;
  string create_date = 5; // eg: 2023-11-26T07:00:00+08:00
  int32 habit_type = 6;
  string timezone_place = 7; // 时区地点，eg: Asia/Shanghai
  string timezone = 8 [(validate.rules).string.len = 6]; // 时区，eg: +08:00
}
message CreateUserHabitReply {
  int32 code = 1;
  string msg = 2;
}

message UpdateUserHabitRequest {
  int32 id = 1;
  string name = 2;
  UserHabitConfig config = 5;
  string update_date = 6; // eg: 2023-11-26T07:00:00+08:00
  string timezone_place = 7; // 时区地点，eg: Asia/Shanghai
  string timezone = 8 [(validate.rules).string.len = 6]; // 时区，eg: +08:00
}
message UpdateUserHabitReply {
  int32 code = 1;
  string msg = 2;
}

message DeleteUserHabitRequest {
  int32 id = 1;
}
message DeleteUserHabitReply {}

message PauseUserHabitRequest {
  int32 id = 1;
}
message PauseUserHabitReply {}

message RecoverUserHabitRequest {
  int32 id = 1;
}
message RecoverUserHabitReply {}

message ArchiveUserHabitRequest {
  int32 id = 1;
}
message ArchiveUserHabitReply {}

message GetUserHabitRequest {
  int32 id = 1;
  string current_date = 2; // eg: 2023-11-26T07:00:00+08:00
}
message GetUserHabitReply {
  message DayData {
    int32 dateTimestamp = 1;
    int32 complete_level = 2; // 完成等级
  }

  message TimeLine {
    int32 time = 1; // eg: 2023-12-25T07:00:00+08:00
    string operateType = 2; // 操作类型
    string small_stage_name = 3;
    int32 small_stage_id = 9;
    int32 duration = 4; // 计时时长/秒
    string memo_content = 5; // 随记内容
    int32 punch_id = 6; // 打卡 ID
    int32 memo_id = 7; // 随记 ID
    int32 reckon_id = 8; // 计时 ID
  }

  message Data {
    int32 id = 1;
    string name = 2;
    int32 status = 3;
    int32 habit_type = 4;
    UserHabitConfig config = 5;
    int32 punchedTotal = 6;
    repeated DayData calendar = 7;
    repeated TimeLine timeline = 8;
    string desc = 9;
    bool is_completed = 10;
    int32 created_at = 11;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message ListUserHabitRequest {
  int32 status = 1;
  string current_date = 2;
}
message ListUserHabitReply {
  message Data {
    int32 id = 1;
    string name = 2;
    int32 status = 3;
    int32 punched_days = 4;
    int32 punch_all_days = 15;
    int32 punch_cycle_type = 5;
    repeated int32 punch_cycle = 6;
    int32 streak_days = 7;
    bool is_set_privacy = 9;
    int32 created_at = 13;
    int32 end_at = 14;
    string health_status = 16;
  }

  int32 code = 1;
  string msg = 2;
  repeated Data data = 3;
}

message CreateUserHabitMemoRequest {
  int32 user_habit_id = 1;
  string content = 2;
  repeated string images = 3;
  int32 small_stage_id = 4; // 微习惯阶段 ID
  string current_date = 5; // eg: 2023-11-26T07:00:00+08:00
}
message CreateUserHabitMemoReply {
  int32 code = 1;
  string msg = 2;
}

message UpdateUserHabitMemoRequest {
  int32 user_habit_id = 1;
  string content = 2;
  int32 memo_id = 3;
}
message UpdateUserHabitMemoReply {
  int32 code = 1;
  string msg = 2;
}

message DeleteUserHabitMemoRequest {
  int32 user_habit_id = 1;
  int32 memo_id = 4;
}
message DeleteUserHabitMemoReply {
  int32 code = 1;
  string msg = 2;
}

message PunchUserHabitRequest {
  int32 user_habit_id = 1;
  int32 small_stage_id = 3; // 微习惯阶段 ID
  string current_date = 4; // eg: 2023-11-26T07:00:00+08:00
}
message PunchUserHabitReply {
  int32 code = 1;
  string msg = 2;
}

message UpdatePunchUserHabitRequest {
  int32 user_habit_id = 1;
  int32 punch_id = 2;
  string current_date = 3; // eg: 2023-11-26T07:00:00+08:00
}
message UpdatePunchUserHabitReply {
  int32 code = 1;
  string msg = 2;
}

message CancelPunchUserHabitRequest {
  int32 user_habit_id = 1;
  int32 small_stage_id = 3; // 微习惯阶段 ID
  string current_date = 4; // eg: 2023-11-26T07:00:00+08:00
  int32 punch_id = 5;
}
message CancelPunchUserHabitReply {
  int32 code = 1;
  string msg = 2;
}

message ReckonUserHabitRequest {
  int32 user_habit_id = 1;
  int32 reckon_duration = 2; // 计时时长/秒
  int32 small_stage_id = 3; // 微习惯阶段 ID
  string current_date = 4; // eg: 2023-11-26T07:00:00+08:00
  bool is_end = 5; // 是否结束计时
}
message ReckonUserHabitReply {
  message Data {
    int32 reckon_duration = 1; // 计时时长/秒
    bool is_reckoning = 2; // 是否正在计时
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message CancelReckonUserHabitRequest {
  int32 user_habit_id = 1;
  int32 small_stage_id = 3; // 微习惯阶段 ID
  string current_date = 4; // eg: 2023-11-26T07:00:00+08:00
}
message CancelReckonUserHabitReply {
  int32 code = 1;
  string msg = 2;
}

message CreateUserHabitReckonRequest {
  int32 user_habit_id = 1;
  int32 reckon_duration = 2; // 计时时长/秒
  int32 small_stage_id = 3; // 微习惯阶段 ID
  string current_date = 4; // eg: 2023-11-26T07:00:00+08:00
}
message CreateUserHabitReckonReply {
  int32 code = 1;
  string msg = 2;
}

message UpdateUserHabitReckonRequest {
  int32 user_habit_id = 1;
  int32 reckon_duration = 2; // 计时时长/秒
  int32 reckon_id = 3;
  int32 small_stage_id = 4; // 微习惯阶段 ID
}
message UpdateUserHabitReckonReply {
  int32 code = 1;
  string msg = 2;
}

message DeleteUserHabitReckonRequest {
  int32 user_habit_id = 1;
  int32 reckon_id = 4;
}
message DeleteUserHabitReckonReply {
  int32 code = 1;
  string msg = 2;
}

message ListUserHabitSnapshotRequest {
  int32 label_type = 1;
  // eg: 2023-11-26T07:00:00+08:00
  string current_date = 2;
}
message ListUserHabitSnapshotReply {
  message SmallStageItem {
    int32 stage_id = 1;
    string name = 2;
    int32 punched_count = 3;
    bool is_reckoning = 4; // 是否正在计时
  }

  message HabitItem {
    int32 id = 1; // 习惯 ID
    string name = 2;
    int32 habit_type = 5;
    repeated SmallStageItem small_stages = 6;
    bool is_reckoning = 7; // 是否正在计时
    int32 duration = 8; // 当前计时的时长（单位：秒）
    bool is_allow_cancel_punch = 9; // 是否允许取消打卡
    bool is_join_award = 10; // 是否参与激励
    bool is_necessary = 11; // 是否必须完成
    bool is_set_privacy = 12;
    int32 created_at = 13;
    int32 punch_cycle_type = 14;
    bool completed = 15;
    int32 per = 16;
    int32 doneCount = 17;
    int32 allCount = 18;
    string end_date = 19;
  }

  message Data {
    TodayStatistic today_statistic_data = 1;
    repeated  HabitItem top_habits = 2;
    repeated  HabitItem low_habits = 3;
    repeated  HabitItem track_habits = 4;
    bool is_need_show = 5;
    repeated int32 pending_motivation_habits = 6;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message RunCronTaskRequest {
  int32 task_id = 1;
  bool not_run_tag = 2;
}
message RunCronTaskReply {
  int32 code = 1;
  string msg = 2;
}

message TodayStatistic {
  message HabitDetail {
    int32 per = 1;
    int32 done_count = 2;
    int32 all_count = 3;
  }

  message WeekData {
    string day = 1;
    string progress = 2;
    bool is_today = 3;
  }

  HabitDetail small_habit = 1;
  HabitDetail normal_habit = 2;
  repeated WeekData week_data = 5;
}
