// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: user_habit/v1/user_habit.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserHabitConfig struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	PunchMaxCount         int32                  `protobuf:"varint,1,opt,name=punch_max_count,json=punchMaxCount,proto3" json:"punch_max_count,omitempty"`
	PunchCycleType        int32                  `protobuf:"varint,4,opt,name=punch_cycle_type,json=punchCycleType,proto3" json:"punch_cycle_type,omitempty"`
	PunchCycle            []int32                `protobuf:"varint,5,rep,packed,name=punch_cycle,json=punchCycle,proto3" json:"punch_cycle,omitempty"`
	SmallStages           []string               `protobuf:"bytes,6,rep,name=small_stages,json=smallStages,proto3" json:"small_stages,omitempty"`
	StagePunches          []int32                `protobuf:"varint,11,rep,packed,name=stage_punches,json=stagePunches,proto3" json:"stage_punches,omitempty"`
	IsAllowCancelPunch    bool                   `protobuf:"varint,12,opt,name=is_allow_cancel_punch,json=isAllowCancelPunch,proto3" json:"is_allow_cancel_punch,omitempty"`       // 是否允许取消打卡
	IsJoinAward           bool                   `protobuf:"varint,13,opt,name=is_join_award,json=isJoinAward,proto3" json:"is_join_award,omitempty"`                              // 是否参与激励
	IsSetPrivacy          bool                   `protobuf:"varint,14,opt,name=is_set_privacy,json=isSetPrivacy,proto3" json:"is_set_privacy,omitempty"`                           //  是否设置隐私
	PrivacyDisplayMode    string                 `protobuf:"bytes,17,opt,name=privacy_display_mode,json=privacyDisplayMode,proto3" json:"privacy_display_mode,omitempty"`          // 隐私显示模式
	PrivacyDisplayContent string                 `protobuf:"bytes,18,opt,name=privacy_display_content,json=privacyDisplayContent,proto3" json:"privacy_display_content,omitempty"` // 隐私显示内容
	RecordType            int32                  `protobuf:"varint,15,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"`                                   // 统计方式
	EndDate               string                 `protobuf:"bytes,16,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`                                             // 结束日期
	ReminderTimes         []string               `protobuf:"bytes,19,rep,name=reminder_times,json=reminderTimes,proto3" json:"reminder_times,omitempty"`                           // 提醒时间列表，格式：["09:00", "18:00", "21:30"]
	BuddyList             []int32                `protobuf:"varint,20,rep,packed,name=buddy_list,json=buddyList,proto3" json:"buddy_list,omitempty"`                               // 伙伴列表
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UserHabitConfig) Reset() {
	*x = UserHabitConfig{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserHabitConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserHabitConfig) ProtoMessage() {}

func (x *UserHabitConfig) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserHabitConfig.ProtoReflect.Descriptor instead.
func (*UserHabitConfig) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{0}
}

func (x *UserHabitConfig) GetPunchMaxCount() int32 {
	if x != nil {
		return x.PunchMaxCount
	}
	return 0
}

func (x *UserHabitConfig) GetPunchCycleType() int32 {
	if x != nil {
		return x.PunchCycleType
	}
	return 0
}

func (x *UserHabitConfig) GetPunchCycle() []int32 {
	if x != nil {
		return x.PunchCycle
	}
	return nil
}

func (x *UserHabitConfig) GetSmallStages() []string {
	if x != nil {
		return x.SmallStages
	}
	return nil
}

func (x *UserHabitConfig) GetStagePunches() []int32 {
	if x != nil {
		return x.StagePunches
	}
	return nil
}

func (x *UserHabitConfig) GetIsAllowCancelPunch() bool {
	if x != nil {
		return x.IsAllowCancelPunch
	}
	return false
}

func (x *UserHabitConfig) GetIsJoinAward() bool {
	if x != nil {
		return x.IsJoinAward
	}
	return false
}

func (x *UserHabitConfig) GetIsSetPrivacy() bool {
	if x != nil {
		return x.IsSetPrivacy
	}
	return false
}

func (x *UserHabitConfig) GetPrivacyDisplayMode() string {
	if x != nil {
		return x.PrivacyDisplayMode
	}
	return ""
}

func (x *UserHabitConfig) GetPrivacyDisplayContent() string {
	if x != nil {
		return x.PrivacyDisplayContent
	}
	return ""
}

func (x *UserHabitConfig) GetRecordType() int32 {
	if x != nil {
		return x.RecordType
	}
	return 0
}

func (x *UserHabitConfig) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

func (x *UserHabitConfig) GetReminderTimes() []string {
	if x != nil {
		return x.ReminderTimes
	}
	return nil
}

func (x *UserHabitConfig) GetBuddyList() []int32 {
	if x != nil {
		return x.BuddyList
	}
	return nil
}

type CreateUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Config        *UserHabitConfig       `protobuf:"bytes,4,opt,name=config,proto3" json:"config,omitempty"`
	CreateDate    string                 `protobuf:"bytes,5,opt,name=create_date,json=createDate,proto3" json:"create_date,omitempty"` // eg: 2023-11-26T07:00:00+08:00
	HabitType     int32                  `protobuf:"varint,6,opt,name=habit_type,json=habitType,proto3" json:"habit_type,omitempty"`
	TimezonePlace string                 `protobuf:"bytes,7,opt,name=timezone_place,json=timezonePlace,proto3" json:"timezone_place,omitempty"` // 时区地点，eg: Asia/Shanghai
	Timezone      string                 `protobuf:"bytes,8,opt,name=timezone,proto3" json:"timezone,omitempty"`                                // 时区，eg: +08:00
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserHabitRequest) Reset() {
	*x = CreateUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserHabitRequest) ProtoMessage() {}

func (x *CreateUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserHabitRequest.ProtoReflect.Descriptor instead.
func (*CreateUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{1}
}

func (x *CreateUserHabitRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateUserHabitRequest) GetConfig() *UserHabitConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *CreateUserHabitRequest) GetCreateDate() string {
	if x != nil {
		return x.CreateDate
	}
	return ""
}

func (x *CreateUserHabitRequest) GetHabitType() int32 {
	if x != nil {
		return x.HabitType
	}
	return 0
}

func (x *CreateUserHabitRequest) GetTimezonePlace() string {
	if x != nil {
		return x.TimezonePlace
	}
	return ""
}

func (x *CreateUserHabitRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type CreateUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserHabitReply) Reset() {
	*x = CreateUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserHabitReply) ProtoMessage() {}

func (x *CreateUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserHabitReply.ProtoReflect.Descriptor instead.
func (*CreateUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{2}
}

func (x *CreateUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdateUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Config        *UserHabitConfig       `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	UpdateDate    string                 `protobuf:"bytes,6,opt,name=update_date,json=updateDate,proto3" json:"update_date,omitempty"`          // eg: 2023-11-26T07:00:00+08:00
	TimezonePlace string                 `protobuf:"bytes,7,opt,name=timezone_place,json=timezonePlace,proto3" json:"timezone_place,omitempty"` // 时区地点，eg: Asia/Shanghai
	Timezone      string                 `protobuf:"bytes,8,opt,name=timezone,proto3" json:"timezone,omitempty"`                                // 时区，eg: +08:00
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserHabitRequest) Reset() {
	*x = UpdateUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserHabitRequest) ProtoMessage() {}

func (x *UpdateUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserHabitRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUserHabitRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserHabitRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateUserHabitRequest) GetConfig() *UserHabitConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *UpdateUserHabitRequest) GetUpdateDate() string {
	if x != nil {
		return x.UpdateDate
	}
	return ""
}

func (x *UpdateUserHabitRequest) GetTimezonePlace() string {
	if x != nil {
		return x.TimezonePlace
	}
	return ""
}

func (x *UpdateUserHabitRequest) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

type UpdateUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserHabitReply) Reset() {
	*x = UpdateUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserHabitReply) ProtoMessage() {}

func (x *UpdateUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserHabitReply.ProtoReflect.Descriptor instead.
func (*UpdateUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeleteUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserHabitRequest) Reset() {
	*x = DeleteUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserHabitRequest) ProtoMessage() {}

func (x *DeleteUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserHabitRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteUserHabitRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserHabitReply) Reset() {
	*x = DeleteUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserHabitReply) ProtoMessage() {}

func (x *DeleteUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserHabitReply.ProtoReflect.Descriptor instead.
func (*DeleteUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{6}
}

type PauseUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PauseUserHabitRequest) Reset() {
	*x = PauseUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PauseUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseUserHabitRequest) ProtoMessage() {}

func (x *PauseUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseUserHabitRequest.ProtoReflect.Descriptor instead.
func (*PauseUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{7}
}

func (x *PauseUserHabitRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type PauseUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PauseUserHabitReply) Reset() {
	*x = PauseUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PauseUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PauseUserHabitReply) ProtoMessage() {}

func (x *PauseUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PauseUserHabitReply.ProtoReflect.Descriptor instead.
func (*PauseUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{8}
}

type RecoverUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecoverUserHabitRequest) Reset() {
	*x = RecoverUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecoverUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverUserHabitRequest) ProtoMessage() {}

func (x *RecoverUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverUserHabitRequest.ProtoReflect.Descriptor instead.
func (*RecoverUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{9}
}

func (x *RecoverUserHabitRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type RecoverUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecoverUserHabitReply) Reset() {
	*x = RecoverUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecoverUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoverUserHabitReply) ProtoMessage() {}

func (x *RecoverUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoverUserHabitReply.ProtoReflect.Descriptor instead.
func (*RecoverUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{10}
}

type ArchiveUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArchiveUserHabitRequest) Reset() {
	*x = ArchiveUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveUserHabitRequest) ProtoMessage() {}

func (x *ArchiveUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveUserHabitRequest.ProtoReflect.Descriptor instead.
func (*ArchiveUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{11}
}

func (x *ArchiveUserHabitRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type ArchiveUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArchiveUserHabitReply) Reset() {
	*x = ArchiveUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveUserHabitReply) ProtoMessage() {}

func (x *ArchiveUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveUserHabitReply.ProtoReflect.Descriptor instead.
func (*ArchiveUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{12}
}

type GetUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CurrentDate   string                 `protobuf:"bytes,2,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"` // eg: 2023-11-26T07:00:00+08:00
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserHabitRequest) Reset() {
	*x = GetUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserHabitRequest) ProtoMessage() {}

func (x *GetUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserHabitRequest.ProtoReflect.Descriptor instead.
func (*GetUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{13}
}

func (x *GetUserHabitRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetUserHabitRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type GetUserHabitReply struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetUserHabitReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserHabitReply) Reset() {
	*x = GetUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserHabitReply) ProtoMessage() {}

func (x *GetUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserHabitReply.ProtoReflect.Descriptor instead.
func (*GetUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{14}
}

func (x *GetUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserHabitReply) GetData() *GetUserHabitReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        int32                  `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	CurrentDate   string                 `protobuf:"bytes,2,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserHabitRequest) Reset() {
	*x = ListUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserHabitRequest) ProtoMessage() {}

func (x *ListUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserHabitRequest.ProtoReflect.Descriptor instead.
func (*ListUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{15}
}

func (x *ListUserHabitRequest) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListUserHabitRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type ListUserHabitReply struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          []*ListUserHabitReply_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserHabitReply) Reset() {
	*x = ListUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserHabitReply) ProtoMessage() {}

func (x *ListUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserHabitReply.ProtoReflect.Descriptor instead.
func (*ListUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{16}
}

func (x *ListUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListUserHabitReply) GetData() []*ListUserHabitReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreateUserHabitMemoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Images        []string               `protobuf:"bytes,3,rep,name=images,proto3" json:"images,omitempty"`
	SmallStageId  int32                  `protobuf:"varint,4,opt,name=small_stage_id,json=smallStageId,proto3" json:"small_stage_id,omitempty"` // 微习惯阶段 ID
	CurrentDate   string                 `protobuf:"bytes,5,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`       // eg: 2023-11-26T07:00:00+08:00
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserHabitMemoRequest) Reset() {
	*x = CreateUserHabitMemoRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserHabitMemoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserHabitMemoRequest) ProtoMessage() {}

func (x *CreateUserHabitMemoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserHabitMemoRequest.ProtoReflect.Descriptor instead.
func (*CreateUserHabitMemoRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{17}
}

func (x *CreateUserHabitMemoRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *CreateUserHabitMemoRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *CreateUserHabitMemoRequest) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *CreateUserHabitMemoRequest) GetSmallStageId() int32 {
	if x != nil {
		return x.SmallStageId
	}
	return 0
}

func (x *CreateUserHabitMemoRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type CreateUserHabitMemoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserHabitMemoReply) Reset() {
	*x = CreateUserHabitMemoReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserHabitMemoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserHabitMemoReply) ProtoMessage() {}

func (x *CreateUserHabitMemoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserHabitMemoReply.ProtoReflect.Descriptor instead.
func (*CreateUserHabitMemoReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{18}
}

func (x *CreateUserHabitMemoReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateUserHabitMemoReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdateUserHabitMemoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	MemoId        int32                  `protobuf:"varint,3,opt,name=memo_id,json=memoId,proto3" json:"memo_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserHabitMemoRequest) Reset() {
	*x = UpdateUserHabitMemoRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserHabitMemoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserHabitMemoRequest) ProtoMessage() {}

func (x *UpdateUserHabitMemoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserHabitMemoRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserHabitMemoRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateUserHabitMemoRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *UpdateUserHabitMemoRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UpdateUserHabitMemoRequest) GetMemoId() int32 {
	if x != nil {
		return x.MemoId
	}
	return 0
}

type UpdateUserHabitMemoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserHabitMemoReply) Reset() {
	*x = UpdateUserHabitMemoReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserHabitMemoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserHabitMemoReply) ProtoMessage() {}

func (x *UpdateUserHabitMemoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserHabitMemoReply.ProtoReflect.Descriptor instead.
func (*UpdateUserHabitMemoReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateUserHabitMemoReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserHabitMemoReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeleteUserHabitMemoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	MemoId        int32                  `protobuf:"varint,4,opt,name=memo_id,json=memoId,proto3" json:"memo_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserHabitMemoRequest) Reset() {
	*x = DeleteUserHabitMemoRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserHabitMemoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserHabitMemoRequest) ProtoMessage() {}

func (x *DeleteUserHabitMemoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserHabitMemoRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserHabitMemoRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{21}
}

func (x *DeleteUserHabitMemoRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *DeleteUserHabitMemoRequest) GetMemoId() int32 {
	if x != nil {
		return x.MemoId
	}
	return 0
}

type DeleteUserHabitMemoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserHabitMemoReply) Reset() {
	*x = DeleteUserHabitMemoReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserHabitMemoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserHabitMemoReply) ProtoMessage() {}

func (x *DeleteUserHabitMemoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserHabitMemoReply.ProtoReflect.Descriptor instead.
func (*DeleteUserHabitMemoReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{22}
}

func (x *DeleteUserHabitMemoReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteUserHabitMemoReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type PunchUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	SmallStageId  int32                  `protobuf:"varint,3,opt,name=small_stage_id,json=smallStageId,proto3" json:"small_stage_id,omitempty"` // 微习惯阶段 ID
	CurrentDate   string                 `protobuf:"bytes,4,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`       // eg: 2023-11-26T07:00:00+08:00
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PunchUserHabitRequest) Reset() {
	*x = PunchUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PunchUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PunchUserHabitRequest) ProtoMessage() {}

func (x *PunchUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PunchUserHabitRequest.ProtoReflect.Descriptor instead.
func (*PunchUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{23}
}

func (x *PunchUserHabitRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *PunchUserHabitRequest) GetSmallStageId() int32 {
	if x != nil {
		return x.SmallStageId
	}
	return 0
}

func (x *PunchUserHabitRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type PunchUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PunchUserHabitReply) Reset() {
	*x = PunchUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PunchUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PunchUserHabitReply) ProtoMessage() {}

func (x *PunchUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PunchUserHabitReply.ProtoReflect.Descriptor instead.
func (*PunchUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{24}
}

func (x *PunchUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PunchUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdatePunchUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	PunchId       int32                  `protobuf:"varint,2,opt,name=punch_id,json=punchId,proto3" json:"punch_id,omitempty"`
	CurrentDate   string                 `protobuf:"bytes,3,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"` // eg: 2023-11-26T07:00:00+08:00
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePunchUserHabitRequest) Reset() {
	*x = UpdatePunchUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePunchUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePunchUserHabitRequest) ProtoMessage() {}

func (x *UpdatePunchUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePunchUserHabitRequest.ProtoReflect.Descriptor instead.
func (*UpdatePunchUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{25}
}

func (x *UpdatePunchUserHabitRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *UpdatePunchUserHabitRequest) GetPunchId() int32 {
	if x != nil {
		return x.PunchId
	}
	return 0
}

func (x *UpdatePunchUserHabitRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type UpdatePunchUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePunchUserHabitReply) Reset() {
	*x = UpdatePunchUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePunchUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePunchUserHabitReply) ProtoMessage() {}

func (x *UpdatePunchUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePunchUserHabitReply.ProtoReflect.Descriptor instead.
func (*UpdatePunchUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{26}
}

func (x *UpdatePunchUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdatePunchUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CancelPunchUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	SmallStageId  int32                  `protobuf:"varint,3,opt,name=small_stage_id,json=smallStageId,proto3" json:"small_stage_id,omitempty"` // 微习惯阶段 ID
	CurrentDate   string                 `protobuf:"bytes,4,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`       // eg: 2023-11-26T07:00:00+08:00
	PunchId       int32                  `protobuf:"varint,5,opt,name=punch_id,json=punchId,proto3" json:"punch_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelPunchUserHabitRequest) Reset() {
	*x = CancelPunchUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelPunchUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPunchUserHabitRequest) ProtoMessage() {}

func (x *CancelPunchUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPunchUserHabitRequest.ProtoReflect.Descriptor instead.
func (*CancelPunchUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{27}
}

func (x *CancelPunchUserHabitRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *CancelPunchUserHabitRequest) GetSmallStageId() int32 {
	if x != nil {
		return x.SmallStageId
	}
	return 0
}

func (x *CancelPunchUserHabitRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

func (x *CancelPunchUserHabitRequest) GetPunchId() int32 {
	if x != nil {
		return x.PunchId
	}
	return 0
}

type CancelPunchUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelPunchUserHabitReply) Reset() {
	*x = CancelPunchUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelPunchUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPunchUserHabitReply) ProtoMessage() {}

func (x *CancelPunchUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPunchUserHabitReply.ProtoReflect.Descriptor instead.
func (*CancelPunchUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{28}
}

func (x *CancelPunchUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CancelPunchUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ReckonUserHabitRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId    int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	ReckonDuration int32                  `protobuf:"varint,2,opt,name=reckon_duration,json=reckonDuration,proto3" json:"reckon_duration,omitempty"` // 计时时长/秒
	SmallStageId   int32                  `protobuf:"varint,3,opt,name=small_stage_id,json=smallStageId,proto3" json:"small_stage_id,omitempty"`     // 微习惯阶段 ID
	CurrentDate    string                 `protobuf:"bytes,4,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`           // eg: 2023-11-26T07:00:00+08:00
	IsEnd          bool                   `protobuf:"varint,5,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`                            // 是否结束计时
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ReckonUserHabitRequest) Reset() {
	*x = ReckonUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReckonUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReckonUserHabitRequest) ProtoMessage() {}

func (x *ReckonUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReckonUserHabitRequest.ProtoReflect.Descriptor instead.
func (*ReckonUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{29}
}

func (x *ReckonUserHabitRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *ReckonUserHabitRequest) GetReckonDuration() int32 {
	if x != nil {
		return x.ReckonDuration
	}
	return 0
}

func (x *ReckonUserHabitRequest) GetSmallStageId() int32 {
	if x != nil {
		return x.SmallStageId
	}
	return 0
}

func (x *ReckonUserHabitRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

func (x *ReckonUserHabitRequest) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

type ReckonUserHabitReply struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *ReckonUserHabitReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReckonUserHabitReply) Reset() {
	*x = ReckonUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReckonUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReckonUserHabitReply) ProtoMessage() {}

func (x *ReckonUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReckonUserHabitReply.ProtoReflect.Descriptor instead.
func (*ReckonUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{30}
}

func (x *ReckonUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ReckonUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ReckonUserHabitReply) GetData() *ReckonUserHabitReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CancelReckonUserHabitRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	SmallStageId  int32                  `protobuf:"varint,3,opt,name=small_stage_id,json=smallStageId,proto3" json:"small_stage_id,omitempty"` // 微习惯阶段 ID
	CurrentDate   string                 `protobuf:"bytes,4,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`       // eg: 2023-11-26T07:00:00+08:00
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelReckonUserHabitRequest) Reset() {
	*x = CancelReckonUserHabitRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelReckonUserHabitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelReckonUserHabitRequest) ProtoMessage() {}

func (x *CancelReckonUserHabitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelReckonUserHabitRequest.ProtoReflect.Descriptor instead.
func (*CancelReckonUserHabitRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{31}
}

func (x *CancelReckonUserHabitRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *CancelReckonUserHabitRequest) GetSmallStageId() int32 {
	if x != nil {
		return x.SmallStageId
	}
	return 0
}

func (x *CancelReckonUserHabitRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type CancelReckonUserHabitReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelReckonUserHabitReply) Reset() {
	*x = CancelReckonUserHabitReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelReckonUserHabitReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelReckonUserHabitReply) ProtoMessage() {}

func (x *CancelReckonUserHabitReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelReckonUserHabitReply.ProtoReflect.Descriptor instead.
func (*CancelReckonUserHabitReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{32}
}

func (x *CancelReckonUserHabitReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CancelReckonUserHabitReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CreateUserHabitReckonRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId    int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	ReckonDuration int32                  `protobuf:"varint,2,opt,name=reckon_duration,json=reckonDuration,proto3" json:"reckon_duration,omitempty"` // 计时时长/秒
	SmallStageId   int32                  `protobuf:"varint,3,opt,name=small_stage_id,json=smallStageId,proto3" json:"small_stage_id,omitempty"`     // 微习惯阶段 ID
	CurrentDate    string                 `protobuf:"bytes,4,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`           // eg: 2023-11-26T07:00:00+08:00
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateUserHabitReckonRequest) Reset() {
	*x = CreateUserHabitReckonRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserHabitReckonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserHabitReckonRequest) ProtoMessage() {}

func (x *CreateUserHabitReckonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserHabitReckonRequest.ProtoReflect.Descriptor instead.
func (*CreateUserHabitReckonRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{33}
}

func (x *CreateUserHabitReckonRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *CreateUserHabitReckonRequest) GetReckonDuration() int32 {
	if x != nil {
		return x.ReckonDuration
	}
	return 0
}

func (x *CreateUserHabitReckonRequest) GetSmallStageId() int32 {
	if x != nil {
		return x.SmallStageId
	}
	return 0
}

func (x *CreateUserHabitReckonRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type CreateUserHabitReckonReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserHabitReckonReply) Reset() {
	*x = CreateUserHabitReckonReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserHabitReckonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserHabitReckonReply) ProtoMessage() {}

func (x *CreateUserHabitReckonReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserHabitReckonReply.ProtoReflect.Descriptor instead.
func (*CreateUserHabitReckonReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{34}
}

func (x *CreateUserHabitReckonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateUserHabitReckonReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdateUserHabitReckonRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId    int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	ReckonDuration int32                  `protobuf:"varint,2,opt,name=reckon_duration,json=reckonDuration,proto3" json:"reckon_duration,omitempty"` // 计时时长/秒
	ReckonId       int32                  `protobuf:"varint,3,opt,name=reckon_id,json=reckonId,proto3" json:"reckon_id,omitempty"`
	SmallStageId   int32                  `protobuf:"varint,4,opt,name=small_stage_id,json=smallStageId,proto3" json:"small_stage_id,omitempty"` // 微习惯阶段 ID
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateUserHabitReckonRequest) Reset() {
	*x = UpdateUserHabitReckonRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserHabitReckonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserHabitReckonRequest) ProtoMessage() {}

func (x *UpdateUserHabitReckonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserHabitReckonRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserHabitReckonRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{35}
}

func (x *UpdateUserHabitReckonRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *UpdateUserHabitReckonRequest) GetReckonDuration() int32 {
	if x != nil {
		return x.ReckonDuration
	}
	return 0
}

func (x *UpdateUserHabitReckonRequest) GetReckonId() int32 {
	if x != nil {
		return x.ReckonId
	}
	return 0
}

func (x *UpdateUserHabitReckonRequest) GetSmallStageId() int32 {
	if x != nil {
		return x.SmallStageId
	}
	return 0
}

type UpdateUserHabitReckonReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserHabitReckonReply) Reset() {
	*x = UpdateUserHabitReckonReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserHabitReckonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserHabitReckonReply) ProtoMessage() {}

func (x *UpdateUserHabitReckonReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserHabitReckonReply.ProtoReflect.Descriptor instead.
func (*UpdateUserHabitReckonReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateUserHabitReckonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserHabitReckonReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeleteUserHabitReckonRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserHabitId   int32                  `protobuf:"varint,1,opt,name=user_habit_id,json=userHabitId,proto3" json:"user_habit_id,omitempty"`
	ReckonId      int32                  `protobuf:"varint,4,opt,name=reckon_id,json=reckonId,proto3" json:"reckon_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserHabitReckonRequest) Reset() {
	*x = DeleteUserHabitReckonRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserHabitReckonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserHabitReckonRequest) ProtoMessage() {}

func (x *DeleteUserHabitReckonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserHabitReckonRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserHabitReckonRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{37}
}

func (x *DeleteUserHabitReckonRequest) GetUserHabitId() int32 {
	if x != nil {
		return x.UserHabitId
	}
	return 0
}

func (x *DeleteUserHabitReckonRequest) GetReckonId() int32 {
	if x != nil {
		return x.ReckonId
	}
	return 0
}

type DeleteUserHabitReckonReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserHabitReckonReply) Reset() {
	*x = DeleteUserHabitReckonReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserHabitReckonReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserHabitReckonReply) ProtoMessage() {}

func (x *DeleteUserHabitReckonReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserHabitReckonReply.ProtoReflect.Descriptor instead.
func (*DeleteUserHabitReckonReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{38}
}

func (x *DeleteUserHabitReckonReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteUserHabitReckonReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ListUserHabitSnapshotRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	LabelType int32                  `protobuf:"varint,1,opt,name=label_type,json=labelType,proto3" json:"label_type,omitempty"`
	// eg: 2023-11-26T07:00:00+08:00
	CurrentDate   string `protobuf:"bytes,2,opt,name=current_date,json=currentDate,proto3" json:"current_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserHabitSnapshotRequest) Reset() {
	*x = ListUserHabitSnapshotRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserHabitSnapshotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserHabitSnapshotRequest) ProtoMessage() {}

func (x *ListUserHabitSnapshotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserHabitSnapshotRequest.ProtoReflect.Descriptor instead.
func (*ListUserHabitSnapshotRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{39}
}

func (x *ListUserHabitSnapshotRequest) GetLabelType() int32 {
	if x != nil {
		return x.LabelType
	}
	return 0
}

func (x *ListUserHabitSnapshotRequest) GetCurrentDate() string {
	if x != nil {
		return x.CurrentDate
	}
	return ""
}

type ListUserHabitSnapshotReply struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Code          int32                            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *ListUserHabitSnapshotReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserHabitSnapshotReply) Reset() {
	*x = ListUserHabitSnapshotReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserHabitSnapshotReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserHabitSnapshotReply) ProtoMessage() {}

func (x *ListUserHabitSnapshotReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserHabitSnapshotReply.ProtoReflect.Descriptor instead.
func (*ListUserHabitSnapshotReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{40}
}

func (x *ListUserHabitSnapshotReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListUserHabitSnapshotReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListUserHabitSnapshotReply) GetData() *ListUserHabitSnapshotReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type RunCronTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        int32                  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	NotRunTag     bool                   `protobuf:"varint,2,opt,name=not_run_tag,json=notRunTag,proto3" json:"not_run_tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RunCronTaskRequest) Reset() {
	*x = RunCronTaskRequest{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunCronTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunCronTaskRequest) ProtoMessage() {}

func (x *RunCronTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunCronTaskRequest.ProtoReflect.Descriptor instead.
func (*RunCronTaskRequest) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{41}
}

func (x *RunCronTaskRequest) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *RunCronTaskRequest) GetNotRunTag() bool {
	if x != nil {
		return x.NotRunTag
	}
	return false
}

type RunCronTaskReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RunCronTaskReply) Reset() {
	*x = RunCronTaskReply{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunCronTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunCronTaskReply) ProtoMessage() {}

func (x *RunCronTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunCronTaskReply.ProtoReflect.Descriptor instead.
func (*RunCronTaskReply) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{42}
}

func (x *RunCronTaskReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RunCronTaskReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type TodayStatistic struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	SmallHabit    *TodayStatistic_HabitDetail `protobuf:"bytes,1,opt,name=small_habit,json=smallHabit,proto3" json:"small_habit,omitempty"`
	NormalHabit   *TodayStatistic_HabitDetail `protobuf:"bytes,2,opt,name=normal_habit,json=normalHabit,proto3" json:"normal_habit,omitempty"`
	WeekData      []*TodayStatistic_WeekData  `protobuf:"bytes,5,rep,name=week_data,json=weekData,proto3" json:"week_data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TodayStatistic) Reset() {
	*x = TodayStatistic{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TodayStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TodayStatistic) ProtoMessage() {}

func (x *TodayStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TodayStatistic.ProtoReflect.Descriptor instead.
func (*TodayStatistic) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{43}
}

func (x *TodayStatistic) GetSmallHabit() *TodayStatistic_HabitDetail {
	if x != nil {
		return x.SmallHabit
	}
	return nil
}

func (x *TodayStatistic) GetNormalHabit() *TodayStatistic_HabitDetail {
	if x != nil {
		return x.NormalHabit
	}
	return nil
}

func (x *TodayStatistic) GetWeekData() []*TodayStatistic_WeekData {
	if x != nil {
		return x.WeekData
	}
	return nil
}

type GetUserHabitReply_DayData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DateTimestamp int32                  `protobuf:"varint,1,opt,name=dateTimestamp,proto3" json:"dateTimestamp,omitempty"`
	CompleteLevel int32                  `protobuf:"varint,2,opt,name=complete_level,json=completeLevel,proto3" json:"complete_level,omitempty"` // 完成等级
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserHabitReply_DayData) Reset() {
	*x = GetUserHabitReply_DayData{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserHabitReply_DayData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserHabitReply_DayData) ProtoMessage() {}

func (x *GetUserHabitReply_DayData) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserHabitReply_DayData.ProtoReflect.Descriptor instead.
func (*GetUserHabitReply_DayData) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{14, 0}
}

func (x *GetUserHabitReply_DayData) GetDateTimestamp() int32 {
	if x != nil {
		return x.DateTimestamp
	}
	return 0
}

func (x *GetUserHabitReply_DayData) GetCompleteLevel() int32 {
	if x != nil {
		return x.CompleteLevel
	}
	return 0
}

type GetUserHabitReply_TimeLine struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Time           int32                  `protobuf:"varint,1,opt,name=time,proto3" json:"time,omitempty"`              // eg: 2023-12-25T07:00:00+08:00
	OperateType    string                 `protobuf:"bytes,2,opt,name=operateType,proto3" json:"operateType,omitempty"` // 操作类型
	SmallStageName string                 `protobuf:"bytes,3,opt,name=small_stage_name,json=smallStageName,proto3" json:"small_stage_name,omitempty"`
	SmallStageId   int32                  `protobuf:"varint,9,opt,name=small_stage_id,json=smallStageId,proto3" json:"small_stage_id,omitempty"`
	Duration       int32                  `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`                         // 计时时长/秒
	MemoContent    string                 `protobuf:"bytes,5,opt,name=memo_content,json=memoContent,proto3" json:"memo_content,omitempty"` // 随记内容
	PunchId        int32                  `protobuf:"varint,6,opt,name=punch_id,json=punchId,proto3" json:"punch_id,omitempty"`            // 打卡 ID
	MemoId         int32                  `protobuf:"varint,7,opt,name=memo_id,json=memoId,proto3" json:"memo_id,omitempty"`               // 随记 ID
	ReckonId       int32                  `protobuf:"varint,8,opt,name=reckon_id,json=reckonId,proto3" json:"reckon_id,omitempty"`         // 计时 ID
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetUserHabitReply_TimeLine) Reset() {
	*x = GetUserHabitReply_TimeLine{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserHabitReply_TimeLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserHabitReply_TimeLine) ProtoMessage() {}

func (x *GetUserHabitReply_TimeLine) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserHabitReply_TimeLine.ProtoReflect.Descriptor instead.
func (*GetUserHabitReply_TimeLine) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{14, 1}
}

func (x *GetUserHabitReply_TimeLine) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

func (x *GetUserHabitReply_TimeLine) GetOperateType() string {
	if x != nil {
		return x.OperateType
	}
	return ""
}

func (x *GetUserHabitReply_TimeLine) GetSmallStageName() string {
	if x != nil {
		return x.SmallStageName
	}
	return ""
}

func (x *GetUserHabitReply_TimeLine) GetSmallStageId() int32 {
	if x != nil {
		return x.SmallStageId
	}
	return 0
}

func (x *GetUserHabitReply_TimeLine) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *GetUserHabitReply_TimeLine) GetMemoContent() string {
	if x != nil {
		return x.MemoContent
	}
	return ""
}

func (x *GetUserHabitReply_TimeLine) GetPunchId() int32 {
	if x != nil {
		return x.PunchId
	}
	return 0
}

func (x *GetUserHabitReply_TimeLine) GetMemoId() int32 {
	if x != nil {
		return x.MemoId
	}
	return 0
}

func (x *GetUserHabitReply_TimeLine) GetReckonId() int32 {
	if x != nil {
		return x.ReckonId
	}
	return 0
}

type GetUserHabitReply_Data struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Id            int32                         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                         `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	HabitType     int32                         `protobuf:"varint,4,opt,name=habit_type,json=habitType,proto3" json:"habit_type,omitempty"`
	Config        *UserHabitConfig              `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	PunchedTotal  int32                         `protobuf:"varint,6,opt,name=punchedTotal,proto3" json:"punchedTotal,omitempty"`
	Calendar      []*GetUserHabitReply_DayData  `protobuf:"bytes,7,rep,name=calendar,proto3" json:"calendar,omitempty"`
	Timeline      []*GetUserHabitReply_TimeLine `protobuf:"bytes,8,rep,name=timeline,proto3" json:"timeline,omitempty"`
	Desc          string                        `protobuf:"bytes,9,opt,name=desc,proto3" json:"desc,omitempty"`
	IsCompleted   bool                          `protobuf:"varint,10,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty"`
	CreatedAt     int32                         `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserHabitReply_Data) Reset() {
	*x = GetUserHabitReply_Data{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserHabitReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserHabitReply_Data) ProtoMessage() {}

func (x *GetUserHabitReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserHabitReply_Data.ProtoReflect.Descriptor instead.
func (*GetUserHabitReply_Data) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{14, 2}
}

func (x *GetUserHabitReply_Data) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetUserHabitReply_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetUserHabitReply_Data) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *GetUserHabitReply_Data) GetHabitType() int32 {
	if x != nil {
		return x.HabitType
	}
	return 0
}

func (x *GetUserHabitReply_Data) GetConfig() *UserHabitConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *GetUserHabitReply_Data) GetPunchedTotal() int32 {
	if x != nil {
		return x.PunchedTotal
	}
	return 0
}

func (x *GetUserHabitReply_Data) GetCalendar() []*GetUserHabitReply_DayData {
	if x != nil {
		return x.Calendar
	}
	return nil
}

func (x *GetUserHabitReply_Data) GetTimeline() []*GetUserHabitReply_TimeLine {
	if x != nil {
		return x.Timeline
	}
	return nil
}

func (x *GetUserHabitReply_Data) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *GetUserHabitReply_Data) GetIsCompleted() bool {
	if x != nil {
		return x.IsCompleted
	}
	return false
}

func (x *GetUserHabitReply_Data) GetCreatedAt() int32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type ListUserHabitReply_Data struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status         int32                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	PunchedDays    int32                  `protobuf:"varint,4,opt,name=punched_days,json=punchedDays,proto3" json:"punched_days,omitempty"`
	PunchAllDays   int32                  `protobuf:"varint,15,opt,name=punch_all_days,json=punchAllDays,proto3" json:"punch_all_days,omitempty"`
	PunchCycleType int32                  `protobuf:"varint,5,opt,name=punch_cycle_type,json=punchCycleType,proto3" json:"punch_cycle_type,omitempty"`
	PunchCycle     []int32                `protobuf:"varint,6,rep,packed,name=punch_cycle,json=punchCycle,proto3" json:"punch_cycle,omitempty"`
	StreakDays     int32                  `protobuf:"varint,7,opt,name=streak_days,json=streakDays,proto3" json:"streak_days,omitempty"`
	IsSetPrivacy   bool                   `protobuf:"varint,9,opt,name=is_set_privacy,json=isSetPrivacy,proto3" json:"is_set_privacy,omitempty"`
	CreatedAt      int32                  `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	EndAt          int32                  `protobuf:"varint,14,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	HealthStatus   string                 `protobuf:"bytes,16,opt,name=health_status,json=healthStatus,proto3" json:"health_status,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListUserHabitReply_Data) Reset() {
	*x = ListUserHabitReply_Data{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserHabitReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserHabitReply_Data) ProtoMessage() {}

func (x *ListUserHabitReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserHabitReply_Data.ProtoReflect.Descriptor instead.
func (*ListUserHabitReply_Data) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ListUserHabitReply_Data) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListUserHabitReply_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListUserHabitReply_Data) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListUserHabitReply_Data) GetPunchedDays() int32 {
	if x != nil {
		return x.PunchedDays
	}
	return 0
}

func (x *ListUserHabitReply_Data) GetPunchAllDays() int32 {
	if x != nil {
		return x.PunchAllDays
	}
	return 0
}

func (x *ListUserHabitReply_Data) GetPunchCycleType() int32 {
	if x != nil {
		return x.PunchCycleType
	}
	return 0
}

func (x *ListUserHabitReply_Data) GetPunchCycle() []int32 {
	if x != nil {
		return x.PunchCycle
	}
	return nil
}

func (x *ListUserHabitReply_Data) GetStreakDays() int32 {
	if x != nil {
		return x.StreakDays
	}
	return 0
}

func (x *ListUserHabitReply_Data) GetIsSetPrivacy() bool {
	if x != nil {
		return x.IsSetPrivacy
	}
	return false
}

func (x *ListUserHabitReply_Data) GetCreatedAt() int32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ListUserHabitReply_Data) GetEndAt() int32 {
	if x != nil {
		return x.EndAt
	}
	return 0
}

func (x *ListUserHabitReply_Data) GetHealthStatus() string {
	if x != nil {
		return x.HealthStatus
	}
	return ""
}

type ReckonUserHabitReply_Data struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ReckonDuration int32                  `protobuf:"varint,1,opt,name=reckon_duration,json=reckonDuration,proto3" json:"reckon_duration,omitempty"` // 计时时长/秒
	IsReckoning    bool                   `protobuf:"varint,2,opt,name=is_reckoning,json=isReckoning,proto3" json:"is_reckoning,omitempty"`          // 是否正在计时
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ReckonUserHabitReply_Data) Reset() {
	*x = ReckonUserHabitReply_Data{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReckonUserHabitReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReckonUserHabitReply_Data) ProtoMessage() {}

func (x *ReckonUserHabitReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReckonUserHabitReply_Data.ProtoReflect.Descriptor instead.
func (*ReckonUserHabitReply_Data) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{30, 0}
}

func (x *ReckonUserHabitReply_Data) GetReckonDuration() int32 {
	if x != nil {
		return x.ReckonDuration
	}
	return 0
}

func (x *ReckonUserHabitReply_Data) GetIsReckoning() bool {
	if x != nil {
		return x.IsReckoning
	}
	return false
}

type ListUserHabitSnapshotReply_SmallStageItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StageId       int32                  `protobuf:"varint,1,opt,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PunchedCount  int32                  `protobuf:"varint,3,opt,name=punched_count,json=punchedCount,proto3" json:"punched_count,omitempty"`
	IsReckoning   bool                   `protobuf:"varint,4,opt,name=is_reckoning,json=isReckoning,proto3" json:"is_reckoning,omitempty"` // 是否正在计时
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserHabitSnapshotReply_SmallStageItem) Reset() {
	*x = ListUserHabitSnapshotReply_SmallStageItem{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserHabitSnapshotReply_SmallStageItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserHabitSnapshotReply_SmallStageItem) ProtoMessage() {}

func (x *ListUserHabitSnapshotReply_SmallStageItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserHabitSnapshotReply_SmallStageItem.ProtoReflect.Descriptor instead.
func (*ListUserHabitSnapshotReply_SmallStageItem) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{40, 0}
}

func (x *ListUserHabitSnapshotReply_SmallStageItem) GetStageId() int32 {
	if x != nil {
		return x.StageId
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_SmallStageItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListUserHabitSnapshotReply_SmallStageItem) GetPunchedCount() int32 {
	if x != nil {
		return x.PunchedCount
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_SmallStageItem) GetIsReckoning() bool {
	if x != nil {
		return x.IsReckoning
	}
	return false
}

type ListUserHabitSnapshotReply_HabitItem struct {
	state              protoimpl.MessageState                       `protogen:"open.v1"`
	Id                 int32                                        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 习惯 ID
	Name               string                                       `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	HabitType          int32                                        `protobuf:"varint,5,opt,name=habit_type,json=habitType,proto3" json:"habit_type,omitempty"`
	SmallStages        []*ListUserHabitSnapshotReply_SmallStageItem `protobuf:"bytes,6,rep,name=small_stages,json=smallStages,proto3" json:"small_stages,omitempty"`
	IsReckoning        bool                                         `protobuf:"varint,7,opt,name=is_reckoning,json=isReckoning,proto3" json:"is_reckoning,omitempty"`                          // 是否正在计时
	Duration           int32                                        `protobuf:"varint,8,opt,name=duration,proto3" json:"duration,omitempty"`                                                   // 当前计时的时长（单位：秒）
	IsAllowCancelPunch bool                                         `protobuf:"varint,9,opt,name=is_allow_cancel_punch,json=isAllowCancelPunch,proto3" json:"is_allow_cancel_punch,omitempty"` // 是否允许取消打卡
	IsJoinAward        bool                                         `protobuf:"varint,10,opt,name=is_join_award,json=isJoinAward,proto3" json:"is_join_award,omitempty"`                       // 是否参与激励
	IsNecessary        bool                                         `protobuf:"varint,11,opt,name=is_necessary,json=isNecessary,proto3" json:"is_necessary,omitempty"`                         // 是否必须完成
	IsSetPrivacy       bool                                         `protobuf:"varint,12,opt,name=is_set_privacy,json=isSetPrivacy,proto3" json:"is_set_privacy,omitempty"`
	CreatedAt          int32                                        `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	PunchCycleType     int32                                        `protobuf:"varint,14,opt,name=punch_cycle_type,json=punchCycleType,proto3" json:"punch_cycle_type,omitempty"`
	Completed          bool                                         `protobuf:"varint,15,opt,name=completed,proto3" json:"completed,omitempty"`
	Per                int32                                        `protobuf:"varint,16,opt,name=per,proto3" json:"per,omitempty"`
	DoneCount          int32                                        `protobuf:"varint,17,opt,name=doneCount,proto3" json:"doneCount,omitempty"`
	AllCount           int32                                        `protobuf:"varint,18,opt,name=allCount,proto3" json:"allCount,omitempty"`
	EndDate            string                                       `protobuf:"bytes,19,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListUserHabitSnapshotReply_HabitItem) Reset() {
	*x = ListUserHabitSnapshotReply_HabitItem{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserHabitSnapshotReply_HabitItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserHabitSnapshotReply_HabitItem) ProtoMessage() {}

func (x *ListUserHabitSnapshotReply_HabitItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserHabitSnapshotReply_HabitItem.ProtoReflect.Descriptor instead.
func (*ListUserHabitSnapshotReply_HabitItem) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{40, 1}
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetHabitType() int32 {
	if x != nil {
		return x.HabitType
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetSmallStages() []*ListUserHabitSnapshotReply_SmallStageItem {
	if x != nil {
		return x.SmallStages
	}
	return nil
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetIsReckoning() bool {
	if x != nil {
		return x.IsReckoning
	}
	return false
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetIsAllowCancelPunch() bool {
	if x != nil {
		return x.IsAllowCancelPunch
	}
	return false
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetIsJoinAward() bool {
	if x != nil {
		return x.IsJoinAward
	}
	return false
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetIsNecessary() bool {
	if x != nil {
		return x.IsNecessary
	}
	return false
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetIsSetPrivacy() bool {
	if x != nil {
		return x.IsSetPrivacy
	}
	return false
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetCreatedAt() int32 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetPunchCycleType() int32 {
	if x != nil {
		return x.PunchCycleType
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetCompleted() bool {
	if x != nil {
		return x.Completed
	}
	return false
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetPer() int32 {
	if x != nil {
		return x.Per
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetDoneCount() int32 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetAllCount() int32 {
	if x != nil {
		return x.AllCount
	}
	return 0
}

func (x *ListUserHabitSnapshotReply_HabitItem) GetEndDate() string {
	if x != nil {
		return x.EndDate
	}
	return ""
}

type ListUserHabitSnapshotReply_Data struct {
	state                   protoimpl.MessageState                  `protogen:"open.v1"`
	TodayStatisticData      *TodayStatistic                         `protobuf:"bytes,1,opt,name=today_statistic_data,json=todayStatisticData,proto3" json:"today_statistic_data,omitempty"`
	TopHabits               []*ListUserHabitSnapshotReply_HabitItem `protobuf:"bytes,2,rep,name=top_habits,json=topHabits,proto3" json:"top_habits,omitempty"`
	LowHabits               []*ListUserHabitSnapshotReply_HabitItem `protobuf:"bytes,3,rep,name=low_habits,json=lowHabits,proto3" json:"low_habits,omitempty"`
	TrackHabits             []*ListUserHabitSnapshotReply_HabitItem `protobuf:"bytes,4,rep,name=track_habits,json=trackHabits,proto3" json:"track_habits,omitempty"`
	IsNeedShow              bool                                    `protobuf:"varint,5,opt,name=is_need_show,json=isNeedShow,proto3" json:"is_need_show,omitempty"`
	PendingMotivationHabits []int32                                 `protobuf:"varint,6,rep,packed,name=pending_motivation_habits,json=pendingMotivationHabits,proto3" json:"pending_motivation_habits,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *ListUserHabitSnapshotReply_Data) Reset() {
	*x = ListUserHabitSnapshotReply_Data{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserHabitSnapshotReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserHabitSnapshotReply_Data) ProtoMessage() {}

func (x *ListUserHabitSnapshotReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserHabitSnapshotReply_Data.ProtoReflect.Descriptor instead.
func (*ListUserHabitSnapshotReply_Data) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{40, 2}
}

func (x *ListUserHabitSnapshotReply_Data) GetTodayStatisticData() *TodayStatistic {
	if x != nil {
		return x.TodayStatisticData
	}
	return nil
}

func (x *ListUserHabitSnapshotReply_Data) GetTopHabits() []*ListUserHabitSnapshotReply_HabitItem {
	if x != nil {
		return x.TopHabits
	}
	return nil
}

func (x *ListUserHabitSnapshotReply_Data) GetLowHabits() []*ListUserHabitSnapshotReply_HabitItem {
	if x != nil {
		return x.LowHabits
	}
	return nil
}

func (x *ListUserHabitSnapshotReply_Data) GetTrackHabits() []*ListUserHabitSnapshotReply_HabitItem {
	if x != nil {
		return x.TrackHabits
	}
	return nil
}

func (x *ListUserHabitSnapshotReply_Data) GetIsNeedShow() bool {
	if x != nil {
		return x.IsNeedShow
	}
	return false
}

func (x *ListUserHabitSnapshotReply_Data) GetPendingMotivationHabits() []int32 {
	if x != nil {
		return x.PendingMotivationHabits
	}
	return nil
}

type TodayStatistic_HabitDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Per           int32                  `protobuf:"varint,1,opt,name=per,proto3" json:"per,omitempty"`
	DoneCount     int32                  `protobuf:"varint,2,opt,name=done_count,json=doneCount,proto3" json:"done_count,omitempty"`
	AllCount      int32                  `protobuf:"varint,3,opt,name=all_count,json=allCount,proto3" json:"all_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TodayStatistic_HabitDetail) Reset() {
	*x = TodayStatistic_HabitDetail{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TodayStatistic_HabitDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TodayStatistic_HabitDetail) ProtoMessage() {}

func (x *TodayStatistic_HabitDetail) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TodayStatistic_HabitDetail.ProtoReflect.Descriptor instead.
func (*TodayStatistic_HabitDetail) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{43, 0}
}

func (x *TodayStatistic_HabitDetail) GetPer() int32 {
	if x != nil {
		return x.Per
	}
	return 0
}

func (x *TodayStatistic_HabitDetail) GetDoneCount() int32 {
	if x != nil {
		return x.DoneCount
	}
	return 0
}

func (x *TodayStatistic_HabitDetail) GetAllCount() int32 {
	if x != nil {
		return x.AllCount
	}
	return 0
}

type TodayStatistic_WeekData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Day           string                 `protobuf:"bytes,1,opt,name=day,proto3" json:"day,omitempty"`
	Progress      string                 `protobuf:"bytes,2,opt,name=progress,proto3" json:"progress,omitempty"`
	IsToday       bool                   `protobuf:"varint,3,opt,name=is_today,json=isToday,proto3" json:"is_today,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TodayStatistic_WeekData) Reset() {
	*x = TodayStatistic_WeekData{}
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TodayStatistic_WeekData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TodayStatistic_WeekData) ProtoMessage() {}

func (x *TodayStatistic_WeekData) ProtoReflect() protoreflect.Message {
	mi := &file_user_habit_v1_user_habit_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TodayStatistic_WeekData.ProtoReflect.Descriptor instead.
func (*TodayStatistic_WeekData) Descriptor() ([]byte, []int) {
	return file_user_habit_v1_user_habit_proto_rawDescGZIP(), []int{43, 1}
}

func (x *TodayStatistic_WeekData) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *TodayStatistic_WeekData) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *TodayStatistic_WeekData) GetIsToday() bool {
	if x != nil {
		return x.IsToday
	}
	return false
}

var File_user_habit_v1_user_habit_proto protoreflect.FileDescriptor

const file_user_habit_v1_user_habit_proto_rawDesc = "" +
	"\n" +
	"\x1euser_habit/v1/user_habit.proto\x12\ruser_habit_v1\x1a\x17validate/validate.proto\"\xb5\x04\n" +
	"\x0fUserHabitConfig\x12&\n" +
	"\x0fpunch_max_count\x18\x01 \x01(\x05R\rpunchMaxCount\x12(\n" +
	"\x10punch_cycle_type\x18\x04 \x01(\x05R\x0epunchCycleType\x12\x1f\n" +
	"\vpunch_cycle\x18\x05 \x03(\x05R\n" +
	"punchCycle\x12!\n" +
	"\fsmall_stages\x18\x06 \x03(\tR\vsmallStages\x12#\n" +
	"\rstage_punches\x18\v \x03(\x05R\fstagePunches\x121\n" +
	"\x15is_allow_cancel_punch\x18\f \x01(\bR\x12isAllowCancelPunch\x12\"\n" +
	"\ris_join_award\x18\r \x01(\bR\visJoinAward\x12$\n" +
	"\x0eis_set_privacy\x18\x0e \x01(\bR\fisSetPrivacy\x120\n" +
	"\x14privacy_display_mode\x18\x11 \x01(\tR\x12privacyDisplayMode\x126\n" +
	"\x17privacy_display_content\x18\x12 \x01(\tR\x15privacyDisplayContent\x12\x1f\n" +
	"\vrecord_type\x18\x0f \x01(\x05R\n" +
	"recordType\x12\x19\n" +
	"\bend_date\x18\x10 \x01(\tR\aendDate\x12%\n" +
	"\x0ereminder_times\x18\x13 \x03(\tR\rreminderTimes\x12\x1d\n" +
	"\n" +
	"buddy_list\x18\x14 \x03(\x05R\tbuddyList\"\xf1\x01\n" +
	"\x16CreateUserHabitRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x126\n" +
	"\x06config\x18\x04 \x01(\v2\x1e.user_habit_v1.UserHabitConfigR\x06config\x12\x1f\n" +
	"\vcreate_date\x18\x05 \x01(\tR\n" +
	"createDate\x12\x1d\n" +
	"\n" +
	"habit_type\x18\x06 \x01(\x05R\thabitType\x12%\n" +
	"\x0etimezone_place\x18\a \x01(\tR\rtimezonePlace\x12$\n" +
	"\btimezone\x18\b \x01(\tB\b\xfaB\x05r\x03\x98\x01\x06R\btimezone\"<\n" +
	"\x14CreateUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xe2\x01\n" +
	"\x16UpdateUserHabitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x126\n" +
	"\x06config\x18\x05 \x01(\v2\x1e.user_habit_v1.UserHabitConfigR\x06config\x12\x1f\n" +
	"\vupdate_date\x18\x06 \x01(\tR\n" +
	"updateDate\x12%\n" +
	"\x0etimezone_place\x18\a \x01(\tR\rtimezonePlace\x12$\n" +
	"\btimezone\x18\b \x01(\tB\b\xfaB\x05r\x03\x98\x01\x06R\btimezone\"<\n" +
	"\x14UpdateUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"(\n" +
	"\x16DeleteUserHabitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"\x16\n" +
	"\x14DeleteUserHabitReply\"'\n" +
	"\x15PauseUserHabitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"\x15\n" +
	"\x13PauseUserHabitReply\")\n" +
	"\x17RecoverUserHabitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"\x17\n" +
	"\x15RecoverUserHabitReply\")\n" +
	"\x17ArchiveUserHabitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"\x17\n" +
	"\x15ArchiveUserHabitReply\"H\n" +
	"\x13GetUserHabitRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12!\n" +
	"\fcurrent_date\x18\x02 \x01(\tR\vcurrentDate\"\x92\a\n" +
	"\x11GetUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x129\n" +
	"\x04data\x18\x03 \x01(\v2%.user_habit_v1.GetUserHabitReply.DataR\x04data\x1aV\n" +
	"\aDayData\x12$\n" +
	"\rdateTimestamp\x18\x01 \x01(\x05R\rdateTimestamp\x12%\n" +
	"\x0ecomplete_level\x18\x02 \x01(\x05R\rcompleteLevel\x1a\xa0\x02\n" +
	"\bTimeLine\x12\x12\n" +
	"\x04time\x18\x01 \x01(\x05R\x04time\x12 \n" +
	"\voperateType\x18\x02 \x01(\tR\voperateType\x12(\n" +
	"\x10small_stage_name\x18\x03 \x01(\tR\x0esmallStageName\x12$\n" +
	"\x0esmall_stage_id\x18\t \x01(\x05R\fsmallStageId\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x05R\bduration\x12!\n" +
	"\fmemo_content\x18\x05 \x01(\tR\vmemoContent\x12\x19\n" +
	"\bpunch_id\x18\x06 \x01(\x05R\apunchId\x12\x17\n" +
	"\amemo_id\x18\a \x01(\x05R\x06memoId\x12\x1b\n" +
	"\treckon_id\x18\b \x01(\x05R\breckonId\x1a\xa0\x03\n" +
	"\x04Data\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x03 \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"habit_type\x18\x04 \x01(\x05R\thabitType\x126\n" +
	"\x06config\x18\x05 \x01(\v2\x1e.user_habit_v1.UserHabitConfigR\x06config\x12\"\n" +
	"\fpunchedTotal\x18\x06 \x01(\x05R\fpunchedTotal\x12D\n" +
	"\bcalendar\x18\a \x03(\v2(.user_habit_v1.GetUserHabitReply.DayDataR\bcalendar\x12E\n" +
	"\btimeline\x18\b \x03(\v2).user_habit_v1.GetUserHabitReply.TimeLineR\btimeline\x12\x12\n" +
	"\x04desc\x18\t \x01(\tR\x04desc\x12!\n" +
	"\fis_completed\x18\n" +
	" \x01(\bR\visCompleted\x12\x1d\n" +
	"\n" +
	"created_at\x18\v \x01(\x05R\tcreatedAt\"Q\n" +
	"\x14ListUserHabitRequest\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12!\n" +
	"\fcurrent_date\x18\x02 \x01(\tR\vcurrentDate\"\xf1\x03\n" +
	"\x12ListUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12:\n" +
	"\x04data\x18\x03 \x03(\v2&.user_habit_v1.ListUserHabitReply.DataR\x04data\x1a\xf8\x02\n" +
	"\x04Data\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x03 \x01(\x05R\x06status\x12!\n" +
	"\fpunched_days\x18\x04 \x01(\x05R\vpunchedDays\x12$\n" +
	"\x0epunch_all_days\x18\x0f \x01(\x05R\fpunchAllDays\x12(\n" +
	"\x10punch_cycle_type\x18\x05 \x01(\x05R\x0epunchCycleType\x12\x1f\n" +
	"\vpunch_cycle\x18\x06 \x03(\x05R\n" +
	"punchCycle\x12\x1f\n" +
	"\vstreak_days\x18\a \x01(\x05R\n" +
	"streakDays\x12$\n" +
	"\x0eis_set_privacy\x18\t \x01(\bR\fisSetPrivacy\x12\x1d\n" +
	"\n" +
	"created_at\x18\r \x01(\x05R\tcreatedAt\x12\x15\n" +
	"\x06end_at\x18\x0e \x01(\x05R\x05endAt\x12#\n" +
	"\rhealth_status\x18\x10 \x01(\tR\fhealthStatus\"\xbb\x01\n" +
	"\x1aCreateUserHabitMemoRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x16\n" +
	"\x06images\x18\x03 \x03(\tR\x06images\x12$\n" +
	"\x0esmall_stage_id\x18\x04 \x01(\x05R\fsmallStageId\x12!\n" +
	"\fcurrent_date\x18\x05 \x01(\tR\vcurrentDate\"@\n" +
	"\x18CreateUserHabitMemoReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"s\n" +
	"\x1aUpdateUserHabitMemoRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\x12\x17\n" +
	"\amemo_id\x18\x03 \x01(\x05R\x06memoId\"@\n" +
	"\x18UpdateUserHabitMemoReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"Y\n" +
	"\x1aDeleteUserHabitMemoRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12\x17\n" +
	"\amemo_id\x18\x04 \x01(\x05R\x06memoId\"@\n" +
	"\x18DeleteUserHabitMemoReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x84\x01\n" +
	"\x15PunchUserHabitRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12$\n" +
	"\x0esmall_stage_id\x18\x03 \x01(\x05R\fsmallStageId\x12!\n" +
	"\fcurrent_date\x18\x04 \x01(\tR\vcurrentDate\";\n" +
	"\x13PunchUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x7f\n" +
	"\x1bUpdatePunchUserHabitRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12\x19\n" +
	"\bpunch_id\x18\x02 \x01(\x05R\apunchId\x12!\n" +
	"\fcurrent_date\x18\x03 \x01(\tR\vcurrentDate\"A\n" +
	"\x19UpdatePunchUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xa5\x01\n" +
	"\x1bCancelPunchUserHabitRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12$\n" +
	"\x0esmall_stage_id\x18\x03 \x01(\x05R\fsmallStageId\x12!\n" +
	"\fcurrent_date\x18\x04 \x01(\tR\vcurrentDate\x12\x19\n" +
	"\bpunch_id\x18\x05 \x01(\x05R\apunchId\"A\n" +
	"\x19CancelPunchUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xc5\x01\n" +
	"\x16ReckonUserHabitRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12'\n" +
	"\x0freckon_duration\x18\x02 \x01(\x05R\x0ereckonDuration\x12$\n" +
	"\x0esmall_stage_id\x18\x03 \x01(\x05R\fsmallStageId\x12!\n" +
	"\fcurrent_date\x18\x04 \x01(\tR\vcurrentDate\x12\x15\n" +
	"\x06is_end\x18\x05 \x01(\bR\x05isEnd\"\xce\x01\n" +
	"\x14ReckonUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12<\n" +
	"\x04data\x18\x03 \x01(\v2(.user_habit_v1.ReckonUserHabitReply.DataR\x04data\x1aR\n" +
	"\x04Data\x12'\n" +
	"\x0freckon_duration\x18\x01 \x01(\x05R\x0ereckonDuration\x12!\n" +
	"\fis_reckoning\x18\x02 \x01(\bR\visReckoning\"\x8b\x01\n" +
	"\x1cCancelReckonUserHabitRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12$\n" +
	"\x0esmall_stage_id\x18\x03 \x01(\x05R\fsmallStageId\x12!\n" +
	"\fcurrent_date\x18\x04 \x01(\tR\vcurrentDate\"B\n" +
	"\x1aCancelReckonUserHabitReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xb4\x01\n" +
	"\x1cCreateUserHabitReckonRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12'\n" +
	"\x0freckon_duration\x18\x02 \x01(\x05R\x0ereckonDuration\x12$\n" +
	"\x0esmall_stage_id\x18\x03 \x01(\x05R\fsmallStageId\x12!\n" +
	"\fcurrent_date\x18\x04 \x01(\tR\vcurrentDate\"B\n" +
	"\x1aCreateUserHabitReckonReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xae\x01\n" +
	"\x1cUpdateUserHabitReckonRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12'\n" +
	"\x0freckon_duration\x18\x02 \x01(\x05R\x0ereckonDuration\x12\x1b\n" +
	"\treckon_id\x18\x03 \x01(\x05R\breckonId\x12$\n" +
	"\x0esmall_stage_id\x18\x04 \x01(\x05R\fsmallStageId\"B\n" +
	"\x1aUpdateUserHabitReckonReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"_\n" +
	"\x1cDeleteUserHabitReckonRequest\x12\"\n" +
	"\ruser_habit_id\x18\x01 \x01(\x05R\vuserHabitId\x12\x1b\n" +
	"\treckon_id\x18\x04 \x01(\x05R\breckonId\"B\n" +
	"\x1aDeleteUserHabitReckonReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"`\n" +
	"\x1cListUserHabitSnapshotRequest\x12\x1d\n" +
	"\n" +
	"label_type\x18\x01 \x01(\x05R\tlabelType\x12!\n" +
	"\fcurrent_date\x18\x02 \x01(\tR\vcurrentDate\"\xa3\n" +
	"\n" +
	"\x1aListUserHabitSnapshotReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12B\n" +
	"\x04data\x18\x03 \x01(\v2..user_habit_v1.ListUserHabitSnapshotReply.DataR\x04data\x1a\x87\x01\n" +
	"\x0eSmallStageItem\x12\x19\n" +
	"\bstage_id\x18\x01 \x01(\x05R\astageId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12#\n" +
	"\rpunched_count\x18\x03 \x01(\x05R\fpunchedCount\x12!\n" +
	"\fis_reckoning\x18\x04 \x01(\bR\visReckoning\x1a\xd8\x04\n" +
	"\tHabitItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"habit_type\x18\x05 \x01(\x05R\thabitType\x12[\n" +
	"\fsmall_stages\x18\x06 \x03(\v28.user_habit_v1.ListUserHabitSnapshotReply.SmallStageItemR\vsmallStages\x12!\n" +
	"\fis_reckoning\x18\a \x01(\bR\visReckoning\x12\x1a\n" +
	"\bduration\x18\b \x01(\x05R\bduration\x121\n" +
	"\x15is_allow_cancel_punch\x18\t \x01(\bR\x12isAllowCancelPunch\x12\"\n" +
	"\ris_join_award\x18\n" +
	" \x01(\bR\visJoinAward\x12!\n" +
	"\fis_necessary\x18\v \x01(\bR\visNecessary\x12$\n" +
	"\x0eis_set_privacy\x18\f \x01(\bR\fisSetPrivacy\x12\x1d\n" +
	"\n" +
	"created_at\x18\r \x01(\x05R\tcreatedAt\x12(\n" +
	"\x10punch_cycle_type\x18\x0e \x01(\x05R\x0epunchCycleType\x12\x1c\n" +
	"\tcompleted\x18\x0f \x01(\bR\tcompleted\x12\x10\n" +
	"\x03per\x18\x10 \x01(\x05R\x03per\x12\x1c\n" +
	"\tdoneCount\x18\x11 \x01(\x05R\tdoneCount\x12\x1a\n" +
	"\ballCount\x18\x12 \x01(\x05R\ballCount\x12\x19\n" +
	"\bend_date\x18\x13 \x01(\tR\aendDate\x1a\xb5\x03\n" +
	"\x04Data\x12O\n" +
	"\x14today_statistic_data\x18\x01 \x01(\v2\x1d.user_habit_v1.TodayStatisticR\x12todayStatisticData\x12R\n" +
	"\n" +
	"top_habits\x18\x02 \x03(\v23.user_habit_v1.ListUserHabitSnapshotReply.HabitItemR\ttopHabits\x12R\n" +
	"\n" +
	"low_habits\x18\x03 \x03(\v23.user_habit_v1.ListUserHabitSnapshotReply.HabitItemR\tlowHabits\x12V\n" +
	"\ftrack_habits\x18\x04 \x03(\v23.user_habit_v1.ListUserHabitSnapshotReply.HabitItemR\vtrackHabits\x12 \n" +
	"\fis_need_show\x18\x05 \x01(\bR\n" +
	"isNeedShow\x12:\n" +
	"\x19pending_motivation_habits\x18\x06 \x03(\x05R\x17pendingMotivationHabits\"M\n" +
	"\x12RunCronTaskRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\x05R\x06taskId\x12\x1e\n" +
	"\vnot_run_tag\x18\x02 \x01(\bR\tnotRunTag\"8\n" +
	"\x10RunCronTaskReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xa1\x03\n" +
	"\x0eTodayStatistic\x12J\n" +
	"\vsmall_habit\x18\x01 \x01(\v2).user_habit_v1.TodayStatistic.HabitDetailR\n" +
	"smallHabit\x12L\n" +
	"\fnormal_habit\x18\x02 \x01(\v2).user_habit_v1.TodayStatistic.HabitDetailR\vnormalHabit\x12C\n" +
	"\tweek_data\x18\x05 \x03(\v2&.user_habit_v1.TodayStatistic.WeekDataR\bweekData\x1a[\n" +
	"\vHabitDetail\x12\x10\n" +
	"\x03per\x18\x01 \x01(\x05R\x03per\x12\x1d\n" +
	"\n" +
	"done_count\x18\x02 \x01(\x05R\tdoneCount\x12\x1b\n" +
	"\tall_count\x18\x03 \x01(\x05R\ballCount\x1aS\n" +
	"\bWeekData\x12\x10\n" +
	"\x03day\x18\x01 \x01(\tR\x03day\x12\x1a\n" +
	"\bprogress\x18\x02 \x01(\tR\bprogress\x12\x19\n" +
	"\bis_today\x18\x03 \x01(\bR\aisTodayB3Z1github.com/wlnil/life-log-be/api/user_habit/v1;v1b\x06proto3"

var (
	file_user_habit_v1_user_habit_proto_rawDescOnce sync.Once
	file_user_habit_v1_user_habit_proto_rawDescData []byte
)

func file_user_habit_v1_user_habit_proto_rawDescGZIP() []byte {
	file_user_habit_v1_user_habit_proto_rawDescOnce.Do(func() {
		file_user_habit_v1_user_habit_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_habit_v1_user_habit_proto_rawDesc), len(file_user_habit_v1_user_habit_proto_rawDesc)))
	})
	return file_user_habit_v1_user_habit_proto_rawDescData
}

var file_user_habit_v1_user_habit_proto_msgTypes = make([]protoimpl.MessageInfo, 54)
var file_user_habit_v1_user_habit_proto_goTypes = []any{
	(*UserHabitConfig)(nil),                           // 0: user_habit_v1.UserHabitConfig
	(*CreateUserHabitRequest)(nil),                    // 1: user_habit_v1.CreateUserHabitRequest
	(*CreateUserHabitReply)(nil),                      // 2: user_habit_v1.CreateUserHabitReply
	(*UpdateUserHabitRequest)(nil),                    // 3: user_habit_v1.UpdateUserHabitRequest
	(*UpdateUserHabitReply)(nil),                      // 4: user_habit_v1.UpdateUserHabitReply
	(*DeleteUserHabitRequest)(nil),                    // 5: user_habit_v1.DeleteUserHabitRequest
	(*DeleteUserHabitReply)(nil),                      // 6: user_habit_v1.DeleteUserHabitReply
	(*PauseUserHabitRequest)(nil),                     // 7: user_habit_v1.PauseUserHabitRequest
	(*PauseUserHabitReply)(nil),                       // 8: user_habit_v1.PauseUserHabitReply
	(*RecoverUserHabitRequest)(nil),                   // 9: user_habit_v1.RecoverUserHabitRequest
	(*RecoverUserHabitReply)(nil),                     // 10: user_habit_v1.RecoverUserHabitReply
	(*ArchiveUserHabitRequest)(nil),                   // 11: user_habit_v1.ArchiveUserHabitRequest
	(*ArchiveUserHabitReply)(nil),                     // 12: user_habit_v1.ArchiveUserHabitReply
	(*GetUserHabitRequest)(nil),                       // 13: user_habit_v1.GetUserHabitRequest
	(*GetUserHabitReply)(nil),                         // 14: user_habit_v1.GetUserHabitReply
	(*ListUserHabitRequest)(nil),                      // 15: user_habit_v1.ListUserHabitRequest
	(*ListUserHabitReply)(nil),                        // 16: user_habit_v1.ListUserHabitReply
	(*CreateUserHabitMemoRequest)(nil),                // 17: user_habit_v1.CreateUserHabitMemoRequest
	(*CreateUserHabitMemoReply)(nil),                  // 18: user_habit_v1.CreateUserHabitMemoReply
	(*UpdateUserHabitMemoRequest)(nil),                // 19: user_habit_v1.UpdateUserHabitMemoRequest
	(*UpdateUserHabitMemoReply)(nil),                  // 20: user_habit_v1.UpdateUserHabitMemoReply
	(*DeleteUserHabitMemoRequest)(nil),                // 21: user_habit_v1.DeleteUserHabitMemoRequest
	(*DeleteUserHabitMemoReply)(nil),                  // 22: user_habit_v1.DeleteUserHabitMemoReply
	(*PunchUserHabitRequest)(nil),                     // 23: user_habit_v1.PunchUserHabitRequest
	(*PunchUserHabitReply)(nil),                       // 24: user_habit_v1.PunchUserHabitReply
	(*UpdatePunchUserHabitRequest)(nil),               // 25: user_habit_v1.UpdatePunchUserHabitRequest
	(*UpdatePunchUserHabitReply)(nil),                 // 26: user_habit_v1.UpdatePunchUserHabitReply
	(*CancelPunchUserHabitRequest)(nil),               // 27: user_habit_v1.CancelPunchUserHabitRequest
	(*CancelPunchUserHabitReply)(nil),                 // 28: user_habit_v1.CancelPunchUserHabitReply
	(*ReckonUserHabitRequest)(nil),                    // 29: user_habit_v1.ReckonUserHabitRequest
	(*ReckonUserHabitReply)(nil),                      // 30: user_habit_v1.ReckonUserHabitReply
	(*CancelReckonUserHabitRequest)(nil),              // 31: user_habit_v1.CancelReckonUserHabitRequest
	(*CancelReckonUserHabitReply)(nil),                // 32: user_habit_v1.CancelReckonUserHabitReply
	(*CreateUserHabitReckonRequest)(nil),              // 33: user_habit_v1.CreateUserHabitReckonRequest
	(*CreateUserHabitReckonReply)(nil),                // 34: user_habit_v1.CreateUserHabitReckonReply
	(*UpdateUserHabitReckonRequest)(nil),              // 35: user_habit_v1.UpdateUserHabitReckonRequest
	(*UpdateUserHabitReckonReply)(nil),                // 36: user_habit_v1.UpdateUserHabitReckonReply
	(*DeleteUserHabitReckonRequest)(nil),              // 37: user_habit_v1.DeleteUserHabitReckonRequest
	(*DeleteUserHabitReckonReply)(nil),                // 38: user_habit_v1.DeleteUserHabitReckonReply
	(*ListUserHabitSnapshotRequest)(nil),              // 39: user_habit_v1.ListUserHabitSnapshotRequest
	(*ListUserHabitSnapshotReply)(nil),                // 40: user_habit_v1.ListUserHabitSnapshotReply
	(*RunCronTaskRequest)(nil),                        // 41: user_habit_v1.RunCronTaskRequest
	(*RunCronTaskReply)(nil),                          // 42: user_habit_v1.RunCronTaskReply
	(*TodayStatistic)(nil),                            // 43: user_habit_v1.TodayStatistic
	(*GetUserHabitReply_DayData)(nil),                 // 44: user_habit_v1.GetUserHabitReply.DayData
	(*GetUserHabitReply_TimeLine)(nil),                // 45: user_habit_v1.GetUserHabitReply.TimeLine
	(*GetUserHabitReply_Data)(nil),                    // 46: user_habit_v1.GetUserHabitReply.Data
	(*ListUserHabitReply_Data)(nil),                   // 47: user_habit_v1.ListUserHabitReply.Data
	(*ReckonUserHabitReply_Data)(nil),                 // 48: user_habit_v1.ReckonUserHabitReply.Data
	(*ListUserHabitSnapshotReply_SmallStageItem)(nil), // 49: user_habit_v1.ListUserHabitSnapshotReply.SmallStageItem
	(*ListUserHabitSnapshotReply_HabitItem)(nil),      // 50: user_habit_v1.ListUserHabitSnapshotReply.HabitItem
	(*ListUserHabitSnapshotReply_Data)(nil),           // 51: user_habit_v1.ListUserHabitSnapshotReply.Data
	(*TodayStatistic_HabitDetail)(nil),                // 52: user_habit_v1.TodayStatistic.HabitDetail
	(*TodayStatistic_WeekData)(nil),                   // 53: user_habit_v1.TodayStatistic.WeekData
}
var file_user_habit_v1_user_habit_proto_depIdxs = []int32{
	0,  // 0: user_habit_v1.CreateUserHabitRequest.config:type_name -> user_habit_v1.UserHabitConfig
	0,  // 1: user_habit_v1.UpdateUserHabitRequest.config:type_name -> user_habit_v1.UserHabitConfig
	46, // 2: user_habit_v1.GetUserHabitReply.data:type_name -> user_habit_v1.GetUserHabitReply.Data
	47, // 3: user_habit_v1.ListUserHabitReply.data:type_name -> user_habit_v1.ListUserHabitReply.Data
	48, // 4: user_habit_v1.ReckonUserHabitReply.data:type_name -> user_habit_v1.ReckonUserHabitReply.Data
	51, // 5: user_habit_v1.ListUserHabitSnapshotReply.data:type_name -> user_habit_v1.ListUserHabitSnapshotReply.Data
	52, // 6: user_habit_v1.TodayStatistic.small_habit:type_name -> user_habit_v1.TodayStatistic.HabitDetail
	52, // 7: user_habit_v1.TodayStatistic.normal_habit:type_name -> user_habit_v1.TodayStatistic.HabitDetail
	53, // 8: user_habit_v1.TodayStatistic.week_data:type_name -> user_habit_v1.TodayStatistic.WeekData
	0,  // 9: user_habit_v1.GetUserHabitReply.Data.config:type_name -> user_habit_v1.UserHabitConfig
	44, // 10: user_habit_v1.GetUserHabitReply.Data.calendar:type_name -> user_habit_v1.GetUserHabitReply.DayData
	45, // 11: user_habit_v1.GetUserHabitReply.Data.timeline:type_name -> user_habit_v1.GetUserHabitReply.TimeLine
	49, // 12: user_habit_v1.ListUserHabitSnapshotReply.HabitItem.small_stages:type_name -> user_habit_v1.ListUserHabitSnapshotReply.SmallStageItem
	43, // 13: user_habit_v1.ListUserHabitSnapshotReply.Data.today_statistic_data:type_name -> user_habit_v1.TodayStatistic
	50, // 14: user_habit_v1.ListUserHabitSnapshotReply.Data.top_habits:type_name -> user_habit_v1.ListUserHabitSnapshotReply.HabitItem
	50, // 15: user_habit_v1.ListUserHabitSnapshotReply.Data.low_habits:type_name -> user_habit_v1.ListUserHabitSnapshotReply.HabitItem
	50, // 16: user_habit_v1.ListUserHabitSnapshotReply.Data.track_habits:type_name -> user_habit_v1.ListUserHabitSnapshotReply.HabitItem
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_user_habit_v1_user_habit_proto_init() }
func file_user_habit_v1_user_habit_proto_init() {
	if File_user_habit_v1_user_habit_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_habit_v1_user_habit_proto_rawDesc), len(file_user_habit_v1_user_habit_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   54,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_habit_v1_user_habit_proto_goTypes,
		DependencyIndexes: file_user_habit_v1_user_habit_proto_depIdxs,
		MessageInfos:      file_user_habit_v1_user_habit_proto_msgTypes,
	}.Build()
	File_user_habit_v1_user_habit_proto = out.File
	file_user_habit_v1_user_habit_proto_goTypes = nil
	file_user_habit_v1_user_habit_proto_depIdxs = nil
}
