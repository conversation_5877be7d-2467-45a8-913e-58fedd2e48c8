// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: user_habit/v1/user_habit.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UserHabitConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UserHabitConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserHabitConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UserHabitConfigMultiError, or nil if none found.
func (m *UserHabitConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *UserHabitConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PunchMaxCount

	// no validation rules for PunchCycleType

	// no validation rules for IsAllowCancelPunch

	// no validation rules for IsJoinAward

	// no validation rules for IsSetPrivacy

	// no validation rules for PrivacyDisplayMode

	// no validation rules for PrivacyDisplayContent

	// no validation rules for RecordType

	// no validation rules for EndDate

	if len(errors) > 0 {
		return UserHabitConfigMultiError(errors)
	}

	return nil
}

// UserHabitConfigMultiError is an error wrapping multiple validation errors
// returned by UserHabitConfig.ValidateAll() if the designated constraints
// aren't met.
type UserHabitConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserHabitConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserHabitConfigMultiError) AllErrors() []error { return m }

// UserHabitConfigValidationError is the validation error returned by
// UserHabitConfig.Validate if the designated constraints aren't met.
type UserHabitConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserHabitConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserHabitConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserHabitConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserHabitConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserHabitConfigValidationError) ErrorName() string { return "UserHabitConfigValidationError" }

// Error satisfies the builtin error interface
func (e UserHabitConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserHabitConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserHabitConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserHabitConfigValidationError{}

// Validate checks the field values on CreateUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserHabitRequestMultiError, or nil if none found.
func (m *CreateUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserHabitRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserHabitRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserHabitRequestValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreateDate

	// no validation rules for HabitType

	// no validation rules for TimezonePlace

	if utf8.RuneCountInString(m.GetTimezone()) != 6 {
		err := CreateUserHabitRequestValidationError{
			field:  "Timezone",
			reason: "value length must be 6 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if len(errors) > 0 {
		return CreateUserHabitRequestMultiError(errors)
	}

	return nil
}

// CreateUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by CreateUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserHabitRequestMultiError) AllErrors() []error { return m }

// CreateUserHabitRequestValidationError is the validation error returned by
// CreateUserHabitRequest.Validate if the designated constraints aren't met.
type CreateUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserHabitRequestValidationError) ErrorName() string {
	return "CreateUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserHabitRequestValidationError{}

// Validate checks the field values on CreateUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserHabitReplyMultiError, or nil if none found.
func (m *CreateUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreateUserHabitReplyMultiError(errors)
	}

	return nil
}

// CreateUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by CreateUserHabitReply.ValidateAll() if the designated
// constraints aren't met.
type CreateUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserHabitReplyMultiError) AllErrors() []error { return m }

// CreateUserHabitReplyValidationError is the validation error returned by
// CreateUserHabitReply.Validate if the designated constraints aren't met.
type CreateUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserHabitReplyValidationError) ErrorName() string {
	return "CreateUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserHabitReplyValidationError{}

// Validate checks the field values on UpdateUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserHabitRequestMultiError, or nil if none found.
func (m *UpdateUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUserHabitRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUserHabitRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUserHabitRequestValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdateDate

	// no validation rules for TimezonePlace

	if utf8.RuneCountInString(m.GetTimezone()) != 6 {
		err := UpdateUserHabitRequestValidationError{
			field:  "Timezone",
			reason: "value length must be 6 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if len(errors) > 0 {
		return UpdateUserHabitRequestMultiError(errors)
	}

	return nil
}

// UpdateUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserHabitRequestMultiError) AllErrors() []error { return m }

// UpdateUserHabitRequestValidationError is the validation error returned by
// UpdateUserHabitRequest.Validate if the designated constraints aren't met.
type UpdateUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserHabitRequestValidationError) ErrorName() string {
	return "UpdateUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserHabitRequestValidationError{}

// Validate checks the field values on UpdateUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserHabitReplyMultiError, or nil if none found.
func (m *UpdateUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdateUserHabitReplyMultiError(errors)
	}

	return nil
}

// UpdateUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by UpdateUserHabitReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserHabitReplyMultiError) AllErrors() []error { return m }

// UpdateUserHabitReplyValidationError is the validation error returned by
// UpdateUserHabitReply.Validate if the designated constraints aren't met.
type UpdateUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserHabitReplyValidationError) ErrorName() string {
	return "UpdateUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserHabitReplyValidationError{}

// Validate checks the field values on DeleteUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserHabitRequestMultiError, or nil if none found.
func (m *DeleteUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteUserHabitRequestMultiError(errors)
	}

	return nil
}

// DeleteUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserHabitRequestMultiError) AllErrors() []error { return m }

// DeleteUserHabitRequestValidationError is the validation error returned by
// DeleteUserHabitRequest.Validate if the designated constraints aren't met.
type DeleteUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserHabitRequestValidationError) ErrorName() string {
	return "DeleteUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserHabitRequestValidationError{}

// Validate checks the field values on DeleteUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserHabitReplyMultiError, or nil if none found.
func (m *DeleteUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteUserHabitReplyMultiError(errors)
	}

	return nil
}

// DeleteUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by DeleteUserHabitReply.ValidateAll() if the designated
// constraints aren't met.
type DeleteUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserHabitReplyMultiError) AllErrors() []error { return m }

// DeleteUserHabitReplyValidationError is the validation error returned by
// DeleteUserHabitReply.Validate if the designated constraints aren't met.
type DeleteUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserHabitReplyValidationError) ErrorName() string {
	return "DeleteUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserHabitReplyValidationError{}

// Validate checks the field values on PauseUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PauseUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PauseUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PauseUserHabitRequestMultiError, or nil if none found.
func (m *PauseUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PauseUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return PauseUserHabitRequestMultiError(errors)
	}

	return nil
}

// PauseUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by PauseUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type PauseUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PauseUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PauseUserHabitRequestMultiError) AllErrors() []error { return m }

// PauseUserHabitRequestValidationError is the validation error returned by
// PauseUserHabitRequest.Validate if the designated constraints aren't met.
type PauseUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PauseUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PauseUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PauseUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PauseUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PauseUserHabitRequestValidationError) ErrorName() string {
	return "PauseUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PauseUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPauseUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PauseUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PauseUserHabitRequestValidationError{}

// Validate checks the field values on PauseUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PauseUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PauseUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PauseUserHabitReplyMultiError, or nil if none found.
func (m *PauseUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PauseUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PauseUserHabitReplyMultiError(errors)
	}

	return nil
}

// PauseUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by PauseUserHabitReply.ValidateAll() if the designated
// constraints aren't met.
type PauseUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PauseUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PauseUserHabitReplyMultiError) AllErrors() []error { return m }

// PauseUserHabitReplyValidationError is the validation error returned by
// PauseUserHabitReply.Validate if the designated constraints aren't met.
type PauseUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PauseUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PauseUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PauseUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PauseUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PauseUserHabitReplyValidationError) ErrorName() string {
	return "PauseUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e PauseUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPauseUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PauseUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PauseUserHabitReplyValidationError{}

// Validate checks the field values on RecoverUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecoverUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecoverUserHabitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecoverUserHabitRequestMultiError, or nil if none found.
func (m *RecoverUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecoverUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return RecoverUserHabitRequestMultiError(errors)
	}

	return nil
}

// RecoverUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by RecoverUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type RecoverUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecoverUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecoverUserHabitRequestMultiError) AllErrors() []error { return m }

// RecoverUserHabitRequestValidationError is the validation error returned by
// RecoverUserHabitRequest.Validate if the designated constraints aren't met.
type RecoverUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecoverUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecoverUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecoverUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecoverUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecoverUserHabitRequestValidationError) ErrorName() string {
	return "RecoverUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecoverUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecoverUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecoverUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecoverUserHabitRequestValidationError{}

// Validate checks the field values on RecoverUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecoverUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecoverUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecoverUserHabitReplyMultiError, or nil if none found.
func (m *RecoverUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *RecoverUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RecoverUserHabitReplyMultiError(errors)
	}

	return nil
}

// RecoverUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by RecoverUserHabitReply.ValidateAll() if the designated
// constraints aren't met.
type RecoverUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecoverUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecoverUserHabitReplyMultiError) AllErrors() []error { return m }

// RecoverUserHabitReplyValidationError is the validation error returned by
// RecoverUserHabitReply.Validate if the designated constraints aren't met.
type RecoverUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecoverUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecoverUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecoverUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecoverUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecoverUserHabitReplyValidationError) ErrorName() string {
	return "RecoverUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e RecoverUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecoverUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecoverUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecoverUserHabitReplyValidationError{}

// Validate checks the field values on ArchiveUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ArchiveUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveUserHabitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ArchiveUserHabitRequestMultiError, or nil if none found.
func (m *ArchiveUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return ArchiveUserHabitRequestMultiError(errors)
	}

	return nil
}

// ArchiveUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by ArchiveUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type ArchiveUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveUserHabitRequestMultiError) AllErrors() []error { return m }

// ArchiveUserHabitRequestValidationError is the validation error returned by
// ArchiveUserHabitRequest.Validate if the designated constraints aren't met.
type ArchiveUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveUserHabitRequestValidationError) ErrorName() string {
	return "ArchiveUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ArchiveUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveUserHabitRequestValidationError{}

// Validate checks the field values on ArchiveUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ArchiveUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ArchiveUserHabitReplyMultiError, or nil if none found.
func (m *ArchiveUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ArchiveUserHabitReplyMultiError(errors)
	}

	return nil
}

// ArchiveUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by ArchiveUserHabitReply.ValidateAll() if the designated
// constraints aren't met.
type ArchiveUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveUserHabitReplyMultiError) AllErrors() []error { return m }

// ArchiveUserHabitReplyValidationError is the validation error returned by
// ArchiveUserHabitReply.Validate if the designated constraints aren't met.
type ArchiveUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveUserHabitReplyValidationError) ErrorName() string {
	return "ArchiveUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ArchiveUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveUserHabitReplyValidationError{}

// Validate checks the field values on GetUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserHabitRequestMultiError, or nil if none found.
func (m *GetUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return GetUserHabitRequestMultiError(errors)
	}

	return nil
}

// GetUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserHabitRequestMultiError) AllErrors() []error { return m }

// GetUserHabitRequestValidationError is the validation error returned by
// GetUserHabitRequest.Validate if the designated constraints aren't met.
type GetUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserHabitRequestValidationError) ErrorName() string {
	return "GetUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserHabitRequestValidationError{}

// Validate checks the field values on GetUserHabitReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserHabitReplyMultiError, or nil if none found.
func (m *GetUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserHabitReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserHabitReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserHabitReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserHabitReplyMultiError(errors)
	}

	return nil
}

// GetUserHabitReplyMultiError is an error wrapping multiple validation errors
// returned by GetUserHabitReply.ValidateAll() if the designated constraints
// aren't met.
type GetUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserHabitReplyMultiError) AllErrors() []error { return m }

// GetUserHabitReplyValidationError is the validation error returned by
// GetUserHabitReply.Validate if the designated constraints aren't met.
type GetUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserHabitReplyValidationError) ErrorName() string {
	return "GetUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserHabitReplyValidationError{}

// Validate checks the field values on ListUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserHabitRequestMultiError, or nil if none found.
func (m *ListUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return ListUserHabitRequestMultiError(errors)
	}

	return nil
}

// ListUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by ListUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type ListUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserHabitRequestMultiError) AllErrors() []error { return m }

// ListUserHabitRequestValidationError is the validation error returned by
// ListUserHabitRequest.Validate if the designated constraints aren't met.
type ListUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserHabitRequestValidationError) ErrorName() string {
	return "ListUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserHabitRequestValidationError{}

// Validate checks the field values on ListUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserHabitReplyMultiError, or nil if none found.
func (m *ListUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserHabitReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserHabitReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserHabitReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListUserHabitReplyMultiError(errors)
	}

	return nil
}

// ListUserHabitReplyMultiError is an error wrapping multiple validation errors
// returned by ListUserHabitReply.ValidateAll() if the designated constraints
// aren't met.
type ListUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserHabitReplyMultiError) AllErrors() []error { return m }

// ListUserHabitReplyValidationError is the validation error returned by
// ListUserHabitReply.Validate if the designated constraints aren't met.
type ListUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserHabitReplyValidationError) ErrorName() string {
	return "ListUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserHabitReplyValidationError{}

// Validate checks the field values on CreateUserHabitMemoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserHabitMemoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserHabitMemoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserHabitMemoRequestMultiError, or nil if none found.
func (m *CreateUserHabitMemoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserHabitMemoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for Content

	// no validation rules for SmallStageId

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return CreateUserHabitMemoRequestMultiError(errors)
	}

	return nil
}

// CreateUserHabitMemoRequestMultiError is an error wrapping multiple
// validation errors returned by CreateUserHabitMemoRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateUserHabitMemoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserHabitMemoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserHabitMemoRequestMultiError) AllErrors() []error { return m }

// CreateUserHabitMemoRequestValidationError is the validation error returned
// by CreateUserHabitMemoRequest.Validate if the designated constraints aren't met.
type CreateUserHabitMemoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserHabitMemoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserHabitMemoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserHabitMemoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserHabitMemoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserHabitMemoRequestValidationError) ErrorName() string {
	return "CreateUserHabitMemoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserHabitMemoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserHabitMemoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserHabitMemoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserHabitMemoRequestValidationError{}

// Validate checks the field values on CreateUserHabitMemoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserHabitMemoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserHabitMemoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserHabitMemoReplyMultiError, or nil if none found.
func (m *CreateUserHabitMemoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserHabitMemoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreateUserHabitMemoReplyMultiError(errors)
	}

	return nil
}

// CreateUserHabitMemoReplyMultiError is an error wrapping multiple validation
// errors returned by CreateUserHabitMemoReply.ValidateAll() if the designated
// constraints aren't met.
type CreateUserHabitMemoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserHabitMemoReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserHabitMemoReplyMultiError) AllErrors() []error { return m }

// CreateUserHabitMemoReplyValidationError is the validation error returned by
// CreateUserHabitMemoReply.Validate if the designated constraints aren't met.
type CreateUserHabitMemoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserHabitMemoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserHabitMemoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserHabitMemoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserHabitMemoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserHabitMemoReplyValidationError) ErrorName() string {
	return "CreateUserHabitMemoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserHabitMemoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserHabitMemoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserHabitMemoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserHabitMemoReplyValidationError{}

// Validate checks the field values on UpdateUserHabitMemoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserHabitMemoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserHabitMemoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserHabitMemoRequestMultiError, or nil if none found.
func (m *UpdateUserHabitMemoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserHabitMemoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for Content

	// no validation rules for MemoId

	if len(errors) > 0 {
		return UpdateUserHabitMemoRequestMultiError(errors)
	}

	return nil
}

// UpdateUserHabitMemoRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateUserHabitMemoRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateUserHabitMemoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserHabitMemoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserHabitMemoRequestMultiError) AllErrors() []error { return m }

// UpdateUserHabitMemoRequestValidationError is the validation error returned
// by UpdateUserHabitMemoRequest.Validate if the designated constraints aren't met.
type UpdateUserHabitMemoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserHabitMemoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserHabitMemoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserHabitMemoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserHabitMemoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserHabitMemoRequestValidationError) ErrorName() string {
	return "UpdateUserHabitMemoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserHabitMemoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserHabitMemoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserHabitMemoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserHabitMemoRequestValidationError{}

// Validate checks the field values on UpdateUserHabitMemoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserHabitMemoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserHabitMemoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserHabitMemoReplyMultiError, or nil if none found.
func (m *UpdateUserHabitMemoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserHabitMemoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdateUserHabitMemoReplyMultiError(errors)
	}

	return nil
}

// UpdateUserHabitMemoReplyMultiError is an error wrapping multiple validation
// errors returned by UpdateUserHabitMemoReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserHabitMemoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserHabitMemoReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserHabitMemoReplyMultiError) AllErrors() []error { return m }

// UpdateUserHabitMemoReplyValidationError is the validation error returned by
// UpdateUserHabitMemoReply.Validate if the designated constraints aren't met.
type UpdateUserHabitMemoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserHabitMemoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserHabitMemoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserHabitMemoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserHabitMemoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserHabitMemoReplyValidationError) ErrorName() string {
	return "UpdateUserHabitMemoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserHabitMemoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserHabitMemoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserHabitMemoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserHabitMemoReplyValidationError{}

// Validate checks the field values on DeleteUserHabitMemoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserHabitMemoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserHabitMemoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserHabitMemoRequestMultiError, or nil if none found.
func (m *DeleteUserHabitMemoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserHabitMemoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for MemoId

	if len(errors) > 0 {
		return DeleteUserHabitMemoRequestMultiError(errors)
	}

	return nil
}

// DeleteUserHabitMemoRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteUserHabitMemoRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteUserHabitMemoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserHabitMemoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserHabitMemoRequestMultiError) AllErrors() []error { return m }

// DeleteUserHabitMemoRequestValidationError is the validation error returned
// by DeleteUserHabitMemoRequest.Validate if the designated constraints aren't met.
type DeleteUserHabitMemoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserHabitMemoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserHabitMemoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserHabitMemoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserHabitMemoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserHabitMemoRequestValidationError) ErrorName() string {
	return "DeleteUserHabitMemoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserHabitMemoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserHabitMemoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserHabitMemoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserHabitMemoRequestValidationError{}

// Validate checks the field values on DeleteUserHabitMemoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserHabitMemoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserHabitMemoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserHabitMemoReplyMultiError, or nil if none found.
func (m *DeleteUserHabitMemoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserHabitMemoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeleteUserHabitMemoReplyMultiError(errors)
	}

	return nil
}

// DeleteUserHabitMemoReplyMultiError is an error wrapping multiple validation
// errors returned by DeleteUserHabitMemoReply.ValidateAll() if the designated
// constraints aren't met.
type DeleteUserHabitMemoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserHabitMemoReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserHabitMemoReplyMultiError) AllErrors() []error { return m }

// DeleteUserHabitMemoReplyValidationError is the validation error returned by
// DeleteUserHabitMemoReply.Validate if the designated constraints aren't met.
type DeleteUserHabitMemoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserHabitMemoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserHabitMemoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserHabitMemoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserHabitMemoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserHabitMemoReplyValidationError) ErrorName() string {
	return "DeleteUserHabitMemoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserHabitMemoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserHabitMemoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserHabitMemoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserHabitMemoReplyValidationError{}

// Validate checks the field values on PunchUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PunchUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PunchUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PunchUserHabitRequestMultiError, or nil if none found.
func (m *PunchUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PunchUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for SmallStageId

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return PunchUserHabitRequestMultiError(errors)
	}

	return nil
}

// PunchUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by PunchUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type PunchUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PunchUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PunchUserHabitRequestMultiError) AllErrors() []error { return m }

// PunchUserHabitRequestValidationError is the validation error returned by
// PunchUserHabitRequest.Validate if the designated constraints aren't met.
type PunchUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PunchUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PunchUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PunchUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PunchUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PunchUserHabitRequestValidationError) ErrorName() string {
	return "PunchUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PunchUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPunchUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PunchUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PunchUserHabitRequestValidationError{}

// Validate checks the field values on PunchUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PunchUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PunchUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PunchUserHabitReplyMultiError, or nil if none found.
func (m *PunchUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PunchUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return PunchUserHabitReplyMultiError(errors)
	}

	return nil
}

// PunchUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by PunchUserHabitReply.ValidateAll() if the designated
// constraints aren't met.
type PunchUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PunchUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PunchUserHabitReplyMultiError) AllErrors() []error { return m }

// PunchUserHabitReplyValidationError is the validation error returned by
// PunchUserHabitReply.Validate if the designated constraints aren't met.
type PunchUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PunchUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PunchUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PunchUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PunchUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PunchUserHabitReplyValidationError) ErrorName() string {
	return "PunchUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e PunchUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPunchUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PunchUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PunchUserHabitReplyValidationError{}

// Validate checks the field values on UpdatePunchUserHabitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePunchUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePunchUserHabitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePunchUserHabitRequestMultiError, or nil if none found.
func (m *UpdatePunchUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePunchUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for PunchId

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return UpdatePunchUserHabitRequestMultiError(errors)
	}

	return nil
}

// UpdatePunchUserHabitRequestMultiError is an error wrapping multiple
// validation errors returned by UpdatePunchUserHabitRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdatePunchUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePunchUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePunchUserHabitRequestMultiError) AllErrors() []error { return m }

// UpdatePunchUserHabitRequestValidationError is the validation error returned
// by UpdatePunchUserHabitRequest.Validate if the designated constraints
// aren't met.
type UpdatePunchUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePunchUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePunchUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePunchUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePunchUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePunchUserHabitRequestValidationError) ErrorName() string {
	return "UpdatePunchUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePunchUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePunchUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePunchUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePunchUserHabitRequestValidationError{}

// Validate checks the field values on UpdatePunchUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePunchUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePunchUserHabitReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePunchUserHabitReplyMultiError, or nil if none found.
func (m *UpdatePunchUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePunchUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdatePunchUserHabitReplyMultiError(errors)
	}

	return nil
}

// UpdatePunchUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by UpdatePunchUserHabitReply.ValidateAll() if the
// designated constraints aren't met.
type UpdatePunchUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePunchUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePunchUserHabitReplyMultiError) AllErrors() []error { return m }

// UpdatePunchUserHabitReplyValidationError is the validation error returned by
// UpdatePunchUserHabitReply.Validate if the designated constraints aren't met.
type UpdatePunchUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePunchUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePunchUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePunchUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePunchUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePunchUserHabitReplyValidationError) ErrorName() string {
	return "UpdatePunchUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePunchUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePunchUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePunchUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePunchUserHabitReplyValidationError{}

// Validate checks the field values on CancelPunchUserHabitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelPunchUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelPunchUserHabitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelPunchUserHabitRequestMultiError, or nil if none found.
func (m *CancelPunchUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelPunchUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for SmallStageId

	// no validation rules for CurrentDate

	// no validation rules for PunchId

	if len(errors) > 0 {
		return CancelPunchUserHabitRequestMultiError(errors)
	}

	return nil
}

// CancelPunchUserHabitRequestMultiError is an error wrapping multiple
// validation errors returned by CancelPunchUserHabitRequest.ValidateAll() if
// the designated constraints aren't met.
type CancelPunchUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelPunchUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelPunchUserHabitRequestMultiError) AllErrors() []error { return m }

// CancelPunchUserHabitRequestValidationError is the validation error returned
// by CancelPunchUserHabitRequest.Validate if the designated constraints
// aren't met.
type CancelPunchUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelPunchUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelPunchUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelPunchUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelPunchUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelPunchUserHabitRequestValidationError) ErrorName() string {
	return "CancelPunchUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelPunchUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelPunchUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelPunchUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelPunchUserHabitRequestValidationError{}

// Validate checks the field values on CancelPunchUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelPunchUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelPunchUserHabitReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelPunchUserHabitReplyMultiError, or nil if none found.
func (m *CancelPunchUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelPunchUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CancelPunchUserHabitReplyMultiError(errors)
	}

	return nil
}

// CancelPunchUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by CancelPunchUserHabitReply.ValidateAll() if the
// designated constraints aren't met.
type CancelPunchUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelPunchUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelPunchUserHabitReplyMultiError) AllErrors() []error { return m }

// CancelPunchUserHabitReplyValidationError is the validation error returned by
// CancelPunchUserHabitReply.Validate if the designated constraints aren't met.
type CancelPunchUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelPunchUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelPunchUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelPunchUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelPunchUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelPunchUserHabitReplyValidationError) ErrorName() string {
	return "CancelPunchUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CancelPunchUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelPunchUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelPunchUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelPunchUserHabitReplyValidationError{}

// Validate checks the field values on ReckonUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReckonUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReckonUserHabitRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReckonUserHabitRequestMultiError, or nil if none found.
func (m *ReckonUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReckonUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for ReckonDuration

	// no validation rules for SmallStageId

	// no validation rules for CurrentDate

	// no validation rules for IsEnd

	if len(errors) > 0 {
		return ReckonUserHabitRequestMultiError(errors)
	}

	return nil
}

// ReckonUserHabitRequestMultiError is an error wrapping multiple validation
// errors returned by ReckonUserHabitRequest.ValidateAll() if the designated
// constraints aren't met.
type ReckonUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReckonUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReckonUserHabitRequestMultiError) AllErrors() []error { return m }

// ReckonUserHabitRequestValidationError is the validation error returned by
// ReckonUserHabitRequest.Validate if the designated constraints aren't met.
type ReckonUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReckonUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReckonUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReckonUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReckonUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReckonUserHabitRequestValidationError) ErrorName() string {
	return "ReckonUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReckonUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReckonUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReckonUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReckonUserHabitRequestValidationError{}

// Validate checks the field values on ReckonUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReckonUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReckonUserHabitReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReckonUserHabitReplyMultiError, or nil if none found.
func (m *ReckonUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ReckonUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReckonUserHabitReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReckonUserHabitReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReckonUserHabitReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReckonUserHabitReplyMultiError(errors)
	}

	return nil
}

// ReckonUserHabitReplyMultiError is an error wrapping multiple validation
// errors returned by ReckonUserHabitReply.ValidateAll() if the designated
// constraints aren't met.
type ReckonUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReckonUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReckonUserHabitReplyMultiError) AllErrors() []error { return m }

// ReckonUserHabitReplyValidationError is the validation error returned by
// ReckonUserHabitReply.Validate if the designated constraints aren't met.
type ReckonUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReckonUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReckonUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReckonUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReckonUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReckonUserHabitReplyValidationError) ErrorName() string {
	return "ReckonUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ReckonUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReckonUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReckonUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReckonUserHabitReplyValidationError{}

// Validate checks the field values on CancelReckonUserHabitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelReckonUserHabitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelReckonUserHabitRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelReckonUserHabitRequestMultiError, or nil if none found.
func (m *CancelReckonUserHabitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelReckonUserHabitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for SmallStageId

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return CancelReckonUserHabitRequestMultiError(errors)
	}

	return nil
}

// CancelReckonUserHabitRequestMultiError is an error wrapping multiple
// validation errors returned by CancelReckonUserHabitRequest.ValidateAll() if
// the designated constraints aren't met.
type CancelReckonUserHabitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelReckonUserHabitRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelReckonUserHabitRequestMultiError) AllErrors() []error { return m }

// CancelReckonUserHabitRequestValidationError is the validation error returned
// by CancelReckonUserHabitRequest.Validate if the designated constraints
// aren't met.
type CancelReckonUserHabitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelReckonUserHabitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelReckonUserHabitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelReckonUserHabitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelReckonUserHabitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelReckonUserHabitRequestValidationError) ErrorName() string {
	return "CancelReckonUserHabitRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelReckonUserHabitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelReckonUserHabitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelReckonUserHabitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelReckonUserHabitRequestValidationError{}

// Validate checks the field values on CancelReckonUserHabitReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelReckonUserHabitReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelReckonUserHabitReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelReckonUserHabitReplyMultiError, or nil if none found.
func (m *CancelReckonUserHabitReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelReckonUserHabitReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CancelReckonUserHabitReplyMultiError(errors)
	}

	return nil
}

// CancelReckonUserHabitReplyMultiError is an error wrapping multiple
// validation errors returned by CancelReckonUserHabitReply.ValidateAll() if
// the designated constraints aren't met.
type CancelReckonUserHabitReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelReckonUserHabitReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelReckonUserHabitReplyMultiError) AllErrors() []error { return m }

// CancelReckonUserHabitReplyValidationError is the validation error returned
// by CancelReckonUserHabitReply.Validate if the designated constraints aren't met.
type CancelReckonUserHabitReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelReckonUserHabitReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelReckonUserHabitReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelReckonUserHabitReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelReckonUserHabitReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelReckonUserHabitReplyValidationError) ErrorName() string {
	return "CancelReckonUserHabitReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CancelReckonUserHabitReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelReckonUserHabitReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelReckonUserHabitReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelReckonUserHabitReplyValidationError{}

// Validate checks the field values on CreateUserHabitReckonRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserHabitReckonRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserHabitReckonRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserHabitReckonRequestMultiError, or nil if none found.
func (m *CreateUserHabitReckonRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserHabitReckonRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for ReckonDuration

	// no validation rules for SmallStageId

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return CreateUserHabitReckonRequestMultiError(errors)
	}

	return nil
}

// CreateUserHabitReckonRequestMultiError is an error wrapping multiple
// validation errors returned by CreateUserHabitReckonRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateUserHabitReckonRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserHabitReckonRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserHabitReckonRequestMultiError) AllErrors() []error { return m }

// CreateUserHabitReckonRequestValidationError is the validation error returned
// by CreateUserHabitReckonRequest.Validate if the designated constraints
// aren't met.
type CreateUserHabitReckonRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserHabitReckonRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserHabitReckonRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserHabitReckonRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserHabitReckonRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserHabitReckonRequestValidationError) ErrorName() string {
	return "CreateUserHabitReckonRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserHabitReckonRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserHabitReckonRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserHabitReckonRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserHabitReckonRequestValidationError{}

// Validate checks the field values on CreateUserHabitReckonReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserHabitReckonReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserHabitReckonReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserHabitReckonReplyMultiError, or nil if none found.
func (m *CreateUserHabitReckonReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserHabitReckonReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreateUserHabitReckonReplyMultiError(errors)
	}

	return nil
}

// CreateUserHabitReckonReplyMultiError is an error wrapping multiple
// validation errors returned by CreateUserHabitReckonReply.ValidateAll() if
// the designated constraints aren't met.
type CreateUserHabitReckonReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserHabitReckonReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserHabitReckonReplyMultiError) AllErrors() []error { return m }

// CreateUserHabitReckonReplyValidationError is the validation error returned
// by CreateUserHabitReckonReply.Validate if the designated constraints aren't met.
type CreateUserHabitReckonReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserHabitReckonReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserHabitReckonReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserHabitReckonReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserHabitReckonReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserHabitReckonReplyValidationError) ErrorName() string {
	return "CreateUserHabitReckonReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserHabitReckonReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserHabitReckonReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserHabitReckonReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserHabitReckonReplyValidationError{}

// Validate checks the field values on UpdateUserHabitReckonRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserHabitReckonRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserHabitReckonRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserHabitReckonRequestMultiError, or nil if none found.
func (m *UpdateUserHabitReckonRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserHabitReckonRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for ReckonDuration

	// no validation rules for ReckonId

	// no validation rules for SmallStageId

	if len(errors) > 0 {
		return UpdateUserHabitReckonRequestMultiError(errors)
	}

	return nil
}

// UpdateUserHabitReckonRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateUserHabitReckonRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateUserHabitReckonRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserHabitReckonRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserHabitReckonRequestMultiError) AllErrors() []error { return m }

// UpdateUserHabitReckonRequestValidationError is the validation error returned
// by UpdateUserHabitReckonRequest.Validate if the designated constraints
// aren't met.
type UpdateUserHabitReckonRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserHabitReckonRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserHabitReckonRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserHabitReckonRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserHabitReckonRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserHabitReckonRequestValidationError) ErrorName() string {
	return "UpdateUserHabitReckonRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserHabitReckonRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserHabitReckonRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserHabitReckonRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserHabitReckonRequestValidationError{}

// Validate checks the field values on UpdateUserHabitReckonReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserHabitReckonReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserHabitReckonReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserHabitReckonReplyMultiError, or nil if none found.
func (m *UpdateUserHabitReckonReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserHabitReckonReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdateUserHabitReckonReplyMultiError(errors)
	}

	return nil
}

// UpdateUserHabitReckonReplyMultiError is an error wrapping multiple
// validation errors returned by UpdateUserHabitReckonReply.ValidateAll() if
// the designated constraints aren't met.
type UpdateUserHabitReckonReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserHabitReckonReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserHabitReckonReplyMultiError) AllErrors() []error { return m }

// UpdateUserHabitReckonReplyValidationError is the validation error returned
// by UpdateUserHabitReckonReply.Validate if the designated constraints aren't met.
type UpdateUserHabitReckonReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserHabitReckonReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserHabitReckonReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserHabitReckonReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserHabitReckonReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserHabitReckonReplyValidationError) ErrorName() string {
	return "UpdateUserHabitReckonReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserHabitReckonReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserHabitReckonReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserHabitReckonReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserHabitReckonReplyValidationError{}

// Validate checks the field values on DeleteUserHabitReckonRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserHabitReckonRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserHabitReckonRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserHabitReckonRequestMultiError, or nil if none found.
func (m *DeleteUserHabitReckonRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserHabitReckonRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserHabitId

	// no validation rules for ReckonId

	if len(errors) > 0 {
		return DeleteUserHabitReckonRequestMultiError(errors)
	}

	return nil
}

// DeleteUserHabitReckonRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteUserHabitReckonRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteUserHabitReckonRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserHabitReckonRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserHabitReckonRequestMultiError) AllErrors() []error { return m }

// DeleteUserHabitReckonRequestValidationError is the validation error returned
// by DeleteUserHabitReckonRequest.Validate if the designated constraints
// aren't met.
type DeleteUserHabitReckonRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserHabitReckonRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserHabitReckonRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserHabitReckonRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserHabitReckonRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserHabitReckonRequestValidationError) ErrorName() string {
	return "DeleteUserHabitReckonRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserHabitReckonRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserHabitReckonRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserHabitReckonRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserHabitReckonRequestValidationError{}

// Validate checks the field values on DeleteUserHabitReckonReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserHabitReckonReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserHabitReckonReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserHabitReckonReplyMultiError, or nil if none found.
func (m *DeleteUserHabitReckonReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserHabitReckonReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeleteUserHabitReckonReplyMultiError(errors)
	}

	return nil
}

// DeleteUserHabitReckonReplyMultiError is an error wrapping multiple
// validation errors returned by DeleteUserHabitReckonReply.ValidateAll() if
// the designated constraints aren't met.
type DeleteUserHabitReckonReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserHabitReckonReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserHabitReckonReplyMultiError) AllErrors() []error { return m }

// DeleteUserHabitReckonReplyValidationError is the validation error returned
// by DeleteUserHabitReckonReply.Validate if the designated constraints aren't met.
type DeleteUserHabitReckonReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserHabitReckonReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserHabitReckonReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserHabitReckonReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserHabitReckonReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserHabitReckonReplyValidationError) ErrorName() string {
	return "DeleteUserHabitReckonReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserHabitReckonReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserHabitReckonReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserHabitReckonReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserHabitReckonReplyValidationError{}

// Validate checks the field values on ListUserHabitSnapshotRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserHabitSnapshotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserHabitSnapshotRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserHabitSnapshotRequestMultiError, or nil if none found.
func (m *ListUserHabitSnapshotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserHabitSnapshotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LabelType

	// no validation rules for CurrentDate

	if len(errors) > 0 {
		return ListUserHabitSnapshotRequestMultiError(errors)
	}

	return nil
}

// ListUserHabitSnapshotRequestMultiError is an error wrapping multiple
// validation errors returned by ListUserHabitSnapshotRequest.ValidateAll() if
// the designated constraints aren't met.
type ListUserHabitSnapshotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserHabitSnapshotRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserHabitSnapshotRequestMultiError) AllErrors() []error { return m }

// ListUserHabitSnapshotRequestValidationError is the validation error returned
// by ListUserHabitSnapshotRequest.Validate if the designated constraints
// aren't met.
type ListUserHabitSnapshotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserHabitSnapshotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserHabitSnapshotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserHabitSnapshotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserHabitSnapshotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserHabitSnapshotRequestValidationError) ErrorName() string {
	return "ListUserHabitSnapshotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserHabitSnapshotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserHabitSnapshotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserHabitSnapshotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserHabitSnapshotRequestValidationError{}

// Validate checks the field values on ListUserHabitSnapshotReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserHabitSnapshotReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserHabitSnapshotReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserHabitSnapshotReplyMultiError, or nil if none found.
func (m *ListUserHabitSnapshotReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserHabitSnapshotReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUserHabitSnapshotReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUserHabitSnapshotReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUserHabitSnapshotReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListUserHabitSnapshotReplyMultiError(errors)
	}

	return nil
}

// ListUserHabitSnapshotReplyMultiError is an error wrapping multiple
// validation errors returned by ListUserHabitSnapshotReply.ValidateAll() if
// the designated constraints aren't met.
type ListUserHabitSnapshotReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserHabitSnapshotReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserHabitSnapshotReplyMultiError) AllErrors() []error { return m }

// ListUserHabitSnapshotReplyValidationError is the validation error returned
// by ListUserHabitSnapshotReply.Validate if the designated constraints aren't met.
type ListUserHabitSnapshotReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserHabitSnapshotReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserHabitSnapshotReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserHabitSnapshotReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserHabitSnapshotReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserHabitSnapshotReplyValidationError) ErrorName() string {
	return "ListUserHabitSnapshotReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserHabitSnapshotReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserHabitSnapshotReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserHabitSnapshotReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserHabitSnapshotReplyValidationError{}

// Validate checks the field values on RunCronTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RunCronTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RunCronTaskRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RunCronTaskRequestMultiError, or nil if none found.
func (m *RunCronTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RunCronTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for NotRunTag

	if len(errors) > 0 {
		return RunCronTaskRequestMultiError(errors)
	}

	return nil
}

// RunCronTaskRequestMultiError is an error wrapping multiple validation errors
// returned by RunCronTaskRequest.ValidateAll() if the designated constraints
// aren't met.
type RunCronTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RunCronTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RunCronTaskRequestMultiError) AllErrors() []error { return m }

// RunCronTaskRequestValidationError is the validation error returned by
// RunCronTaskRequest.Validate if the designated constraints aren't met.
type RunCronTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RunCronTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RunCronTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RunCronTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RunCronTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RunCronTaskRequestValidationError) ErrorName() string {
	return "RunCronTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RunCronTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRunCronTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RunCronTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RunCronTaskRequestValidationError{}

// Validate checks the field values on RunCronTaskReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RunCronTaskReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RunCronTaskReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RunCronTaskReplyMultiError, or nil if none found.
func (m *RunCronTaskReply) ValidateAll() error {
	return m.validate(true)
}

func (m *RunCronTaskReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return RunCronTaskReplyMultiError(errors)
	}

	return nil
}

// RunCronTaskReplyMultiError is an error wrapping multiple validation errors
// returned by RunCronTaskReply.ValidateAll() if the designated constraints
// aren't met.
type RunCronTaskReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RunCronTaskReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RunCronTaskReplyMultiError) AllErrors() []error { return m }

// RunCronTaskReplyValidationError is the validation error returned by
// RunCronTaskReply.Validate if the designated constraints aren't met.
type RunCronTaskReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RunCronTaskReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RunCronTaskReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RunCronTaskReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RunCronTaskReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RunCronTaskReplyValidationError) ErrorName() string { return "RunCronTaskReplyValidationError" }

// Error satisfies the builtin error interface
func (e RunCronTaskReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRunCronTaskReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RunCronTaskReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RunCronTaskReplyValidationError{}

// Validate checks the field values on TodayStatistic with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TodayStatistic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TodayStatistic with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TodayStatisticMultiError,
// or nil if none found.
func (m *TodayStatistic) ValidateAll() error {
	return m.validate(true)
}

func (m *TodayStatistic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSmallHabit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TodayStatisticValidationError{
					field:  "SmallHabit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TodayStatisticValidationError{
					field:  "SmallHabit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSmallHabit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TodayStatisticValidationError{
				field:  "SmallHabit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNormalHabit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TodayStatisticValidationError{
					field:  "NormalHabit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TodayStatisticValidationError{
					field:  "NormalHabit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNormalHabit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TodayStatisticValidationError{
				field:  "NormalHabit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetWeekData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TodayStatisticValidationError{
						field:  fmt.Sprintf("WeekData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TodayStatisticValidationError{
						field:  fmt.Sprintf("WeekData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TodayStatisticValidationError{
					field:  fmt.Sprintf("WeekData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TodayStatisticMultiError(errors)
	}

	return nil
}

// TodayStatisticMultiError is an error wrapping multiple validation errors
// returned by TodayStatistic.ValidateAll() if the designated constraints
// aren't met.
type TodayStatisticMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TodayStatisticMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TodayStatisticMultiError) AllErrors() []error { return m }

// TodayStatisticValidationError is the validation error returned by
// TodayStatistic.Validate if the designated constraints aren't met.
type TodayStatisticValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TodayStatisticValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TodayStatisticValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TodayStatisticValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TodayStatisticValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TodayStatisticValidationError) ErrorName() string { return "TodayStatisticValidationError" }

// Error satisfies the builtin error interface
func (e TodayStatisticValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTodayStatistic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TodayStatisticValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TodayStatisticValidationError{}

// Validate checks the field values on GetUserHabitReply_DayData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserHabitReply_DayData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserHabitReply_DayData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserHabitReply_DayDataMultiError, or nil if none found.
func (m *GetUserHabitReply_DayData) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserHabitReply_DayData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DateTimestamp

	// no validation rules for CompleteLevel

	if len(errors) > 0 {
		return GetUserHabitReply_DayDataMultiError(errors)
	}

	return nil
}

// GetUserHabitReply_DayDataMultiError is an error wrapping multiple validation
// errors returned by GetUserHabitReply_DayData.ValidateAll() if the
// designated constraints aren't met.
type GetUserHabitReply_DayDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserHabitReply_DayDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserHabitReply_DayDataMultiError) AllErrors() []error { return m }

// GetUserHabitReply_DayDataValidationError is the validation error returned by
// GetUserHabitReply_DayData.Validate if the designated constraints aren't met.
type GetUserHabitReply_DayDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserHabitReply_DayDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserHabitReply_DayDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserHabitReply_DayDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserHabitReply_DayDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserHabitReply_DayDataValidationError) ErrorName() string {
	return "GetUserHabitReply_DayDataValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserHabitReply_DayDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserHabitReply_DayData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserHabitReply_DayDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserHabitReply_DayDataValidationError{}

// Validate checks the field values on GetUserHabitReply_TimeLine with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserHabitReply_TimeLine) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserHabitReply_TimeLine with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserHabitReply_TimeLineMultiError, or nil if none found.
func (m *GetUserHabitReply_TimeLine) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserHabitReply_TimeLine) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Time

	// no validation rules for OperateType

	// no validation rules for SmallStageName

	// no validation rules for SmallStageId

	// no validation rules for Duration

	// no validation rules for MemoContent

	// no validation rules for PunchId

	// no validation rules for MemoId

	// no validation rules for ReckonId

	if len(errors) > 0 {
		return GetUserHabitReply_TimeLineMultiError(errors)
	}

	return nil
}

// GetUserHabitReply_TimeLineMultiError is an error wrapping multiple
// validation errors returned by GetUserHabitReply_TimeLine.ValidateAll() if
// the designated constraints aren't met.
type GetUserHabitReply_TimeLineMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserHabitReply_TimeLineMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserHabitReply_TimeLineMultiError) AllErrors() []error { return m }

// GetUserHabitReply_TimeLineValidationError is the validation error returned
// by GetUserHabitReply_TimeLine.Validate if the designated constraints aren't met.
type GetUserHabitReply_TimeLineValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserHabitReply_TimeLineValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserHabitReply_TimeLineValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserHabitReply_TimeLineValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserHabitReply_TimeLineValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserHabitReply_TimeLineValidationError) ErrorName() string {
	return "GetUserHabitReply_TimeLineValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserHabitReply_TimeLineValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserHabitReply_TimeLine.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserHabitReply_TimeLineValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserHabitReply_TimeLineValidationError{}

// Validate checks the field values on GetUserHabitReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserHabitReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserHabitReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserHabitReply_DataMultiError, or nil if none found.
func (m *GetUserHabitReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserHabitReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Status

	// no validation rules for HabitType

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserHabitReply_DataValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserHabitReply_DataValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserHabitReply_DataValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PunchedTotal

	for idx, item := range m.GetCalendar() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserHabitReply_DataValidationError{
						field:  fmt.Sprintf("Calendar[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserHabitReply_DataValidationError{
						field:  fmt.Sprintf("Calendar[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserHabitReply_DataValidationError{
					field:  fmt.Sprintf("Calendar[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTimeline() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserHabitReply_DataValidationError{
						field:  fmt.Sprintf("Timeline[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserHabitReply_DataValidationError{
						field:  fmt.Sprintf("Timeline[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserHabitReply_DataValidationError{
					field:  fmt.Sprintf("Timeline[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Desc

	// no validation rules for IsCompleted

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return GetUserHabitReply_DataMultiError(errors)
	}

	return nil
}

// GetUserHabitReply_DataMultiError is an error wrapping multiple validation
// errors returned by GetUserHabitReply_Data.ValidateAll() if the designated
// constraints aren't met.
type GetUserHabitReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserHabitReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserHabitReply_DataMultiError) AllErrors() []error { return m }

// GetUserHabitReply_DataValidationError is the validation error returned by
// GetUserHabitReply_Data.Validate if the designated constraints aren't met.
type GetUserHabitReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserHabitReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserHabitReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserHabitReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserHabitReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserHabitReply_DataValidationError) ErrorName() string {
	return "GetUserHabitReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserHabitReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserHabitReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserHabitReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserHabitReply_DataValidationError{}

// Validate checks the field values on ListUserHabitReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserHabitReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserHabitReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserHabitReply_DataMultiError, or nil if none found.
func (m *ListUserHabitReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserHabitReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Status

	// no validation rules for PunchedDays

	// no validation rules for PunchAllDays

	// no validation rules for PunchCycleType

	// no validation rules for StreakDays

	// no validation rules for IsSetPrivacy

	// no validation rules for CreatedAt

	// no validation rules for EndAt

	// no validation rules for HealthStatus

	if len(errors) > 0 {
		return ListUserHabitReply_DataMultiError(errors)
	}

	return nil
}

// ListUserHabitReply_DataMultiError is an error wrapping multiple validation
// errors returned by ListUserHabitReply_Data.ValidateAll() if the designated
// constraints aren't met.
type ListUserHabitReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserHabitReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserHabitReply_DataMultiError) AllErrors() []error { return m }

// ListUserHabitReply_DataValidationError is the validation error returned by
// ListUserHabitReply_Data.Validate if the designated constraints aren't met.
type ListUserHabitReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserHabitReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserHabitReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserHabitReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserHabitReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserHabitReply_DataValidationError) ErrorName() string {
	return "ListUserHabitReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserHabitReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserHabitReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserHabitReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserHabitReply_DataValidationError{}

// Validate checks the field values on ReckonUserHabitReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReckonUserHabitReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReckonUserHabitReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReckonUserHabitReply_DataMultiError, or nil if none found.
func (m *ReckonUserHabitReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ReckonUserHabitReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReckonDuration

	// no validation rules for IsReckoning

	if len(errors) > 0 {
		return ReckonUserHabitReply_DataMultiError(errors)
	}

	return nil
}

// ReckonUserHabitReply_DataMultiError is an error wrapping multiple validation
// errors returned by ReckonUserHabitReply_Data.ValidateAll() if the
// designated constraints aren't met.
type ReckonUserHabitReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReckonUserHabitReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReckonUserHabitReply_DataMultiError) AllErrors() []error { return m }

// ReckonUserHabitReply_DataValidationError is the validation error returned by
// ReckonUserHabitReply_Data.Validate if the designated constraints aren't met.
type ReckonUserHabitReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReckonUserHabitReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReckonUserHabitReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReckonUserHabitReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReckonUserHabitReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReckonUserHabitReply_DataValidationError) ErrorName() string {
	return "ReckonUserHabitReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ReckonUserHabitReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReckonUserHabitReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReckonUserHabitReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReckonUserHabitReply_DataValidationError{}

// Validate checks the field values on
// ListUserHabitSnapshotReply_SmallStageItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListUserHabitSnapshotReply_SmallStageItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListUserHabitSnapshotReply_SmallStageItem with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListUserHabitSnapshotReply_SmallStageItemMultiError, or nil if none found.
func (m *ListUserHabitSnapshotReply_SmallStageItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserHabitSnapshotReply_SmallStageItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StageId

	// no validation rules for Name

	// no validation rules for PunchedCount

	// no validation rules for IsReckoning

	if len(errors) > 0 {
		return ListUserHabitSnapshotReply_SmallStageItemMultiError(errors)
	}

	return nil
}

// ListUserHabitSnapshotReply_SmallStageItemMultiError is an error wrapping
// multiple validation errors returned by
// ListUserHabitSnapshotReply_SmallStageItem.ValidateAll() if the designated
// constraints aren't met.
type ListUserHabitSnapshotReply_SmallStageItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserHabitSnapshotReply_SmallStageItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserHabitSnapshotReply_SmallStageItemMultiError) AllErrors() []error { return m }

// ListUserHabitSnapshotReply_SmallStageItemValidationError is the validation
// error returned by ListUserHabitSnapshotReply_SmallStageItem.Validate if the
// designated constraints aren't met.
type ListUserHabitSnapshotReply_SmallStageItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserHabitSnapshotReply_SmallStageItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserHabitSnapshotReply_SmallStageItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserHabitSnapshotReply_SmallStageItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserHabitSnapshotReply_SmallStageItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserHabitSnapshotReply_SmallStageItemValidationError) ErrorName() string {
	return "ListUserHabitSnapshotReply_SmallStageItemValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserHabitSnapshotReply_SmallStageItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserHabitSnapshotReply_SmallStageItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserHabitSnapshotReply_SmallStageItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserHabitSnapshotReply_SmallStageItemValidationError{}

// Validate checks the field values on ListUserHabitSnapshotReply_HabitItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListUserHabitSnapshotReply_HabitItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserHabitSnapshotReply_HabitItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListUserHabitSnapshotReply_HabitItemMultiError, or nil if none found.
func (m *ListUserHabitSnapshotReply_HabitItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserHabitSnapshotReply_HabitItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for HabitType

	for idx, item := range m.GetSmallStages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserHabitSnapshotReply_HabitItemValidationError{
						field:  fmt.Sprintf("SmallStages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserHabitSnapshotReply_HabitItemValidationError{
						field:  fmt.Sprintf("SmallStages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserHabitSnapshotReply_HabitItemValidationError{
					field:  fmt.Sprintf("SmallStages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsReckoning

	// no validation rules for Duration

	// no validation rules for IsAllowCancelPunch

	// no validation rules for IsJoinAward

	// no validation rules for IsNecessary

	// no validation rules for IsSetPrivacy

	// no validation rules for CreatedAt

	// no validation rules for PunchCycleType

	// no validation rules for Completed

	// no validation rules for Per

	// no validation rules for DoneCount

	// no validation rules for AllCount

	// no validation rules for EndDate

	if len(errors) > 0 {
		return ListUserHabitSnapshotReply_HabitItemMultiError(errors)
	}

	return nil
}

// ListUserHabitSnapshotReply_HabitItemMultiError is an error wrapping multiple
// validation errors returned by
// ListUserHabitSnapshotReply_HabitItem.ValidateAll() if the designated
// constraints aren't met.
type ListUserHabitSnapshotReply_HabitItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserHabitSnapshotReply_HabitItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserHabitSnapshotReply_HabitItemMultiError) AllErrors() []error { return m }

// ListUserHabitSnapshotReply_HabitItemValidationError is the validation error
// returned by ListUserHabitSnapshotReply_HabitItem.Validate if the designated
// constraints aren't met.
type ListUserHabitSnapshotReply_HabitItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserHabitSnapshotReply_HabitItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserHabitSnapshotReply_HabitItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserHabitSnapshotReply_HabitItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserHabitSnapshotReply_HabitItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserHabitSnapshotReply_HabitItemValidationError) ErrorName() string {
	return "ListUserHabitSnapshotReply_HabitItemValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserHabitSnapshotReply_HabitItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserHabitSnapshotReply_HabitItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserHabitSnapshotReply_HabitItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserHabitSnapshotReply_HabitItemValidationError{}

// Validate checks the field values on ListUserHabitSnapshotReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserHabitSnapshotReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUserHabitSnapshotReply_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListUserHabitSnapshotReply_DataMultiError, or nil if none found.
func (m *ListUserHabitSnapshotReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserHabitSnapshotReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTodayStatisticData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUserHabitSnapshotReply_DataValidationError{
					field:  "TodayStatisticData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUserHabitSnapshotReply_DataValidationError{
					field:  "TodayStatisticData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTodayStatisticData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUserHabitSnapshotReply_DataValidationError{
				field:  "TodayStatisticData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTopHabits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserHabitSnapshotReply_DataValidationError{
						field:  fmt.Sprintf("TopHabits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserHabitSnapshotReply_DataValidationError{
						field:  fmt.Sprintf("TopHabits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserHabitSnapshotReply_DataValidationError{
					field:  fmt.Sprintf("TopHabits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLowHabits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserHabitSnapshotReply_DataValidationError{
						field:  fmt.Sprintf("LowHabits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserHabitSnapshotReply_DataValidationError{
						field:  fmt.Sprintf("LowHabits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserHabitSnapshotReply_DataValidationError{
					field:  fmt.Sprintf("LowHabits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTrackHabits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserHabitSnapshotReply_DataValidationError{
						field:  fmt.Sprintf("TrackHabits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserHabitSnapshotReply_DataValidationError{
						field:  fmt.Sprintf("TrackHabits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserHabitSnapshotReply_DataValidationError{
					field:  fmt.Sprintf("TrackHabits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsNeedShow

	if len(errors) > 0 {
		return ListUserHabitSnapshotReply_DataMultiError(errors)
	}

	return nil
}

// ListUserHabitSnapshotReply_DataMultiError is an error wrapping multiple
// validation errors returned by ListUserHabitSnapshotReply_Data.ValidateAll()
// if the designated constraints aren't met.
type ListUserHabitSnapshotReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserHabitSnapshotReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserHabitSnapshotReply_DataMultiError) AllErrors() []error { return m }

// ListUserHabitSnapshotReply_DataValidationError is the validation error
// returned by ListUserHabitSnapshotReply_Data.Validate if the designated
// constraints aren't met.
type ListUserHabitSnapshotReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserHabitSnapshotReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserHabitSnapshotReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserHabitSnapshotReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserHabitSnapshotReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserHabitSnapshotReply_DataValidationError) ErrorName() string {
	return "ListUserHabitSnapshotReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserHabitSnapshotReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserHabitSnapshotReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserHabitSnapshotReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserHabitSnapshotReply_DataValidationError{}

// Validate checks the field values on TodayStatistic_HabitDetail with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TodayStatistic_HabitDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TodayStatistic_HabitDetail with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TodayStatistic_HabitDetailMultiError, or nil if none found.
func (m *TodayStatistic_HabitDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *TodayStatistic_HabitDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Per

	// no validation rules for DoneCount

	// no validation rules for AllCount

	if len(errors) > 0 {
		return TodayStatistic_HabitDetailMultiError(errors)
	}

	return nil
}

// TodayStatistic_HabitDetailMultiError is an error wrapping multiple
// validation errors returned by TodayStatistic_HabitDetail.ValidateAll() if
// the designated constraints aren't met.
type TodayStatistic_HabitDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TodayStatistic_HabitDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TodayStatistic_HabitDetailMultiError) AllErrors() []error { return m }

// TodayStatistic_HabitDetailValidationError is the validation error returned
// by TodayStatistic_HabitDetail.Validate if the designated constraints aren't met.
type TodayStatistic_HabitDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TodayStatistic_HabitDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TodayStatistic_HabitDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TodayStatistic_HabitDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TodayStatistic_HabitDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TodayStatistic_HabitDetailValidationError) ErrorName() string {
	return "TodayStatistic_HabitDetailValidationError"
}

// Error satisfies the builtin error interface
func (e TodayStatistic_HabitDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTodayStatistic_HabitDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TodayStatistic_HabitDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TodayStatistic_HabitDetailValidationError{}

// Validate checks the field values on TodayStatistic_WeekData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TodayStatistic_WeekData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TodayStatistic_WeekData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TodayStatistic_WeekDataMultiError, or nil if none found.
func (m *TodayStatistic_WeekData) ValidateAll() error {
	return m.validate(true)
}

func (m *TodayStatistic_WeekData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Day

	// no validation rules for Progress

	// no validation rules for IsToday

	if len(errors) > 0 {
		return TodayStatistic_WeekDataMultiError(errors)
	}

	return nil
}

// TodayStatistic_WeekDataMultiError is an error wrapping multiple validation
// errors returned by TodayStatistic_WeekData.ValidateAll() if the designated
// constraints aren't met.
type TodayStatistic_WeekDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TodayStatistic_WeekDataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TodayStatistic_WeekDataMultiError) AllErrors() []error { return m }

// TodayStatistic_WeekDataValidationError is the validation error returned by
// TodayStatistic_WeekData.Validate if the designated constraints aren't met.
type TodayStatistic_WeekDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TodayStatistic_WeekDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TodayStatistic_WeekDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TodayStatistic_WeekDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TodayStatistic_WeekDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TodayStatistic_WeekDataValidationError) ErrorName() string {
	return "TodayStatistic_WeekDataValidationError"
}

// Error satisfies the builtin error interface
func (e TodayStatistic_WeekDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTodayStatistic_WeekData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TodayStatistic_WeekDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TodayStatistic_WeekDataValidationError{}
