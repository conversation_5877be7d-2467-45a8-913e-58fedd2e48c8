// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api.proto

package api

import (
	v13 "github.com/wlnil/life-log-be/api/planet/v1"
	v11 "github.com/wlnil/life-log-be/api/site/v1"
	v1 "github.com/wlnil/life-log-be/api/user/v1"
	v12 "github.com/wlnil/life-log-be/api/user_habit/v1"
	v16 "github.com/wlnil/life-log-be/api/user_message/v1"
	v14 "github.com/wlnil/life-log-be/api/user_planet/v1"
	v15 "github.com/wlnil/life-log-be/api/user_statistic/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_proto protoreflect.FileDescriptor

const file_api_proto_rawDesc = "" +
	"\n" +
	"\tapi.proto\x12\x03api\x1a\x1cgoogle/api/annotations.proto\x1a\x12user/v1/user.proto\x1a\x12site/v1/site.proto\x1a\x14site/v1/health.proto\x1a\x1euser_habit/v1/user_habit.proto\x1a\x16planet/v1/planet.proto\x1a user_planet/v1/user_planet.proto\x1a&user_statistic/v1/user_statistic.proto\x1a\"user_message/v1/user_message.proto2\xf7T\n" +
	"\aLifeLog\x12^\n" +
	"\bRegister\x12\x18.user_v1.RegisterRequest\x1a\x16.user_v1.RegisterReply\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/api/v1/user/register\x12R\n" +
	"\x05Login\x12\x15.user_v1.LoginRequest\x1a\x13.user_v1.LoginReply\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/api/v1/user/login\x12V\n" +
	"\x06Logout\x12\x16.user_v1.LogoutRequest\x1a\x14.user_v1.LogoutReply\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/api/v1/user/logout\x12c\n" +
	"\tPushToken\x12\x19.user_v1.PushTokenRequest\x1a\x17.user_v1.PushTokenReply\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/api/v1/user/push-token\x12w\n" +
	"\x0eForgetPassword\x12\x1e.user_v1.ForgetPasswordRequest\x1a\x1c.user_v1.ForgetPasswordReply\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/api/v1/user/forget-password\x12w\n" +
	"\x0eChangePassword\x12\x1e.user_v1.ChangePasswordRequest\x1a\x1c.user_v1.ChangePasswordReply\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/api/v1/user/change-password\x12[\n" +
	"\n" +
	"DeleteUser\x12\x1a.user_v1.DeleteUserRequest\x1a\x18.user_v1.DeleteUserReply\"\x17\x82\xd3\xe4\x93\x02\x11:\x01*\"\f/api/v1/user\x12k\n" +
	"\vChangePhone\x12\x1b.user_v1.ChangePhoneRequest\x1a\x19.user_v1.ChangePhoneReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/api/v1/user/change-phone\x12k\n" +
	"\vChangeEmail\x12\x1b.user_v1.ChangeEmailRequest\x1a\x19.user_v1.ChangeEmailReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/api/v1/user/change-email\x12l\n" +
	"\x0eGetUserProfile\x12\x1e.user_v1.GetUserProfileRequest\x1a\x1c.user_v1.GetUserProfileReply\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/api/v1/user/profile\x12x\n" +
	"\x11UpdateUserProfile\x12!.user_v1.UpdateUserProfileRequest\x1a\x1f.user_v1.UpdateUserProfileReply\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\x1a\x14/api/v1/user/profile\x12l\n" +
	"\n" +
	"FollowUser\x12\x1a.user_v1.FollowUserRequest\x1a\x18.user_v1.FollowUserReply\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/api/v1/user/{user_id}/follow\x12t\n" +
	"\fUnfollowUser\x12\x1c.user_v1.UnfollowUserRequest\x1a\x1a.user_v1.UnfollowUserReply\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/api/v1/user/{user_id}/unfollow\x12h\n" +
	"\fRefreshToken\x12\x1c.user_v1.RefreshTokenRequest\x1a\x13.user_v1.LoginReply\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/api/v1/user/refresh-token\x12l\n" +
	"\x0eGetUserSetting\x12\x1e.user_v1.GetUserSettingRequest\x1a\x1c.user_v1.GetUserSettingReply\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/api/v1/user/setting\x12p\n" +
	"\x0fUpdateUserAward\x12\x1f.user_v1.UpdateUserAwardRequest\x1a\x1d.user_v1.UpdateUserAwardReply\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/api/v1/user/award\x12\x99\x01\n" +
	"\x19ChangeUserPrivacyPassword\x12).user_v1.ChangeUserPrivacyPasswordRequest\x1a'.user_v1.ChangeUserPrivacyPasswordReply\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/api/v1/user/privacy-password\x12\x9c\x01\n" +
	"\x18CheckUserPrivacyPassword\x12(.user_v1.CheckUserPrivacyPasswordRequest\x1a&.user_v1.CheckUserPrivacyPasswordReply\".\x82\xd3\xe4\x93\x02(:\x01*\"#/api/v1/user/privacy-password/check\x12h\n" +
	"\x0eGetUserVipInfo\x12\x1e.user_v1.GetUserVipInfoRequest\x1a\x1c.user_v1.GetUserVipInfoReply\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/api/v1/user/vip\x12c\n" +
	"\vBuddySearch\x12\x1b.user_v1.BuddySearchRequest\x1a\x19.user_v1.BuddySearchReply\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/api/v1/buddy/search\x12v\n" +
	"\x0fBuddyInvitation\x12\x1f.user_v1.BuddyInvitationRequest\x1a\x1d.user_v1.BuddyInvitationReply\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/api/v1/buddy/invitation\x12`\n" +
	"\vGetSiteInfo\x12\x1b.site_v1.GetSiteInfoRequest\x1a\x19.site_v1.GetSiteInfoReply\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/api/v1/site-info\x12]\n" +
	"\vHealthCheck\x12\x1b.site_v1.HealthCheckRequest\x1a\x19.site_v1.HealthCheckReply\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/api/v1/health\x12q\n" +
	"\rCreateUpToken\x12\x1d.site_v1.CreateUpTokenRequest\x1a\x1b.site_v1.CreateUpTokenReply\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v1/storage/upload-token\x12t\n" +
	"\rCreateDownURL\x12\x1d.site_v1.CreateDownURLRequest\x1a\x1b.site_v1.CreateDownURLReply\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/api/v1/storage/download-url\x12w\n" +
	"\x0eSendVerifyCode\x12\x1e.site_v1.SendVerifyCodeRequest\x1a\x1c.site_v1.SendVerifyCodeReply\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/api/v1/sms/verify-code/send\x12k\n" +
	"\x0eListMotiveMemo\x12\x1e.site_v1.ListMotiveMemoRequest\x1a\x1c.site_v1.ListMotiveMemoReply\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/api/v1/motive-memo\x12k\n" +
	"\x0eCreateFeedback\x12\x1e.site_v1.CreateFeedbackRequest\x1a\x1c.site_v1.CreateFeedbackReply\"\x1b\x82\xd3\xe4\x93\x02\x15:\x01*\"\x10/api/v1/feedback\x12g\n" +
	"\fVersionCheck\x12\x1c.site_v1.VersionCheckRequest\x1a\x1a.site_v1.VersionCheckReply\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/api/v1/version/check\x12|\n" +
	"\x0fCreateUserHabit\x12%.user_habit_v1.CreateUserHabitRequest\x1a#.user_habit_v1.CreateUserHabitReply\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/api/v1/user/habit\x12s\n" +
	"\rListUserHabit\x12#.user_habit_v1.ListUserHabitRequest\x1a!.user_habit_v1.ListUserHabitReply\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/api/v1/user/habit\x12u\n" +
	"\fGetUserHabit\x12\".user_habit_v1.GetUserHabitRequest\x1a .user_habit_v1.GetUserHabitReply\"\x1f\x82\xd3\xe4\x93\x02\x19\x12\x17/api/v1/user/habit/{id}\x12\x81\x01\n" +
	"\x0fUpdateUserHabit\x12%.user_habit_v1.UpdateUserHabitRequest\x1a#.user_habit_v1.UpdateUserHabitReply\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\x1a\x17/api/v1/user/habit/{id}\x12~\n" +
	"\x0fDeleteUserHabit\x12%.user_habit_v1.DeleteUserHabitRequest\x1a#.user_habit_v1.DeleteUserHabitReply\"\x1f\x82\xd3\xe4\x93\x02\x19*\x17/api/v1/user/habit/{id}\x12\x84\x01\n" +
	"\x0ePauseUserHabit\x12$.user_habit_v1.PauseUserHabitRequest\x1a\".user_habit_v1.PauseUserHabitReply\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/api/v1/user/habit/{id}/pause\x12\x8c\x01\n" +
	"\x10RecoverUserHabit\x12&.user_habit_v1.RecoverUserHabitRequest\x1a$.user_habit_v1.RecoverUserHabitReply\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/api/v1/user/habit/{id}/recover\x12\x8c\x01\n" +
	"\x10ArchiveUserHabit\x12&.user_habit_v1.ArchiveUserHabitRequest\x1a$.user_habit_v1.ArchiveUserHabitReply\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/api/v1/user/habit/{id}/archive\x12\x94\x01\n" +
	"\x15ListUserHabitSnapshot\x12+.user_habit_v1.ListUserHabitSnapshotRequest\x1a).user_habit_v1.ListUserHabitSnapshotReply\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/api/v1/user/habit_snapshot\x12\x9d\x01\n" +
	"\x13CreateUserHabitMemo\x12).user_habit_v1.CreateUserHabitMemoRequest\x1a'.user_habit_v1.CreateUserHabitMemoReply\"2\x82\xd3\xe4\x93\x02,:\x01*\"'/api/v1/user/habit/{user_habit_id}/memo\x12\xa7\x01\n" +
	"\x13UpdateUserHabitMemo\x12).user_habit_v1.UpdateUserHabitMemoRequest\x1a'.user_habit_v1.UpdateUserHabitMemoReply\"<\x82\xd3\xe4\x93\x026:\x01*\x1a1/api/v1/user/habit/{user_habit_id}/memo/{memo_id}\x12\xa4\x01\n" +
	"\x13DeleteUserHabitMemo\x12).user_habit_v1.DeleteUserHabitMemoRequest\x1a'.user_habit_v1.DeleteUserHabitMemoReply\"9\x82\xd3\xe4\x93\x023*1/api/v1/user/habit/{user_habit_id}/memo/{memo_id}\x12\x8f\x01\n" +
	"\x0ePunchUserHabit\x12$.user_habit_v1.PunchUserHabitRequest\x1a\".user_habit_v1.PunchUserHabitReply\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/api/v1/user/habit/{user_habit_id}/punch\x12\xa8\x01\n" +
	"\x14CancelPunchUserHabit\x12*.user_habit_v1.CancelPunchUserHabitRequest\x1a(.user_habit_v1.CancelPunchUserHabitReply\":\x82\xd3\xe4\x93\x024:\x01*\"//api/v1/user/habit/{user_habit_id}/cancel-punch\x12\xac\x01\n" +
	"\x14UpdatePunchUserHabit\x12*.user_habit_v1.UpdatePunchUserHabitRequest\x1a(.user_habit_v1.UpdatePunchUserHabitReply\">\x82\xd3\xe4\x93\x028:\x01*\x1a3/api/v1/user/habit/{user_habit_id}/punch/{punch_id}\x12\x93\x01\n" +
	"\x0fReckonUserHabit\x12%.user_habit_v1.ReckonUserHabitRequest\x1a#.user_habit_v1.ReckonUserHabitReply\"4\x82\xd3\xe4\x93\x02.:\x01*\")/api/v1/user/habit/{user_habit_id}/reckon\x12\xac\x01\n" +
	"\x15CancelReckonUserHabit\x12+.user_habit_v1.CancelReckonUserHabitRequest\x1a).user_habit_v1.CancelReckonUserHabitReply\";\x82\xd3\xe4\x93\x025:\x01*\"0/api/v1/user/habit/{user_habit_id}/cancel-reckon\x12\xaa\x01\n" +
	"\x15CreateUserHabitReckon\x12+.user_habit_v1.CreateUserHabitReckonRequest\x1a).user_habit_v1.CreateUserHabitReckonReply\"9\x82\xd3\xe4\x93\x023:\x01*\"./api/v1/user/habit/{user_habit_id}/reckon/save\x12\xb1\x01\n" +
	"\x15UpdateUserHabitReckon\x12+.user_habit_v1.UpdateUserHabitReckonRequest\x1a).user_habit_v1.UpdateUserHabitReckonReply\"@\x82\xd3\xe4\x93\x02::\x01*\x1a5/api/v1/user/habit/{user_habit_id}/reckon/{reckon_id}\x12\xae\x01\n" +
	"\x15DeleteUserHabitReckon\x12+.user_habit_v1.DeleteUserHabitReckonRequest\x1a).user_habit_v1.DeleteUserHabitReckonReply\"=\x82\xd3\xe4\x93\x027*5/api/v1/user/habit/{user_habit_id}/reckon/{reckon_id}\x12g\n" +
	"\fCreatePlanet\x12\x1e.planet_v1.CreatePlanetRequest\x1a\x1c.planet_v1.CreatePlanetReply\"\x19\x82\xd3\xe4\x93\x02\x13:\x01*\"\x0e/api/v1/planet\x12`\n" +
	"\tGetPlanet\x12\x1b.planet_v1.GetPlanetRequest\x1a\x19.planet_v1.GetPlanetReply\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/api/v1/planet/{id}\x12l\n" +
	"\fUpdatePlanet\x12\x1e.planet_v1.UpdatePlanetRequest\x1a\x1c.planet_v1.UpdatePlanetReply\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\x1a\x13/api/v1/planet/{id}\x12i\n" +
	"\fDeletePlanet\x12\x1e.planet_v1.DeletePlanetRequest\x1a\x1c.planet_v1.DeletePlanetReply\"\x1b\x82\xd3\xe4\x93\x02\x15*\x13/api/v1/planet/{id}\x12\x8c\x01\n" +
	"\x12CreatePlanetTarget\x12$.planet_v1.CreatePlanetTargetRequest\x1a\".planet_v1.CreatePlanetTargetReply\",\x82\xd3\xe4\x93\x02&:\x01*\"!/api/v1/planet/{planet_id}/target\x12\x85\x01\n" +
	"\x0fGetPlanetTarget\x12!.planet_v1.GetPlanetTargetRequest\x1a\x1f.planet_v1.GetPlanetTargetReply\".\x82\xd3\xe4\x93\x02(\x12&/api/v1/planet/{planet_id}/target/{id}\x12\x91\x01\n" +
	"\x12UpdatePlanetTarget\x12$.planet_v1.UpdatePlanetTargetRequest\x1a\".planet_v1.UpdatePlanetTargetReply\"1\x82\xd3\xe4\x93\x02+:\x01*\x1a&/api/v1/planet/{planet_id}/target/{id}\x12\x8e\x01\n" +
	"\x12DeletePlanetTarget\x12$.planet_v1.DeletePlanetTargetRequest\x1a\".planet_v1.DeletePlanetTargetReply\".\x82\xd3\xe4\x93\x02(*&/api/v1/planet/{planet_id}/target/{id}\x12\x83\x01\n" +
	"\x10ListPlanetTarget\x12\".planet_v1.ListPlanetTargetRequest\x1a .planet_v1.ListPlanetTargetReply\")\x82\xd3\xe4\x93\x02#\x12!/api/v1/planet/{planet_id}/target\x12\x80\x01\n" +
	"\x12ListPlanetByUserID\x12).user_planet_v1.ListPlanetByUserIDRequest\x1a'.user_planet_v1.ListPlanetByUserIDReply\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/api/v1/planet\x12|\n" +
	"\n" +
	"JoinPlanet\x12!.user_planet_v1.JoinPlanetRequest\x1a\x1f.user_planet_v1.JoinPlanetReply\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/api/v1/planet/{planet_id}/join\x12|\n" +
	"\n" +
	"QuitPlanet\x12!.user_planet_v1.QuitPlanetRequest\x1a\x1f.user_planet_v1.QuitPlanetReply\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/api/v1/planet/{planet_id}/quit\x12\xa1\x01\n" +
	"\x10JoinPlanetTarget\x12'.user_planet_v1.JoinPlanetTargetRequest\x1a%.user_planet_v1.JoinPlanetTargetReply\"=\x82\xd3\xe4\x93\x027:\x01*\"2/api/v1/planet/{planet_id}/target/{target_id}/join\x12\xa1\x01\n" +
	"\x10QuitPlanetTarget\x12'.user_planet_v1.QuitPlanetTargetRequest\x1a%.user_planet_v1.QuitPlanetTargetReply\"=\x82\xd3\xe4\x93\x027:\x01*\"2/api/v1/planet/{planet_id}/target/{target_id}/quit\x12\x97\x01\n" +
	"\x0eLikePlanetPost\x12%.user_planet_v1.LikePlanetPostRequest\x1a#.user_planet_v1.LikePlanetPostReply\"9\x82\xd3\xe4\x93\x023:\x01*\"./api/v1/planet/{planet_id}/post/{post_id}/like\x12\xb0\x01\n" +
	"\x14CancelLikePlanetPost\x12+.user_planet_v1.CancelLikePlanetPostRequest\x1a).user_planet_v1.CancelLikePlanetPostReply\"@\x82\xd3\xe4\x93\x02::\x01*\"5/api/v1/planet/{planet_id}/post/{post_id}/cancel-like\x12\xa7\x01\n" +
	"\x12FavoritePlanetPost\x12).user_planet_v1.FavoritePlanetPostRequest\x1a'.user_planet_v1.FavoritePlanetPostReply\"=\x82\xd3\xe4\x93\x027:\x01*\"2/api/v1/planet/{planet_id}/post/{post_id}/favorite\x12\xc0\x01\n" +
	"\x18CancelFavoritePlanetPost\x12/.user_planet_v1.CancelFavoritePlanetPostRequest\x1a-.user_planet_v1.CancelFavoritePlanetPostReply\"D\x82\xd3\xe4\x93\x02>:\x01*\"9/api/v1/planet/{planet_id}/post/{post_id}/cancel-favorite\x12\x8e\x01\n" +
	"\x10CreatePlanetPost\x12'.user_planet_v1.CreatePlanetPostRequest\x1a%.user_planet_v1.CreatePlanetPostReply\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/api/v1/planet/{planet_id}/post\x12\x98\x01\n" +
	"\x10UpdatePlanetPost\x12'.user_planet_v1.UpdatePlanetPostRequest\x1a%.user_planet_v1.UpdatePlanetPostReply\"4\x82\xd3\xe4\x93\x02.:\x01*\x1a)/api/v1/planet/{planet_id}/post/{post_id}\x12\x95\x01\n" +
	"\x10DeletePlanetPost\x12'.user_planet_v1.DeletePlanetPostRequest\x1a%.user_planet_v1.DeletePlanetPostReply\"1\x82\xd3\xe4\x93\x02+*)/api/v1/planet/{planet_id}/post/{post_id}\x12\x9f\x01\n" +
	"\x10ToppedPlanetPost\x12'.user_planet_v1.ToppedPlanetPostRequest\x1a%.user_planet_v1.ToppedPlanetPostReply\";\x82\xd3\xe4\x93\x025:\x01*\"0/api/v1/planet/{planet_id}/post/{post_id}/topped\x12\x85\x01\n" +
	"\x0eListPlanetPost\x12%.user_planet_v1.ListPlanetPostRequest\x1a#.user_planet_v1.ListPlanetPostReply\"'\x82\xd3\xe4\x93\x02!\x12\x1f/api/v1/planet/{planet_id}/post\x12\x92\x01\n" +
	"\x11ListPlanetTopPost\x12(.user_planet_v1.ListPlanetTopPostRequest\x1a&.user_planet_v1.ListPlanetTopPostReply\"+\x82\xd3\xe4\x93\x02%\x12#/api/v1/planet/{planet_id}/top-post\x12\xb5\x01\n" +
	"\x17CreatePlanetPostComment\x12..user_planet_v1.CreatePlanetPostCommentRequest\x1a,.user_planet_v1.CreatePlanetPostCommentReply\"<\x82\xd3\xe4\x93\x026:\x01*\"1/api/v1/planet/{planet_id}/post/{post_id}/comment\x12\xbf\x01\n" +
	"\x17DeletePlanetPostComment\x12..user_planet_v1.DeletePlanetPostCommentRequest\x1a,.user_planet_v1.DeletePlanetPostCommentReply\"F\x82\xd3\xe4\x93\x02@*>/api/v1/planet/{planet_id}/post/{post_id}/comment/{comment_id}\x12\xac\x01\n" +
	"\x15ListPlanetPostComment\x12,.user_planet_v1.ListPlanetPostCommentRequest\x1a*.user_planet_v1.ListPlanetPostCommentReply\"9\x82\xd3\xe4\x93\x023\x121/api/v1/planet/{planet_id}/post/{post_id}/comment\x12n\n" +
	"\vRunCronTask\x12!.user_habit_v1.RunCronTaskRequest\x1a\x1f.user_habit_v1.RunCronTaskReply\"\x1b\x82\xd3\xe4\x93\x02\x15:\x01*\"\x10/api/v1/cron/run\x12\xa8\x01\n" +
	"\x16TodayStatisticFromHome\x124.api.user_statistic_v1.TodayStatisticFromHomeRequest\x1a2.api.user_statistic_v1.TodayStatisticFromHomeReply\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v1/user_statistic/today\x12\xca\x01\n" +
	"\x1cUserHabitStatisticFromDetail\x12:.api.user_statistic_v1.UserHabitStatisticFromDetailRequest\x1a8.api.user_statistic_v1.UserHabitStatisticFromDetailReply\"4\x82\xd3\xe4\x93\x02.\x12,/api/v1/user_statistic/habit/{user_habit_id}\x12{\n" +
	"\vUnreadCount\x12#.user_message_v1.UnreadCountRequest\x1a!.user_message_v1.UnreadCountReply\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v1/message/unread-countB&Z$github.com/wlnil/life-log-be/api;apib\x06proto3"

var file_api_proto_goTypes = []any{
	(*v1.RegisterRequest)(nil),                      // 0: user_v1.RegisterRequest
	(*v1.LoginRequest)(nil),                         // 1: user_v1.LoginRequest
	(*v1.LogoutRequest)(nil),                        // 2: user_v1.LogoutRequest
	(*v1.PushTokenRequest)(nil),                     // 3: user_v1.PushTokenRequest
	(*v1.ForgetPasswordRequest)(nil),                // 4: user_v1.ForgetPasswordRequest
	(*v1.ChangePasswordRequest)(nil),                // 5: user_v1.ChangePasswordRequest
	(*v1.DeleteUserRequest)(nil),                    // 6: user_v1.DeleteUserRequest
	(*v1.ChangePhoneRequest)(nil),                   // 7: user_v1.ChangePhoneRequest
	(*v1.ChangeEmailRequest)(nil),                   // 8: user_v1.ChangeEmailRequest
	(*v1.GetUserProfileRequest)(nil),                // 9: user_v1.GetUserProfileRequest
	(*v1.UpdateUserProfileRequest)(nil),             // 10: user_v1.UpdateUserProfileRequest
	(*v1.FollowUserRequest)(nil),                    // 11: user_v1.FollowUserRequest
	(*v1.UnfollowUserRequest)(nil),                  // 12: user_v1.UnfollowUserRequest
	(*v1.RefreshTokenRequest)(nil),                  // 13: user_v1.RefreshTokenRequest
	(*v1.GetUserSettingRequest)(nil),                // 14: user_v1.GetUserSettingRequest
	(*v1.UpdateUserAwardRequest)(nil),               // 15: user_v1.UpdateUserAwardRequest
	(*v1.ChangeUserPrivacyPasswordRequest)(nil),     // 16: user_v1.ChangeUserPrivacyPasswordRequest
	(*v1.CheckUserPrivacyPasswordRequest)(nil),      // 17: user_v1.CheckUserPrivacyPasswordRequest
	(*v1.GetUserVipInfoRequest)(nil),                // 18: user_v1.GetUserVipInfoRequest
	(*v1.BuddySearchRequest)(nil),                   // 19: user_v1.BuddySearchRequest
	(*v1.BuddyInvitationRequest)(nil),               // 20: user_v1.BuddyInvitationRequest
	(*v11.GetSiteInfoRequest)(nil),                  // 21: site_v1.GetSiteInfoRequest
	(*v11.HealthCheckRequest)(nil),                  // 22: site_v1.HealthCheckRequest
	(*v11.CreateUpTokenRequest)(nil),                // 23: site_v1.CreateUpTokenRequest
	(*v11.CreateDownURLRequest)(nil),                // 24: site_v1.CreateDownURLRequest
	(*v11.SendVerifyCodeRequest)(nil),               // 25: site_v1.SendVerifyCodeRequest
	(*v11.ListMotiveMemoRequest)(nil),               // 26: site_v1.ListMotiveMemoRequest
	(*v11.CreateFeedbackRequest)(nil),               // 27: site_v1.CreateFeedbackRequest
	(*v11.VersionCheckRequest)(nil),                 // 28: site_v1.VersionCheckRequest
	(*v12.CreateUserHabitRequest)(nil),              // 29: user_habit_v1.CreateUserHabitRequest
	(*v12.ListUserHabitRequest)(nil),                // 30: user_habit_v1.ListUserHabitRequest
	(*v12.GetUserHabitRequest)(nil),                 // 31: user_habit_v1.GetUserHabitRequest
	(*v12.UpdateUserHabitRequest)(nil),              // 32: user_habit_v1.UpdateUserHabitRequest
	(*v12.DeleteUserHabitRequest)(nil),              // 33: user_habit_v1.DeleteUserHabitRequest
	(*v12.PauseUserHabitRequest)(nil),               // 34: user_habit_v1.PauseUserHabitRequest
	(*v12.RecoverUserHabitRequest)(nil),             // 35: user_habit_v1.RecoverUserHabitRequest
	(*v12.ArchiveUserHabitRequest)(nil),             // 36: user_habit_v1.ArchiveUserHabitRequest
	(*v12.ListUserHabitSnapshotRequest)(nil),        // 37: user_habit_v1.ListUserHabitSnapshotRequest
	(*v12.CreateUserHabitMemoRequest)(nil),          // 38: user_habit_v1.CreateUserHabitMemoRequest
	(*v12.UpdateUserHabitMemoRequest)(nil),          // 39: user_habit_v1.UpdateUserHabitMemoRequest
	(*v12.DeleteUserHabitMemoRequest)(nil),          // 40: user_habit_v1.DeleteUserHabitMemoRequest
	(*v12.PunchUserHabitRequest)(nil),               // 41: user_habit_v1.PunchUserHabitRequest
	(*v12.CancelPunchUserHabitRequest)(nil),         // 42: user_habit_v1.CancelPunchUserHabitRequest
	(*v12.UpdatePunchUserHabitRequest)(nil),         // 43: user_habit_v1.UpdatePunchUserHabitRequest
	(*v12.ReckonUserHabitRequest)(nil),              // 44: user_habit_v1.ReckonUserHabitRequest
	(*v12.CancelReckonUserHabitRequest)(nil),        // 45: user_habit_v1.CancelReckonUserHabitRequest
	(*v12.CreateUserHabitReckonRequest)(nil),        // 46: user_habit_v1.CreateUserHabitReckonRequest
	(*v12.UpdateUserHabitReckonRequest)(nil),        // 47: user_habit_v1.UpdateUserHabitReckonRequest
	(*v12.DeleteUserHabitReckonRequest)(nil),        // 48: user_habit_v1.DeleteUserHabitReckonRequest
	(*v13.CreatePlanetRequest)(nil),                 // 49: planet_v1.CreatePlanetRequest
	(*v13.GetPlanetRequest)(nil),                    // 50: planet_v1.GetPlanetRequest
	(*v13.UpdatePlanetRequest)(nil),                 // 51: planet_v1.UpdatePlanetRequest
	(*v13.DeletePlanetRequest)(nil),                 // 52: planet_v1.DeletePlanetRequest
	(*v13.CreatePlanetTargetRequest)(nil),           // 53: planet_v1.CreatePlanetTargetRequest
	(*v13.GetPlanetTargetRequest)(nil),              // 54: planet_v1.GetPlanetTargetRequest
	(*v13.UpdatePlanetTargetRequest)(nil),           // 55: planet_v1.UpdatePlanetTargetRequest
	(*v13.DeletePlanetTargetRequest)(nil),           // 56: planet_v1.DeletePlanetTargetRequest
	(*v13.ListPlanetTargetRequest)(nil),             // 57: planet_v1.ListPlanetTargetRequest
	(*v14.ListPlanetByUserIDRequest)(nil),           // 58: user_planet_v1.ListPlanetByUserIDRequest
	(*v14.JoinPlanetRequest)(nil),                   // 59: user_planet_v1.JoinPlanetRequest
	(*v14.QuitPlanetRequest)(nil),                   // 60: user_planet_v1.QuitPlanetRequest
	(*v14.JoinPlanetTargetRequest)(nil),             // 61: user_planet_v1.JoinPlanetTargetRequest
	(*v14.QuitPlanetTargetRequest)(nil),             // 62: user_planet_v1.QuitPlanetTargetRequest
	(*v14.LikePlanetPostRequest)(nil),               // 63: user_planet_v1.LikePlanetPostRequest
	(*v14.CancelLikePlanetPostRequest)(nil),         // 64: user_planet_v1.CancelLikePlanetPostRequest
	(*v14.FavoritePlanetPostRequest)(nil),           // 65: user_planet_v1.FavoritePlanetPostRequest
	(*v14.CancelFavoritePlanetPostRequest)(nil),     // 66: user_planet_v1.CancelFavoritePlanetPostRequest
	(*v14.CreatePlanetPostRequest)(nil),             // 67: user_planet_v1.CreatePlanetPostRequest
	(*v14.UpdatePlanetPostRequest)(nil),             // 68: user_planet_v1.UpdatePlanetPostRequest
	(*v14.DeletePlanetPostRequest)(nil),             // 69: user_planet_v1.DeletePlanetPostRequest
	(*v14.ToppedPlanetPostRequest)(nil),             // 70: user_planet_v1.ToppedPlanetPostRequest
	(*v14.ListPlanetPostRequest)(nil),               // 71: user_planet_v1.ListPlanetPostRequest
	(*v14.ListPlanetTopPostRequest)(nil),            // 72: user_planet_v1.ListPlanetTopPostRequest
	(*v14.CreatePlanetPostCommentRequest)(nil),      // 73: user_planet_v1.CreatePlanetPostCommentRequest
	(*v14.DeletePlanetPostCommentRequest)(nil),      // 74: user_planet_v1.DeletePlanetPostCommentRequest
	(*v14.ListPlanetPostCommentRequest)(nil),        // 75: user_planet_v1.ListPlanetPostCommentRequest
	(*v12.RunCronTaskRequest)(nil),                  // 76: user_habit_v1.RunCronTaskRequest
	(*v15.TodayStatisticFromHomeRequest)(nil),       // 77: api.user_statistic_v1.TodayStatisticFromHomeRequest
	(*v15.UserHabitStatisticFromDetailRequest)(nil), // 78: api.user_statistic_v1.UserHabitStatisticFromDetailRequest
	(*v16.UnreadCountRequest)(nil),                  // 79: user_message_v1.UnreadCountRequest
	(*v1.RegisterReply)(nil),                        // 80: user_v1.RegisterReply
	(*v1.LoginReply)(nil),                           // 81: user_v1.LoginReply
	(*v1.LogoutReply)(nil),                          // 82: user_v1.LogoutReply
	(*v1.PushTokenReply)(nil),                       // 83: user_v1.PushTokenReply
	(*v1.ForgetPasswordReply)(nil),                  // 84: user_v1.ForgetPasswordReply
	(*v1.ChangePasswordReply)(nil),                  // 85: user_v1.ChangePasswordReply
	(*v1.DeleteUserReply)(nil),                      // 86: user_v1.DeleteUserReply
	(*v1.ChangePhoneReply)(nil),                     // 87: user_v1.ChangePhoneReply
	(*v1.ChangeEmailReply)(nil),                     // 88: user_v1.ChangeEmailReply
	(*v1.GetUserProfileReply)(nil),                  // 89: user_v1.GetUserProfileReply
	(*v1.UpdateUserProfileReply)(nil),               // 90: user_v1.UpdateUserProfileReply
	(*v1.FollowUserReply)(nil),                      // 91: user_v1.FollowUserReply
	(*v1.UnfollowUserReply)(nil),                    // 92: user_v1.UnfollowUserReply
	(*v1.GetUserSettingReply)(nil),                  // 93: user_v1.GetUserSettingReply
	(*v1.UpdateUserAwardReply)(nil),                 // 94: user_v1.UpdateUserAwardReply
	(*v1.ChangeUserPrivacyPasswordReply)(nil),       // 95: user_v1.ChangeUserPrivacyPasswordReply
	(*v1.CheckUserPrivacyPasswordReply)(nil),        // 96: user_v1.CheckUserPrivacyPasswordReply
	(*v1.GetUserVipInfoReply)(nil),                  // 97: user_v1.GetUserVipInfoReply
	(*v1.BuddySearchReply)(nil),                     // 98: user_v1.BuddySearchReply
	(*v1.BuddyInvitationReply)(nil),                 // 99: user_v1.BuddyInvitationReply
	(*v11.GetSiteInfoReply)(nil),                    // 100: site_v1.GetSiteInfoReply
	(*v11.HealthCheckReply)(nil),                    // 101: site_v1.HealthCheckReply
	(*v11.CreateUpTokenReply)(nil),                  // 102: site_v1.CreateUpTokenReply
	(*v11.CreateDownURLReply)(nil),                  // 103: site_v1.CreateDownURLReply
	(*v11.SendVerifyCodeReply)(nil),                 // 104: site_v1.SendVerifyCodeReply
	(*v11.ListMotiveMemoReply)(nil),                 // 105: site_v1.ListMotiveMemoReply
	(*v11.CreateFeedbackReply)(nil),                 // 106: site_v1.CreateFeedbackReply
	(*v11.VersionCheckReply)(nil),                   // 107: site_v1.VersionCheckReply
	(*v12.CreateUserHabitReply)(nil),                // 108: user_habit_v1.CreateUserHabitReply
	(*v12.ListUserHabitReply)(nil),                  // 109: user_habit_v1.ListUserHabitReply
	(*v12.GetUserHabitReply)(nil),                   // 110: user_habit_v1.GetUserHabitReply
	(*v12.UpdateUserHabitReply)(nil),                // 111: user_habit_v1.UpdateUserHabitReply
	(*v12.DeleteUserHabitReply)(nil),                // 112: user_habit_v1.DeleteUserHabitReply
	(*v12.PauseUserHabitReply)(nil),                 // 113: user_habit_v1.PauseUserHabitReply
	(*v12.RecoverUserHabitReply)(nil),               // 114: user_habit_v1.RecoverUserHabitReply
	(*v12.ArchiveUserHabitReply)(nil),               // 115: user_habit_v1.ArchiveUserHabitReply
	(*v12.ListUserHabitSnapshotReply)(nil),          // 116: user_habit_v1.ListUserHabitSnapshotReply
	(*v12.CreateUserHabitMemoReply)(nil),            // 117: user_habit_v1.CreateUserHabitMemoReply
	(*v12.UpdateUserHabitMemoReply)(nil),            // 118: user_habit_v1.UpdateUserHabitMemoReply
	(*v12.DeleteUserHabitMemoReply)(nil),            // 119: user_habit_v1.DeleteUserHabitMemoReply
	(*v12.PunchUserHabitReply)(nil),                 // 120: user_habit_v1.PunchUserHabitReply
	(*v12.CancelPunchUserHabitReply)(nil),           // 121: user_habit_v1.CancelPunchUserHabitReply
	(*v12.UpdatePunchUserHabitReply)(nil),           // 122: user_habit_v1.UpdatePunchUserHabitReply
	(*v12.ReckonUserHabitReply)(nil),                // 123: user_habit_v1.ReckonUserHabitReply
	(*v12.CancelReckonUserHabitReply)(nil),          // 124: user_habit_v1.CancelReckonUserHabitReply
	(*v12.CreateUserHabitReckonReply)(nil),          // 125: user_habit_v1.CreateUserHabitReckonReply
	(*v12.UpdateUserHabitReckonReply)(nil),          // 126: user_habit_v1.UpdateUserHabitReckonReply
	(*v12.DeleteUserHabitReckonReply)(nil),          // 127: user_habit_v1.DeleteUserHabitReckonReply
	(*v13.CreatePlanetReply)(nil),                   // 128: planet_v1.CreatePlanetReply
	(*v13.GetPlanetReply)(nil),                      // 129: planet_v1.GetPlanetReply
	(*v13.UpdatePlanetReply)(nil),                   // 130: planet_v1.UpdatePlanetReply
	(*v13.DeletePlanetReply)(nil),                   // 131: planet_v1.DeletePlanetReply
	(*v13.CreatePlanetTargetReply)(nil),             // 132: planet_v1.CreatePlanetTargetReply
	(*v13.GetPlanetTargetReply)(nil),                // 133: planet_v1.GetPlanetTargetReply
	(*v13.UpdatePlanetTargetReply)(nil),             // 134: planet_v1.UpdatePlanetTargetReply
	(*v13.DeletePlanetTargetReply)(nil),             // 135: planet_v1.DeletePlanetTargetReply
	(*v13.ListPlanetTargetReply)(nil),               // 136: planet_v1.ListPlanetTargetReply
	(*v14.ListPlanetByUserIDReply)(nil),             // 137: user_planet_v1.ListPlanetByUserIDReply
	(*v14.JoinPlanetReply)(nil),                     // 138: user_planet_v1.JoinPlanetReply
	(*v14.QuitPlanetReply)(nil),                     // 139: user_planet_v1.QuitPlanetReply
	(*v14.JoinPlanetTargetReply)(nil),               // 140: user_planet_v1.JoinPlanetTargetReply
	(*v14.QuitPlanetTargetReply)(nil),               // 141: user_planet_v1.QuitPlanetTargetReply
	(*v14.LikePlanetPostReply)(nil),                 // 142: user_planet_v1.LikePlanetPostReply
	(*v14.CancelLikePlanetPostReply)(nil),           // 143: user_planet_v1.CancelLikePlanetPostReply
	(*v14.FavoritePlanetPostReply)(nil),             // 144: user_planet_v1.FavoritePlanetPostReply
	(*v14.CancelFavoritePlanetPostReply)(nil),       // 145: user_planet_v1.CancelFavoritePlanetPostReply
	(*v14.CreatePlanetPostReply)(nil),               // 146: user_planet_v1.CreatePlanetPostReply
	(*v14.UpdatePlanetPostReply)(nil),               // 147: user_planet_v1.UpdatePlanetPostReply
	(*v14.DeletePlanetPostReply)(nil),               // 148: user_planet_v1.DeletePlanetPostReply
	(*v14.ToppedPlanetPostReply)(nil),               // 149: user_planet_v1.ToppedPlanetPostReply
	(*v14.ListPlanetPostReply)(nil),                 // 150: user_planet_v1.ListPlanetPostReply
	(*v14.ListPlanetTopPostReply)(nil),              // 151: user_planet_v1.ListPlanetTopPostReply
	(*v14.CreatePlanetPostCommentReply)(nil),        // 152: user_planet_v1.CreatePlanetPostCommentReply
	(*v14.DeletePlanetPostCommentReply)(nil),        // 153: user_planet_v1.DeletePlanetPostCommentReply
	(*v14.ListPlanetPostCommentReply)(nil),          // 154: user_planet_v1.ListPlanetPostCommentReply
	(*v12.RunCronTaskReply)(nil),                    // 155: user_habit_v1.RunCronTaskReply
	(*v15.TodayStatisticFromHomeReply)(nil),         // 156: api.user_statistic_v1.TodayStatisticFromHomeReply
	(*v15.UserHabitStatisticFromDetailReply)(nil),   // 157: api.user_statistic_v1.UserHabitStatisticFromDetailReply
	(*v16.UnreadCountReply)(nil),                    // 158: user_message_v1.UnreadCountReply
}
var file_api_proto_depIdxs = []int32{
	0,   // 0: api.LifeLog.Register:input_type -> user_v1.RegisterRequest
	1,   // 1: api.LifeLog.Login:input_type -> user_v1.LoginRequest
	2,   // 2: api.LifeLog.Logout:input_type -> user_v1.LogoutRequest
	3,   // 3: api.LifeLog.PushToken:input_type -> user_v1.PushTokenRequest
	4,   // 4: api.LifeLog.ForgetPassword:input_type -> user_v1.ForgetPasswordRequest
	5,   // 5: api.LifeLog.ChangePassword:input_type -> user_v1.ChangePasswordRequest
	6,   // 6: api.LifeLog.DeleteUser:input_type -> user_v1.DeleteUserRequest
	7,   // 7: api.LifeLog.ChangePhone:input_type -> user_v1.ChangePhoneRequest
	8,   // 8: api.LifeLog.ChangeEmail:input_type -> user_v1.ChangeEmailRequest
	9,   // 9: api.LifeLog.GetUserProfile:input_type -> user_v1.GetUserProfileRequest
	10,  // 10: api.LifeLog.UpdateUserProfile:input_type -> user_v1.UpdateUserProfileRequest
	11,  // 11: api.LifeLog.FollowUser:input_type -> user_v1.FollowUserRequest
	12,  // 12: api.LifeLog.UnfollowUser:input_type -> user_v1.UnfollowUserRequest
	13,  // 13: api.LifeLog.RefreshToken:input_type -> user_v1.RefreshTokenRequest
	14,  // 14: api.LifeLog.GetUserSetting:input_type -> user_v1.GetUserSettingRequest
	15,  // 15: api.LifeLog.UpdateUserAward:input_type -> user_v1.UpdateUserAwardRequest
	16,  // 16: api.LifeLog.ChangeUserPrivacyPassword:input_type -> user_v1.ChangeUserPrivacyPasswordRequest
	17,  // 17: api.LifeLog.CheckUserPrivacyPassword:input_type -> user_v1.CheckUserPrivacyPasswordRequest
	18,  // 18: api.LifeLog.GetUserVipInfo:input_type -> user_v1.GetUserVipInfoRequest
	19,  // 19: api.LifeLog.BuddySearch:input_type -> user_v1.BuddySearchRequest
	20,  // 20: api.LifeLog.BuddyInvitation:input_type -> user_v1.BuddyInvitationRequest
	21,  // 21: api.LifeLog.GetSiteInfo:input_type -> site_v1.GetSiteInfoRequest
	22,  // 22: api.LifeLog.HealthCheck:input_type -> site_v1.HealthCheckRequest
	23,  // 23: api.LifeLog.CreateUpToken:input_type -> site_v1.CreateUpTokenRequest
	24,  // 24: api.LifeLog.CreateDownURL:input_type -> site_v1.CreateDownURLRequest
	25,  // 25: api.LifeLog.SendVerifyCode:input_type -> site_v1.SendVerifyCodeRequest
	26,  // 26: api.LifeLog.ListMotiveMemo:input_type -> site_v1.ListMotiveMemoRequest
	27,  // 27: api.LifeLog.CreateFeedback:input_type -> site_v1.CreateFeedbackRequest
	28,  // 28: api.LifeLog.VersionCheck:input_type -> site_v1.VersionCheckRequest
	29,  // 29: api.LifeLog.CreateUserHabit:input_type -> user_habit_v1.CreateUserHabitRequest
	30,  // 30: api.LifeLog.ListUserHabit:input_type -> user_habit_v1.ListUserHabitRequest
	31,  // 31: api.LifeLog.GetUserHabit:input_type -> user_habit_v1.GetUserHabitRequest
	32,  // 32: api.LifeLog.UpdateUserHabit:input_type -> user_habit_v1.UpdateUserHabitRequest
	33,  // 33: api.LifeLog.DeleteUserHabit:input_type -> user_habit_v1.DeleteUserHabitRequest
	34,  // 34: api.LifeLog.PauseUserHabit:input_type -> user_habit_v1.PauseUserHabitRequest
	35,  // 35: api.LifeLog.RecoverUserHabit:input_type -> user_habit_v1.RecoverUserHabitRequest
	36,  // 36: api.LifeLog.ArchiveUserHabit:input_type -> user_habit_v1.ArchiveUserHabitRequest
	37,  // 37: api.LifeLog.ListUserHabitSnapshot:input_type -> user_habit_v1.ListUserHabitSnapshotRequest
	38,  // 38: api.LifeLog.CreateUserHabitMemo:input_type -> user_habit_v1.CreateUserHabitMemoRequest
	39,  // 39: api.LifeLog.UpdateUserHabitMemo:input_type -> user_habit_v1.UpdateUserHabitMemoRequest
	40,  // 40: api.LifeLog.DeleteUserHabitMemo:input_type -> user_habit_v1.DeleteUserHabitMemoRequest
	41,  // 41: api.LifeLog.PunchUserHabit:input_type -> user_habit_v1.PunchUserHabitRequest
	42,  // 42: api.LifeLog.CancelPunchUserHabit:input_type -> user_habit_v1.CancelPunchUserHabitRequest
	43,  // 43: api.LifeLog.UpdatePunchUserHabit:input_type -> user_habit_v1.UpdatePunchUserHabitRequest
	44,  // 44: api.LifeLog.ReckonUserHabit:input_type -> user_habit_v1.ReckonUserHabitRequest
	45,  // 45: api.LifeLog.CancelReckonUserHabit:input_type -> user_habit_v1.CancelReckonUserHabitRequest
	46,  // 46: api.LifeLog.CreateUserHabitReckon:input_type -> user_habit_v1.CreateUserHabitReckonRequest
	47,  // 47: api.LifeLog.UpdateUserHabitReckon:input_type -> user_habit_v1.UpdateUserHabitReckonRequest
	48,  // 48: api.LifeLog.DeleteUserHabitReckon:input_type -> user_habit_v1.DeleteUserHabitReckonRequest
	49,  // 49: api.LifeLog.CreatePlanet:input_type -> planet_v1.CreatePlanetRequest
	50,  // 50: api.LifeLog.GetPlanet:input_type -> planet_v1.GetPlanetRequest
	51,  // 51: api.LifeLog.UpdatePlanet:input_type -> planet_v1.UpdatePlanetRequest
	52,  // 52: api.LifeLog.DeletePlanet:input_type -> planet_v1.DeletePlanetRequest
	53,  // 53: api.LifeLog.CreatePlanetTarget:input_type -> planet_v1.CreatePlanetTargetRequest
	54,  // 54: api.LifeLog.GetPlanetTarget:input_type -> planet_v1.GetPlanetTargetRequest
	55,  // 55: api.LifeLog.UpdatePlanetTarget:input_type -> planet_v1.UpdatePlanetTargetRequest
	56,  // 56: api.LifeLog.DeletePlanetTarget:input_type -> planet_v1.DeletePlanetTargetRequest
	57,  // 57: api.LifeLog.ListPlanetTarget:input_type -> planet_v1.ListPlanetTargetRequest
	58,  // 58: api.LifeLog.ListPlanetByUserID:input_type -> user_planet_v1.ListPlanetByUserIDRequest
	59,  // 59: api.LifeLog.JoinPlanet:input_type -> user_planet_v1.JoinPlanetRequest
	60,  // 60: api.LifeLog.QuitPlanet:input_type -> user_planet_v1.QuitPlanetRequest
	61,  // 61: api.LifeLog.JoinPlanetTarget:input_type -> user_planet_v1.JoinPlanetTargetRequest
	62,  // 62: api.LifeLog.QuitPlanetTarget:input_type -> user_planet_v1.QuitPlanetTargetRequest
	63,  // 63: api.LifeLog.LikePlanetPost:input_type -> user_planet_v1.LikePlanetPostRequest
	64,  // 64: api.LifeLog.CancelLikePlanetPost:input_type -> user_planet_v1.CancelLikePlanetPostRequest
	65,  // 65: api.LifeLog.FavoritePlanetPost:input_type -> user_planet_v1.FavoritePlanetPostRequest
	66,  // 66: api.LifeLog.CancelFavoritePlanetPost:input_type -> user_planet_v1.CancelFavoritePlanetPostRequest
	67,  // 67: api.LifeLog.CreatePlanetPost:input_type -> user_planet_v1.CreatePlanetPostRequest
	68,  // 68: api.LifeLog.UpdatePlanetPost:input_type -> user_planet_v1.UpdatePlanetPostRequest
	69,  // 69: api.LifeLog.DeletePlanetPost:input_type -> user_planet_v1.DeletePlanetPostRequest
	70,  // 70: api.LifeLog.ToppedPlanetPost:input_type -> user_planet_v1.ToppedPlanetPostRequest
	71,  // 71: api.LifeLog.ListPlanetPost:input_type -> user_planet_v1.ListPlanetPostRequest
	72,  // 72: api.LifeLog.ListPlanetTopPost:input_type -> user_planet_v1.ListPlanetTopPostRequest
	73,  // 73: api.LifeLog.CreatePlanetPostComment:input_type -> user_planet_v1.CreatePlanetPostCommentRequest
	74,  // 74: api.LifeLog.DeletePlanetPostComment:input_type -> user_planet_v1.DeletePlanetPostCommentRequest
	75,  // 75: api.LifeLog.ListPlanetPostComment:input_type -> user_planet_v1.ListPlanetPostCommentRequest
	76,  // 76: api.LifeLog.RunCronTask:input_type -> user_habit_v1.RunCronTaskRequest
	77,  // 77: api.LifeLog.TodayStatisticFromHome:input_type -> api.user_statistic_v1.TodayStatisticFromHomeRequest
	78,  // 78: api.LifeLog.UserHabitStatisticFromDetail:input_type -> api.user_statistic_v1.UserHabitStatisticFromDetailRequest
	79,  // 79: api.LifeLog.UnreadCount:input_type -> user_message_v1.UnreadCountRequest
	80,  // 80: api.LifeLog.Register:output_type -> user_v1.RegisterReply
	81,  // 81: api.LifeLog.Login:output_type -> user_v1.LoginReply
	82,  // 82: api.LifeLog.Logout:output_type -> user_v1.LogoutReply
	83,  // 83: api.LifeLog.PushToken:output_type -> user_v1.PushTokenReply
	84,  // 84: api.LifeLog.ForgetPassword:output_type -> user_v1.ForgetPasswordReply
	85,  // 85: api.LifeLog.ChangePassword:output_type -> user_v1.ChangePasswordReply
	86,  // 86: api.LifeLog.DeleteUser:output_type -> user_v1.DeleteUserReply
	87,  // 87: api.LifeLog.ChangePhone:output_type -> user_v1.ChangePhoneReply
	88,  // 88: api.LifeLog.ChangeEmail:output_type -> user_v1.ChangeEmailReply
	89,  // 89: api.LifeLog.GetUserProfile:output_type -> user_v1.GetUserProfileReply
	90,  // 90: api.LifeLog.UpdateUserProfile:output_type -> user_v1.UpdateUserProfileReply
	91,  // 91: api.LifeLog.FollowUser:output_type -> user_v1.FollowUserReply
	92,  // 92: api.LifeLog.UnfollowUser:output_type -> user_v1.UnfollowUserReply
	81,  // 93: api.LifeLog.RefreshToken:output_type -> user_v1.LoginReply
	93,  // 94: api.LifeLog.GetUserSetting:output_type -> user_v1.GetUserSettingReply
	94,  // 95: api.LifeLog.UpdateUserAward:output_type -> user_v1.UpdateUserAwardReply
	95,  // 96: api.LifeLog.ChangeUserPrivacyPassword:output_type -> user_v1.ChangeUserPrivacyPasswordReply
	96,  // 97: api.LifeLog.CheckUserPrivacyPassword:output_type -> user_v1.CheckUserPrivacyPasswordReply
	97,  // 98: api.LifeLog.GetUserVipInfo:output_type -> user_v1.GetUserVipInfoReply
	98,  // 99: api.LifeLog.BuddySearch:output_type -> user_v1.BuddySearchReply
	99,  // 100: api.LifeLog.BuddyInvitation:output_type -> user_v1.BuddyInvitationReply
	100, // 101: api.LifeLog.GetSiteInfo:output_type -> site_v1.GetSiteInfoReply
	101, // 102: api.LifeLog.HealthCheck:output_type -> site_v1.HealthCheckReply
	102, // 103: api.LifeLog.CreateUpToken:output_type -> site_v1.CreateUpTokenReply
	103, // 104: api.LifeLog.CreateDownURL:output_type -> site_v1.CreateDownURLReply
	104, // 105: api.LifeLog.SendVerifyCode:output_type -> site_v1.SendVerifyCodeReply
	105, // 106: api.LifeLog.ListMotiveMemo:output_type -> site_v1.ListMotiveMemoReply
	106, // 107: api.LifeLog.CreateFeedback:output_type -> site_v1.CreateFeedbackReply
	107, // 108: api.LifeLog.VersionCheck:output_type -> site_v1.VersionCheckReply
	108, // 109: api.LifeLog.CreateUserHabit:output_type -> user_habit_v1.CreateUserHabitReply
	109, // 110: api.LifeLog.ListUserHabit:output_type -> user_habit_v1.ListUserHabitReply
	110, // 111: api.LifeLog.GetUserHabit:output_type -> user_habit_v1.GetUserHabitReply
	111, // 112: api.LifeLog.UpdateUserHabit:output_type -> user_habit_v1.UpdateUserHabitReply
	112, // 113: api.LifeLog.DeleteUserHabit:output_type -> user_habit_v1.DeleteUserHabitReply
	113, // 114: api.LifeLog.PauseUserHabit:output_type -> user_habit_v1.PauseUserHabitReply
	114, // 115: api.LifeLog.RecoverUserHabit:output_type -> user_habit_v1.RecoverUserHabitReply
	115, // 116: api.LifeLog.ArchiveUserHabit:output_type -> user_habit_v1.ArchiveUserHabitReply
	116, // 117: api.LifeLog.ListUserHabitSnapshot:output_type -> user_habit_v1.ListUserHabitSnapshotReply
	117, // 118: api.LifeLog.CreateUserHabitMemo:output_type -> user_habit_v1.CreateUserHabitMemoReply
	118, // 119: api.LifeLog.UpdateUserHabitMemo:output_type -> user_habit_v1.UpdateUserHabitMemoReply
	119, // 120: api.LifeLog.DeleteUserHabitMemo:output_type -> user_habit_v1.DeleteUserHabitMemoReply
	120, // 121: api.LifeLog.PunchUserHabit:output_type -> user_habit_v1.PunchUserHabitReply
	121, // 122: api.LifeLog.CancelPunchUserHabit:output_type -> user_habit_v1.CancelPunchUserHabitReply
	122, // 123: api.LifeLog.UpdatePunchUserHabit:output_type -> user_habit_v1.UpdatePunchUserHabitReply
	123, // 124: api.LifeLog.ReckonUserHabit:output_type -> user_habit_v1.ReckonUserHabitReply
	124, // 125: api.LifeLog.CancelReckonUserHabit:output_type -> user_habit_v1.CancelReckonUserHabitReply
	125, // 126: api.LifeLog.CreateUserHabitReckon:output_type -> user_habit_v1.CreateUserHabitReckonReply
	126, // 127: api.LifeLog.UpdateUserHabitReckon:output_type -> user_habit_v1.UpdateUserHabitReckonReply
	127, // 128: api.LifeLog.DeleteUserHabitReckon:output_type -> user_habit_v1.DeleteUserHabitReckonReply
	128, // 129: api.LifeLog.CreatePlanet:output_type -> planet_v1.CreatePlanetReply
	129, // 130: api.LifeLog.GetPlanet:output_type -> planet_v1.GetPlanetReply
	130, // 131: api.LifeLog.UpdatePlanet:output_type -> planet_v1.UpdatePlanetReply
	131, // 132: api.LifeLog.DeletePlanet:output_type -> planet_v1.DeletePlanetReply
	132, // 133: api.LifeLog.CreatePlanetTarget:output_type -> planet_v1.CreatePlanetTargetReply
	133, // 134: api.LifeLog.GetPlanetTarget:output_type -> planet_v1.GetPlanetTargetReply
	134, // 135: api.LifeLog.UpdatePlanetTarget:output_type -> planet_v1.UpdatePlanetTargetReply
	135, // 136: api.LifeLog.DeletePlanetTarget:output_type -> planet_v1.DeletePlanetTargetReply
	136, // 137: api.LifeLog.ListPlanetTarget:output_type -> planet_v1.ListPlanetTargetReply
	137, // 138: api.LifeLog.ListPlanetByUserID:output_type -> user_planet_v1.ListPlanetByUserIDReply
	138, // 139: api.LifeLog.JoinPlanet:output_type -> user_planet_v1.JoinPlanetReply
	139, // 140: api.LifeLog.QuitPlanet:output_type -> user_planet_v1.QuitPlanetReply
	140, // 141: api.LifeLog.JoinPlanetTarget:output_type -> user_planet_v1.JoinPlanetTargetReply
	141, // 142: api.LifeLog.QuitPlanetTarget:output_type -> user_planet_v1.QuitPlanetTargetReply
	142, // 143: api.LifeLog.LikePlanetPost:output_type -> user_planet_v1.LikePlanetPostReply
	143, // 144: api.LifeLog.CancelLikePlanetPost:output_type -> user_planet_v1.CancelLikePlanetPostReply
	144, // 145: api.LifeLog.FavoritePlanetPost:output_type -> user_planet_v1.FavoritePlanetPostReply
	145, // 146: api.LifeLog.CancelFavoritePlanetPost:output_type -> user_planet_v1.CancelFavoritePlanetPostReply
	146, // 147: api.LifeLog.CreatePlanetPost:output_type -> user_planet_v1.CreatePlanetPostReply
	147, // 148: api.LifeLog.UpdatePlanetPost:output_type -> user_planet_v1.UpdatePlanetPostReply
	148, // 149: api.LifeLog.DeletePlanetPost:output_type -> user_planet_v1.DeletePlanetPostReply
	149, // 150: api.LifeLog.ToppedPlanetPost:output_type -> user_planet_v1.ToppedPlanetPostReply
	150, // 151: api.LifeLog.ListPlanetPost:output_type -> user_planet_v1.ListPlanetPostReply
	151, // 152: api.LifeLog.ListPlanetTopPost:output_type -> user_planet_v1.ListPlanetTopPostReply
	152, // 153: api.LifeLog.CreatePlanetPostComment:output_type -> user_planet_v1.CreatePlanetPostCommentReply
	153, // 154: api.LifeLog.DeletePlanetPostComment:output_type -> user_planet_v1.DeletePlanetPostCommentReply
	154, // 155: api.LifeLog.ListPlanetPostComment:output_type -> user_planet_v1.ListPlanetPostCommentReply
	155, // 156: api.LifeLog.RunCronTask:output_type -> user_habit_v1.RunCronTaskReply
	156, // 157: api.LifeLog.TodayStatisticFromHome:output_type -> api.user_statistic_v1.TodayStatisticFromHomeReply
	157, // 158: api.LifeLog.UserHabitStatisticFromDetail:output_type -> api.user_statistic_v1.UserHabitStatisticFromDetailReply
	158, // 159: api.LifeLog.UnreadCount:output_type -> user_message_v1.UnreadCountReply
	80,  // [80:160] is the sub-list for method output_type
	0,   // [0:80] is the sub-list for method input_type
	0,   // [0:0] is the sub-list for extension type_name
	0,   // [0:0] is the sub-list for extension extendee
	0,   // [0:0] is the sub-list for field type_name
}

func init() { file_api_proto_init() }
func file_api_proto_init() {
	if File_api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_proto_rawDesc), len(file_api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_proto_goTypes,
		DependencyIndexes: file_api_proto_depIdxs,
	}.Build()
	File_api_proto = out.File
	file_api_proto_goTypes = nil
	file_api_proto_depIdxs = nil
}
