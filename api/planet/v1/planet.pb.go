// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: planet/v1/planet.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreatePlanetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ImgUrl        string                 `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePlanetRequest) Reset() {
	*x = CreatePlanetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePlanetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlanetRequest) ProtoMessage() {}

func (x *CreatePlanetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlanetRequest.ProtoReflect.Descriptor instead.
func (*CreatePlanetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePlanetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreatePlanetRequest) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

func (x *CreatePlanetRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreatePlanetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePlanetReply) Reset() {
	*x = CreatePlanetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePlanetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlanetReply) ProtoMessage() {}

func (x *CreatePlanetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlanetReply.ProtoReflect.Descriptor instead.
func (*CreatePlanetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{1}
}

func (x *CreatePlanetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreatePlanetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdatePlanetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ImgUrl        string                 `protobuf:"bytes,3,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePlanetRequest) Reset() {
	*x = UpdatePlanetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePlanetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlanetRequest) ProtoMessage() {}

func (x *UpdatePlanetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlanetRequest.ProtoReflect.Descriptor instead.
func (*UpdatePlanetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{2}
}

func (x *UpdatePlanetRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePlanetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdatePlanetRequest) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

func (x *UpdatePlanetRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type UpdatePlanetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePlanetReply) Reset() {
	*x = UpdatePlanetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePlanetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlanetReply) ProtoMessage() {}

func (x *UpdatePlanetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlanetReply.ProtoReflect.Descriptor instead.
func (*UpdatePlanetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{3}
}

func (x *UpdatePlanetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdatePlanetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeletePlanetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanetRequest) Reset() {
	*x = DeletePlanetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanetRequest) ProtoMessage() {}

func (x *DeletePlanetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanetRequest.ProtoReflect.Descriptor instead.
func (*DeletePlanetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{4}
}

func (x *DeletePlanetRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeletePlanetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanetReply) Reset() {
	*x = DeletePlanetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanetReply) ProtoMessage() {}

func (x *DeletePlanetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanetReply.ProtoReflect.Descriptor instead.
func (*DeletePlanetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{5}
}

func (x *DeletePlanetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeletePlanetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetPlanetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPlanetRequest) Reset() {
	*x = GetPlanetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlanetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlanetRequest) ProtoMessage() {}

func (x *GetPlanetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlanetRequest.ProtoReflect.Descriptor instead.
func (*GetPlanetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{6}
}

func (x *GetPlanetRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetPlanetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetPlanetReply_Data   `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPlanetReply) Reset() {
	*x = GetPlanetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlanetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlanetReply) ProtoMessage() {}

func (x *GetPlanetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlanetReply.ProtoReflect.Descriptor instead.
func (*GetPlanetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{7}
}

func (x *GetPlanetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPlanetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetPlanetReply) GetData() *GetPlanetReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type CreatePlanetTargetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePlanetTargetRequest) Reset() {
	*x = CreatePlanetTargetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePlanetTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlanetTargetRequest) ProtoMessage() {}

func (x *CreatePlanetTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlanetTargetRequest.ProtoReflect.Descriptor instead.
func (*CreatePlanetTargetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{8}
}

func (x *CreatePlanetTargetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

func (x *CreatePlanetTargetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CreatePlanetTargetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePlanetTargetReply) Reset() {
	*x = CreatePlanetTargetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePlanetTargetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlanetTargetReply) ProtoMessage() {}

func (x *CreatePlanetTargetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlanetTargetReply.ProtoReflect.Descriptor instead.
func (*CreatePlanetTargetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{9}
}

func (x *CreatePlanetTargetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreatePlanetTargetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdatePlanetTargetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PlanetId      int32                  `protobuf:"varint,3,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePlanetTargetRequest) Reset() {
	*x = UpdatePlanetTargetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePlanetTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlanetTargetRequest) ProtoMessage() {}

func (x *UpdatePlanetTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlanetTargetRequest.ProtoReflect.Descriptor instead.
func (*UpdatePlanetTargetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{10}
}

func (x *UpdatePlanetTargetRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePlanetTargetRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdatePlanetTargetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type UpdatePlanetTargetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePlanetTargetReply) Reset() {
	*x = UpdatePlanetTargetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePlanetTargetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlanetTargetReply) ProtoMessage() {}

func (x *UpdatePlanetTargetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlanetTargetReply.ProtoReflect.Descriptor instead.
func (*UpdatePlanetTargetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{11}
}

func (x *UpdatePlanetTargetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdatePlanetTargetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeletePlanetTargetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PlanetId      int32                  `protobuf:"varint,2,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanetTargetRequest) Reset() {
	*x = DeletePlanetTargetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanetTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanetTargetRequest) ProtoMessage() {}

func (x *DeletePlanetTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanetTargetRequest.ProtoReflect.Descriptor instead.
func (*DeletePlanetTargetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{12}
}

func (x *DeletePlanetTargetRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeletePlanetTargetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type DeletePlanetTargetReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanetTargetReply) Reset() {
	*x = DeletePlanetTargetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanetTargetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanetTargetReply) ProtoMessage() {}

func (x *DeletePlanetTargetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanetTargetReply.ProtoReflect.Descriptor instead.
func (*DeletePlanetTargetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{13}
}

func (x *DeletePlanetTargetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeletePlanetTargetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ListPlanetTargetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanetId      int32                  `protobuf:"varint,1,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetTargetRequest) Reset() {
	*x = ListPlanetTargetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetTargetRequest) ProtoMessage() {}

func (x *ListPlanetTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetTargetRequest.ProtoReflect.Descriptor instead.
func (*ListPlanetTargetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{14}
}

func (x *ListPlanetTargetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type ListPlanetTargetReply struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Code          int32                         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          []*ListPlanetTargetReply_Data `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetTargetReply) Reset() {
	*x = ListPlanetTargetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetTargetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetTargetReply) ProtoMessage() {}

func (x *ListPlanetTargetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetTargetReply.ProtoReflect.Descriptor instead.
func (*ListPlanetTargetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{15}
}

func (x *ListPlanetTargetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListPlanetTargetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListPlanetTargetReply) GetData() []*ListPlanetTargetReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetPlanetTargetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	PlanetId      int32                  `protobuf:"varint,2,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPlanetTargetRequest) Reset() {
	*x = GetPlanetTargetRequest{}
	mi := &file_planet_v1_planet_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlanetTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlanetTargetRequest) ProtoMessage() {}

func (x *GetPlanetTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlanetTargetRequest.ProtoReflect.Descriptor instead.
func (*GetPlanetTargetRequest) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{16}
}

func (x *GetPlanetTargetRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPlanetTargetRequest) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type GetPlanetTargetReply struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetPlanetTargetReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPlanetTargetReply) Reset() {
	*x = GetPlanetTargetReply{}
	mi := &file_planet_v1_planet_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlanetTargetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlanetTargetReply) ProtoMessage() {}

func (x *GetPlanetTargetReply) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlanetTargetReply.ProtoReflect.Descriptor instead.
func (*GetPlanetTargetReply) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{17}
}

func (x *GetPlanetTargetReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPlanetTargetReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetPlanetTargetReply) GetData() *GetPlanetTargetReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetPlanetReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ImgUrl        string                 `protobuf:"bytes,3,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPlanetReply_Data) Reset() {
	*x = GetPlanetReply_Data{}
	mi := &file_planet_v1_planet_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlanetReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlanetReply_Data) ProtoMessage() {}

func (x *GetPlanetReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlanetReply_Data.ProtoReflect.Descriptor instead.
func (*GetPlanetReply_Data) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetPlanetReply_Data) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPlanetReply_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPlanetReply_Data) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

func (x *GetPlanetReply_Data) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type ListPlanetTargetReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PlanetId      int32                  `protobuf:"varint,3,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlanetTargetReply_Data) Reset() {
	*x = ListPlanetTargetReply_Data{}
	mi := &file_planet_v1_planet_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlanetTargetReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlanetTargetReply_Data) ProtoMessage() {}

func (x *ListPlanetTargetReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlanetTargetReply_Data.ProtoReflect.Descriptor instead.
func (*ListPlanetTargetReply_Data) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{15, 0}
}

func (x *ListPlanetTargetReply_Data) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListPlanetTargetReply_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListPlanetTargetReply_Data) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

type GetPlanetTargetReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PlanetId      int32                  `protobuf:"varint,3,opt,name=planet_id,json=planetId,proto3" json:"planet_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPlanetTargetReply_Data) Reset() {
	*x = GetPlanetTargetReply_Data{}
	mi := &file_planet_v1_planet_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlanetTargetReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlanetTargetReply_Data) ProtoMessage() {}

func (x *GetPlanetTargetReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_planet_v1_planet_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlanetTargetReply_Data.ProtoReflect.Descriptor instead.
func (*GetPlanetTargetReply_Data) Descriptor() ([]byte, []int) {
	return file_planet_v1_planet_proto_rawDescGZIP(), []int{17, 0}
}

func (x *GetPlanetTargetReply_Data) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetPlanetTargetReply_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetPlanetTargetReply_Data) GetPlanetId() int32 {
	if x != nil {
		return x.PlanetId
	}
	return 0
}

var File_planet_v1_planet_proto protoreflect.FileDescriptor

const file_planet_v1_planet_proto_rawDesc = "" +
	"\n" +
	"\x16planet/v1/planet.proto\x12\tplanet_v1\"d\n" +
	"\x13CreatePlanetRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x17\n" +
	"\aimg_url\x18\x02 \x01(\tR\x06imgUrl\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\"9\n" +
	"\x11CreatePlanetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"t\n" +
	"\x13UpdatePlanetRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x17\n" +
	"\aimg_url\x18\x03 \x01(\tR\x06imgUrl\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"9\n" +
	"\x11UpdatePlanetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"%\n" +
	"\x13DeletePlanetRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"9\n" +
	"\x11DeletePlanetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\"\n" +
	"\x10GetPlanetRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"\xd1\x01\n" +
	"\x0eGetPlanetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x122\n" +
	"\x04data\x18\x03 \x01(\v2\x1e.planet_v1.GetPlanetReply.DataR\x04data\x1ae\n" +
	"\x04Data\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x17\n" +
	"\aimg_url\x18\x03 \x01(\tR\x06imgUrl\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\"L\n" +
	"\x19CreatePlanetTargetRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"?\n" +
	"\x17CreatePlanetTargetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\\\n" +
	"\x19UpdatePlanetTargetRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tplanet_id\x18\x03 \x01(\x05R\bplanetId\"?\n" +
	"\x17UpdatePlanetTargetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"H\n" +
	"\x19DeletePlanetTargetRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1b\n" +
	"\tplanet_id\x18\x02 \x01(\x05R\bplanetId\"?\n" +
	"\x17DeletePlanetTargetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"6\n" +
	"\x17ListPlanetTargetRequest\x12\x1b\n" +
	"\tplanet_id\x18\x01 \x01(\x05R\bplanetId\"\xc1\x01\n" +
	"\x15ListPlanetTargetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x129\n" +
	"\x04data\x18\x03 \x03(\v2%.planet_v1.ListPlanetTargetReply.DataR\x04data\x1aG\n" +
	"\x04Data\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tplanet_id\x18\x03 \x01(\x05R\bplanetId\"E\n" +
	"\x16GetPlanetTargetRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1b\n" +
	"\tplanet_id\x18\x02 \x01(\x05R\bplanetId\"\xbf\x01\n" +
	"\x14GetPlanetTargetReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x128\n" +
	"\x04data\x18\x03 \x01(\v2$.planet_v1.GetPlanetTargetReply.DataR\x04data\x1aG\n" +
	"\x04Data\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tplanet_id\x18\x03 \x01(\x05R\bplanetIdB/Z-github.com/wlnil/life-log-be/api/planet/v1;v1b\x06proto3"

var (
	file_planet_v1_planet_proto_rawDescOnce sync.Once
	file_planet_v1_planet_proto_rawDescData []byte
)

func file_planet_v1_planet_proto_rawDescGZIP() []byte {
	file_planet_v1_planet_proto_rawDescOnce.Do(func() {
		file_planet_v1_planet_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_planet_v1_planet_proto_rawDesc), len(file_planet_v1_planet_proto_rawDesc)))
	})
	return file_planet_v1_planet_proto_rawDescData
}

var file_planet_v1_planet_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_planet_v1_planet_proto_goTypes = []any{
	(*CreatePlanetRequest)(nil),        // 0: planet_v1.CreatePlanetRequest
	(*CreatePlanetReply)(nil),          // 1: planet_v1.CreatePlanetReply
	(*UpdatePlanetRequest)(nil),        // 2: planet_v1.UpdatePlanetRequest
	(*UpdatePlanetReply)(nil),          // 3: planet_v1.UpdatePlanetReply
	(*DeletePlanetRequest)(nil),        // 4: planet_v1.DeletePlanetRequest
	(*DeletePlanetReply)(nil),          // 5: planet_v1.DeletePlanetReply
	(*GetPlanetRequest)(nil),           // 6: planet_v1.GetPlanetRequest
	(*GetPlanetReply)(nil),             // 7: planet_v1.GetPlanetReply
	(*CreatePlanetTargetRequest)(nil),  // 8: planet_v1.CreatePlanetTargetRequest
	(*CreatePlanetTargetReply)(nil),    // 9: planet_v1.CreatePlanetTargetReply
	(*UpdatePlanetTargetRequest)(nil),  // 10: planet_v1.UpdatePlanetTargetRequest
	(*UpdatePlanetTargetReply)(nil),    // 11: planet_v1.UpdatePlanetTargetReply
	(*DeletePlanetTargetRequest)(nil),  // 12: planet_v1.DeletePlanetTargetRequest
	(*DeletePlanetTargetReply)(nil),    // 13: planet_v1.DeletePlanetTargetReply
	(*ListPlanetTargetRequest)(nil),    // 14: planet_v1.ListPlanetTargetRequest
	(*ListPlanetTargetReply)(nil),      // 15: planet_v1.ListPlanetTargetReply
	(*GetPlanetTargetRequest)(nil),     // 16: planet_v1.GetPlanetTargetRequest
	(*GetPlanetTargetReply)(nil),       // 17: planet_v1.GetPlanetTargetReply
	(*GetPlanetReply_Data)(nil),        // 18: planet_v1.GetPlanetReply.Data
	(*ListPlanetTargetReply_Data)(nil), // 19: planet_v1.ListPlanetTargetReply.Data
	(*GetPlanetTargetReply_Data)(nil),  // 20: planet_v1.GetPlanetTargetReply.Data
}
var file_planet_v1_planet_proto_depIdxs = []int32{
	18, // 0: planet_v1.GetPlanetReply.data:type_name -> planet_v1.GetPlanetReply.Data
	19, // 1: planet_v1.ListPlanetTargetReply.data:type_name -> planet_v1.ListPlanetTargetReply.Data
	20, // 2: planet_v1.GetPlanetTargetReply.data:type_name -> planet_v1.GetPlanetTargetReply.Data
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_planet_v1_planet_proto_init() }
func file_planet_v1_planet_proto_init() {
	if File_planet_v1_planet_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_planet_v1_planet_proto_rawDesc), len(file_planet_v1_planet_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_planet_v1_planet_proto_goTypes,
		DependencyIndexes: file_planet_v1_planet_proto_depIdxs,
		MessageInfos:      file_planet_v1_planet_proto_msgTypes,
	}.Build()
	File_planet_v1_planet_proto = out.File
	file_planet_v1_planet_proto_goTypes = nil
	file_planet_v1_planet_proto_depIdxs = nil
}
