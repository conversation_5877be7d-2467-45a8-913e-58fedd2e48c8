// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: planet/v1/planet.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreatePlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePlanetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePlanetRequestMultiError, or nil if none found.
func (m *CreatePlanetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePlanetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for ImgUrl

	// no validation rules for Description

	if len(errors) > 0 {
		return CreatePlanetRequestMultiError(errors)
	}

	return nil
}

// CreatePlanetRequestMultiError is an error wrapping multiple validation
// errors returned by CreatePlanetRequest.ValidateAll() if the designated
// constraints aren't met.
type CreatePlanetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePlanetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePlanetRequestMultiError) AllErrors() []error { return m }

// CreatePlanetRequestValidationError is the validation error returned by
// CreatePlanetRequest.Validate if the designated constraints aren't met.
type CreatePlanetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePlanetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePlanetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePlanetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePlanetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePlanetRequestValidationError) ErrorName() string {
	return "CreatePlanetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePlanetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePlanetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePlanetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePlanetRequestValidationError{}

// Validate checks the field values on CreatePlanetReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreatePlanetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePlanetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePlanetReplyMultiError, or nil if none found.
func (m *CreatePlanetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePlanetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreatePlanetReplyMultiError(errors)
	}

	return nil
}

// CreatePlanetReplyMultiError is an error wrapping multiple validation errors
// returned by CreatePlanetReply.ValidateAll() if the designated constraints
// aren't met.
type CreatePlanetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePlanetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePlanetReplyMultiError) AllErrors() []error { return m }

// CreatePlanetReplyValidationError is the validation error returned by
// CreatePlanetReply.Validate if the designated constraints aren't met.
type CreatePlanetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePlanetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePlanetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePlanetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePlanetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePlanetReplyValidationError) ErrorName() string {
	return "CreatePlanetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePlanetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePlanetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePlanetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePlanetReplyValidationError{}

// Validate checks the field values on UpdatePlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePlanetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePlanetRequestMultiError, or nil if none found.
func (m *UpdatePlanetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePlanetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for ImgUrl

	// no validation rules for Description

	if len(errors) > 0 {
		return UpdatePlanetRequestMultiError(errors)
	}

	return nil
}

// UpdatePlanetRequestMultiError is an error wrapping multiple validation
// errors returned by UpdatePlanetRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdatePlanetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePlanetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePlanetRequestMultiError) AllErrors() []error { return m }

// UpdatePlanetRequestValidationError is the validation error returned by
// UpdatePlanetRequest.Validate if the designated constraints aren't met.
type UpdatePlanetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePlanetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePlanetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePlanetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePlanetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePlanetRequestValidationError) ErrorName() string {
	return "UpdatePlanetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePlanetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePlanetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePlanetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePlanetRequestValidationError{}

// Validate checks the field values on UpdatePlanetReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdatePlanetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePlanetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePlanetReplyMultiError, or nil if none found.
func (m *UpdatePlanetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePlanetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdatePlanetReplyMultiError(errors)
	}

	return nil
}

// UpdatePlanetReplyMultiError is an error wrapping multiple validation errors
// returned by UpdatePlanetReply.ValidateAll() if the designated constraints
// aren't met.
type UpdatePlanetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePlanetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePlanetReplyMultiError) AllErrors() []error { return m }

// UpdatePlanetReplyValidationError is the validation error returned by
// UpdatePlanetReply.Validate if the designated constraints aren't met.
type UpdatePlanetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePlanetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePlanetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePlanetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePlanetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePlanetReplyValidationError) ErrorName() string {
	return "UpdatePlanetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePlanetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePlanetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePlanetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePlanetReplyValidationError{}

// Validate checks the field values on DeletePlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePlanetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePlanetRequestMultiError, or nil if none found.
func (m *DeletePlanetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePlanetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeletePlanetRequestMultiError(errors)
	}

	return nil
}

// DeletePlanetRequestMultiError is an error wrapping multiple validation
// errors returned by DeletePlanetRequest.ValidateAll() if the designated
// constraints aren't met.
type DeletePlanetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePlanetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePlanetRequestMultiError) AllErrors() []error { return m }

// DeletePlanetRequestValidationError is the validation error returned by
// DeletePlanetRequest.Validate if the designated constraints aren't met.
type DeletePlanetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePlanetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePlanetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePlanetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePlanetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePlanetRequestValidationError) ErrorName() string {
	return "DeletePlanetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePlanetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePlanetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePlanetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePlanetRequestValidationError{}

// Validate checks the field values on DeletePlanetReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeletePlanetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePlanetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePlanetReplyMultiError, or nil if none found.
func (m *DeletePlanetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePlanetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeletePlanetReplyMultiError(errors)
	}

	return nil
}

// DeletePlanetReplyMultiError is an error wrapping multiple validation errors
// returned by DeletePlanetReply.ValidateAll() if the designated constraints
// aren't met.
type DeletePlanetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePlanetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePlanetReplyMultiError) AllErrors() []error { return m }

// DeletePlanetReplyValidationError is the validation error returned by
// DeletePlanetReply.Validate if the designated constraints aren't met.
type DeletePlanetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePlanetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePlanetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePlanetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePlanetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePlanetReplyValidationError) ErrorName() string {
	return "DeletePlanetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePlanetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePlanetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePlanetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePlanetReplyValidationError{}

// Validate checks the field values on GetPlanetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPlanetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlanetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlanetRequestMultiError, or nil if none found.
func (m *GetPlanetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlanetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetPlanetRequestMultiError(errors)
	}

	return nil
}

// GetPlanetRequestMultiError is an error wrapping multiple validation errors
// returned by GetPlanetRequest.ValidateAll() if the designated constraints
// aren't met.
type GetPlanetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlanetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlanetRequestMultiError) AllErrors() []error { return m }

// GetPlanetRequestValidationError is the validation error returned by
// GetPlanetRequest.Validate if the designated constraints aren't met.
type GetPlanetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlanetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlanetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlanetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlanetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlanetRequestValidationError) ErrorName() string { return "GetPlanetRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetPlanetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlanetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlanetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlanetRequestValidationError{}

// Validate checks the field values on GetPlanetReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPlanetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlanetReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPlanetReplyMultiError,
// or nil if none found.
func (m *GetPlanetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlanetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPlanetReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPlanetReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPlanetReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPlanetReplyMultiError(errors)
	}

	return nil
}

// GetPlanetReplyMultiError is an error wrapping multiple validation errors
// returned by GetPlanetReply.ValidateAll() if the designated constraints
// aren't met.
type GetPlanetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlanetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlanetReplyMultiError) AllErrors() []error { return m }

// GetPlanetReplyValidationError is the validation error returned by
// GetPlanetReply.Validate if the designated constraints aren't met.
type GetPlanetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlanetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlanetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlanetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlanetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlanetReplyValidationError) ErrorName() string { return "GetPlanetReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetPlanetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlanetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlanetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlanetReplyValidationError{}

// Validate checks the field values on CreatePlanetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePlanetTargetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePlanetTargetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePlanetTargetRequestMultiError, or nil if none found.
func (m *CreatePlanetTargetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePlanetTargetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	// no validation rules for Name

	if len(errors) > 0 {
		return CreatePlanetTargetRequestMultiError(errors)
	}

	return nil
}

// CreatePlanetTargetRequestMultiError is an error wrapping multiple validation
// errors returned by CreatePlanetTargetRequest.ValidateAll() if the
// designated constraints aren't met.
type CreatePlanetTargetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePlanetTargetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePlanetTargetRequestMultiError) AllErrors() []error { return m }

// CreatePlanetTargetRequestValidationError is the validation error returned by
// CreatePlanetTargetRequest.Validate if the designated constraints aren't met.
type CreatePlanetTargetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePlanetTargetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePlanetTargetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePlanetTargetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePlanetTargetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePlanetTargetRequestValidationError) ErrorName() string {
	return "CreatePlanetTargetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePlanetTargetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePlanetTargetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePlanetTargetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePlanetTargetRequestValidationError{}

// Validate checks the field values on CreatePlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePlanetTargetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePlanetTargetReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePlanetTargetReplyMultiError, or nil if none found.
func (m *CreatePlanetTargetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePlanetTargetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreatePlanetTargetReplyMultiError(errors)
	}

	return nil
}

// CreatePlanetTargetReplyMultiError is an error wrapping multiple validation
// errors returned by CreatePlanetTargetReply.ValidateAll() if the designated
// constraints aren't met.
type CreatePlanetTargetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePlanetTargetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePlanetTargetReplyMultiError) AllErrors() []error { return m }

// CreatePlanetTargetReplyValidationError is the validation error returned by
// CreatePlanetTargetReply.Validate if the designated constraints aren't met.
type CreatePlanetTargetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePlanetTargetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePlanetTargetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePlanetTargetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePlanetTargetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePlanetTargetReplyValidationError) ErrorName() string {
	return "CreatePlanetTargetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePlanetTargetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePlanetTargetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePlanetTargetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePlanetTargetReplyValidationError{}

// Validate checks the field values on UpdatePlanetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePlanetTargetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePlanetTargetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePlanetTargetRequestMultiError, or nil if none found.
func (m *UpdatePlanetTargetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePlanetTargetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return UpdatePlanetTargetRequestMultiError(errors)
	}

	return nil
}

// UpdatePlanetTargetRequestMultiError is an error wrapping multiple validation
// errors returned by UpdatePlanetTargetRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdatePlanetTargetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePlanetTargetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePlanetTargetRequestMultiError) AllErrors() []error { return m }

// UpdatePlanetTargetRequestValidationError is the validation error returned by
// UpdatePlanetTargetRequest.Validate if the designated constraints aren't met.
type UpdatePlanetTargetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePlanetTargetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePlanetTargetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePlanetTargetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePlanetTargetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePlanetTargetRequestValidationError) ErrorName() string {
	return "UpdatePlanetTargetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePlanetTargetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePlanetTargetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePlanetTargetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePlanetTargetRequestValidationError{}

// Validate checks the field values on UpdatePlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdatePlanetTargetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePlanetTargetReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdatePlanetTargetReplyMultiError, or nil if none found.
func (m *UpdatePlanetTargetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePlanetTargetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdatePlanetTargetReplyMultiError(errors)
	}

	return nil
}

// UpdatePlanetTargetReplyMultiError is an error wrapping multiple validation
// errors returned by UpdatePlanetTargetReply.ValidateAll() if the designated
// constraints aren't met.
type UpdatePlanetTargetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePlanetTargetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePlanetTargetReplyMultiError) AllErrors() []error { return m }

// UpdatePlanetTargetReplyValidationError is the validation error returned by
// UpdatePlanetTargetReply.Validate if the designated constraints aren't met.
type UpdatePlanetTargetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePlanetTargetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePlanetTargetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePlanetTargetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePlanetTargetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePlanetTargetReplyValidationError) ErrorName() string {
	return "UpdatePlanetTargetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdatePlanetTargetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePlanetTargetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePlanetTargetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePlanetTargetReplyValidationError{}

// Validate checks the field values on DeletePlanetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePlanetTargetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePlanetTargetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePlanetTargetRequestMultiError, or nil if none found.
func (m *DeletePlanetTargetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePlanetTargetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return DeletePlanetTargetRequestMultiError(errors)
	}

	return nil
}

// DeletePlanetTargetRequestMultiError is an error wrapping multiple validation
// errors returned by DeletePlanetTargetRequest.ValidateAll() if the
// designated constraints aren't met.
type DeletePlanetTargetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePlanetTargetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePlanetTargetRequestMultiError) AllErrors() []error { return m }

// DeletePlanetTargetRequestValidationError is the validation error returned by
// DeletePlanetTargetRequest.Validate if the designated constraints aren't met.
type DeletePlanetTargetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePlanetTargetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePlanetTargetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePlanetTargetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePlanetTargetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePlanetTargetRequestValidationError) ErrorName() string {
	return "DeletePlanetTargetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePlanetTargetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePlanetTargetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePlanetTargetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePlanetTargetRequestValidationError{}

// Validate checks the field values on DeletePlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeletePlanetTargetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeletePlanetTargetReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeletePlanetTargetReplyMultiError, or nil if none found.
func (m *DeletePlanetTargetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeletePlanetTargetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeletePlanetTargetReplyMultiError(errors)
	}

	return nil
}

// DeletePlanetTargetReplyMultiError is an error wrapping multiple validation
// errors returned by DeletePlanetTargetReply.ValidateAll() if the designated
// constraints aren't met.
type DeletePlanetTargetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeletePlanetTargetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeletePlanetTargetReplyMultiError) AllErrors() []error { return m }

// DeletePlanetTargetReplyValidationError is the validation error returned by
// DeletePlanetTargetReply.Validate if the designated constraints aren't met.
type DeletePlanetTargetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeletePlanetTargetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeletePlanetTargetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeletePlanetTargetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeletePlanetTargetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeletePlanetTargetReplyValidationError) ErrorName() string {
	return "DeletePlanetTargetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeletePlanetTargetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeletePlanetTargetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeletePlanetTargetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeletePlanetTargetReplyValidationError{}

// Validate checks the field values on ListPlanetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetTargetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetTargetRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetTargetRequestMultiError, or nil if none found.
func (m *ListPlanetTargetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetTargetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return ListPlanetTargetRequestMultiError(errors)
	}

	return nil
}

// ListPlanetTargetRequestMultiError is an error wrapping multiple validation
// errors returned by ListPlanetTargetRequest.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetTargetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetTargetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetTargetRequestMultiError) AllErrors() []error { return m }

// ListPlanetTargetRequestValidationError is the validation error returned by
// ListPlanetTargetRequest.Validate if the designated constraints aren't met.
type ListPlanetTargetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetTargetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetTargetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetTargetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetTargetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetTargetRequestValidationError) ErrorName() string {
	return "ListPlanetTargetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetTargetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetTargetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetTargetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetTargetRequestValidationError{}

// Validate checks the field values on ListPlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetTargetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetTargetReplyMultiError, or nil if none found.
func (m *ListPlanetTargetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetTargetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPlanetTargetReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPlanetTargetReplyValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPlanetTargetReplyValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListPlanetTargetReplyMultiError(errors)
	}

	return nil
}

// ListPlanetTargetReplyMultiError is an error wrapping multiple validation
// errors returned by ListPlanetTargetReply.ValidateAll() if the designated
// constraints aren't met.
type ListPlanetTargetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetTargetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetTargetReplyMultiError) AllErrors() []error { return m }

// ListPlanetTargetReplyValidationError is the validation error returned by
// ListPlanetTargetReply.Validate if the designated constraints aren't met.
type ListPlanetTargetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetTargetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetTargetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetTargetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetTargetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetTargetReplyValidationError) ErrorName() string {
	return "ListPlanetTargetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetTargetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetTargetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetTargetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetTargetReplyValidationError{}

// Validate checks the field values on GetPlanetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlanetTargetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlanetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlanetTargetRequestMultiError, or nil if none found.
func (m *GetPlanetTargetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlanetTargetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return GetPlanetTargetRequestMultiError(errors)
	}

	return nil
}

// GetPlanetTargetRequestMultiError is an error wrapping multiple validation
// errors returned by GetPlanetTargetRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPlanetTargetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlanetTargetRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlanetTargetRequestMultiError) AllErrors() []error { return m }

// GetPlanetTargetRequestValidationError is the validation error returned by
// GetPlanetTargetRequest.Validate if the designated constraints aren't met.
type GetPlanetTargetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlanetTargetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlanetTargetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlanetTargetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlanetTargetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlanetTargetRequestValidationError) ErrorName() string {
	return "GetPlanetTargetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlanetTargetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlanetTargetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlanetTargetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlanetTargetRequestValidationError{}

// Validate checks the field values on GetPlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlanetTargetReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlanetTargetReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlanetTargetReplyMultiError, or nil if none found.
func (m *GetPlanetTargetReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlanetTargetReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPlanetTargetReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPlanetTargetReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPlanetTargetReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPlanetTargetReplyMultiError(errors)
	}

	return nil
}

// GetPlanetTargetReplyMultiError is an error wrapping multiple validation
// errors returned by GetPlanetTargetReply.ValidateAll() if the designated
// constraints aren't met.
type GetPlanetTargetReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlanetTargetReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlanetTargetReplyMultiError) AllErrors() []error { return m }

// GetPlanetTargetReplyValidationError is the validation error returned by
// GetPlanetTargetReply.Validate if the designated constraints aren't met.
type GetPlanetTargetReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlanetTargetReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlanetTargetReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlanetTargetReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlanetTargetReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlanetTargetReplyValidationError) ErrorName() string {
	return "GetPlanetTargetReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlanetTargetReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlanetTargetReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlanetTargetReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlanetTargetReplyValidationError{}

// Validate checks the field values on GetPlanetReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlanetReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlanetReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlanetReply_DataMultiError, or nil if none found.
func (m *GetPlanetReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlanetReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for ImgUrl

	// no validation rules for Description

	if len(errors) > 0 {
		return GetPlanetReply_DataMultiError(errors)
	}

	return nil
}

// GetPlanetReply_DataMultiError is an error wrapping multiple validation
// errors returned by GetPlanetReply_Data.ValidateAll() if the designated
// constraints aren't met.
type GetPlanetReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlanetReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlanetReply_DataMultiError) AllErrors() []error { return m }

// GetPlanetReply_DataValidationError is the validation error returned by
// GetPlanetReply_Data.Validate if the designated constraints aren't met.
type GetPlanetReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlanetReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlanetReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlanetReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlanetReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlanetReply_DataValidationError) ErrorName() string {
	return "GetPlanetReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlanetReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlanetReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlanetReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlanetReply_DataValidationError{}

// Validate checks the field values on ListPlanetTargetReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListPlanetTargetReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPlanetTargetReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListPlanetTargetReply_DataMultiError, or nil if none found.
func (m *ListPlanetTargetReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPlanetTargetReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return ListPlanetTargetReply_DataMultiError(errors)
	}

	return nil
}

// ListPlanetTargetReply_DataMultiError is an error wrapping multiple
// validation errors returned by ListPlanetTargetReply_Data.ValidateAll() if
// the designated constraints aren't met.
type ListPlanetTargetReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPlanetTargetReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPlanetTargetReply_DataMultiError) AllErrors() []error { return m }

// ListPlanetTargetReply_DataValidationError is the validation error returned
// by ListPlanetTargetReply_Data.Validate if the designated constraints aren't met.
type ListPlanetTargetReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPlanetTargetReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPlanetTargetReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPlanetTargetReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPlanetTargetReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPlanetTargetReply_DataValidationError) ErrorName() string {
	return "ListPlanetTargetReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e ListPlanetTargetReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPlanetTargetReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPlanetTargetReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPlanetTargetReply_DataValidationError{}

// Validate checks the field values on GetPlanetTargetReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPlanetTargetReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPlanetTargetReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPlanetTargetReply_DataMultiError, or nil if none found.
func (m *GetPlanetTargetReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPlanetTargetReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for PlanetId

	if len(errors) > 0 {
		return GetPlanetTargetReply_DataMultiError(errors)
	}

	return nil
}

// GetPlanetTargetReply_DataMultiError is an error wrapping multiple validation
// errors returned by GetPlanetTargetReply_Data.ValidateAll() if the
// designated constraints aren't met.
type GetPlanetTargetReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPlanetTargetReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPlanetTargetReply_DataMultiError) AllErrors() []error { return m }

// GetPlanetTargetReply_DataValidationError is the validation error returned by
// GetPlanetTargetReply_Data.Validate if the designated constraints aren't met.
type GetPlanetTargetReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPlanetTargetReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPlanetTargetReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPlanetTargetReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPlanetTargetReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPlanetTargetReply_DataValidationError) ErrorName() string {
	return "GetPlanetTargetReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetPlanetTargetReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPlanetTargetReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPlanetTargetReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPlanetTargetReply_DataValidationError{}
