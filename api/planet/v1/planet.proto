syntax = "proto3";

package planet_v1;

option go_package = "github.com/wlnil/life-log-be/api/planet/v1;v1";

message CreatePlanetRequest {
  string name = 1;
  string img_url = 2;
  string description = 3;
}
message CreatePlanetReply {
  int32 code = 1;
  string msg = 2;
}

message UpdatePlanetRequest {
  int32 id = 1;
  string name = 2;
  string img_url = 3;
  string description = 4;
}
message UpdatePlanetReply {
  int32 code = 1;
  string msg = 2;
}

message DeletePlanetRequest {
  int32 id = 1;
}
message DeletePlanetReply {
  int32 code = 1;
  string msg = 2;
}

message GetPlanetRequest {
  int32 id = 1;
}
message GetPlanetReply {
  message Data {
    int32 id = 1;
    string name = 2;
    string img_url = 3;
    string description = 4;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message CreatePlanetTargetRequest {
  int32 planet_id = 1;
  string name = 2;
}
message CreatePlanetTargetReply {
  int32 code = 1;
  string msg = 2;
}

message UpdatePlanetTargetRequest {
  int32 id = 1;
  string name = 2;
  int32 planet_id = 3;
}
message UpdatePlanetTargetReply {
  int32 code = 1;
  string msg = 2;
}

message DeletePlanetTargetRequest {
  int32 id = 1;
  int32 planet_id = 2;
}
message DeletePlanetTargetReply {
  int32 code = 1;
  string msg = 2;
}

message ListPlanetTargetRequest {
  int32 planet_id = 1;
}
message ListPlanetTargetReply {
  message Data {
    int32 id = 1;
    string name = 2;
    int32 planet_id = 3;
  }

  int32 code = 1;
  string msg = 2;
  repeated Data data = 3;
}

message GetPlanetTargetRequest {
  int32 id = 1;
  int32 planet_id = 2;
}
message GetPlanetTargetReply {
  message Data {
    int32 id = 1;
    string name = 2;
    int32 planet_id = 3;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}
