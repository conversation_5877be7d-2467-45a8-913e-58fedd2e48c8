syntax = "proto3";

package user_v1;

option go_package = "github.com/wlnil/life-log-be/api/user/v1;v1";

import "validate/validate.proto";

message RegisterRequest {
  string user_name = 1 [(validate.rules).string = {min_len: 1}];
  string password = 2 [(validate.rules).string = {min_len: 6, max_len: 20}];
  string confirm_password = 3 [(validate.rules).string = {min_len: 6, max_len: 20}];
  string verify_code = 4 [(validate.rules).string.len = 6];
  string Phone = 5;
}
message RegisterReply {
  message Data {
    string token = 1;
    int32 expire_at = 2;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message LoginRequest {
  // 核心认证参数（字段1-6）
  string phone = 1;                        // 手机号登录
  string password = 2;                     // 密码认证
  string email = 3;                        // 邮箱登录
  string verify_code = 4;                  // 验证码认证
  int32 login_type = 5;                    // 登录方式：0=密码，1=验证码，2=Google，3=Apple
  int32 pass_type = 6;                     // 认证类型：0=密码，1=验证码

  // Firebase认证参数（字段7）
  string third_party_token = 7;            // Firebase ID Token

  // 环境信息参数（字段8-10）
  string timezone_name = 8;                // 时区名称
  string timezone_offset = 9;              // 时区偏移量
  string locale = 10;                      // 语言区域设置
  string platform = 12;                   // 设备平台：ios, android, web
}
message LoginReply {
  message Data {
    string token = 1;
    int32 expire_at = 2;
    int32 user_id = 3;
    string name = 4;
    string avatar_url = 5;
    bool is_system_admin = 6;
    int32 role_type = 7;
    string phone = 8;
    string email = 9;
    int32 status = 10;
    string desc = 11;
    int32 gender = 12;
    bool is_set_pwd = 13;
    bool is_vip = 14;
    bool is_set_privacy = 15;
    string uid = 16;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message LogoutRequest {
  string device_id = 1;                   // 设备唯一标识
  string platform = 2;                   // 设备平台：ios, android, web
}
message LogoutReply {
  int32 code = 1;
  string msg = 2;
}

message GetUserProfileRequest {}
message GetUserProfileReply {
  message Stats {
    int32 all_habits = 1;
    int32 on_track_habits = 2;
    int32 done_habits = 3;
  }

  message Data {
    string username = 1;
    string phone = 2;
    string avatar_url = 3;
    string desc = 4;
    Stats stats = 5;
    repeated string goals = 6;
    string email = 7;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message ForgetPasswordRequest {
  string phone = 1;
  string verify_code = 2;
  string password = 3;
  string confirm_password = 4;
  string email = 5;
}
message ForgetPasswordReply {
  int32 code = 1;
  string msg = 2;
}

message UpdateUserProfileRequest {
  string username = 1 [(validate.rules).string = {max_len: 16}];
  string avatar_url = 3;
  string desc = 4 [(validate.rules).string = {max_len: 50}];
}
message UpdateUserProfileReply {
  message Data {
    string username = 1;
    string avatar_url = 3;
    string desc = 4;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message FollowUserRequest {
  int32 user_id = 1;
}
message FollowUserReply {
  int32 code = 1;
  string msg = 2;
}

message UnfollowUserRequest {
  int32 user_id = 1;
}
message UnfollowUserReply {
  int32 code = 1;
  string msg = 2;
}

message RefreshTokenRequest {
  string device_id = 1;                   // 设备唯一标识
  string platform = 2;                   // 设备平台：ios, android, web
  string device_brand = 3;               // 设备品牌

  string fcm_token = 4;                   // FCM推送token
  string apns_token = 5;                  // APNs推送token
  string push_service_type = 6;           // 推送服务类型：jpush, fcm, apns
  string jpush_registration_id = 7;       // 极光推送设备注册ID
  string recommended_push_channel = 8;    // 极光推送推荐渠道
}
message RefreshTokenReply {
  message Data {
    string token = 1;
    int32 expire_at = 2;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message UpdateUserAwardRequest {
  string award_image_url = 1;
}
message UpdateUserAwardReply {
  int32 code = 1;
  string msg = 2;
}

message GetUserSettingRequest {
  string scene = 1; // 场景
}
message GetUserSettingReply {
  message Data {
    string award_image_url = 1;
    string award_image_url_with_domain = 2;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message ChangePhoneRequest {
  string phone = 1;
  string verify_code = 2;
}
message ChangePhoneReply {
  int32 code = 1;
  string msg = 2;
}

message ChangeEmailRequest {
  string email = 1;
  string verify_code = 2;
}
message ChangeEmailReply {
  int32 code = 1;
  string msg = 2;
}

message ChangePasswordRequest {
  string old_password = 1;
  string new_password = 2;
  string confirm_password = 3;
}
message ChangePasswordReply {
  int32 code = 1;
  string msg = 2;
}

message DeleteUserRequest {
  string phone = 1;
  string password = 2;
  string email = 3;
}
message DeleteUserReply {
  int32 code = 1;
  string msg = 2;
}

message CheckUserPrivacyPasswordRequest {
  string password = 1;
}
message CheckUserPrivacyPasswordReply {
  message Data {
    bool is_pass = 1;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message ChangeUserPrivacyPasswordRequest {
  string old_password = 1;
  string new_password = 2;
  string confirm_password = 3;
}
message ChangeUserPrivacyPasswordReply {
  int32 code = 1;
  string msg = 2;
}

message GetUserVipInfoRequest {}
message GetUserVipInfoReply {
  message VipDetail {
    int32 price = 1;
    int32 discount_price = 2;
  }
  message Data {
    bool is_vip = 1;
    int32 remain_days = 2;
    VipDetail month_price = 3;
    VipDetail quarter_price = 4;
    VipDetail year_price = 5;
  }

  int32 code = 1;
  string msg = 2;
  Data data = 3;
}

message PushTokenRequest {
  // 环境信息参数（字段8-10）
  string timezone_name = 8;                // 时区名称
  string timezone_offset = 9;              // 时区偏移量
  string locale = 10;                      // 语言区域设置

  // 基础设备信息参数（字段11-15）
  string device_id = 11;                   // 设备唯一标识
  string platform = 12;                    // 设备平台：ios, android, web
  string system_version = 13;              // 系统版本
  string app_version = 14;                 // 应用版本
  string device_brand = 15;                // 设备品牌

  // 推送相关参数（字段16-21）
  string fcm_token = 16;                   // FCM推送token
  string apns_token = 17;                  // APNs推送token
  string push_service_type = 18;           // 推送服务类型：jpush, fcm, apns
  string jpush_registration_id = 19;       // 极光推送设备注册ID
  string recommended_push_channel = 20;    // 极光推送推荐渠道
  bool push_permission_granted = 21;       // 推送权限状态
}
message PushTokenReply {
  int32 code = 1;
  string msg = 2;
}
