// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: user/v1/user.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RegisterRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RegisterRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterRequestMultiError, or nil if none found.
func (m *RegisterRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetUserName()) < 1 {
		err := RegisterRequestValidationError{
			field:  "UserName",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetPassword()); l < 6 || l > 20 {
		err := RegisterRequestValidationError{
			field:  "Password",
			reason: "value length must be between 6 and 20 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetConfirmPassword()); l < 6 || l > 20 {
		err := RegisterRequestValidationError{
			field:  "ConfirmPassword",
			reason: "value length must be between 6 and 20 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetVerifyCode()) != 6 {
		err := RegisterRequestValidationError{
			field:  "VerifyCode",
			reason: "value length must be 6 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	// no validation rules for Phone

	if len(errors) > 0 {
		return RegisterRequestMultiError(errors)
	}

	return nil
}

// RegisterRequestMultiError is an error wrapping multiple validation errors
// returned by RegisterRequest.ValidateAll() if the designated constraints
// aren't met.
type RegisterRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterRequestMultiError) AllErrors() []error { return m }

// RegisterRequestValidationError is the validation error returned by
// RegisterRequest.Validate if the designated constraints aren't met.
type RegisterRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterRequestValidationError) ErrorName() string { return "RegisterRequestValidationError" }

// Error satisfies the builtin error interface
func (e RegisterRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterRequestValidationError{}

// Validate checks the field values on RegisterReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RegisterReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RegisterReplyMultiError, or
// nil if none found.
func (m *RegisterReply) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegisterReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegisterReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegisterReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RegisterReplyMultiError(errors)
	}

	return nil
}

// RegisterReplyMultiError is an error wrapping multiple validation errors
// returned by RegisterReply.ValidateAll() if the designated constraints
// aren't met.
type RegisterReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterReplyMultiError) AllErrors() []error { return m }

// RegisterReplyValidationError is the validation error returned by
// RegisterReply.Validate if the designated constraints aren't met.
type RegisterReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterReplyValidationError) ErrorName() string { return "RegisterReplyValidationError" }

// Error satisfies the builtin error interface
func (e RegisterReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterReplyValidationError{}

// Validate checks the field values on LoginRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginRequestMultiError, or
// nil if none found.
func (m *LoginRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phone

	// no validation rules for Password

	// no validation rules for Email

	// no validation rules for VerifyCode

	// no validation rules for LoginType

	// no validation rules for PassType

	// no validation rules for ThirdPartyToken

	// no validation rules for TimezoneName

	// no validation rules for TimezoneOffset

	// no validation rules for Locale

	// no validation rules for Platform

	if len(errors) > 0 {
		return LoginRequestMultiError(errors)
	}

	return nil
}

// LoginRequestMultiError is an error wrapping multiple validation errors
// returned by LoginRequest.ValidateAll() if the designated constraints aren't met.
type LoginRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginRequestMultiError) AllErrors() []error { return m }

// LoginRequestValidationError is the validation error returned by
// LoginRequest.Validate if the designated constraints aren't met.
type LoginRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginRequestValidationError) ErrorName() string { return "LoginRequestValidationError" }

// Error satisfies the builtin error interface
func (e LoginRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginRequestValidationError{}

// Validate checks the field values on LoginReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoginReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoginReplyMultiError, or
// nil if none found.
func (m *LoginReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoginReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoginReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoginReplyMultiError(errors)
	}

	return nil
}

// LoginReplyMultiError is an error wrapping multiple validation errors
// returned by LoginReply.ValidateAll() if the designated constraints aren't met.
type LoginReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginReplyMultiError) AllErrors() []error { return m }

// LoginReplyValidationError is the validation error returned by
// LoginReply.Validate if the designated constraints aren't met.
type LoginReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginReplyValidationError) ErrorName() string { return "LoginReplyValidationError" }

// Error satisfies the builtin error interface
func (e LoginReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginReplyValidationError{}

// Validate checks the field values on LogoutRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogoutRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogoutRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogoutRequestMultiError, or
// nil if none found.
func (m *LogoutRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LogoutRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	// no validation rules for Platform

	if len(errors) > 0 {
		return LogoutRequestMultiError(errors)
	}

	return nil
}

// LogoutRequestMultiError is an error wrapping multiple validation errors
// returned by LogoutRequest.ValidateAll() if the designated constraints
// aren't met.
type LogoutRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogoutRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogoutRequestMultiError) AllErrors() []error { return m }

// LogoutRequestValidationError is the validation error returned by
// LogoutRequest.Validate if the designated constraints aren't met.
type LogoutRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogoutRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogoutRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogoutRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogoutRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogoutRequestValidationError) ErrorName() string { return "LogoutRequestValidationError" }

// Error satisfies the builtin error interface
func (e LogoutRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogoutRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogoutRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogoutRequestValidationError{}

// Validate checks the field values on LogoutReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogoutReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogoutReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogoutReplyMultiError, or
// nil if none found.
func (m *LogoutReply) ValidateAll() error {
	return m.validate(true)
}

func (m *LogoutReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return LogoutReplyMultiError(errors)
	}

	return nil
}

// LogoutReplyMultiError is an error wrapping multiple validation errors
// returned by LogoutReply.ValidateAll() if the designated constraints aren't met.
type LogoutReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogoutReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogoutReplyMultiError) AllErrors() []error { return m }

// LogoutReplyValidationError is the validation error returned by
// LogoutReply.Validate if the designated constraints aren't met.
type LogoutReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogoutReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogoutReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogoutReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogoutReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogoutReplyValidationError) ErrorName() string { return "LogoutReplyValidationError" }

// Error satisfies the builtin error interface
func (e LogoutReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogoutReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogoutReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogoutReplyValidationError{}

// Validate checks the field values on GetUserProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserProfileRequestMultiError, or nil if none found.
func (m *GetUserProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetUserProfileRequestMultiError(errors)
	}

	return nil
}

// GetUserProfileRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserProfileRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserProfileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserProfileRequestMultiError) AllErrors() []error { return m }

// GetUserProfileRequestValidationError is the validation error returned by
// GetUserProfileRequest.Validate if the designated constraints aren't met.
type GetUserProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserProfileRequestValidationError) ErrorName() string {
	return "GetUserProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserProfileRequestValidationError{}

// Validate checks the field values on GetUserProfileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserProfileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserProfileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserProfileReplyMultiError, or nil if none found.
func (m *GetUserProfileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserProfileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserProfileReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserProfileReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserProfileReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserProfileReplyMultiError(errors)
	}

	return nil
}

// GetUserProfileReplyMultiError is an error wrapping multiple validation
// errors returned by GetUserProfileReply.ValidateAll() if the designated
// constraints aren't met.
type GetUserProfileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserProfileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserProfileReplyMultiError) AllErrors() []error { return m }

// GetUserProfileReplyValidationError is the validation error returned by
// GetUserProfileReply.Validate if the designated constraints aren't met.
type GetUserProfileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserProfileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserProfileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserProfileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserProfileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserProfileReplyValidationError) ErrorName() string {
	return "GetUserProfileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserProfileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserProfileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserProfileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserProfileReplyValidationError{}

// Validate checks the field values on ForgetPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForgetPasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForgetPasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForgetPasswordRequestMultiError, or nil if none found.
func (m *ForgetPasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ForgetPasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phone

	// no validation rules for VerifyCode

	// no validation rules for Password

	// no validation rules for ConfirmPassword

	// no validation rules for Email

	if len(errors) > 0 {
		return ForgetPasswordRequestMultiError(errors)
	}

	return nil
}

// ForgetPasswordRequestMultiError is an error wrapping multiple validation
// errors returned by ForgetPasswordRequest.ValidateAll() if the designated
// constraints aren't met.
type ForgetPasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForgetPasswordRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForgetPasswordRequestMultiError) AllErrors() []error { return m }

// ForgetPasswordRequestValidationError is the validation error returned by
// ForgetPasswordRequest.Validate if the designated constraints aren't met.
type ForgetPasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForgetPasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForgetPasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForgetPasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForgetPasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForgetPasswordRequestValidationError) ErrorName() string {
	return "ForgetPasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ForgetPasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForgetPasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForgetPasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForgetPasswordRequestValidationError{}

// Validate checks the field values on ForgetPasswordReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForgetPasswordReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForgetPasswordReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForgetPasswordReplyMultiError, or nil if none found.
func (m *ForgetPasswordReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ForgetPasswordReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ForgetPasswordReplyMultiError(errors)
	}

	return nil
}

// ForgetPasswordReplyMultiError is an error wrapping multiple validation
// errors returned by ForgetPasswordReply.ValidateAll() if the designated
// constraints aren't met.
type ForgetPasswordReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForgetPasswordReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForgetPasswordReplyMultiError) AllErrors() []error { return m }

// ForgetPasswordReplyValidationError is the validation error returned by
// ForgetPasswordReply.Validate if the designated constraints aren't met.
type ForgetPasswordReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForgetPasswordReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForgetPasswordReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForgetPasswordReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForgetPasswordReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForgetPasswordReplyValidationError) ErrorName() string {
	return "ForgetPasswordReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ForgetPasswordReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForgetPasswordReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForgetPasswordReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForgetPasswordReplyValidationError{}

// Validate checks the field values on UpdateUserProfileRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserProfileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserProfileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserProfileRequestMultiError, or nil if none found.
func (m *UpdateUserProfileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserProfileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetUsername()) > 16 {
		err := UpdateUserProfileRequestValidationError{
			field:  "Username",
			reason: "value length must be at most 16 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AvatarUrl

	if utf8.RuneCountInString(m.GetDesc()) > 50 {
		err := UpdateUserProfileRequestValidationError{
			field:  "Desc",
			reason: "value length must be at most 50 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return UpdateUserProfileRequestMultiError(errors)
	}

	return nil
}

// UpdateUserProfileRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateUserProfileRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserProfileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserProfileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserProfileRequestMultiError) AllErrors() []error { return m }

// UpdateUserProfileRequestValidationError is the validation error returned by
// UpdateUserProfileRequest.Validate if the designated constraints aren't met.
type UpdateUserProfileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserProfileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserProfileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserProfileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserProfileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserProfileRequestValidationError) ErrorName() string {
	return "UpdateUserProfileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserProfileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserProfileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserProfileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserProfileRequestValidationError{}

// Validate checks the field values on UpdateUserProfileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserProfileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserProfileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserProfileReplyMultiError, or nil if none found.
func (m *UpdateUserProfileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserProfileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUserProfileReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUserProfileReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUserProfileReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateUserProfileReplyMultiError(errors)
	}

	return nil
}

// UpdateUserProfileReplyMultiError is an error wrapping multiple validation
// errors returned by UpdateUserProfileReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserProfileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserProfileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserProfileReplyMultiError) AllErrors() []error { return m }

// UpdateUserProfileReplyValidationError is the validation error returned by
// UpdateUserProfileReply.Validate if the designated constraints aren't met.
type UpdateUserProfileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserProfileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserProfileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserProfileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserProfileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserProfileReplyValidationError) ErrorName() string {
	return "UpdateUserProfileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserProfileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserProfileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserProfileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserProfileReplyValidationError{}

// Validate checks the field values on FollowUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FollowUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FollowUserRequestMultiError, or nil if none found.
func (m *FollowUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return FollowUserRequestMultiError(errors)
	}

	return nil
}

// FollowUserRequestMultiError is an error wrapping multiple validation errors
// returned by FollowUserRequest.ValidateAll() if the designated constraints
// aren't met.
type FollowUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowUserRequestMultiError) AllErrors() []error { return m }

// FollowUserRequestValidationError is the validation error returned by
// FollowUserRequest.Validate if the designated constraints aren't met.
type FollowUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowUserRequestValidationError) ErrorName() string {
	return "FollowUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FollowUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowUserRequestValidationError{}

// Validate checks the field values on FollowUserReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FollowUserReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FollowUserReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FollowUserReplyMultiError, or nil if none found.
func (m *FollowUserReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FollowUserReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return FollowUserReplyMultiError(errors)
	}

	return nil
}

// FollowUserReplyMultiError is an error wrapping multiple validation errors
// returned by FollowUserReply.ValidateAll() if the designated constraints
// aren't met.
type FollowUserReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FollowUserReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FollowUserReplyMultiError) AllErrors() []error { return m }

// FollowUserReplyValidationError is the validation error returned by
// FollowUserReply.Validate if the designated constraints aren't met.
type FollowUserReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FollowUserReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FollowUserReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FollowUserReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FollowUserReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FollowUserReplyValidationError) ErrorName() string { return "FollowUserReplyValidationError" }

// Error satisfies the builtin error interface
func (e FollowUserReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFollowUserReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FollowUserReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FollowUserReplyValidationError{}

// Validate checks the field values on UnfollowUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnfollowUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfollowUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnfollowUserRequestMultiError, or nil if none found.
func (m *UnfollowUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfollowUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return UnfollowUserRequestMultiError(errors)
	}

	return nil
}

// UnfollowUserRequestMultiError is an error wrapping multiple validation
// errors returned by UnfollowUserRequest.ValidateAll() if the designated
// constraints aren't met.
type UnfollowUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfollowUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfollowUserRequestMultiError) AllErrors() []error { return m }

// UnfollowUserRequestValidationError is the validation error returned by
// UnfollowUserRequest.Validate if the designated constraints aren't met.
type UnfollowUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfollowUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfollowUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfollowUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfollowUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfollowUserRequestValidationError) ErrorName() string {
	return "UnfollowUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UnfollowUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfollowUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfollowUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfollowUserRequestValidationError{}

// Validate checks the field values on UnfollowUserReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnfollowUserReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnfollowUserReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnfollowUserReplyMultiError, or nil if none found.
func (m *UnfollowUserReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UnfollowUserReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UnfollowUserReplyMultiError(errors)
	}

	return nil
}

// UnfollowUserReplyMultiError is an error wrapping multiple validation errors
// returned by UnfollowUserReply.ValidateAll() if the designated constraints
// aren't met.
type UnfollowUserReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnfollowUserReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnfollowUserReplyMultiError) AllErrors() []error { return m }

// UnfollowUserReplyValidationError is the validation error returned by
// UnfollowUserReply.Validate if the designated constraints aren't met.
type UnfollowUserReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnfollowUserReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnfollowUserReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnfollowUserReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnfollowUserReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnfollowUserReplyValidationError) ErrorName() string {
	return "UnfollowUserReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UnfollowUserReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnfollowUserReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnfollowUserReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnfollowUserReplyValidationError{}

// Validate checks the field values on RefreshTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RefreshTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefreshTokenRequestMultiError, or nil if none found.
func (m *RefreshTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceId

	// no validation rules for Platform

	// no validation rules for DeviceBrand

	// no validation rules for FcmToken

	// no validation rules for ApnsToken

	// no validation rules for PushServiceType

	// no validation rules for JpushRegistrationId

	// no validation rules for RecommendedPushChannel

	if len(errors) > 0 {
		return RefreshTokenRequestMultiError(errors)
	}

	return nil
}

// RefreshTokenRequestMultiError is an error wrapping multiple validation
// errors returned by RefreshTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type RefreshTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshTokenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshTokenRequestMultiError) AllErrors() []error { return m }

// RefreshTokenRequestValidationError is the validation error returned by
// RefreshTokenRequest.Validate if the designated constraints aren't met.
type RefreshTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshTokenRequestValidationError) ErrorName() string {
	return "RefreshTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshTokenRequestValidationError{}

// Validate checks the field values on RefreshTokenReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RefreshTokenReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshTokenReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefreshTokenReplyMultiError, or nil if none found.
func (m *RefreshTokenReply) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshTokenReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RefreshTokenReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RefreshTokenReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RefreshTokenReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RefreshTokenReplyMultiError(errors)
	}

	return nil
}

// RefreshTokenReplyMultiError is an error wrapping multiple validation errors
// returned by RefreshTokenReply.ValidateAll() if the designated constraints
// aren't met.
type RefreshTokenReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshTokenReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshTokenReplyMultiError) AllErrors() []error { return m }

// RefreshTokenReplyValidationError is the validation error returned by
// RefreshTokenReply.Validate if the designated constraints aren't met.
type RefreshTokenReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshTokenReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshTokenReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshTokenReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshTokenReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshTokenReplyValidationError) ErrorName() string {
	return "RefreshTokenReplyValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshTokenReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshTokenReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshTokenReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshTokenReplyValidationError{}

// Validate checks the field values on UpdateUserAwardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserAwardRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserAwardRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserAwardRequestMultiError, or nil if none found.
func (m *UpdateUserAwardRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserAwardRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AwardImageUrl

	if len(errors) > 0 {
		return UpdateUserAwardRequestMultiError(errors)
	}

	return nil
}

// UpdateUserAwardRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateUserAwardRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserAwardRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserAwardRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserAwardRequestMultiError) AllErrors() []error { return m }

// UpdateUserAwardRequestValidationError is the validation error returned by
// UpdateUserAwardRequest.Validate if the designated constraints aren't met.
type UpdateUserAwardRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserAwardRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserAwardRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserAwardRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserAwardRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserAwardRequestValidationError) ErrorName() string {
	return "UpdateUserAwardRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserAwardRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserAwardRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserAwardRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserAwardRequestValidationError{}

// Validate checks the field values on UpdateUserAwardReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserAwardReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserAwardReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserAwardReplyMultiError, or nil if none found.
func (m *UpdateUserAwardReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserAwardReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return UpdateUserAwardReplyMultiError(errors)
	}

	return nil
}

// UpdateUserAwardReplyMultiError is an error wrapping multiple validation
// errors returned by UpdateUserAwardReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserAwardReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserAwardReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserAwardReplyMultiError) AllErrors() []error { return m }

// UpdateUserAwardReplyValidationError is the validation error returned by
// UpdateUserAwardReply.Validate if the designated constraints aren't met.
type UpdateUserAwardReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserAwardReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserAwardReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserAwardReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserAwardReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserAwardReplyValidationError) ErrorName() string {
	return "UpdateUserAwardReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserAwardReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserAwardReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserAwardReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserAwardReplyValidationError{}

// Validate checks the field values on GetUserSettingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserSettingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserSettingRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserSettingRequestMultiError, or nil if none found.
func (m *GetUserSettingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSettingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Scene

	if len(errors) > 0 {
		return GetUserSettingRequestMultiError(errors)
	}

	return nil
}

// GetUserSettingRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserSettingRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserSettingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSettingRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSettingRequestMultiError) AllErrors() []error { return m }

// GetUserSettingRequestValidationError is the validation error returned by
// GetUserSettingRequest.Validate if the designated constraints aren't met.
type GetUserSettingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSettingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSettingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSettingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSettingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSettingRequestValidationError) ErrorName() string {
	return "GetUserSettingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSettingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSettingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSettingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSettingRequestValidationError{}

// Validate checks the field values on GetUserSettingReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserSettingReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserSettingReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserSettingReplyMultiError, or nil if none found.
func (m *GetUserSettingReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSettingReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserSettingReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserSettingReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserSettingReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserSettingReplyMultiError(errors)
	}

	return nil
}

// GetUserSettingReplyMultiError is an error wrapping multiple validation
// errors returned by GetUserSettingReply.ValidateAll() if the designated
// constraints aren't met.
type GetUserSettingReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSettingReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSettingReplyMultiError) AllErrors() []error { return m }

// GetUserSettingReplyValidationError is the validation error returned by
// GetUserSettingReply.Validate if the designated constraints aren't met.
type GetUserSettingReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSettingReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSettingReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSettingReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSettingReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSettingReplyValidationError) ErrorName() string {
	return "GetUserSettingReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSettingReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSettingReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSettingReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSettingReplyValidationError{}

// Validate checks the field values on ChangePhoneRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangePhoneRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangePhoneRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangePhoneRequestMultiError, or nil if none found.
func (m *ChangePhoneRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangePhoneRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phone

	// no validation rules for VerifyCode

	if len(errors) > 0 {
		return ChangePhoneRequestMultiError(errors)
	}

	return nil
}

// ChangePhoneRequestMultiError is an error wrapping multiple validation errors
// returned by ChangePhoneRequest.ValidateAll() if the designated constraints
// aren't met.
type ChangePhoneRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangePhoneRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangePhoneRequestMultiError) AllErrors() []error { return m }

// ChangePhoneRequestValidationError is the validation error returned by
// ChangePhoneRequest.Validate if the designated constraints aren't met.
type ChangePhoneRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangePhoneRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangePhoneRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangePhoneRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangePhoneRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangePhoneRequestValidationError) ErrorName() string {
	return "ChangePhoneRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangePhoneRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangePhoneRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangePhoneRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangePhoneRequestValidationError{}

// Validate checks the field values on ChangePhoneReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChangePhoneReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangePhoneReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangePhoneReplyMultiError, or nil if none found.
func (m *ChangePhoneReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangePhoneReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ChangePhoneReplyMultiError(errors)
	}

	return nil
}

// ChangePhoneReplyMultiError is an error wrapping multiple validation errors
// returned by ChangePhoneReply.ValidateAll() if the designated constraints
// aren't met.
type ChangePhoneReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangePhoneReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangePhoneReplyMultiError) AllErrors() []error { return m }

// ChangePhoneReplyValidationError is the validation error returned by
// ChangePhoneReply.Validate if the designated constraints aren't met.
type ChangePhoneReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangePhoneReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangePhoneReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangePhoneReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangePhoneReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangePhoneReplyValidationError) ErrorName() string { return "ChangePhoneReplyValidationError" }

// Error satisfies the builtin error interface
func (e ChangePhoneReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangePhoneReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangePhoneReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangePhoneReplyValidationError{}

// Validate checks the field values on ChangeEmailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeEmailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeEmailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeEmailRequestMultiError, or nil if none found.
func (m *ChangeEmailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeEmailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for VerifyCode

	if len(errors) > 0 {
		return ChangeEmailRequestMultiError(errors)
	}

	return nil
}

// ChangeEmailRequestMultiError is an error wrapping multiple validation errors
// returned by ChangeEmailRequest.ValidateAll() if the designated constraints
// aren't met.
type ChangeEmailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeEmailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeEmailRequestMultiError) AllErrors() []error { return m }

// ChangeEmailRequestValidationError is the validation error returned by
// ChangeEmailRequest.Validate if the designated constraints aren't met.
type ChangeEmailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeEmailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeEmailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeEmailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeEmailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeEmailRequestValidationError) ErrorName() string {
	return "ChangeEmailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeEmailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeEmailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeEmailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeEmailRequestValidationError{}

// Validate checks the field values on ChangeEmailReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChangeEmailReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeEmailReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangeEmailReplyMultiError, or nil if none found.
func (m *ChangeEmailReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeEmailReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ChangeEmailReplyMultiError(errors)
	}

	return nil
}

// ChangeEmailReplyMultiError is an error wrapping multiple validation errors
// returned by ChangeEmailReply.ValidateAll() if the designated constraints
// aren't met.
type ChangeEmailReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeEmailReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeEmailReplyMultiError) AllErrors() []error { return m }

// ChangeEmailReplyValidationError is the validation error returned by
// ChangeEmailReply.Validate if the designated constraints aren't met.
type ChangeEmailReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeEmailReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeEmailReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeEmailReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeEmailReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeEmailReplyValidationError) ErrorName() string { return "ChangeEmailReplyValidationError" }

// Error satisfies the builtin error interface
func (e ChangeEmailReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeEmailReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeEmailReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeEmailReplyValidationError{}

// Validate checks the field values on ChangePasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangePasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangePasswordRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangePasswordRequestMultiError, or nil if none found.
func (m *ChangePasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangePasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OldPassword

	// no validation rules for NewPassword

	// no validation rules for ConfirmPassword

	if len(errors) > 0 {
		return ChangePasswordRequestMultiError(errors)
	}

	return nil
}

// ChangePasswordRequestMultiError is an error wrapping multiple validation
// errors returned by ChangePasswordRequest.ValidateAll() if the designated
// constraints aren't met.
type ChangePasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangePasswordRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangePasswordRequestMultiError) AllErrors() []error { return m }

// ChangePasswordRequestValidationError is the validation error returned by
// ChangePasswordRequest.Validate if the designated constraints aren't met.
type ChangePasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangePasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangePasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangePasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangePasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangePasswordRequestValidationError) ErrorName() string {
	return "ChangePasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangePasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangePasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangePasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangePasswordRequestValidationError{}

// Validate checks the field values on ChangePasswordReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangePasswordReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangePasswordReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChangePasswordReplyMultiError, or nil if none found.
func (m *ChangePasswordReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangePasswordReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ChangePasswordReplyMultiError(errors)
	}

	return nil
}

// ChangePasswordReplyMultiError is an error wrapping multiple validation
// errors returned by ChangePasswordReply.ValidateAll() if the designated
// constraints aren't met.
type ChangePasswordReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangePasswordReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangePasswordReplyMultiError) AllErrors() []error { return m }

// ChangePasswordReplyValidationError is the validation error returned by
// ChangePasswordReply.Validate if the designated constraints aren't met.
type ChangePasswordReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangePasswordReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangePasswordReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangePasswordReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangePasswordReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangePasswordReplyValidationError) ErrorName() string {
	return "ChangePasswordReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ChangePasswordReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangePasswordReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangePasswordReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangePasswordReplyValidationError{}

// Validate checks the field values on DeleteUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserRequestMultiError, or nil if none found.
func (m *DeleteUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Phone

	// no validation rules for Password

	// no validation rules for Email

	if len(errors) > 0 {
		return DeleteUserRequestMultiError(errors)
	}

	return nil
}

// DeleteUserRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteUserRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserRequestMultiError) AllErrors() []error { return m }

// DeleteUserRequestValidationError is the validation error returned by
// DeleteUserRequest.Validate if the designated constraints aren't met.
type DeleteUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserRequestValidationError) ErrorName() string {
	return "DeleteUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserRequestValidationError{}

// Validate checks the field values on DeleteUserReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteUserReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteUserReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteUserReplyMultiError, or nil if none found.
func (m *DeleteUserReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteUserReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return DeleteUserReplyMultiError(errors)
	}

	return nil
}

// DeleteUserReplyMultiError is an error wrapping multiple validation errors
// returned by DeleteUserReply.ValidateAll() if the designated constraints
// aren't met.
type DeleteUserReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteUserReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteUserReplyMultiError) AllErrors() []error { return m }

// DeleteUserReplyValidationError is the validation error returned by
// DeleteUserReply.Validate if the designated constraints aren't met.
type DeleteUserReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteUserReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteUserReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteUserReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteUserReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteUserReplyValidationError) ErrorName() string { return "DeleteUserReplyValidationError" }

// Error satisfies the builtin error interface
func (e DeleteUserReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteUserReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteUserReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteUserReplyValidationError{}

// Validate checks the field values on BuddySearchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BuddySearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuddySearchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BuddySearchRequestMultiError, or nil if none found.
func (m *BuddySearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BuddySearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetUid()) < 1 {
		err := BuddySearchRequestValidationError{
			field:  "Uid",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return BuddySearchRequestMultiError(errors)
	}

	return nil
}

// BuddySearchRequestMultiError is an error wrapping multiple validation errors
// returned by BuddySearchRequest.ValidateAll() if the designated constraints
// aren't met.
type BuddySearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuddySearchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuddySearchRequestMultiError) AllErrors() []error { return m }

// BuddySearchRequestValidationError is the validation error returned by
// BuddySearchRequest.Validate if the designated constraints aren't met.
type BuddySearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuddySearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuddySearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuddySearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuddySearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuddySearchRequestValidationError) ErrorName() string {
	return "BuddySearchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BuddySearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuddySearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuddySearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuddySearchRequestValidationError{}

// Validate checks the field values on BuddySearchReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BuddySearchReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuddySearchReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BuddySearchReplyMultiError, or nil if none found.
func (m *BuddySearchReply) ValidateAll() error {
	return m.validate(true)
}

func (m *BuddySearchReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BuddySearchReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BuddySearchReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BuddySearchReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BuddySearchReplyMultiError(errors)
	}

	return nil
}

// BuddySearchReplyMultiError is an error wrapping multiple validation errors
// returned by BuddySearchReply.ValidateAll() if the designated constraints
// aren't met.
type BuddySearchReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuddySearchReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuddySearchReplyMultiError) AllErrors() []error { return m }

// BuddySearchReplyValidationError is the validation error returned by
// BuddySearchReply.Validate if the designated constraints aren't met.
type BuddySearchReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuddySearchReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuddySearchReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuddySearchReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuddySearchReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuddySearchReplyValidationError) ErrorName() string { return "BuddySearchReplyValidationError" }

// Error satisfies the builtin error interface
func (e BuddySearchReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuddySearchReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuddySearchReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuddySearchReplyValidationError{}

// Validate checks the field values on BuddyInvitationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BuddyInvitationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuddyInvitationRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BuddyInvitationRequestMultiError, or nil if none found.
func (m *BuddyInvitationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BuddyInvitationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetToUserUid()) < 1 {
		err := BuddyInvitationRequestValidationError{
			field:  "ToUserUid",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Message

	if len(errors) > 0 {
		return BuddyInvitationRequestMultiError(errors)
	}

	return nil
}

// BuddyInvitationRequestMultiError is an error wrapping multiple validation
// errors returned by BuddyInvitationRequest.ValidateAll() if the designated
// constraints aren't met.
type BuddyInvitationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuddyInvitationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuddyInvitationRequestMultiError) AllErrors() []error { return m }

// BuddyInvitationRequestValidationError is the validation error returned by
// BuddyInvitationRequest.Validate if the designated constraints aren't met.
type BuddyInvitationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuddyInvitationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuddyInvitationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuddyInvitationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuddyInvitationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuddyInvitationRequestValidationError) ErrorName() string {
	return "BuddyInvitationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BuddyInvitationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuddyInvitationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuddyInvitationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuddyInvitationRequestValidationError{}

// Validate checks the field values on BuddyInvitationReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BuddyInvitationReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuddyInvitationReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BuddyInvitationReplyMultiError, or nil if none found.
func (m *BuddyInvitationReply) ValidateAll() error {
	return m.validate(true)
}

func (m *BuddyInvitationReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	if len(errors) > 0 {
		return BuddyInvitationReplyMultiError(errors)
	}

	return nil
}

// BuddyInvitationReplyMultiError is an error wrapping multiple validation
// errors returned by BuddyInvitationReply.ValidateAll() if the designated
// constraints aren't met.
type BuddyInvitationReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuddyInvitationReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuddyInvitationReplyMultiError) AllErrors() []error { return m }

// BuddyInvitationReplyValidationError is the validation error returned by
// BuddyInvitationReply.Validate if the designated constraints aren't met.
type BuddyInvitationReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuddyInvitationReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuddyInvitationReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuddyInvitationReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuddyInvitationReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuddyInvitationReplyValidationError) ErrorName() string {
	return "BuddyInvitationReplyValidationError"
}

// Error satisfies the builtin error interface
func (e BuddyInvitationReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuddyInvitationReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuddyInvitationReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuddyInvitationReplyValidationError{}

// Validate checks the field values on CheckUserPrivacyPasswordRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckUserPrivacyPasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckUserPrivacyPasswordRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckUserPrivacyPasswordRequestMultiError, or nil if none found.
func (m *CheckUserPrivacyPasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckUserPrivacyPasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Password

	if len(errors) > 0 {
		return CheckUserPrivacyPasswordRequestMultiError(errors)
	}

	return nil
}

// CheckUserPrivacyPasswordRequestMultiError is an error wrapping multiple
// validation errors returned by CheckUserPrivacyPasswordRequest.ValidateAll()
// if the designated constraints aren't met.
type CheckUserPrivacyPasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckUserPrivacyPasswordRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckUserPrivacyPasswordRequestMultiError) AllErrors() []error { return m }

// CheckUserPrivacyPasswordRequestValidationError is the validation error
// returned by CheckUserPrivacyPasswordRequest.Validate if the designated
// constraints aren't met.
type CheckUserPrivacyPasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckUserPrivacyPasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckUserPrivacyPasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckUserPrivacyPasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckUserPrivacyPasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckUserPrivacyPasswordRequestValidationError) ErrorName() string {
	return "CheckUserPrivacyPasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckUserPrivacyPasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckUserPrivacyPasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckUserPrivacyPasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckUserPrivacyPasswordRequestValidationError{}

// Validate checks the field values on CheckUserPrivacyPasswordReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckUserPrivacyPasswordReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckUserPrivacyPasswordReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CheckUserPrivacyPasswordReplyMultiError, or nil if none found.
func (m *CheckUserPrivacyPasswordReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckUserPrivacyPasswordReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckUserPrivacyPasswordReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckUserPrivacyPasswordReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckUserPrivacyPasswordReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckUserPrivacyPasswordReplyMultiError(errors)
	}

	return nil
}

// CheckUserPrivacyPasswordReplyMultiError is an error wrapping multiple
// validation errors returned by CheckUserPrivacyPasswordReply.ValidateAll()
// if the designated constraints aren't met.
type CheckUserPrivacyPasswordReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckUserPrivacyPasswordReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckUserPrivacyPasswordReplyMultiError) AllErrors() []error { return m }

// CheckUserPrivacyPasswordReplyValidationError is the validation error
// returned by CheckUserPrivacyPasswordReply.Validate if the designated
// constraints aren't met.
type CheckUserPrivacyPasswordReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckUserPrivacyPasswordReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckUserPrivacyPasswordReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckUserPrivacyPasswordReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckUserPrivacyPasswordReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckUserPrivacyPasswordReplyValidationError) ErrorName() string {
	return "CheckUserPrivacyPasswordReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CheckUserPrivacyPasswordReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckUserPrivacyPasswordReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckUserPrivacyPasswordReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckUserPrivacyPasswordReplyValidationError{}

// Validate checks the field values on ChangeUserPrivacyPasswordRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ChangeUserPrivacyPasswordRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeUserPrivacyPasswordRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ChangeUserPrivacyPasswordRequestMultiError, or nil if none found.
func (m *ChangeUserPrivacyPasswordRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeUserPrivacyPasswordRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OldPassword

	// no validation rules for NewPassword

	// no validation rules for ConfirmPassword

	if len(errors) > 0 {
		return ChangeUserPrivacyPasswordRequestMultiError(errors)
	}

	return nil
}

// ChangeUserPrivacyPasswordRequestMultiError is an error wrapping multiple
// validation errors returned by
// ChangeUserPrivacyPasswordRequest.ValidateAll() if the designated
// constraints aren't met.
type ChangeUserPrivacyPasswordRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeUserPrivacyPasswordRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeUserPrivacyPasswordRequestMultiError) AllErrors() []error { return m }

// ChangeUserPrivacyPasswordRequestValidationError is the validation error
// returned by ChangeUserPrivacyPasswordRequest.Validate if the designated
// constraints aren't met.
type ChangeUserPrivacyPasswordRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeUserPrivacyPasswordRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeUserPrivacyPasswordRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeUserPrivacyPasswordRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeUserPrivacyPasswordRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeUserPrivacyPasswordRequestValidationError) ErrorName() string {
	return "ChangeUserPrivacyPasswordRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeUserPrivacyPasswordRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeUserPrivacyPasswordRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeUserPrivacyPasswordRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeUserPrivacyPasswordRequestValidationError{}

// Validate checks the field values on ChangeUserPrivacyPasswordReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeUserPrivacyPasswordReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeUserPrivacyPasswordReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ChangeUserPrivacyPasswordReplyMultiError, or nil if none found.
func (m *ChangeUserPrivacyPasswordReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeUserPrivacyPasswordReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return ChangeUserPrivacyPasswordReplyMultiError(errors)
	}

	return nil
}

// ChangeUserPrivacyPasswordReplyMultiError is an error wrapping multiple
// validation errors returned by ChangeUserPrivacyPasswordReply.ValidateAll()
// if the designated constraints aren't met.
type ChangeUserPrivacyPasswordReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeUserPrivacyPasswordReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeUserPrivacyPasswordReplyMultiError) AllErrors() []error { return m }

// ChangeUserPrivacyPasswordReplyValidationError is the validation error
// returned by ChangeUserPrivacyPasswordReply.Validate if the designated
// constraints aren't met.
type ChangeUserPrivacyPasswordReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeUserPrivacyPasswordReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeUserPrivacyPasswordReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeUserPrivacyPasswordReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeUserPrivacyPasswordReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeUserPrivacyPasswordReplyValidationError) ErrorName() string {
	return "ChangeUserPrivacyPasswordReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeUserPrivacyPasswordReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeUserPrivacyPasswordReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeUserPrivacyPasswordReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeUserPrivacyPasswordReplyValidationError{}

// Validate checks the field values on GetUserVipInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserVipInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserVipInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserVipInfoRequestMultiError, or nil if none found.
func (m *GetUserVipInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserVipInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetUserVipInfoRequestMultiError(errors)
	}

	return nil
}

// GetUserVipInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetUserVipInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserVipInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserVipInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserVipInfoRequestMultiError) AllErrors() []error { return m }

// GetUserVipInfoRequestValidationError is the validation error returned by
// GetUserVipInfoRequest.Validate if the designated constraints aren't met.
type GetUserVipInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserVipInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserVipInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserVipInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserVipInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserVipInfoRequestValidationError) ErrorName() string {
	return "GetUserVipInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserVipInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserVipInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserVipInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserVipInfoRequestValidationError{}

// Validate checks the field values on GetUserVipInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserVipInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserVipInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserVipInfoReplyMultiError, or nil if none found.
func (m *GetUserVipInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserVipInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserVipInfoReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserVipInfoReplyValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserVipInfoReplyValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserVipInfoReplyMultiError(errors)
	}

	return nil
}

// GetUserVipInfoReplyMultiError is an error wrapping multiple validation
// errors returned by GetUserVipInfoReply.ValidateAll() if the designated
// constraints aren't met.
type GetUserVipInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserVipInfoReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserVipInfoReplyMultiError) AllErrors() []error { return m }

// GetUserVipInfoReplyValidationError is the validation error returned by
// GetUserVipInfoReply.Validate if the designated constraints aren't met.
type GetUserVipInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserVipInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserVipInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserVipInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserVipInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserVipInfoReplyValidationError) ErrorName() string {
	return "GetUserVipInfoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserVipInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserVipInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserVipInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserVipInfoReplyValidationError{}

// Validate checks the field values on PushTokenRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PushTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PushTokenRequestMultiError, or nil if none found.
func (m *PushTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PushTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TimezoneName

	// no validation rules for TimezoneOffset

	// no validation rules for Locale

	// no validation rules for DeviceId

	// no validation rules for Platform

	// no validation rules for SystemVersion

	// no validation rules for AppVersion

	// no validation rules for DeviceBrand

	// no validation rules for FcmToken

	// no validation rules for ApnsToken

	// no validation rules for PushServiceType

	// no validation rules for JpushRegistrationId

	// no validation rules for RecommendedPushChannel

	// no validation rules for PushPermissionGranted

	if len(errors) > 0 {
		return PushTokenRequestMultiError(errors)
	}

	return nil
}

// PushTokenRequestMultiError is an error wrapping multiple validation errors
// returned by PushTokenRequest.ValidateAll() if the designated constraints
// aren't met.
type PushTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushTokenRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushTokenRequestMultiError) AllErrors() []error { return m }

// PushTokenRequestValidationError is the validation error returned by
// PushTokenRequest.Validate if the designated constraints aren't met.
type PushTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushTokenRequestValidationError) ErrorName() string { return "PushTokenRequestValidationError" }

// Error satisfies the builtin error interface
func (e PushTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushTokenRequestValidationError{}

// Validate checks the field values on PushTokenReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PushTokenReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushTokenReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PushTokenReplyMultiError,
// or nil if none found.
func (m *PushTokenReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PushTokenReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return PushTokenReplyMultiError(errors)
	}

	return nil
}

// PushTokenReplyMultiError is an error wrapping multiple validation errors
// returned by PushTokenReply.ValidateAll() if the designated constraints
// aren't met.
type PushTokenReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushTokenReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushTokenReplyMultiError) AllErrors() []error { return m }

// PushTokenReplyValidationError is the validation error returned by
// PushTokenReply.Validate if the designated constraints aren't met.
type PushTokenReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushTokenReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushTokenReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushTokenReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushTokenReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushTokenReplyValidationError) ErrorName() string { return "PushTokenReplyValidationError" }

// Error satisfies the builtin error interface
func (e PushTokenReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushTokenReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushTokenReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushTokenReplyValidationError{}

// Validate checks the field values on RegisterReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegisterReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegisterReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegisterReply_DataMultiError, or nil if none found.
func (m *RegisterReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *RegisterReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for ExpireAt

	if len(errors) > 0 {
		return RegisterReply_DataMultiError(errors)
	}

	return nil
}

// RegisterReply_DataMultiError is an error wrapping multiple validation errors
// returned by RegisterReply_Data.ValidateAll() if the designated constraints
// aren't met.
type RegisterReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegisterReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegisterReply_DataMultiError) AllErrors() []error { return m }

// RegisterReply_DataValidationError is the validation error returned by
// RegisterReply_Data.Validate if the designated constraints aren't met.
type RegisterReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegisterReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegisterReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegisterReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegisterReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegisterReply_DataValidationError) ErrorName() string {
	return "RegisterReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e RegisterReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegisterReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegisterReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegisterReply_DataValidationError{}

// Validate checks the field values on LoginReply_Data with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LoginReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoginReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoginReply_DataMultiError, or nil if none found.
func (m *LoginReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *LoginReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for ExpireAt

	// no validation rules for UserId

	// no validation rules for Name

	// no validation rules for AvatarUrl

	// no validation rules for IsSystemAdmin

	// no validation rules for RoleType

	// no validation rules for Phone

	// no validation rules for Email

	// no validation rules for Status

	// no validation rules for Desc

	// no validation rules for Gender

	// no validation rules for IsSetPwd

	// no validation rules for IsVip

	// no validation rules for IsSetPrivacy

	// no validation rules for Uid

	if len(errors) > 0 {
		return LoginReply_DataMultiError(errors)
	}

	return nil
}

// LoginReply_DataMultiError is an error wrapping multiple validation errors
// returned by LoginReply_Data.ValidateAll() if the designated constraints
// aren't met.
type LoginReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoginReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoginReply_DataMultiError) AllErrors() []error { return m }

// LoginReply_DataValidationError is the validation error returned by
// LoginReply_Data.Validate if the designated constraints aren't met.
type LoginReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoginReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoginReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoginReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoginReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoginReply_DataValidationError) ErrorName() string { return "LoginReply_DataValidationError" }

// Error satisfies the builtin error interface
func (e LoginReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoginReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoginReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoginReply_DataValidationError{}

// Validate checks the field values on GetUserProfileReply_Stats with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserProfileReply_Stats) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserProfileReply_Stats with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserProfileReply_StatsMultiError, or nil if none found.
func (m *GetUserProfileReply_Stats) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserProfileReply_Stats) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AllHabits

	// no validation rules for OnTrackHabits

	// no validation rules for DoneHabits

	if len(errors) > 0 {
		return GetUserProfileReply_StatsMultiError(errors)
	}

	return nil
}

// GetUserProfileReply_StatsMultiError is an error wrapping multiple validation
// errors returned by GetUserProfileReply_Stats.ValidateAll() if the
// designated constraints aren't met.
type GetUserProfileReply_StatsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserProfileReply_StatsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserProfileReply_StatsMultiError) AllErrors() []error { return m }

// GetUserProfileReply_StatsValidationError is the validation error returned by
// GetUserProfileReply_Stats.Validate if the designated constraints aren't met.
type GetUserProfileReply_StatsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserProfileReply_StatsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserProfileReply_StatsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserProfileReply_StatsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserProfileReply_StatsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserProfileReply_StatsValidationError) ErrorName() string {
	return "GetUserProfileReply_StatsValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserProfileReply_StatsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserProfileReply_Stats.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserProfileReply_StatsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserProfileReply_StatsValidationError{}

// Validate checks the field values on GetUserProfileReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserProfileReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserProfileReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserProfileReply_DataMultiError, or nil if none found.
func (m *GetUserProfileReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserProfileReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Username

	// no validation rules for Phone

	// no validation rules for AvatarUrl

	// no validation rules for Desc

	if all {
		switch v := interface{}(m.GetStats()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserProfileReply_DataValidationError{
					field:  "Stats",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserProfileReply_DataValidationError{
					field:  "Stats",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStats()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserProfileReply_DataValidationError{
				field:  "Stats",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	if len(errors) > 0 {
		return GetUserProfileReply_DataMultiError(errors)
	}

	return nil
}

// GetUserProfileReply_DataMultiError is an error wrapping multiple validation
// errors returned by GetUserProfileReply_Data.ValidateAll() if the designated
// constraints aren't met.
type GetUserProfileReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserProfileReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserProfileReply_DataMultiError) AllErrors() []error { return m }

// GetUserProfileReply_DataValidationError is the validation error returned by
// GetUserProfileReply_Data.Validate if the designated constraints aren't met.
type GetUserProfileReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserProfileReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserProfileReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserProfileReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserProfileReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserProfileReply_DataValidationError) ErrorName() string {
	return "GetUserProfileReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserProfileReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserProfileReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserProfileReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserProfileReply_DataValidationError{}

// Validate checks the field values on UpdateUserProfileReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserProfileReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserProfileReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserProfileReply_DataMultiError, or nil if none found.
func (m *UpdateUserProfileReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserProfileReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Username

	// no validation rules for AvatarUrl

	// no validation rules for Desc

	if len(errors) > 0 {
		return UpdateUserProfileReply_DataMultiError(errors)
	}

	return nil
}

// UpdateUserProfileReply_DataMultiError is an error wrapping multiple
// validation errors returned by UpdateUserProfileReply_Data.ValidateAll() if
// the designated constraints aren't met.
type UpdateUserProfileReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserProfileReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserProfileReply_DataMultiError) AllErrors() []error { return m }

// UpdateUserProfileReply_DataValidationError is the validation error returned
// by UpdateUserProfileReply_Data.Validate if the designated constraints
// aren't met.
type UpdateUserProfileReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserProfileReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserProfileReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserProfileReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserProfileReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserProfileReply_DataValidationError) ErrorName() string {
	return "UpdateUserProfileReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserProfileReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserProfileReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserProfileReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserProfileReply_DataValidationError{}

// Validate checks the field values on RefreshTokenReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RefreshTokenReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RefreshTokenReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RefreshTokenReply_DataMultiError, or nil if none found.
func (m *RefreshTokenReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *RefreshTokenReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for ExpireAt

	if len(errors) > 0 {
		return RefreshTokenReply_DataMultiError(errors)
	}

	return nil
}

// RefreshTokenReply_DataMultiError is an error wrapping multiple validation
// errors returned by RefreshTokenReply_Data.ValidateAll() if the designated
// constraints aren't met.
type RefreshTokenReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RefreshTokenReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RefreshTokenReply_DataMultiError) AllErrors() []error { return m }

// RefreshTokenReply_DataValidationError is the validation error returned by
// RefreshTokenReply_Data.Validate if the designated constraints aren't met.
type RefreshTokenReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RefreshTokenReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RefreshTokenReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RefreshTokenReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RefreshTokenReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RefreshTokenReply_DataValidationError) ErrorName() string {
	return "RefreshTokenReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e RefreshTokenReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRefreshTokenReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RefreshTokenReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RefreshTokenReply_DataValidationError{}

// Validate checks the field values on GetUserSettingReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserSettingReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserSettingReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserSettingReply_DataMultiError, or nil if none found.
func (m *GetUserSettingReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserSettingReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AwardImageUrl

	// no validation rules for AwardImageUrlWithDomain

	if len(errors) > 0 {
		return GetUserSettingReply_DataMultiError(errors)
	}

	return nil
}

// GetUserSettingReply_DataMultiError is an error wrapping multiple validation
// errors returned by GetUserSettingReply_Data.ValidateAll() if the designated
// constraints aren't met.
type GetUserSettingReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserSettingReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserSettingReply_DataMultiError) AllErrors() []error { return m }

// GetUserSettingReply_DataValidationError is the validation error returned by
// GetUserSettingReply_Data.Validate if the designated constraints aren't met.
type GetUserSettingReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserSettingReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserSettingReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserSettingReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserSettingReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserSettingReply_DataValidationError) ErrorName() string {
	return "GetUserSettingReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserSettingReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserSettingReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserSettingReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserSettingReply_DataValidationError{}

// Validate checks the field values on BuddySearchReply_User with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BuddySearchReply_User) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuddySearchReply_User with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BuddySearchReply_UserMultiError, or nil if none found.
func (m *BuddySearchReply_User) ValidateAll() error {
	return m.validate(true)
}

func (m *BuddySearchReply_User) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Nickname

	// no validation rules for AvatarUrl

	// no validation rules for Description

	// no validation rules for HabitCount

	// no validation rules for Status

	if len(errors) > 0 {
		return BuddySearchReply_UserMultiError(errors)
	}

	return nil
}

// BuddySearchReply_UserMultiError is an error wrapping multiple validation
// errors returned by BuddySearchReply_User.ValidateAll() if the designated
// constraints aren't met.
type BuddySearchReply_UserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuddySearchReply_UserMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuddySearchReply_UserMultiError) AllErrors() []error { return m }

// BuddySearchReply_UserValidationError is the validation error returned by
// BuddySearchReply_User.Validate if the designated constraints aren't met.
type BuddySearchReply_UserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuddySearchReply_UserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuddySearchReply_UserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuddySearchReply_UserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuddySearchReply_UserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuddySearchReply_UserValidationError) ErrorName() string {
	return "BuddySearchReply_UserValidationError"
}

// Error satisfies the builtin error interface
func (e BuddySearchReply_UserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuddySearchReply_User.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuddySearchReply_UserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuddySearchReply_UserValidationError{}

// Validate checks the field values on BuddySearchReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BuddySearchReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BuddySearchReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BuddySearchReply_DataMultiError, or nil if none found.
func (m *BuddySearchReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *BuddySearchReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BuddySearchReply_DataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BuddySearchReply_DataValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BuddySearchReply_DataValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for CanAddBuddy

	// no validation rules for StatusMessage

	if len(errors) > 0 {
		return BuddySearchReply_DataMultiError(errors)
	}

	return nil
}

// BuddySearchReply_DataMultiError is an error wrapping multiple validation
// errors returned by BuddySearchReply_Data.ValidateAll() if the designated
// constraints aren't met.
type BuddySearchReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BuddySearchReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BuddySearchReply_DataMultiError) AllErrors() []error { return m }

// BuddySearchReply_DataValidationError is the validation error returned by
// BuddySearchReply_Data.Validate if the designated constraints aren't met.
type BuddySearchReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BuddySearchReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BuddySearchReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BuddySearchReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BuddySearchReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BuddySearchReply_DataValidationError) ErrorName() string {
	return "BuddySearchReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e BuddySearchReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBuddySearchReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BuddySearchReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BuddySearchReply_DataValidationError{}

// Validate checks the field values on CheckUserPrivacyPasswordReply_Data with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckUserPrivacyPasswordReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckUserPrivacyPasswordReply_Data
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckUserPrivacyPasswordReply_DataMultiError, or nil if none found.
func (m *CheckUserPrivacyPasswordReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckUserPrivacyPasswordReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsPass

	if len(errors) > 0 {
		return CheckUserPrivacyPasswordReply_DataMultiError(errors)
	}

	return nil
}

// CheckUserPrivacyPasswordReply_DataMultiError is an error wrapping multiple
// validation errors returned by
// CheckUserPrivacyPasswordReply_Data.ValidateAll() if the designated
// constraints aren't met.
type CheckUserPrivacyPasswordReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckUserPrivacyPasswordReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckUserPrivacyPasswordReply_DataMultiError) AllErrors() []error { return m }

// CheckUserPrivacyPasswordReply_DataValidationError is the validation error
// returned by CheckUserPrivacyPasswordReply_Data.Validate if the designated
// constraints aren't met.
type CheckUserPrivacyPasswordReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckUserPrivacyPasswordReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckUserPrivacyPasswordReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckUserPrivacyPasswordReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckUserPrivacyPasswordReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckUserPrivacyPasswordReply_DataValidationError) ErrorName() string {
	return "CheckUserPrivacyPasswordReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e CheckUserPrivacyPasswordReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckUserPrivacyPasswordReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckUserPrivacyPasswordReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckUserPrivacyPasswordReply_DataValidationError{}

// Validate checks the field values on GetUserVipInfoReply_VipDetail with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserVipInfoReply_VipDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserVipInfoReply_VipDetail with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUserVipInfoReply_VipDetailMultiError, or nil if none found.
func (m *GetUserVipInfoReply_VipDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserVipInfoReply_VipDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Price

	// no validation rules for DiscountPrice

	if len(errors) > 0 {
		return GetUserVipInfoReply_VipDetailMultiError(errors)
	}

	return nil
}

// GetUserVipInfoReply_VipDetailMultiError is an error wrapping multiple
// validation errors returned by GetUserVipInfoReply_VipDetail.ValidateAll()
// if the designated constraints aren't met.
type GetUserVipInfoReply_VipDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserVipInfoReply_VipDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserVipInfoReply_VipDetailMultiError) AllErrors() []error { return m }

// GetUserVipInfoReply_VipDetailValidationError is the validation error
// returned by GetUserVipInfoReply_VipDetail.Validate if the designated
// constraints aren't met.
type GetUserVipInfoReply_VipDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserVipInfoReply_VipDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserVipInfoReply_VipDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserVipInfoReply_VipDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserVipInfoReply_VipDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserVipInfoReply_VipDetailValidationError) ErrorName() string {
	return "GetUserVipInfoReply_VipDetailValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserVipInfoReply_VipDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserVipInfoReply_VipDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserVipInfoReply_VipDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserVipInfoReply_VipDetailValidationError{}

// Validate checks the field values on GetUserVipInfoReply_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserVipInfoReply_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserVipInfoReply_Data with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserVipInfoReply_DataMultiError, or nil if none found.
func (m *GetUserVipInfoReply_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserVipInfoReply_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsVip

	// no validation rules for RemainDays

	if all {
		switch v := interface{}(m.GetMonthPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserVipInfoReply_DataValidationError{
					field:  "MonthPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserVipInfoReply_DataValidationError{
					field:  "MonthPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserVipInfoReply_DataValidationError{
				field:  "MonthPrice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetQuarterPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserVipInfoReply_DataValidationError{
					field:  "QuarterPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserVipInfoReply_DataValidationError{
					field:  "QuarterPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuarterPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserVipInfoReply_DataValidationError{
				field:  "QuarterPrice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetYearPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserVipInfoReply_DataValidationError{
					field:  "YearPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserVipInfoReply_DataValidationError{
					field:  "YearPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetYearPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserVipInfoReply_DataValidationError{
				field:  "YearPrice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserVipInfoReply_DataMultiError(errors)
	}

	return nil
}

// GetUserVipInfoReply_DataMultiError is an error wrapping multiple validation
// errors returned by GetUserVipInfoReply_Data.ValidateAll() if the designated
// constraints aren't met.
type GetUserVipInfoReply_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserVipInfoReply_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserVipInfoReply_DataMultiError) AllErrors() []error { return m }

// GetUserVipInfoReply_DataValidationError is the validation error returned by
// GetUserVipInfoReply_Data.Validate if the designated constraints aren't met.
type GetUserVipInfoReply_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserVipInfoReply_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserVipInfoReply_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserVipInfoReply_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserVipInfoReply_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserVipInfoReply_DataValidationError) ErrorName() string {
	return "GetUserVipInfoReply_DataValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserVipInfoReply_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserVipInfoReply_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserVipInfoReply_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserVipInfoReply_DataValidationError{}
