// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: user/v1/user.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RegisterRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserName        string                 `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Password        string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	ConfirmPassword string                 `protobuf:"bytes,3,opt,name=confirm_password,json=confirmPassword,proto3" json:"confirm_password,omitempty"`
	VerifyCode      string                 `protobuf:"bytes,4,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"`
	Phone           string                 `protobuf:"bytes,5,opt,name=Phone,proto3" json:"Phone,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RegisterRequest) Reset() {
	*x = RegisterRequest{}
	mi := &file_user_v1_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterRequest) ProtoMessage() {}

func (x *RegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterRequest.ProtoReflect.Descriptor instead.
func (*RegisterRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *RegisterRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RegisterRequest) GetConfirmPassword() string {
	if x != nil {
		return x.ConfirmPassword
	}
	return ""
}

func (x *RegisterRequest) GetVerifyCode() string {
	if x != nil {
		return x.VerifyCode
	}
	return ""
}

func (x *RegisterRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

type RegisterReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *RegisterReply_Data    `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterReply) Reset() {
	*x = RegisterReply{}
	mi := &file_user_v1_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterReply) ProtoMessage() {}

func (x *RegisterReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterReply.ProtoReflect.Descriptor instead.
func (*RegisterReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RegisterReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RegisterReply) GetData() *RegisterReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type LoginRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 核心认证参数（字段1-6）
	Phone      string `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`                             // 手机号登录
	Password   string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`                       // 密码认证
	Email      string `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`                             // 邮箱登录
	VerifyCode string `protobuf:"bytes,4,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"` // 验证码认证
	LoginType  int32  `protobuf:"varint,5,opt,name=login_type,json=loginType,proto3" json:"login_type,omitempty"`   // 登录方式：0=密码，1=验证码，2=Google，3=Apple
	PassType   int32  `protobuf:"varint,6,opt,name=pass_type,json=passType,proto3" json:"pass_type,omitempty"`      // 认证类型：0=密码，1=验证码
	// Firebase认证参数（字段7）
	ThirdPartyToken string `protobuf:"bytes,7,opt,name=third_party_token,json=thirdPartyToken,proto3" json:"third_party_token,omitempty"` // Firebase ID Token
	// 环境信息参数（字段8-10）
	TimezoneName   string `protobuf:"bytes,8,opt,name=timezone_name,json=timezoneName,proto3" json:"timezone_name,omitempty"`       // 时区名称
	TimezoneOffset string `protobuf:"bytes,9,opt,name=timezone_offset,json=timezoneOffset,proto3" json:"timezone_offset,omitempty"` // 时区偏移量
	Locale         string `protobuf:"bytes,10,opt,name=locale,proto3" json:"locale,omitempty"`                                      // 语言区域设置
	Platform       string `protobuf:"bytes,12,opt,name=platform,proto3" json:"platform,omitempty"`                                  // 设备平台：ios, android, web
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	mi := &file_user_v1_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *LoginRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *LoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *LoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *LoginRequest) GetVerifyCode() string {
	if x != nil {
		return x.VerifyCode
	}
	return ""
}

func (x *LoginRequest) GetLoginType() int32 {
	if x != nil {
		return x.LoginType
	}
	return 0
}

func (x *LoginRequest) GetPassType() int32 {
	if x != nil {
		return x.PassType
	}
	return 0
}

func (x *LoginRequest) GetThirdPartyToken() string {
	if x != nil {
		return x.ThirdPartyToken
	}
	return ""
}

func (x *LoginRequest) GetTimezoneName() string {
	if x != nil {
		return x.TimezoneName
	}
	return ""
}

func (x *LoginRequest) GetTimezoneOffset() string {
	if x != nil {
		return x.TimezoneOffset
	}
	return ""
}

func (x *LoginRequest) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *LoginRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type LoginReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *LoginReply_Data       `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginReply) Reset() {
	*x = LoginReply{}
	mi := &file_user_v1_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReply) ProtoMessage() {}

func (x *LoginReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReply.ProtoReflect.Descriptor instead.
func (*LoginReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *LoginReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LoginReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *LoginReply) GetData() *LoginReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type LogoutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"` // 设备唯一标识
	Platform      string                 `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`                 // 设备平台：ios, android, web
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutRequest) Reset() {
	*x = LogoutRequest{}
	mi := &file_user_v1_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutRequest) ProtoMessage() {}

func (x *LogoutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutRequest.ProtoReflect.Descriptor instead.
func (*LogoutRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *LogoutRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *LogoutRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type LogoutReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutReply) Reset() {
	*x = LogoutReply{}
	mi := &file_user_v1_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutReply) ProtoMessage() {}

func (x *LogoutReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutReply.ProtoReflect.Descriptor instead.
func (*LogoutReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *LogoutReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LogoutReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetUserProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserProfileRequest) Reset() {
	*x = GetUserProfileRequest{}
	mi := &file_user_v1_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileRequest) ProtoMessage() {}

func (x *GetUserProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileRequest.ProtoReflect.Descriptor instead.
func (*GetUserProfileRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{6}
}

type GetUserProfileReply struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetUserProfileReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserProfileReply) Reset() {
	*x = GetUserProfileReply{}
	mi := &file_user_v1_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserProfileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileReply) ProtoMessage() {}

func (x *GetUserProfileReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileReply.ProtoReflect.Descriptor instead.
func (*GetUserProfileReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserProfileReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserProfileReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserProfileReply) GetData() *GetUserProfileReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ForgetPasswordRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Phone           string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	VerifyCode      string                 `protobuf:"bytes,2,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"`
	Password        string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	ConfirmPassword string                 `protobuf:"bytes,4,opt,name=confirm_password,json=confirmPassword,proto3" json:"confirm_password,omitempty"`
	Email           string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ForgetPasswordRequest) Reset() {
	*x = ForgetPasswordRequest{}
	mi := &file_user_v1_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForgetPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForgetPasswordRequest) ProtoMessage() {}

func (x *ForgetPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForgetPasswordRequest.ProtoReflect.Descriptor instead.
func (*ForgetPasswordRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{8}
}

func (x *ForgetPasswordRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ForgetPasswordRequest) GetVerifyCode() string {
	if x != nil {
		return x.VerifyCode
	}
	return ""
}

func (x *ForgetPasswordRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ForgetPasswordRequest) GetConfirmPassword() string {
	if x != nil {
		return x.ConfirmPassword
	}
	return ""
}

func (x *ForgetPasswordRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type ForgetPasswordReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForgetPasswordReply) Reset() {
	*x = ForgetPasswordReply{}
	mi := &file_user_v1_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForgetPasswordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForgetPasswordReply) ProtoMessage() {}

func (x *ForgetPasswordReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForgetPasswordReply.ProtoReflect.Descriptor instead.
func (*ForgetPasswordReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{9}
}

func (x *ForgetPasswordReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ForgetPasswordReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UpdateUserProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	AvatarUrl     string                 `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	Desc          string                 `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserProfileRequest) Reset() {
	*x = UpdateUserProfileRequest{}
	mi := &file_user_v1_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserProfileRequest) ProtoMessage() {}

func (x *UpdateUserProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserProfileRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserProfileRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateUserProfileRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *UpdateUserProfileRequest) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type UpdateUserProfileReply struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Code          int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *UpdateUserProfileReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserProfileReply) Reset() {
	*x = UpdateUserProfileReply{}
	mi := &file_user_v1_user_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserProfileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserProfileReply) ProtoMessage() {}

func (x *UpdateUserProfileReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserProfileReply.ProtoReflect.Descriptor instead.
func (*UpdateUserProfileReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateUserProfileReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserProfileReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateUserProfileReply) GetData() *UpdateUserProfileReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type FollowUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int32                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FollowUserRequest) Reset() {
	*x = FollowUserRequest{}
	mi := &file_user_v1_user_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FollowUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowUserRequest) ProtoMessage() {}

func (x *FollowUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowUserRequest.ProtoReflect.Descriptor instead.
func (*FollowUserRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{12}
}

func (x *FollowUserRequest) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type FollowUserReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FollowUserReply) Reset() {
	*x = FollowUserReply{}
	mi := &file_user_v1_user_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FollowUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FollowUserReply) ProtoMessage() {}

func (x *FollowUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FollowUserReply.ProtoReflect.Descriptor instead.
func (*FollowUserReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{13}
}

func (x *FollowUserReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FollowUserReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type UnfollowUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        int32                  `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnfollowUserRequest) Reset() {
	*x = UnfollowUserRequest{}
	mi := &file_user_v1_user_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnfollowUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfollowUserRequest) ProtoMessage() {}

func (x *UnfollowUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfollowUserRequest.ProtoReflect.Descriptor instead.
func (*UnfollowUserRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{14}
}

func (x *UnfollowUserRequest) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type UnfollowUserReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnfollowUserReply) Reset() {
	*x = UnfollowUserReply{}
	mi := &file_user_v1_user_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnfollowUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnfollowUserReply) ProtoMessage() {}

func (x *UnfollowUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnfollowUserReply.ProtoReflect.Descriptor instead.
func (*UnfollowUserReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{15}
}

func (x *UnfollowUserReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UnfollowUserReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type RefreshTokenRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	DeviceId               string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`                                             // 设备唯一标识
	Platform               string                 `protobuf:"bytes,2,opt,name=platform,proto3" json:"platform,omitempty"`                                                             // 设备平台：ios, android, web
	DeviceBrand            string                 `protobuf:"bytes,3,opt,name=device_brand,json=deviceBrand,proto3" json:"device_brand,omitempty"`                                    // 设备品牌
	FcmToken               string                 `protobuf:"bytes,4,opt,name=fcm_token,json=fcmToken,proto3" json:"fcm_token,omitempty"`                                             // FCM推送token
	ApnsToken              string                 `protobuf:"bytes,5,opt,name=apns_token,json=apnsToken,proto3" json:"apns_token,omitempty"`                                          // APNs推送token
	PushServiceType        string                 `protobuf:"bytes,6,opt,name=push_service_type,json=pushServiceType,proto3" json:"push_service_type,omitempty"`                      // 推送服务类型：jpush, fcm, apns
	JpushRegistrationId    string                 `protobuf:"bytes,7,opt,name=jpush_registration_id,json=jpushRegistrationId,proto3" json:"jpush_registration_id,omitempty"`          // 极光推送设备注册ID
	RecommendedPushChannel string                 `protobuf:"bytes,8,opt,name=recommended_push_channel,json=recommendedPushChannel,proto3" json:"recommended_push_channel,omitempty"` // 极光推送推荐渠道
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *RefreshTokenRequest) Reset() {
	*x = RefreshTokenRequest{}
	mi := &file_user_v1_user_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRequest) ProtoMessage() {}

func (x *RefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*RefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{16}
}

func (x *RefreshTokenRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *RefreshTokenRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *RefreshTokenRequest) GetDeviceBrand() string {
	if x != nil {
		return x.DeviceBrand
	}
	return ""
}

func (x *RefreshTokenRequest) GetFcmToken() string {
	if x != nil {
		return x.FcmToken
	}
	return ""
}

func (x *RefreshTokenRequest) GetApnsToken() string {
	if x != nil {
		return x.ApnsToken
	}
	return ""
}

func (x *RefreshTokenRequest) GetPushServiceType() string {
	if x != nil {
		return x.PushServiceType
	}
	return ""
}

func (x *RefreshTokenRequest) GetJpushRegistrationId() string {
	if x != nil {
		return x.JpushRegistrationId
	}
	return ""
}

func (x *RefreshTokenRequest) GetRecommendedPushChannel() string {
	if x != nil {
		return x.RecommendedPushChannel
	}
	return ""
}

type RefreshTokenReply struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *RefreshTokenReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenReply) Reset() {
	*x = RefreshTokenReply{}
	mi := &file_user_v1_user_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenReply) ProtoMessage() {}

func (x *RefreshTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenReply.ProtoReflect.Descriptor instead.
func (*RefreshTokenReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{17}
}

func (x *RefreshTokenReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RefreshTokenReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RefreshTokenReply) GetData() *RefreshTokenReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type UpdateUserAwardRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AwardImageUrl string                 `protobuf:"bytes,1,opt,name=award_image_url,json=awardImageUrl,proto3" json:"award_image_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserAwardRequest) Reset() {
	*x = UpdateUserAwardRequest{}
	mi := &file_user_v1_user_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserAwardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserAwardRequest) ProtoMessage() {}

func (x *UpdateUserAwardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserAwardRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserAwardRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateUserAwardRequest) GetAwardImageUrl() string {
	if x != nil {
		return x.AwardImageUrl
	}
	return ""
}

type UpdateUserAwardReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserAwardReply) Reset() {
	*x = UpdateUserAwardReply{}
	mi := &file_user_v1_user_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserAwardReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserAwardReply) ProtoMessage() {}

func (x *UpdateUserAwardReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserAwardReply.ProtoReflect.Descriptor instead.
func (*UpdateUserAwardReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{19}
}

func (x *UpdateUserAwardReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserAwardReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetUserSettingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Scene         string                 `protobuf:"bytes,1,opt,name=scene,proto3" json:"scene,omitempty"` // 场景
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSettingRequest) Reset() {
	*x = GetUserSettingRequest{}
	mi := &file_user_v1_user_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSettingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSettingRequest) ProtoMessage() {}

func (x *GetUserSettingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSettingRequest.ProtoReflect.Descriptor instead.
func (*GetUserSettingRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{20}
}

func (x *GetUserSettingRequest) GetScene() string {
	if x != nil {
		return x.Scene
	}
	return ""
}

type GetUserSettingReply struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetUserSettingReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserSettingReply) Reset() {
	*x = GetUserSettingReply{}
	mi := &file_user_v1_user_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSettingReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSettingReply) ProtoMessage() {}

func (x *GetUserSettingReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSettingReply.ProtoReflect.Descriptor instead.
func (*GetUserSettingReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{21}
}

func (x *GetUserSettingReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserSettingReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserSettingReply) GetData() *GetUserSettingReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ChangePhoneRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	VerifyCode    string                 `protobuf:"bytes,2,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangePhoneRequest) Reset() {
	*x = ChangePhoneRequest{}
	mi := &file_user_v1_user_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangePhoneRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePhoneRequest) ProtoMessage() {}

func (x *ChangePhoneRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePhoneRequest.ProtoReflect.Descriptor instead.
func (*ChangePhoneRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{22}
}

func (x *ChangePhoneRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ChangePhoneRequest) GetVerifyCode() string {
	if x != nil {
		return x.VerifyCode
	}
	return ""
}

type ChangePhoneReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangePhoneReply) Reset() {
	*x = ChangePhoneReply{}
	mi := &file_user_v1_user_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangePhoneReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePhoneReply) ProtoMessage() {}

func (x *ChangePhoneReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePhoneReply.ProtoReflect.Descriptor instead.
func (*ChangePhoneReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{23}
}

func (x *ChangePhoneReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChangePhoneReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ChangeEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	VerifyCode    string                 `protobuf:"bytes,2,opt,name=verify_code,json=verifyCode,proto3" json:"verify_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeEmailRequest) Reset() {
	*x = ChangeEmailRequest{}
	mi := &file_user_v1_user_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeEmailRequest) ProtoMessage() {}

func (x *ChangeEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeEmailRequest.ProtoReflect.Descriptor instead.
func (*ChangeEmailRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{24}
}

func (x *ChangeEmailRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ChangeEmailRequest) GetVerifyCode() string {
	if x != nil {
		return x.VerifyCode
	}
	return ""
}

type ChangeEmailReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeEmailReply) Reset() {
	*x = ChangeEmailReply{}
	mi := &file_user_v1_user_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeEmailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeEmailReply) ProtoMessage() {}

func (x *ChangeEmailReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeEmailReply.ProtoReflect.Descriptor instead.
func (*ChangeEmailReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{25}
}

func (x *ChangeEmailReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChangeEmailReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type ChangePasswordRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	OldPassword     string                 `protobuf:"bytes,1,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword     string                 `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	ConfirmPassword string                 `protobuf:"bytes,3,opt,name=confirm_password,json=confirmPassword,proto3" json:"confirm_password,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ChangePasswordRequest) Reset() {
	*x = ChangePasswordRequest{}
	mi := &file_user_v1_user_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangePasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePasswordRequest) ProtoMessage() {}

func (x *ChangePasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePasswordRequest.ProtoReflect.Descriptor instead.
func (*ChangePasswordRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{26}
}

func (x *ChangePasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *ChangePasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

func (x *ChangePasswordRequest) GetConfirmPassword() string {
	if x != nil {
		return x.ConfirmPassword
	}
	return ""
}

type ChangePasswordReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangePasswordReply) Reset() {
	*x = ChangePasswordReply{}
	mi := &file_user_v1_user_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangePasswordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePasswordReply) ProtoMessage() {}

func (x *ChangePasswordReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePasswordReply.ProtoReflect.Descriptor instead.
func (*ChangePasswordReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{27}
}

func (x *ChangePasswordReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChangePasswordReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type DeleteUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	mi := &file_user_v1_user_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{28}
}

func (x *DeleteUserRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *DeleteUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *DeleteUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type DeleteUserReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserReply) Reset() {
	*x = DeleteUserReply{}
	mi := &file_user_v1_user_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserReply) ProtoMessage() {}

func (x *DeleteUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserReply.ProtoReflect.Descriptor instead.
func (*DeleteUserReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{29}
}

func (x *DeleteUserReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteUserReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type CheckUserPrivacyPasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Password      string                 `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserPrivacyPasswordRequest) Reset() {
	*x = CheckUserPrivacyPasswordRequest{}
	mi := &file_user_v1_user_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserPrivacyPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPrivacyPasswordRequest) ProtoMessage() {}

func (x *CheckUserPrivacyPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPrivacyPasswordRequest.ProtoReflect.Descriptor instead.
func (*CheckUserPrivacyPasswordRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{30}
}

func (x *CheckUserPrivacyPasswordRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type CheckUserPrivacyPasswordReply struct {
	state         protoimpl.MessageState              `protogen:"open.v1"`
	Code          int32                               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *CheckUserPrivacyPasswordReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserPrivacyPasswordReply) Reset() {
	*x = CheckUserPrivacyPasswordReply{}
	mi := &file_user_v1_user_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserPrivacyPasswordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPrivacyPasswordReply) ProtoMessage() {}

func (x *CheckUserPrivacyPasswordReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPrivacyPasswordReply.ProtoReflect.Descriptor instead.
func (*CheckUserPrivacyPasswordReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{31}
}

func (x *CheckUserPrivacyPasswordReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckUserPrivacyPasswordReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckUserPrivacyPasswordReply) GetData() *CheckUserPrivacyPasswordReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type ChangeUserPrivacyPasswordRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	OldPassword     string                 `protobuf:"bytes,1,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword     string                 `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	ConfirmPassword string                 `protobuf:"bytes,3,opt,name=confirm_password,json=confirmPassword,proto3" json:"confirm_password,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ChangeUserPrivacyPasswordRequest) Reset() {
	*x = ChangeUserPrivacyPasswordRequest{}
	mi := &file_user_v1_user_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserPrivacyPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserPrivacyPasswordRequest) ProtoMessage() {}

func (x *ChangeUserPrivacyPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserPrivacyPasswordRequest.ProtoReflect.Descriptor instead.
func (*ChangeUserPrivacyPasswordRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{32}
}

func (x *ChangeUserPrivacyPasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *ChangeUserPrivacyPasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

func (x *ChangeUserPrivacyPasswordRequest) GetConfirmPassword() string {
	if x != nil {
		return x.ConfirmPassword
	}
	return ""
}

type ChangeUserPrivacyPasswordReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeUserPrivacyPasswordReply) Reset() {
	*x = ChangeUserPrivacyPasswordReply{}
	mi := &file_user_v1_user_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeUserPrivacyPasswordReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeUserPrivacyPasswordReply) ProtoMessage() {}

func (x *ChangeUserPrivacyPasswordReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeUserPrivacyPasswordReply.ProtoReflect.Descriptor instead.
func (*ChangeUserPrivacyPasswordReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{33}
}

func (x *ChangeUserPrivacyPasswordReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChangeUserPrivacyPasswordReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type GetUserVipInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserVipInfoRequest) Reset() {
	*x = GetUserVipInfoRequest{}
	mi := &file_user_v1_user_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserVipInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserVipInfoRequest) ProtoMessage() {}

func (x *GetUserVipInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserVipInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserVipInfoRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{34}
}

type GetUserVipInfoReply struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Data          *GetUserVipInfoReply_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserVipInfoReply) Reset() {
	*x = GetUserVipInfoReply{}
	mi := &file_user_v1_user_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserVipInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserVipInfoReply) ProtoMessage() {}

func (x *GetUserVipInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserVipInfoReply.ProtoReflect.Descriptor instead.
func (*GetUserVipInfoReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{35}
}

func (x *GetUserVipInfoReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserVipInfoReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GetUserVipInfoReply) GetData() *GetUserVipInfoReply_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

type PushTokenRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 环境信息参数（字段8-10）
	TimezoneName   string `protobuf:"bytes,8,opt,name=timezone_name,json=timezoneName,proto3" json:"timezone_name,omitempty"`       // 时区名称
	TimezoneOffset string `protobuf:"bytes,9,opt,name=timezone_offset,json=timezoneOffset,proto3" json:"timezone_offset,omitempty"` // 时区偏移量
	Locale         string `protobuf:"bytes,10,opt,name=locale,proto3" json:"locale,omitempty"`                                      // 语言区域设置
	// 基础设备信息参数（字段11-15）
	DeviceId      string `protobuf:"bytes,11,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`                // 设备唯一标识
	Platform      string `protobuf:"bytes,12,opt,name=platform,proto3" json:"platform,omitempty"`                                // 设备平台：ios, android, web
	SystemVersion string `protobuf:"bytes,13,opt,name=system_version,json=systemVersion,proto3" json:"system_version,omitempty"` // 系统版本
	AppVersion    string `protobuf:"bytes,14,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`          // 应用版本
	DeviceBrand   string `protobuf:"bytes,15,opt,name=device_brand,json=deviceBrand,proto3" json:"device_brand,omitempty"`       // 设备品牌
	// 推送相关参数（字段16-21）
	FcmToken               string `protobuf:"bytes,16,opt,name=fcm_token,json=fcmToken,proto3" json:"fcm_token,omitempty"`                                             // FCM推送token
	ApnsToken              string `protobuf:"bytes,17,opt,name=apns_token,json=apnsToken,proto3" json:"apns_token,omitempty"`                                          // APNs推送token
	PushServiceType        string `protobuf:"bytes,18,opt,name=push_service_type,json=pushServiceType,proto3" json:"push_service_type,omitempty"`                      // 推送服务类型：jpush, fcm, apns
	JpushRegistrationId    string `protobuf:"bytes,19,opt,name=jpush_registration_id,json=jpushRegistrationId,proto3" json:"jpush_registration_id,omitempty"`          // 极光推送设备注册ID
	RecommendedPushChannel string `protobuf:"bytes,20,opt,name=recommended_push_channel,json=recommendedPushChannel,proto3" json:"recommended_push_channel,omitempty"` // 极光推送推荐渠道
	PushPermissionGranted  bool   `protobuf:"varint,21,opt,name=push_permission_granted,json=pushPermissionGranted,proto3" json:"push_permission_granted,omitempty"`   // 推送权限状态
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *PushTokenRequest) Reset() {
	*x = PushTokenRequest{}
	mi := &file_user_v1_user_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushTokenRequest) ProtoMessage() {}

func (x *PushTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushTokenRequest.ProtoReflect.Descriptor instead.
func (*PushTokenRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{36}
}

func (x *PushTokenRequest) GetTimezoneName() string {
	if x != nil {
		return x.TimezoneName
	}
	return ""
}

func (x *PushTokenRequest) GetTimezoneOffset() string {
	if x != nil {
		return x.TimezoneOffset
	}
	return ""
}

func (x *PushTokenRequest) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *PushTokenRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PushTokenRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *PushTokenRequest) GetSystemVersion() string {
	if x != nil {
		return x.SystemVersion
	}
	return ""
}

func (x *PushTokenRequest) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *PushTokenRequest) GetDeviceBrand() string {
	if x != nil {
		return x.DeviceBrand
	}
	return ""
}

func (x *PushTokenRequest) GetFcmToken() string {
	if x != nil {
		return x.FcmToken
	}
	return ""
}

func (x *PushTokenRequest) GetApnsToken() string {
	if x != nil {
		return x.ApnsToken
	}
	return ""
}

func (x *PushTokenRequest) GetPushServiceType() string {
	if x != nil {
		return x.PushServiceType
	}
	return ""
}

func (x *PushTokenRequest) GetJpushRegistrationId() string {
	if x != nil {
		return x.JpushRegistrationId
	}
	return ""
}

func (x *PushTokenRequest) GetRecommendedPushChannel() string {
	if x != nil {
		return x.RecommendedPushChannel
	}
	return ""
}

func (x *PushTokenRequest) GetPushPermissionGranted() bool {
	if x != nil {
		return x.PushPermissionGranted
	}
	return false
}

type PushTokenReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushTokenReply) Reset() {
	*x = PushTokenReply{}
	mi := &file_user_v1_user_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushTokenReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushTokenReply) ProtoMessage() {}

func (x *PushTokenReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushTokenReply.ProtoReflect.Descriptor instead.
func (*PushTokenReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{37}
}

func (x *PushTokenReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PushTokenReply) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type RegisterReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	ExpireAt      int32                  `protobuf:"varint,2,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterReply_Data) Reset() {
	*x = RegisterReply_Data{}
	mi := &file_user_v1_user_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterReply_Data) ProtoMessage() {}

func (x *RegisterReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterReply_Data.ProtoReflect.Descriptor instead.
func (*RegisterReply_Data) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RegisterReply_Data) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *RegisterReply_Data) GetExpireAt() int32 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

type LoginReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	ExpireAt      int32                  `protobuf:"varint,2,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	UserId        int32                  `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	AvatarUrl     string                 `protobuf:"bytes,5,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	IsSystemAdmin bool                   `protobuf:"varint,6,opt,name=is_system_admin,json=isSystemAdmin,proto3" json:"is_system_admin,omitempty"`
	RoleType      int32                  `protobuf:"varint,7,opt,name=role_type,json=roleType,proto3" json:"role_type,omitempty"`
	Phone         string                 `protobuf:"bytes,8,opt,name=phone,proto3" json:"phone,omitempty"`
	Email         string                 `protobuf:"bytes,9,opt,name=email,proto3" json:"email,omitempty"`
	Status        int32                  `protobuf:"varint,10,opt,name=status,proto3" json:"status,omitempty"`
	Desc          string                 `protobuf:"bytes,11,opt,name=desc,proto3" json:"desc,omitempty"`
	Gender        int32                  `protobuf:"varint,12,opt,name=gender,proto3" json:"gender,omitempty"`
	IsSetPwd      bool                   `protobuf:"varint,13,opt,name=is_set_pwd,json=isSetPwd,proto3" json:"is_set_pwd,omitempty"`
	IsVip         bool                   `protobuf:"varint,14,opt,name=is_vip,json=isVip,proto3" json:"is_vip,omitempty"`
	IsSetPrivacy  bool                   `protobuf:"varint,15,opt,name=is_set_privacy,json=isSetPrivacy,proto3" json:"is_set_privacy,omitempty"`
	Uid           string                 `protobuf:"bytes,16,opt,name=uid,proto3" json:"uid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginReply_Data) Reset() {
	*x = LoginReply_Data{}
	mi := &file_user_v1_user_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReply_Data) ProtoMessage() {}

func (x *LoginReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReply_Data.ProtoReflect.Descriptor instead.
func (*LoginReply_Data) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{3, 0}
}

func (x *LoginReply_Data) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *LoginReply_Data) GetExpireAt() int32 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *LoginReply_Data) GetUserId() int32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LoginReply_Data) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LoginReply_Data) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *LoginReply_Data) GetIsSystemAdmin() bool {
	if x != nil {
		return x.IsSystemAdmin
	}
	return false
}

func (x *LoginReply_Data) GetRoleType() int32 {
	if x != nil {
		return x.RoleType
	}
	return 0
}

func (x *LoginReply_Data) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *LoginReply_Data) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *LoginReply_Data) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *LoginReply_Data) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *LoginReply_Data) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *LoginReply_Data) GetIsSetPwd() bool {
	if x != nil {
		return x.IsSetPwd
	}
	return false
}

func (x *LoginReply_Data) GetIsVip() bool {
	if x != nil {
		return x.IsVip
	}
	return false
}

func (x *LoginReply_Data) GetIsSetPrivacy() bool {
	if x != nil {
		return x.IsSetPrivacy
	}
	return false
}

func (x *LoginReply_Data) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

type GetUserProfileReply_Stats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AllHabits     int32                  `protobuf:"varint,1,opt,name=all_habits,json=allHabits,proto3" json:"all_habits,omitempty"`
	OnTrackHabits int32                  `protobuf:"varint,2,opt,name=on_track_habits,json=onTrackHabits,proto3" json:"on_track_habits,omitempty"`
	DoneHabits    int32                  `protobuf:"varint,3,opt,name=done_habits,json=doneHabits,proto3" json:"done_habits,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserProfileReply_Stats) Reset() {
	*x = GetUserProfileReply_Stats{}
	mi := &file_user_v1_user_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserProfileReply_Stats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileReply_Stats) ProtoMessage() {}

func (x *GetUserProfileReply_Stats) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileReply_Stats.ProtoReflect.Descriptor instead.
func (*GetUserProfileReply_Stats) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{7, 0}
}

func (x *GetUserProfileReply_Stats) GetAllHabits() int32 {
	if x != nil {
		return x.AllHabits
	}
	return 0
}

func (x *GetUserProfileReply_Stats) GetOnTrackHabits() int32 {
	if x != nil {
		return x.OnTrackHabits
	}
	return 0
}

func (x *GetUserProfileReply_Stats) GetDoneHabits() int32 {
	if x != nil {
		return x.DoneHabits
	}
	return 0
}

type GetUserProfileReply_Data struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Username      string                     `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Phone         string                     `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`
	AvatarUrl     string                     `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	Desc          string                     `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	Stats         *GetUserProfileReply_Stats `protobuf:"bytes,5,opt,name=stats,proto3" json:"stats,omitempty"`
	Goals         []string                   `protobuf:"bytes,6,rep,name=goals,proto3" json:"goals,omitempty"`
	Email         string                     `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserProfileReply_Data) Reset() {
	*x = GetUserProfileReply_Data{}
	mi := &file_user_v1_user_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserProfileReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserProfileReply_Data) ProtoMessage() {}

func (x *GetUserProfileReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserProfileReply_Data.ProtoReflect.Descriptor instead.
func (*GetUserProfileReply_Data) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{7, 1}
}

func (x *GetUserProfileReply_Data) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *GetUserProfileReply_Data) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *GetUserProfileReply_Data) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *GetUserProfileReply_Data) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *GetUserProfileReply_Data) GetStats() *GetUserProfileReply_Stats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *GetUserProfileReply_Data) GetGoals() []string {
	if x != nil {
		return x.Goals
	}
	return nil
}

func (x *GetUserProfileReply_Data) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type UpdateUserProfileReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	AvatarUrl     string                 `protobuf:"bytes,3,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	Desc          string                 `protobuf:"bytes,4,opt,name=desc,proto3" json:"desc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserProfileReply_Data) Reset() {
	*x = UpdateUserProfileReply_Data{}
	mi := &file_user_v1_user_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserProfileReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserProfileReply_Data) ProtoMessage() {}

func (x *UpdateUserProfileReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserProfileReply_Data.ProtoReflect.Descriptor instead.
func (*UpdateUserProfileReply_Data) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{11, 0}
}

func (x *UpdateUserProfileReply_Data) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UpdateUserProfileReply_Data) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *UpdateUserProfileReply_Data) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

type RefreshTokenReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	ExpireAt      int32                  `protobuf:"varint,2,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefreshTokenReply_Data) Reset() {
	*x = RefreshTokenReply_Data{}
	mi := &file_user_v1_user_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefreshTokenReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenReply_Data) ProtoMessage() {}

func (x *RefreshTokenReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenReply_Data.ProtoReflect.Descriptor instead.
func (*RefreshTokenReply_Data) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{17, 0}
}

func (x *RefreshTokenReply_Data) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *RefreshTokenReply_Data) GetExpireAt() int32 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

type GetUserSettingReply_Data struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	AwardImageUrl           string                 `protobuf:"bytes,1,opt,name=award_image_url,json=awardImageUrl,proto3" json:"award_image_url,omitempty"`
	AwardImageUrlWithDomain string                 `protobuf:"bytes,2,opt,name=award_image_url_with_domain,json=awardImageUrlWithDomain,proto3" json:"award_image_url_with_domain,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *GetUserSettingReply_Data) Reset() {
	*x = GetUserSettingReply_Data{}
	mi := &file_user_v1_user_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserSettingReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSettingReply_Data) ProtoMessage() {}

func (x *GetUserSettingReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSettingReply_Data.ProtoReflect.Descriptor instead.
func (*GetUserSettingReply_Data) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{21, 0}
}

func (x *GetUserSettingReply_Data) GetAwardImageUrl() string {
	if x != nil {
		return x.AwardImageUrl
	}
	return ""
}

func (x *GetUserSettingReply_Data) GetAwardImageUrlWithDomain() string {
	if x != nil {
		return x.AwardImageUrlWithDomain
	}
	return ""
}

type CheckUserPrivacyPasswordReply_Data struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsPass        bool                   `protobuf:"varint,1,opt,name=is_pass,json=isPass,proto3" json:"is_pass,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserPrivacyPasswordReply_Data) Reset() {
	*x = CheckUserPrivacyPasswordReply_Data{}
	mi := &file_user_v1_user_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserPrivacyPasswordReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserPrivacyPasswordReply_Data) ProtoMessage() {}

func (x *CheckUserPrivacyPasswordReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserPrivacyPasswordReply_Data.ProtoReflect.Descriptor instead.
func (*CheckUserPrivacyPasswordReply_Data) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{31, 0}
}

func (x *CheckUserPrivacyPasswordReply_Data) GetIsPass() bool {
	if x != nil {
		return x.IsPass
	}
	return false
}

type GetUserVipInfoReply_VipDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Price         int32                  `protobuf:"varint,1,opt,name=price,proto3" json:"price,omitempty"`
	DiscountPrice int32                  `protobuf:"varint,2,opt,name=discount_price,json=discountPrice,proto3" json:"discount_price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserVipInfoReply_VipDetail) Reset() {
	*x = GetUserVipInfoReply_VipDetail{}
	mi := &file_user_v1_user_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserVipInfoReply_VipDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserVipInfoReply_VipDetail) ProtoMessage() {}

func (x *GetUserVipInfoReply_VipDetail) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserVipInfoReply_VipDetail.ProtoReflect.Descriptor instead.
func (*GetUserVipInfoReply_VipDetail) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{35, 0}
}

func (x *GetUserVipInfoReply_VipDetail) GetPrice() int32 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *GetUserVipInfoReply_VipDetail) GetDiscountPrice() int32 {
	if x != nil {
		return x.DiscountPrice
	}
	return 0
}

type GetUserVipInfoReply_Data struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	IsVip         bool                           `protobuf:"varint,1,opt,name=is_vip,json=isVip,proto3" json:"is_vip,omitempty"`
	RemainDays    int32                          `protobuf:"varint,2,opt,name=remain_days,json=remainDays,proto3" json:"remain_days,omitempty"`
	MonthPrice    *GetUserVipInfoReply_VipDetail `protobuf:"bytes,3,opt,name=month_price,json=monthPrice,proto3" json:"month_price,omitempty"`
	QuarterPrice  *GetUserVipInfoReply_VipDetail `protobuf:"bytes,4,opt,name=quarter_price,json=quarterPrice,proto3" json:"quarter_price,omitempty"`
	YearPrice     *GetUserVipInfoReply_VipDetail `protobuf:"bytes,5,opt,name=year_price,json=yearPrice,proto3" json:"year_price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserVipInfoReply_Data) Reset() {
	*x = GetUserVipInfoReply_Data{}
	mi := &file_user_v1_user_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserVipInfoReply_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserVipInfoReply_Data) ProtoMessage() {}

func (x *GetUserVipInfoReply_Data) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserVipInfoReply_Data.ProtoReflect.Descriptor instead.
func (*GetUserVipInfoReply_Data) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{35, 1}
}

func (x *GetUserVipInfoReply_Data) GetIsVip() bool {
	if x != nil {
		return x.IsVip
	}
	return false
}

func (x *GetUserVipInfoReply_Data) GetRemainDays() int32 {
	if x != nil {
		return x.RemainDays
	}
	return 0
}

func (x *GetUserVipInfoReply_Data) GetMonthPrice() *GetUserVipInfoReply_VipDetail {
	if x != nil {
		return x.MonthPrice
	}
	return nil
}

func (x *GetUserVipInfoReply_Data) GetQuarterPrice() *GetUserVipInfoReply_VipDetail {
	if x != nil {
		return x.QuarterPrice
	}
	return nil
}

func (x *GetUserVipInfoReply_Data) GetYearPrice() *GetUserVipInfoReply_VipDetail {
	if x != nil {
		return x.YearPrice
	}
	return nil
}

var File_user_v1_user_proto protoreflect.FileDescriptor

const file_user_v1_user_proto_rawDesc = "" +
	"\n" +
	"\x12user/v1/user.proto\x12\auser_v1\x1a\x17validate/validate.proto\"\xd5\x01\n" +
	"\x0fRegisterRequest\x12$\n" +
	"\tuser_name\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\buserName\x12%\n" +
	"\bpassword\x18\x02 \x01(\tB\t\xfaB\x06r\x04\x10\x06\x18\x14R\bpassword\x124\n" +
	"\x10confirm_password\x18\x03 \x01(\tB\t\xfaB\x06r\x04\x10\x06\x18\x14R\x0fconfirmPassword\x12)\n" +
	"\vverify_code\x18\x04 \x01(\tB\b\xfaB\x05r\x03\x98\x01\x06R\n" +
	"verifyCode\x12\x14\n" +
	"\x05Phone\x18\x05 \x01(\tR\x05Phone\"\xa1\x01\n" +
	"\rRegisterReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12/\n" +
	"\x04data\x18\x03 \x01(\v2\x1b.user_v1.RegisterReply.DataR\x04data\x1a9\n" +
	"\x04Data\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x1b\n" +
	"\texpire_at\x18\x02 \x01(\x05R\bexpireAt\"\xe1\x02\n" +
	"\fLoginRequest\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1f\n" +
	"\vverify_code\x18\x04 \x01(\tR\n" +
	"verifyCode\x12\x1d\n" +
	"\n" +
	"login_type\x18\x05 \x01(\x05R\tloginType\x12\x1b\n" +
	"\tpass_type\x18\x06 \x01(\x05R\bpassType\x12*\n" +
	"\x11third_party_token\x18\a \x01(\tR\x0fthirdPartyToken\x12#\n" +
	"\rtimezone_name\x18\b \x01(\tR\ftimezoneName\x12'\n" +
	"\x0ftimezone_offset\x18\t \x01(\tR\x0etimezoneOffset\x12\x16\n" +
	"\x06locale\x18\n" +
	" \x01(\tR\x06locale\x12\x1a\n" +
	"\bplatform\x18\f \x01(\tR\bplatform\"\x8a\x04\n" +
	"\n" +
	"LoginReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12,\n" +
	"\x04data\x18\x03 \x01(\v2\x18.user_v1.LoginReply.DataR\x04data\x1a\xa7\x03\n" +
	"\x04Data\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x1b\n" +
	"\texpire_at\x18\x02 \x01(\x05R\bexpireAt\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x05R\x06userId\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x05 \x01(\tR\tavatarUrl\x12&\n" +
	"\x0fis_system_admin\x18\x06 \x01(\bR\risSystemAdmin\x12\x1b\n" +
	"\trole_type\x18\a \x01(\x05R\broleType\x12\x14\n" +
	"\x05phone\x18\b \x01(\tR\x05phone\x12\x14\n" +
	"\x05email\x18\t \x01(\tR\x05email\x12\x16\n" +
	"\x06status\x18\n" +
	" \x01(\x05R\x06status\x12\x12\n" +
	"\x04desc\x18\v \x01(\tR\x04desc\x12\x16\n" +
	"\x06gender\x18\f \x01(\x05R\x06gender\x12\x1c\n" +
	"\n" +
	"is_set_pwd\x18\r \x01(\bR\bisSetPwd\x12\x15\n" +
	"\x06is_vip\x18\x0e \x01(\bR\x05isVip\x12$\n" +
	"\x0eis_set_privacy\x18\x0f \x01(\bR\fisSetPrivacy\x12\x10\n" +
	"\x03uid\x18\x10 \x01(\tR\x03uid\"H\n" +
	"\rLogoutRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1a\n" +
	"\bplatform\x18\x02 \x01(\tR\bplatform\"3\n" +
	"\vLogoutReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x17\n" +
	"\x15GetUserProfileRequest\"\xb7\x03\n" +
	"\x13GetUserProfileReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x125\n" +
	"\x04data\x18\x03 \x01(\v2!.user_v1.GetUserProfileReply.DataR\x04data\x1ao\n" +
	"\x05Stats\x12\x1d\n" +
	"\n" +
	"all_habits\x18\x01 \x01(\x05R\tallHabits\x12&\n" +
	"\x0fon_track_habits\x18\x02 \x01(\x05R\ronTrackHabits\x12\x1f\n" +
	"\vdone_habits\x18\x03 \x01(\x05R\n" +
	"doneHabits\x1a\xd1\x01\n" +
	"\x04Data\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x14\n" +
	"\x05phone\x18\x02 \x01(\tR\x05phone\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x03 \x01(\tR\tavatarUrl\x12\x12\n" +
	"\x04desc\x18\x04 \x01(\tR\x04desc\x128\n" +
	"\x05stats\x18\x05 \x01(\v2\".user_v1.GetUserProfileReply.StatsR\x05stats\x12\x14\n" +
	"\x05goals\x18\x06 \x03(\tR\x05goals\x12\x14\n" +
	"\x05email\x18\a \x01(\tR\x05email\"\xab\x01\n" +
	"\x15ForgetPasswordRequest\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x1f\n" +
	"\vverify_code\x18\x02 \x01(\tR\n" +
	"verifyCode\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12)\n" +
	"\x10confirm_password\x18\x04 \x01(\tR\x0fconfirmPassword\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\";\n" +
	"\x13ForgetPasswordReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"{\n" +
	"\x18UpdateUserProfileRequest\x12#\n" +
	"\busername\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x18\x10R\busername\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x03 \x01(\tR\tavatarUrl\x12\x1b\n" +
	"\x04desc\x18\x04 \x01(\tB\a\xfaB\x04r\x02\x182R\x04desc\"\xcf\x01\n" +
	"\x16UpdateUserProfileReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x128\n" +
	"\x04data\x18\x03 \x01(\v2$.user_v1.UpdateUserProfileReply.DataR\x04data\x1aU\n" +
	"\x04Data\x12\x1a\n" +
	"\busername\x18\x01 \x01(\tR\busername\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\x03 \x01(\tR\tavatarUrl\x12\x12\n" +
	"\x04desc\x18\x04 \x01(\tR\x04desc\",\n" +
	"\x11FollowUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x05R\x06userId\"7\n" +
	"\x0fFollowUserReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\".\n" +
	"\x13UnfollowUserRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x05R\x06userId\"9\n" +
	"\x11UnfollowUserReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\xc7\x02\n" +
	"\x13RefreshTokenRequest\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1a\n" +
	"\bplatform\x18\x02 \x01(\tR\bplatform\x12!\n" +
	"\fdevice_brand\x18\x03 \x01(\tR\vdeviceBrand\x12\x1b\n" +
	"\tfcm_token\x18\x04 \x01(\tR\bfcmToken\x12\x1d\n" +
	"\n" +
	"apns_token\x18\x05 \x01(\tR\tapnsToken\x12*\n" +
	"\x11push_service_type\x18\x06 \x01(\tR\x0fpushServiceType\x122\n" +
	"\x15jpush_registration_id\x18\a \x01(\tR\x13jpushRegistrationId\x128\n" +
	"\x18recommended_push_channel\x18\b \x01(\tR\x16recommendedPushChannel\"\xa9\x01\n" +
	"\x11RefreshTokenReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x123\n" +
	"\x04data\x18\x03 \x01(\v2\x1f.user_v1.RefreshTokenReply.DataR\x04data\x1a9\n" +
	"\x04Data\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x1b\n" +
	"\texpire_at\x18\x02 \x01(\x05R\bexpireAt\"@\n" +
	"\x16UpdateUserAwardRequest\x12&\n" +
	"\x0faward_image_url\x18\x01 \x01(\tR\rawardImageUrl\"<\n" +
	"\x14UpdateUserAwardReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"-\n" +
	"\x15GetUserSettingRequest\x12\x14\n" +
	"\x05scene\x18\x01 \x01(\tR\x05scene\"\xe0\x01\n" +
	"\x13GetUserSettingReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x125\n" +
	"\x04data\x18\x03 \x01(\v2!.user_v1.GetUserSettingReply.DataR\x04data\x1al\n" +
	"\x04Data\x12&\n" +
	"\x0faward_image_url\x18\x01 \x01(\tR\rawardImageUrl\x12<\n" +
	"\x1baward_image_url_with_domain\x18\x02 \x01(\tR\x17awardImageUrlWithDomain\"K\n" +
	"\x12ChangePhoneRequest\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x1f\n" +
	"\vverify_code\x18\x02 \x01(\tR\n" +
	"verifyCode\"8\n" +
	"\x10ChangePhoneReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"K\n" +
	"\x12ChangeEmailRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1f\n" +
	"\vverify_code\x18\x02 \x01(\tR\n" +
	"verifyCode\"8\n" +
	"\x10ChangeEmailReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x88\x01\n" +
	"\x15ChangePasswordRequest\x12!\n" +
	"\fold_password\x18\x01 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x02 \x01(\tR\vnewPassword\x12)\n" +
	"\x10confirm_password\x18\x03 \x01(\tR\x0fconfirmPassword\";\n" +
	"\x13ChangePasswordReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"[\n" +
	"\x11DeleteUserRequest\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\"7\n" +
	"\x0fDeleteUserReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"=\n" +
	"\x1fCheckUserPrivacyPasswordRequest\x12\x1a\n" +
	"\bpassword\x18\x01 \x01(\tR\bpassword\"\xa7\x01\n" +
	"\x1dCheckUserPrivacyPasswordReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12?\n" +
	"\x04data\x18\x03 \x01(\v2+.user_v1.CheckUserPrivacyPasswordReply.DataR\x04data\x1a\x1f\n" +
	"\x04Data\x12\x17\n" +
	"\ais_pass\x18\x01 \x01(\bR\x06isPass\"\x93\x01\n" +
	" ChangeUserPrivacyPasswordRequest\x12!\n" +
	"\fold_password\x18\x01 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x02 \x01(\tR\vnewPassword\x12)\n" +
	"\x10confirm_password\x18\x03 \x01(\tR\x0fconfirmPassword\"F\n" +
	"\x1eChangeUserPrivacyPasswordReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\"\x17\n" +
	"\x15GetUserVipInfoRequest\"\xda\x03\n" +
	"\x13GetUserVipInfoReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x125\n" +
	"\x04data\x18\x03 \x01(\v2!.user_v1.GetUserVipInfoReply.DataR\x04data\x1aH\n" +
	"\tVipDetail\x12\x14\n" +
	"\x05price\x18\x01 \x01(\x05R\x05price\x12%\n" +
	"\x0ediscount_price\x18\x02 \x01(\x05R\rdiscountPrice\x1a\x9b\x02\n" +
	"\x04Data\x12\x15\n" +
	"\x06is_vip\x18\x01 \x01(\bR\x05isVip\x12\x1f\n" +
	"\vremain_days\x18\x02 \x01(\x05R\n" +
	"remainDays\x12G\n" +
	"\vmonth_price\x18\x03 \x01(\v2&.user_v1.GetUserVipInfoReply.VipDetailR\n" +
	"monthPrice\x12K\n" +
	"\rquarter_price\x18\x04 \x01(\v2&.user_v1.GetUserVipInfoReply.VipDetailR\fquarterPrice\x12E\n" +
	"\n" +
	"year_price\x18\x05 \x01(\v2&.user_v1.GetUserVipInfoReply.VipDetailR\tyearPrice\"\xaa\x04\n" +
	"\x10PushTokenRequest\x12#\n" +
	"\rtimezone_name\x18\b \x01(\tR\ftimezoneName\x12'\n" +
	"\x0ftimezone_offset\x18\t \x01(\tR\x0etimezoneOffset\x12\x16\n" +
	"\x06locale\x18\n" +
	" \x01(\tR\x06locale\x12\x1b\n" +
	"\tdevice_id\x18\v \x01(\tR\bdeviceId\x12\x1a\n" +
	"\bplatform\x18\f \x01(\tR\bplatform\x12%\n" +
	"\x0esystem_version\x18\r \x01(\tR\rsystemVersion\x12\x1f\n" +
	"\vapp_version\x18\x0e \x01(\tR\n" +
	"appVersion\x12!\n" +
	"\fdevice_brand\x18\x0f \x01(\tR\vdeviceBrand\x12\x1b\n" +
	"\tfcm_token\x18\x10 \x01(\tR\bfcmToken\x12\x1d\n" +
	"\n" +
	"apns_token\x18\x11 \x01(\tR\tapnsToken\x12*\n" +
	"\x11push_service_type\x18\x12 \x01(\tR\x0fpushServiceType\x122\n" +
	"\x15jpush_registration_id\x18\x13 \x01(\tR\x13jpushRegistrationId\x128\n" +
	"\x18recommended_push_channel\x18\x14 \x01(\tR\x16recommendedPushChannel\x126\n" +
	"\x17push_permission_granted\x18\x15 \x01(\bR\x15pushPermissionGranted\"6\n" +
	"\x0ePushTokenReply\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msgB-Z+github.com/wlnil/life-log-be/api/user/v1;v1b\x06proto3"

var (
	file_user_v1_user_proto_rawDescOnce sync.Once
	file_user_v1_user_proto_rawDescData []byte
)

func file_user_v1_user_proto_rawDescGZIP() []byte {
	file_user_v1_user_proto_rawDescOnce.Do(func() {
		file_user_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)))
	})
	return file_user_v1_user_proto_rawDescData
}

var file_user_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 48)
var file_user_v1_user_proto_goTypes = []any{
	(*RegisterRequest)(nil),                    // 0: user_v1.RegisterRequest
	(*RegisterReply)(nil),                      // 1: user_v1.RegisterReply
	(*LoginRequest)(nil),                       // 2: user_v1.LoginRequest
	(*LoginReply)(nil),                         // 3: user_v1.LoginReply
	(*LogoutRequest)(nil),                      // 4: user_v1.LogoutRequest
	(*LogoutReply)(nil),                        // 5: user_v1.LogoutReply
	(*GetUserProfileRequest)(nil),              // 6: user_v1.GetUserProfileRequest
	(*GetUserProfileReply)(nil),                // 7: user_v1.GetUserProfileReply
	(*ForgetPasswordRequest)(nil),              // 8: user_v1.ForgetPasswordRequest
	(*ForgetPasswordReply)(nil),                // 9: user_v1.ForgetPasswordReply
	(*UpdateUserProfileRequest)(nil),           // 10: user_v1.UpdateUserProfileRequest
	(*UpdateUserProfileReply)(nil),             // 11: user_v1.UpdateUserProfileReply
	(*FollowUserRequest)(nil),                  // 12: user_v1.FollowUserRequest
	(*FollowUserReply)(nil),                    // 13: user_v1.FollowUserReply
	(*UnfollowUserRequest)(nil),                // 14: user_v1.UnfollowUserRequest
	(*UnfollowUserReply)(nil),                  // 15: user_v1.UnfollowUserReply
	(*RefreshTokenRequest)(nil),                // 16: user_v1.RefreshTokenRequest
	(*RefreshTokenReply)(nil),                  // 17: user_v1.RefreshTokenReply
	(*UpdateUserAwardRequest)(nil),             // 18: user_v1.UpdateUserAwardRequest
	(*UpdateUserAwardReply)(nil),               // 19: user_v1.UpdateUserAwardReply
	(*GetUserSettingRequest)(nil),              // 20: user_v1.GetUserSettingRequest
	(*GetUserSettingReply)(nil),                // 21: user_v1.GetUserSettingReply
	(*ChangePhoneRequest)(nil),                 // 22: user_v1.ChangePhoneRequest
	(*ChangePhoneReply)(nil),                   // 23: user_v1.ChangePhoneReply
	(*ChangeEmailRequest)(nil),                 // 24: user_v1.ChangeEmailRequest
	(*ChangeEmailReply)(nil),                   // 25: user_v1.ChangeEmailReply
	(*ChangePasswordRequest)(nil),              // 26: user_v1.ChangePasswordRequest
	(*ChangePasswordReply)(nil),                // 27: user_v1.ChangePasswordReply
	(*DeleteUserRequest)(nil),                  // 28: user_v1.DeleteUserRequest
	(*DeleteUserReply)(nil),                    // 29: user_v1.DeleteUserReply
	(*CheckUserPrivacyPasswordRequest)(nil),    // 30: user_v1.CheckUserPrivacyPasswordRequest
	(*CheckUserPrivacyPasswordReply)(nil),      // 31: user_v1.CheckUserPrivacyPasswordReply
	(*ChangeUserPrivacyPasswordRequest)(nil),   // 32: user_v1.ChangeUserPrivacyPasswordRequest
	(*ChangeUserPrivacyPasswordReply)(nil),     // 33: user_v1.ChangeUserPrivacyPasswordReply
	(*GetUserVipInfoRequest)(nil),              // 34: user_v1.GetUserVipInfoRequest
	(*GetUserVipInfoReply)(nil),                // 35: user_v1.GetUserVipInfoReply
	(*PushTokenRequest)(nil),                   // 36: user_v1.PushTokenRequest
	(*PushTokenReply)(nil),                     // 37: user_v1.PushTokenReply
	(*RegisterReply_Data)(nil),                 // 38: user_v1.RegisterReply.Data
	(*LoginReply_Data)(nil),                    // 39: user_v1.LoginReply.Data
	(*GetUserProfileReply_Stats)(nil),          // 40: user_v1.GetUserProfileReply.Stats
	(*GetUserProfileReply_Data)(nil),           // 41: user_v1.GetUserProfileReply.Data
	(*UpdateUserProfileReply_Data)(nil),        // 42: user_v1.UpdateUserProfileReply.Data
	(*RefreshTokenReply_Data)(nil),             // 43: user_v1.RefreshTokenReply.Data
	(*GetUserSettingReply_Data)(nil),           // 44: user_v1.GetUserSettingReply.Data
	(*CheckUserPrivacyPasswordReply_Data)(nil), // 45: user_v1.CheckUserPrivacyPasswordReply.Data
	(*GetUserVipInfoReply_VipDetail)(nil),      // 46: user_v1.GetUserVipInfoReply.VipDetail
	(*GetUserVipInfoReply_Data)(nil),           // 47: user_v1.GetUserVipInfoReply.Data
}
var file_user_v1_user_proto_depIdxs = []int32{
	38, // 0: user_v1.RegisterReply.data:type_name -> user_v1.RegisterReply.Data
	39, // 1: user_v1.LoginReply.data:type_name -> user_v1.LoginReply.Data
	41, // 2: user_v1.GetUserProfileReply.data:type_name -> user_v1.GetUserProfileReply.Data
	42, // 3: user_v1.UpdateUserProfileReply.data:type_name -> user_v1.UpdateUserProfileReply.Data
	43, // 4: user_v1.RefreshTokenReply.data:type_name -> user_v1.RefreshTokenReply.Data
	44, // 5: user_v1.GetUserSettingReply.data:type_name -> user_v1.GetUserSettingReply.Data
	45, // 6: user_v1.CheckUserPrivacyPasswordReply.data:type_name -> user_v1.CheckUserPrivacyPasswordReply.Data
	47, // 7: user_v1.GetUserVipInfoReply.data:type_name -> user_v1.GetUserVipInfoReply.Data
	40, // 8: user_v1.GetUserProfileReply.Data.stats:type_name -> user_v1.GetUserProfileReply.Stats
	46, // 9: user_v1.GetUserVipInfoReply.Data.month_price:type_name -> user_v1.GetUserVipInfoReply.VipDetail
	46, // 10: user_v1.GetUserVipInfoReply.Data.quarter_price:type_name -> user_v1.GetUserVipInfoReply.VipDetail
	46, // 11: user_v1.GetUserVipInfoReply.Data.year_price:type_name -> user_v1.GetUserVipInfoReply.VipDetail
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_user_v1_user_proto_init() }
func file_user_v1_user_proto_init() {
	if File_user_v1_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   48,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_user_v1_user_proto_goTypes,
		DependencyIndexes: file_user_v1_user_proto_depIdxs,
		MessageInfos:      file_user_v1_user_proto_msgTypes,
	}.Build()
	File_user_v1_user_proto = out.File
	file_user_v1_user_proto_goTypes = nil
	file_user_v1_user_proto_depIdxs = nil
}
