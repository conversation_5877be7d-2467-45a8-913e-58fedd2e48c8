// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: common/v1/error_reason.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	ErrorReason_Normal            ErrorReason = 0
	ErrorReason_InternalError     ErrorReason = 100100
	ErrorReason_DataBaseError     ErrorReason = 100101
	ErrorReason_RedisError        ErrorReason = 100102
	ErrorReason_UserNotFound      ErrorReason = 200100
	ErrorReason_UserExited        ErrorReason = 200101
	ErrorReason_PasswordNotMatch  ErrorReason = 200102
	ErrorReason_PasswordNotSecure ErrorReason = 200103
	ErrorReason_VerifyCodeError   ErrorReason = 200104
	ErrorReason_PhoneExisted      ErrorReason = 200105
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0:      "Normal",
		100100: "InternalError",
		100101: "DataBaseError",
		100102: "RedisError",
		200100: "UserNotFound",
		200101: "UserExited",
		200102: "PasswordNotMatch",
		200103: "PasswordNotSecure",
		200104: "VerifyCodeError",
		200105: "PhoneExisted",
	}
	ErrorReason_value = map[string]int32{
		"Normal":            0,
		"InternalError":     100100,
		"DataBaseError":     100101,
		"RedisError":        100102,
		"UserNotFound":      200100,
		"UserExited":        200101,
		"PasswordNotMatch":  200102,
		"PasswordNotSecure": 200103,
		"VerifyCodeError":   200104,
		"PhoneExisted":      200105,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_common_v1_error_reason_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_common_v1_error_reason_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_common_v1_error_reason_proto_rawDescGZIP(), []int{0}
}

var File_common_v1_error_reason_proto protoreflect.FileDescriptor

const file_common_v1_error_reason_proto_rawDesc = "" +
	"\n" +
	"\x1ccommon/v1/error_reason.proto\x12\berror_v1*\xd7\x01\n" +
	"\vErrorReason\x12\n" +
	"\n" +
	"\x06Normal\x10\x00\x12\x13\n" +
	"\rInternalError\x10\x84\x8e\x06\x12\x13\n" +
	"\rDataBaseError\x10\x85\x8e\x06\x12\x10\n" +
	"\n" +
	"RedisError\x10\x86\x8e\x06\x12\x12\n" +
	"\fUserNotFound\x10\xa4\x9b\f\x12\x10\n" +
	"\n" +
	"UserExited\x10\xa5\x9b\f\x12\x16\n" +
	"\x10PasswordNotMatch\x10\xa6\x9b\f\x12\x17\n" +
	"\x11PasswordNotSecure\x10\xa7\x9b\f\x12\x15\n" +
	"\x0fVerifyCodeError\x10\xa8\x9b\f\x12\x12\n" +
	"\fPhoneExisted\x10\xa9\x9b\fB/Z-github.com/wlnil/life-log-be/api/common/v1;v1b\x06proto3"

var (
	file_common_v1_error_reason_proto_rawDescOnce sync.Once
	file_common_v1_error_reason_proto_rawDescData []byte
)

func file_common_v1_error_reason_proto_rawDescGZIP() []byte {
	file_common_v1_error_reason_proto_rawDescOnce.Do(func() {
		file_common_v1_error_reason_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_v1_error_reason_proto_rawDesc), len(file_common_v1_error_reason_proto_rawDesc)))
	})
	return file_common_v1_error_reason_proto_rawDescData
}

var file_common_v1_error_reason_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_v1_error_reason_proto_goTypes = []any{
	(ErrorReason)(0), // 0: error_v1.ErrorReason
}
var file_common_v1_error_reason_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_common_v1_error_reason_proto_init() }
func file_common_v1_error_reason_proto_init() {
	if File_common_v1_error_reason_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_v1_error_reason_proto_rawDesc), len(file_common_v1_error_reason_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_v1_error_reason_proto_goTypes,
		DependencyIndexes: file_common_v1_error_reason_proto_depIdxs,
		EnumInfos:         file_common_v1_error_reason_proto_enumTypes,
	}.Build()
	File_common_v1_error_reason_proto = out.File
	file_common_v1_error_reason_proto_goTypes = nil
	file_common_v1_error_reason_proto_depIdxs = nil
}
