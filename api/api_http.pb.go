// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v5.29.3
// source: api.proto

package api

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	v13 "github.com/wlnil/life-log-be/api/planet/v1"
	v11 "github.com/wlnil/life-log-be/api/site/v1"
	v1 "github.com/wlnil/life-log-be/api/user/v1"
	v12 "github.com/wlnil/life-log-be/api/user_habit/v1"
	v16 "github.com/wlnil/life-log-be/api/user_message/v1"
	v14 "github.com/wlnil/life-log-be/api/user_planet/v1"
	v15 "github.com/wlnil/life-log-be/api/user_statistic/v1"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationLifeLogArchiveUserHabit = "/api.LifeLog/ArchiveUserHabit"
const OperationLifeLogBuddyInvitation = "/api.LifeLog/BuddyInvitation"
const OperationLifeLogBuddySearch = "/api.LifeLog/BuddySearch"
const OperationLifeLogCancelFavoritePlanetPost = "/api.LifeLog/CancelFavoritePlanetPost"
const OperationLifeLogCancelLikePlanetPost = "/api.LifeLog/CancelLikePlanetPost"
const OperationLifeLogCancelPunchUserHabit = "/api.LifeLog/CancelPunchUserHabit"
const OperationLifeLogCancelReckonUserHabit = "/api.LifeLog/CancelReckonUserHabit"
const OperationLifeLogChangeEmail = "/api.LifeLog/ChangeEmail"
const OperationLifeLogChangePassword = "/api.LifeLog/ChangePassword"
const OperationLifeLogChangePhone = "/api.LifeLog/ChangePhone"
const OperationLifeLogChangeUserPrivacyPassword = "/api.LifeLog/ChangeUserPrivacyPassword"
const OperationLifeLogCheckUserPrivacyPassword = "/api.LifeLog/CheckUserPrivacyPassword"
const OperationLifeLogCreateDownURL = "/api.LifeLog/CreateDownURL"
const OperationLifeLogCreateFeedback = "/api.LifeLog/CreateFeedback"
const OperationLifeLogCreatePlanet = "/api.LifeLog/CreatePlanet"
const OperationLifeLogCreatePlanetPost = "/api.LifeLog/CreatePlanetPost"
const OperationLifeLogCreatePlanetPostComment = "/api.LifeLog/CreatePlanetPostComment"
const OperationLifeLogCreatePlanetTarget = "/api.LifeLog/CreatePlanetTarget"
const OperationLifeLogCreateUpToken = "/api.LifeLog/CreateUpToken"
const OperationLifeLogCreateUserHabit = "/api.LifeLog/CreateUserHabit"
const OperationLifeLogCreateUserHabitMemo = "/api.LifeLog/CreateUserHabitMemo"
const OperationLifeLogCreateUserHabitReckon = "/api.LifeLog/CreateUserHabitReckon"
const OperationLifeLogDeletePlanet = "/api.LifeLog/DeletePlanet"
const OperationLifeLogDeletePlanetPost = "/api.LifeLog/DeletePlanetPost"
const OperationLifeLogDeletePlanetPostComment = "/api.LifeLog/DeletePlanetPostComment"
const OperationLifeLogDeletePlanetTarget = "/api.LifeLog/DeletePlanetTarget"
const OperationLifeLogDeleteUser = "/api.LifeLog/DeleteUser"
const OperationLifeLogDeleteUserHabit = "/api.LifeLog/DeleteUserHabit"
const OperationLifeLogDeleteUserHabitMemo = "/api.LifeLog/DeleteUserHabitMemo"
const OperationLifeLogDeleteUserHabitReckon = "/api.LifeLog/DeleteUserHabitReckon"
const OperationLifeLogFavoritePlanetPost = "/api.LifeLog/FavoritePlanetPost"
const OperationLifeLogFollowUser = "/api.LifeLog/FollowUser"
const OperationLifeLogForgetPassword = "/api.LifeLog/ForgetPassword"
const OperationLifeLogGetPlanet = "/api.LifeLog/GetPlanet"
const OperationLifeLogGetPlanetTarget = "/api.LifeLog/GetPlanetTarget"
const OperationLifeLogGetSiteInfo = "/api.LifeLog/GetSiteInfo"
const OperationLifeLogGetUserHabit = "/api.LifeLog/GetUserHabit"
const OperationLifeLogGetUserProfile = "/api.LifeLog/GetUserProfile"
const OperationLifeLogGetUserSetting = "/api.LifeLog/GetUserSetting"
const OperationLifeLogGetUserVipInfo = "/api.LifeLog/GetUserVipInfo"
const OperationLifeLogHealthCheck = "/api.LifeLog/HealthCheck"
const OperationLifeLogJoinPlanet = "/api.LifeLog/JoinPlanet"
const OperationLifeLogJoinPlanetTarget = "/api.LifeLog/JoinPlanetTarget"
const OperationLifeLogLikePlanetPost = "/api.LifeLog/LikePlanetPost"
const OperationLifeLogListMotiveMemo = "/api.LifeLog/ListMotiveMemo"
const OperationLifeLogListPlanetByUserID = "/api.LifeLog/ListPlanetByUserID"
const OperationLifeLogListPlanetPost = "/api.LifeLog/ListPlanetPost"
const OperationLifeLogListPlanetPostComment = "/api.LifeLog/ListPlanetPostComment"
const OperationLifeLogListPlanetTarget = "/api.LifeLog/ListPlanetTarget"
const OperationLifeLogListPlanetTopPost = "/api.LifeLog/ListPlanetTopPost"
const OperationLifeLogListUserHabit = "/api.LifeLog/ListUserHabit"
const OperationLifeLogListUserHabitSnapshot = "/api.LifeLog/ListUserHabitSnapshot"
const OperationLifeLogLogin = "/api.LifeLog/Login"
const OperationLifeLogLogout = "/api.LifeLog/Logout"
const OperationLifeLogPauseUserHabit = "/api.LifeLog/PauseUserHabit"
const OperationLifeLogPunchUserHabit = "/api.LifeLog/PunchUserHabit"
const OperationLifeLogPushToken = "/api.LifeLog/PushToken"
const OperationLifeLogQuitPlanet = "/api.LifeLog/QuitPlanet"
const OperationLifeLogQuitPlanetTarget = "/api.LifeLog/QuitPlanetTarget"
const OperationLifeLogReckonUserHabit = "/api.LifeLog/ReckonUserHabit"
const OperationLifeLogRecoverUserHabit = "/api.LifeLog/RecoverUserHabit"
const OperationLifeLogRefreshToken = "/api.LifeLog/RefreshToken"
const OperationLifeLogRegister = "/api.LifeLog/Register"
const OperationLifeLogRunCronTask = "/api.LifeLog/RunCronTask"
const OperationLifeLogSendVerifyCode = "/api.LifeLog/SendVerifyCode"
const OperationLifeLogTodayStatisticFromHome = "/api.LifeLog/TodayStatisticFromHome"
const OperationLifeLogToppedPlanetPost = "/api.LifeLog/ToppedPlanetPost"
const OperationLifeLogUnfollowUser = "/api.LifeLog/UnfollowUser"
const OperationLifeLogUnreadCount = "/api.LifeLog/UnreadCount"
const OperationLifeLogUpdatePlanet = "/api.LifeLog/UpdatePlanet"
const OperationLifeLogUpdatePlanetPost = "/api.LifeLog/UpdatePlanetPost"
const OperationLifeLogUpdatePlanetTarget = "/api.LifeLog/UpdatePlanetTarget"
const OperationLifeLogUpdatePunchUserHabit = "/api.LifeLog/UpdatePunchUserHabit"
const OperationLifeLogUpdateUserAward = "/api.LifeLog/UpdateUserAward"
const OperationLifeLogUpdateUserHabit = "/api.LifeLog/UpdateUserHabit"
const OperationLifeLogUpdateUserHabitMemo = "/api.LifeLog/UpdateUserHabitMemo"
const OperationLifeLogUpdateUserHabitReckon = "/api.LifeLog/UpdateUserHabitReckon"
const OperationLifeLogUpdateUserProfile = "/api.LifeLog/UpdateUserProfile"
const OperationLifeLogUserHabitStatisticFromDetail = "/api.LifeLog/UserHabitStatisticFromDetail"
const OperationLifeLogVersionCheck = "/api.LifeLog/VersionCheck"

type LifeLogHTTPServer interface {
	// ArchiveUserHabit 归档用户习惯
	ArchiveUserHabit(context.Context, *v12.ArchiveUserHabitRequest) (*v12.ArchiveUserHabitReply, error)
	// BuddyInvitation 发送搭子邀请
	BuddyInvitation(context.Context, *v1.BuddyInvitationRequest) (*v1.BuddyInvitationReply, error)
	// BuddySearch 搭子搜索
	BuddySearch(context.Context, *v1.BuddySearchRequest) (*v1.BuddySearchReply, error)
	// CancelFavoritePlanetPost 取消收藏星球动态
	CancelFavoritePlanetPost(context.Context, *v14.CancelFavoritePlanetPostRequest) (*v14.CancelFavoritePlanetPostReply, error)
	// CancelLikePlanetPost 取消点赞星球动态
	CancelLikePlanetPost(context.Context, *v14.CancelLikePlanetPostRequest) (*v14.CancelLikePlanetPostReply, error)
	// CancelPunchUserHabit 取消习惯打卡
	CancelPunchUserHabit(context.Context, *v12.CancelPunchUserHabitRequest) (*v12.CancelPunchUserHabitReply, error)
	// CancelReckonUserHabit 取消习惯计时
	CancelReckonUserHabit(context.Context, *v12.CancelReckonUserHabitRequest) (*v12.CancelReckonUserHabitReply, error)
	// ChangeEmail 修改邮箱
	ChangeEmail(context.Context, *v1.ChangeEmailRequest) (*v1.ChangeEmailReply, error)
	// ChangePassword 修改密码
	ChangePassword(context.Context, *v1.ChangePasswordRequest) (*v1.ChangePasswordReply, error)
	// ChangePhone 修改手机号
	ChangePhone(context.Context, *v1.ChangePhoneRequest) (*v1.ChangePhoneReply, error)
	// ChangeUserPrivacyPassword 修改用户隐私密码
	ChangeUserPrivacyPassword(context.Context, *v1.ChangeUserPrivacyPasswordRequest) (*v1.ChangeUserPrivacyPasswordReply, error)
	// CheckUserPrivacyPassword 检查用户隐私密码
	CheckUserPrivacyPassword(context.Context, *v1.CheckUserPrivacyPasswordRequest) (*v1.CheckUserPrivacyPasswordReply, error)
	// CreateDownURL 获取七牛下载地址
	CreateDownURL(context.Context, *v11.CreateDownURLRequest) (*v11.CreateDownURLReply, error)
	// CreateFeedback 添加意见反馈
	CreateFeedback(context.Context, *v11.CreateFeedbackRequest) (*v11.CreateFeedbackReply, error)
	// CreatePlanet 创建星球
	CreatePlanet(context.Context, *v13.CreatePlanetRequest) (*v13.CreatePlanetReply, error)
	// CreatePlanetPost 创建星球动态
	CreatePlanetPost(context.Context, *v14.CreatePlanetPostRequest) (*v14.CreatePlanetPostReply, error)
	// CreatePlanetPostComment 创建动态评论
	CreatePlanetPostComment(context.Context, *v14.CreatePlanetPostCommentRequest) (*v14.CreatePlanetPostCommentReply, error)
	// CreatePlanetTarget 创建星球目标
	CreatePlanetTarget(context.Context, *v13.CreatePlanetTargetRequest) (*v13.CreatePlanetTargetReply, error)
	// CreateUpToken 获取七牛上传凭证
	CreateUpToken(context.Context, *v11.CreateUpTokenRequest) (*v11.CreateUpTokenReply, error)
	// CreateUserHabit 创建用户习惯
	CreateUserHabit(context.Context, *v12.CreateUserHabitRequest) (*v12.CreateUserHabitReply, error)
	// CreateUserHabitMemo 创建用户习惯想法
	CreateUserHabitMemo(context.Context, *v12.CreateUserHabitMemoRequest) (*v12.CreateUserHabitMemoReply, error)
	// CreateUserHabitReckon 创建用户习惯计时
	CreateUserHabitReckon(context.Context, *v12.CreateUserHabitReckonRequest) (*v12.CreateUserHabitReckonReply, error)
	// DeletePlanet 删除星球
	DeletePlanet(context.Context, *v13.DeletePlanetRequest) (*v13.DeletePlanetReply, error)
	// DeletePlanetPost 删除星球动态
	DeletePlanetPost(context.Context, *v14.DeletePlanetPostRequest) (*v14.DeletePlanetPostReply, error)
	// DeletePlanetPostComment 删除动态评论
	DeletePlanetPostComment(context.Context, *v14.DeletePlanetPostCommentRequest) (*v14.DeletePlanetPostCommentReply, error)
	// DeletePlanetTarget 删除星球目标
	DeletePlanetTarget(context.Context, *v13.DeletePlanetTargetRequest) (*v13.DeletePlanetTargetReply, error)
	// DeleteUser 删除用户
	DeleteUser(context.Context, *v1.DeleteUserRequest) (*v1.DeleteUserReply, error)
	// DeleteUserHabit 删除用户习惯
	DeleteUserHabit(context.Context, *v12.DeleteUserHabitRequest) (*v12.DeleteUserHabitReply, error)
	// DeleteUserHabitMemo 删除用户习惯想法
	DeleteUserHabitMemo(context.Context, *v12.DeleteUserHabitMemoRequest) (*v12.DeleteUserHabitMemoReply, error)
	// DeleteUserHabitReckon 删除用户习惯计时
	DeleteUserHabitReckon(context.Context, *v12.DeleteUserHabitReckonRequest) (*v12.DeleteUserHabitReckonReply, error)
	// FavoritePlanetPost 收藏星球动态
	FavoritePlanetPost(context.Context, *v14.FavoritePlanetPostRequest) (*v14.FavoritePlanetPostReply, error)
	// FollowUser 关注
	FollowUser(context.Context, *v1.FollowUserRequest) (*v1.FollowUserReply, error)
	// ForgetPassword 忘记密码
	ForgetPassword(context.Context, *v1.ForgetPasswordRequest) (*v1.ForgetPasswordReply, error)
	// GetPlanet 获取星球详情
	GetPlanet(context.Context, *v13.GetPlanetRequest) (*v13.GetPlanetReply, error)
	// GetPlanetTarget 获取星球目标详情
	GetPlanetTarget(context.Context, *v13.GetPlanetTargetRequest) (*v13.GetPlanetTargetReply, error)
	// GetSiteInfo 获取网站信息
	GetSiteInfo(context.Context, *v11.GetSiteInfoRequest) (*v11.GetSiteInfoReply, error)
	// GetUserHabit 获取用户习惯详情
	GetUserHabit(context.Context, *v12.GetUserHabitRequest) (*v12.GetUserHabitReply, error)
	// GetUserProfile 获取用户资料
	GetUserProfile(context.Context, *v1.GetUserProfileRequest) (*v1.GetUserProfileReply, error)
	// GetUserSetting 获取用户配置信息
	GetUserSetting(context.Context, *v1.GetUserSettingRequest) (*v1.GetUserSettingReply, error)
	// GetUserVipInfo 获取用户会员详情
	GetUserVipInfo(context.Context, *v1.GetUserVipInfoRequest) (*v1.GetUserVipInfoReply, error)
	// HealthCheck 健康检查接口
	HealthCheck(context.Context, *v11.HealthCheckRequest) (*v11.HealthCheckReply, error)
	// JoinPlanet 加入星球
	JoinPlanet(context.Context, *v14.JoinPlanetRequest) (*v14.JoinPlanetReply, error)
	// JoinPlanetTarget 加入星球目标
	JoinPlanetTarget(context.Context, *v14.JoinPlanetTargetRequest) (*v14.JoinPlanetTargetReply, error)
	// LikePlanetPost 点赞星球动态
	LikePlanetPost(context.Context, *v14.LikePlanetPostRequest) (*v14.LikePlanetPostReply, error)
	// ListMotiveMemo 获取每日随想
	ListMotiveMemo(context.Context, *v11.ListMotiveMemoRequest) (*v11.ListMotiveMemoReply, error)
	// ListPlanetByUserID 获取用户加入星球列表
	ListPlanetByUserID(context.Context, *v14.ListPlanetByUserIDRequest) (*v14.ListPlanetByUserIDReply, error)
	// ListPlanetPost 获取星球动态列表
	ListPlanetPost(context.Context, *v14.ListPlanetPostRequest) (*v14.ListPlanetPostReply, error)
	// ListPlanetPostComment 获取动态评论列表
	ListPlanetPostComment(context.Context, *v14.ListPlanetPostCommentRequest) (*v14.ListPlanetPostCommentReply, error)
	// ListPlanetTarget 获取星球目标列表
	ListPlanetTarget(context.Context, *v13.ListPlanetTargetRequest) (*v13.ListPlanetTargetReply, error)
	// ListPlanetTopPost 获取星球热点动态列表
	ListPlanetTopPost(context.Context, *v14.ListPlanetTopPostRequest) (*v14.ListPlanetTopPostReply, error)
	// ListUserHabit 获取用户习惯列表
	ListUserHabit(context.Context, *v12.ListUserHabitRequest) (*v12.ListUserHabitReply, error)
	// ListUserHabitSnapshot 获取用户每日习惯列表
	ListUserHabitSnapshot(context.Context, *v12.ListUserHabitSnapshotRequest) (*v12.ListUserHabitSnapshotReply, error)
	// Login 登录
	Login(context.Context, *v1.LoginRequest) (*v1.LoginReply, error)
	// Logout 登出
	Logout(context.Context, *v1.LogoutRequest) (*v1.LogoutReply, error)
	// PauseUserHabit 暂停用户习惯
	PauseUserHabit(context.Context, *v12.PauseUserHabitRequest) (*v12.PauseUserHabitReply, error)
	// PunchUserHabit 习惯打卡
	PunchUserHabit(context.Context, *v12.PunchUserHabitRequest) (*v12.PunchUserHabitReply, error)
	// PushToken 推送 token 和设备信息
	PushToken(context.Context, *v1.PushTokenRequest) (*v1.PushTokenReply, error)
	// QuitPlanet 退出星球
	QuitPlanet(context.Context, *v14.QuitPlanetRequest) (*v14.QuitPlanetReply, error)
	// QuitPlanetTarget 退出星球目标
	QuitPlanetTarget(context.Context, *v14.QuitPlanetTargetRequest) (*v14.QuitPlanetTargetReply, error)
	// ReckonUserHabit 习惯计时
	ReckonUserHabit(context.Context, *v12.ReckonUserHabitRequest) (*v12.ReckonUserHabitReply, error)
	// RecoverUserHabit 恢复用户习惯
	RecoverUserHabit(context.Context, *v12.RecoverUserHabitRequest) (*v12.RecoverUserHabitReply, error)
	// RefreshToken 刷新 token
	RefreshToken(context.Context, *v1.RefreshTokenRequest) (*v1.LoginReply, error)
	// Register 注册用户
	Register(context.Context, *v1.RegisterRequest) (*v1.RegisterReply, error)
	// RunCronTask 运行定时任务
	RunCronTask(context.Context, *v12.RunCronTaskRequest) (*v12.RunCronTaskReply, error)
	// SendVerifyCode 发送验证码
	SendVerifyCode(context.Context, *v11.SendVerifyCodeRequest) (*v11.SendVerifyCodeReply, error)
	// TodayStatisticFromHome 数据统计
	// 获取首页每日数据
	TodayStatisticFromHome(context.Context, *v15.TodayStatisticFromHomeRequest) (*v15.TodayStatisticFromHomeReply, error)
	// ToppedPlanetPost 置顶星球动态
	ToppedPlanetPost(context.Context, *v14.ToppedPlanetPostRequest) (*v14.ToppedPlanetPostReply, error)
	// UnfollowUser 取消关注
	UnfollowUser(context.Context, *v1.UnfollowUserRequest) (*v1.UnfollowUserReply, error)
	UnreadCount(context.Context, *v16.UnreadCountRequest) (*v16.UnreadCountReply, error)
	// UpdatePlanet 更新星球
	UpdatePlanet(context.Context, *v13.UpdatePlanetRequest) (*v13.UpdatePlanetReply, error)
	// UpdatePlanetPost 修改星球动态
	UpdatePlanetPost(context.Context, *v14.UpdatePlanetPostRequest) (*v14.UpdatePlanetPostReply, error)
	// UpdatePlanetTarget 更新星球目标
	UpdatePlanetTarget(context.Context, *v13.UpdatePlanetTargetRequest) (*v13.UpdatePlanetTargetReply, error)
	// UpdatePunchUserHabit 更新习惯打卡
	UpdatePunchUserHabit(context.Context, *v12.UpdatePunchUserHabitRequest) (*v12.UpdatePunchUserHabitReply, error)
	// UpdateUserAward 修改用户配置信息
	UpdateUserAward(context.Context, *v1.UpdateUserAwardRequest) (*v1.UpdateUserAwardReply, error)
	// UpdateUserHabit 更新用户习惯
	UpdateUserHabit(context.Context, *v12.UpdateUserHabitRequest) (*v12.UpdateUserHabitReply, error)
	// UpdateUserHabitMemo 更新用户习惯想法
	UpdateUserHabitMemo(context.Context, *v12.UpdateUserHabitMemoRequest) (*v12.UpdateUserHabitMemoReply, error)
	// UpdateUserHabitReckon 更新用户习惯计时
	UpdateUserHabitReckon(context.Context, *v12.UpdateUserHabitReckonRequest) (*v12.UpdateUserHabitReckonReply, error)
	// UpdateUserProfile 修改用户资料
	UpdateUserProfile(context.Context, *v1.UpdateUserProfileRequest) (*v1.UpdateUserProfileReply, error)
	// UserHabitStatisticFromDetail 获取用户习惯详情统计数据
	UserHabitStatisticFromDetail(context.Context, *v15.UserHabitStatisticFromDetailRequest) (*v15.UserHabitStatisticFromDetailReply, error)
	// VersionCheck 获取版本更新
	VersionCheck(context.Context, *v11.VersionCheckRequest) (*v11.VersionCheckReply, error)
}

func RegisterLifeLogHTTPServer(s *http.Server, srv LifeLogHTTPServer) {
	r := s.Route("/")
	r.POST("/api/v1/user/register", _LifeLog_Register0_HTTP_Handler(srv))
	r.POST("/api/v1/user/login", _LifeLog_Login0_HTTP_Handler(srv))
	r.POST("/api/v1/user/logout", _LifeLog_Logout0_HTTP_Handler(srv))
	r.POST("/api/v1/user/push-token", _LifeLog_PushToken0_HTTP_Handler(srv))
	r.POST("/api/v1/user/forget-password", _LifeLog_ForgetPassword0_HTTP_Handler(srv))
	r.POST("/api/v1/user/change-password", _LifeLog_ChangePassword0_HTTP_Handler(srv))
	r.POST("/api/v1/user", _LifeLog_DeleteUser0_HTTP_Handler(srv))
	r.POST("/api/v1/user/change-phone", _LifeLog_ChangePhone0_HTTP_Handler(srv))
	r.POST("/api/v1/user/change-email", _LifeLog_ChangeEmail0_HTTP_Handler(srv))
	r.GET("/api/v1/user/profile", _LifeLog_GetUserProfile0_HTTP_Handler(srv))
	r.PUT("/api/v1/user/profile", _LifeLog_UpdateUserProfile0_HTTP_Handler(srv))
	r.POST("/api/v1/user/{user_id}/follow", _LifeLog_FollowUser0_HTTP_Handler(srv))
	r.POST("/api/v1/user/{user_id}/unfollow", _LifeLog_UnfollowUser0_HTTP_Handler(srv))
	r.POST("/api/v1/user/refresh-token", _LifeLog_RefreshToken0_HTTP_Handler(srv))
	r.GET("/api/v1/user/setting", _LifeLog_GetUserSetting0_HTTP_Handler(srv))
	r.POST("/api/v1/user/award", _LifeLog_UpdateUserAward0_HTTP_Handler(srv))
	r.POST("/api/v1/user/privacy-password", _LifeLog_ChangeUserPrivacyPassword0_HTTP_Handler(srv))
	r.POST("/api/v1/user/privacy-password/check", _LifeLog_CheckUserPrivacyPassword0_HTTP_Handler(srv))
	r.GET("/api/v1/user/vip", _LifeLog_GetUserVipInfo0_HTTP_Handler(srv))
	r.GET("/api/v1/buddy/search", _LifeLog_BuddySearch0_HTTP_Handler(srv))
	r.POST("/api/v1/buddy/invitation", _LifeLog_BuddyInvitation0_HTTP_Handler(srv))
	r.GET("/api/v1/site-info", _LifeLog_GetSiteInfo0_HTTP_Handler(srv))
	r.GET("/api/v1/health", _LifeLog_HealthCheck0_HTTP_Handler(srv))
	r.GET("/api/v1/storage/upload-token", _LifeLog_CreateUpToken0_HTTP_Handler(srv))
	r.POST("/api/v1/storage/download-url", _LifeLog_CreateDownURL0_HTTP_Handler(srv))
	r.POST("/api/v1/sms/verify-code/send", _LifeLog_SendVerifyCode0_HTTP_Handler(srv))
	r.GET("/api/v1/motive-memo", _LifeLog_ListMotiveMemo0_HTTP_Handler(srv))
	r.POST("/api/v1/feedback", _LifeLog_CreateFeedback0_HTTP_Handler(srv))
	r.GET("/api/v1/version/check", _LifeLog_VersionCheck0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit", _LifeLog_CreateUserHabit0_HTTP_Handler(srv))
	r.GET("/api/v1/user/habit", _LifeLog_ListUserHabit0_HTTP_Handler(srv))
	r.GET("/api/v1/user/habit/{id}", _LifeLog_GetUserHabit0_HTTP_Handler(srv))
	r.PUT("/api/v1/user/habit/{id}", _LifeLog_UpdateUserHabit0_HTTP_Handler(srv))
	r.DELETE("/api/v1/user/habit/{id}", _LifeLog_DeleteUserHabit0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{id}/pause", _LifeLog_PauseUserHabit0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{id}/recover", _LifeLog_RecoverUserHabit0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{id}/archive", _LifeLog_ArchiveUserHabit0_HTTP_Handler(srv))
	r.GET("/api/v1/user/habit_snapshot", _LifeLog_ListUserHabitSnapshot0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{user_habit_id}/memo", _LifeLog_CreateUserHabitMemo0_HTTP_Handler(srv))
	r.PUT("/api/v1/user/habit/{user_habit_id}/memo/{memo_id}", _LifeLog_UpdateUserHabitMemo0_HTTP_Handler(srv))
	r.DELETE("/api/v1/user/habit/{user_habit_id}/memo/{memo_id}", _LifeLog_DeleteUserHabitMemo0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{user_habit_id}/punch", _LifeLog_PunchUserHabit0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{user_habit_id}/cancel-punch", _LifeLog_CancelPunchUserHabit0_HTTP_Handler(srv))
	r.PUT("/api/v1/user/habit/{user_habit_id}/punch/{punch_id}", _LifeLog_UpdatePunchUserHabit0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{user_habit_id}/reckon", _LifeLog_ReckonUserHabit0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{user_habit_id}/cancel-reckon", _LifeLog_CancelReckonUserHabit0_HTTP_Handler(srv))
	r.POST("/api/v1/user/habit/{user_habit_id}/reckon/save", _LifeLog_CreateUserHabitReckon0_HTTP_Handler(srv))
	r.PUT("/api/v1/user/habit/{user_habit_id}/reckon/{reckon_id}", _LifeLog_UpdateUserHabitReckon0_HTTP_Handler(srv))
	r.DELETE("/api/v1/user/habit/{user_habit_id}/reckon/{reckon_id}", _LifeLog_DeleteUserHabitReckon0_HTTP_Handler(srv))
	r.POST("/api/v1/planet", _LifeLog_CreatePlanet0_HTTP_Handler(srv))
	r.GET("/api/v1/planet/{id}", _LifeLog_GetPlanet0_HTTP_Handler(srv))
	r.PUT("/api/v1/planet/{id}", _LifeLog_UpdatePlanet0_HTTP_Handler(srv))
	r.DELETE("/api/v1/planet/{id}", _LifeLog_DeletePlanet0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/target", _LifeLog_CreatePlanetTarget0_HTTP_Handler(srv))
	r.GET("/api/v1/planet/{planet_id}/target/{id}", _LifeLog_GetPlanetTarget0_HTTP_Handler(srv))
	r.PUT("/api/v1/planet/{planet_id}/target/{id}", _LifeLog_UpdatePlanetTarget0_HTTP_Handler(srv))
	r.DELETE("/api/v1/planet/{planet_id}/target/{id}", _LifeLog_DeletePlanetTarget0_HTTP_Handler(srv))
	r.GET("/api/v1/planet/{planet_id}/target", _LifeLog_ListPlanetTarget0_HTTP_Handler(srv))
	r.GET("/api/v1/planet", _LifeLog_ListPlanetByUserID0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/join", _LifeLog_JoinPlanet0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/quit", _LifeLog_QuitPlanet0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/target/{target_id}/join", _LifeLog_JoinPlanetTarget0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/target/{target_id}/quit", _LifeLog_QuitPlanetTarget0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/post/{post_id}/like", _LifeLog_LikePlanetPost0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/post/{post_id}/cancel-like", _LifeLog_CancelLikePlanetPost0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/post/{post_id}/favorite", _LifeLog_FavoritePlanetPost0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/post/{post_id}/cancel-favorite", _LifeLog_CancelFavoritePlanetPost0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/post", _LifeLog_CreatePlanetPost0_HTTP_Handler(srv))
	r.PUT("/api/v1/planet/{planet_id}/post/{post_id}", _LifeLog_UpdatePlanetPost0_HTTP_Handler(srv))
	r.DELETE("/api/v1/planet/{planet_id}/post/{post_id}", _LifeLog_DeletePlanetPost0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/post/{post_id}/topped", _LifeLog_ToppedPlanetPost0_HTTP_Handler(srv))
	r.GET("/api/v1/planet/{planet_id}/post", _LifeLog_ListPlanetPost0_HTTP_Handler(srv))
	r.GET("/api/v1/planet/{planet_id}/top-post", _LifeLog_ListPlanetTopPost0_HTTP_Handler(srv))
	r.POST("/api/v1/planet/{planet_id}/post/{post_id}/comment", _LifeLog_CreatePlanetPostComment0_HTTP_Handler(srv))
	r.DELETE("/api/v1/planet/{planet_id}/post/{post_id}/comment/{comment_id}", _LifeLog_DeletePlanetPostComment0_HTTP_Handler(srv))
	r.GET("/api/v1/planet/{planet_id}/post/{post_id}/comment", _LifeLog_ListPlanetPostComment0_HTTP_Handler(srv))
	r.POST("/api/v1/cron/run", _LifeLog_RunCronTask0_HTTP_Handler(srv))
	r.GET("/api/v1/user_statistic/today", _LifeLog_TodayStatisticFromHome0_HTTP_Handler(srv))
	r.GET("/api/v1/user_statistic/habit/{user_habit_id}", _LifeLog_UserHabitStatisticFromDetail0_HTTP_Handler(srv))
	r.GET("/api/v1/message/unread-count", _LifeLog_UnreadCount0_HTTP_Handler(srv))
}

func _LifeLog_Register0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.RegisterRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogRegister)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Register(ctx, req.(*v1.RegisterRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.RegisterReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_Login0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.LoginRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogLogin)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Login(ctx, req.(*v1.LoginRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.LoginReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_Logout0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.LogoutRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogLogout)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Logout(ctx, req.(*v1.LogoutRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.LogoutReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_PushToken0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.PushTokenRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogPushToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PushToken(ctx, req.(*v1.PushTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.PushTokenReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ForgetPassword0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.ForgetPasswordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogForgetPassword)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ForgetPassword(ctx, req.(*v1.ForgetPasswordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.ForgetPasswordReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ChangePassword0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.ChangePasswordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogChangePassword)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangePassword(ctx, req.(*v1.ChangePasswordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.ChangePasswordReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_DeleteUser0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.DeleteUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogDeleteUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUser(ctx, req.(*v1.DeleteUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.DeleteUserReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ChangePhone0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.ChangePhoneRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogChangePhone)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangePhone(ctx, req.(*v1.ChangePhoneRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.ChangePhoneReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ChangeEmail0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.ChangeEmailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogChangeEmail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeEmail(ctx, req.(*v1.ChangeEmailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.ChangeEmailReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_GetUserProfile0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GetUserProfileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogGetUserProfile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserProfile(ctx, req.(*v1.GetUserProfileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GetUserProfileReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdateUserProfile0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.UpdateUserProfileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdateUserProfile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserProfile(ctx, req.(*v1.UpdateUserProfileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.UpdateUserProfileReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_FollowUser0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.FollowUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogFollowUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FollowUser(ctx, req.(*v1.FollowUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.FollowUserReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UnfollowUser0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.UnfollowUserRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUnfollowUser)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UnfollowUser(ctx, req.(*v1.UnfollowUserRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.UnfollowUserReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_RefreshToken0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.RefreshTokenRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogRefreshToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RefreshToken(ctx, req.(*v1.RefreshTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.LoginReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_GetUserSetting0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GetUserSettingRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogGetUserSetting)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserSetting(ctx, req.(*v1.GetUserSettingRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GetUserSettingReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdateUserAward0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.UpdateUserAwardRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdateUserAward)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserAward(ctx, req.(*v1.UpdateUserAwardRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.UpdateUserAwardReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ChangeUserPrivacyPassword0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.ChangeUserPrivacyPasswordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogChangeUserPrivacyPassword)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeUserPrivacyPassword(ctx, req.(*v1.ChangeUserPrivacyPasswordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.ChangeUserPrivacyPasswordReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CheckUserPrivacyPassword0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.CheckUserPrivacyPasswordRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCheckUserPrivacyPassword)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckUserPrivacyPassword(ctx, req.(*v1.CheckUserPrivacyPasswordRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.CheckUserPrivacyPasswordReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_GetUserVipInfo0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.GetUserVipInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogGetUserVipInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserVipInfo(ctx, req.(*v1.GetUserVipInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.GetUserVipInfoReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_BuddySearch0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.BuddySearchRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogBuddySearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuddySearch(ctx, req.(*v1.BuddySearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.BuddySearchReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_BuddyInvitation0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.BuddyInvitationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogBuddyInvitation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BuddyInvitation(ctx, req.(*v1.BuddyInvitationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.BuddyInvitationReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_GetSiteInfo0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.GetSiteInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogGetSiteInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSiteInfo(ctx, req.(*v11.GetSiteInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.GetSiteInfoReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_HealthCheck0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.HealthCheckRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogHealthCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.HealthCheck(ctx, req.(*v11.HealthCheckRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.HealthCheckReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreateUpToken0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateUpTokenRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreateUpToken)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUpToken(ctx, req.(*v11.CreateUpTokenRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.CreateUpTokenReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreateDownURL0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateDownURLRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreateDownURL)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateDownURL(ctx, req.(*v11.CreateDownURLRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.CreateDownURLReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_SendVerifyCode0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.SendVerifyCodeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogSendVerifyCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendVerifyCode(ctx, req.(*v11.SendVerifyCodeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.SendVerifyCodeReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ListMotiveMemo0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.ListMotiveMemoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogListMotiveMemo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListMotiveMemo(ctx, req.(*v11.ListMotiveMemoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListMotiveMemoReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreateFeedback0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateFeedbackRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreateFeedback)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateFeedback(ctx, req.(*v11.CreateFeedbackRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.CreateFeedbackReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_VersionCheck0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.VersionCheckRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogVersionCheck)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.VersionCheck(ctx, req.(*v11.VersionCheckRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.VersionCheckReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreateUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.CreateUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreateUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserHabit(ctx, req.(*v12.CreateUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.CreateUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ListUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.ListUserHabitRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogListUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserHabit(ctx, req.(*v12.ListUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.ListUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_GetUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.GetUserHabitRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogGetUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserHabit(ctx, req.(*v12.GetUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.GetUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdateUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.UpdateUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdateUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserHabit(ctx, req.(*v12.UpdateUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.UpdateUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_DeleteUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.DeleteUserHabitRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogDeleteUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserHabit(ctx, req.(*v12.DeleteUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.DeleteUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_PauseUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.PauseUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogPauseUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PauseUserHabit(ctx, req.(*v12.PauseUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.PauseUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_RecoverUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.RecoverUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogRecoverUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RecoverUserHabit(ctx, req.(*v12.RecoverUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.RecoverUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ArchiveUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.ArchiveUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogArchiveUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ArchiveUserHabit(ctx, req.(*v12.ArchiveUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.ArchiveUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ListUserHabitSnapshot0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.ListUserHabitSnapshotRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogListUserHabitSnapshot)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserHabitSnapshot(ctx, req.(*v12.ListUserHabitSnapshotRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.ListUserHabitSnapshotReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreateUserHabitMemo0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.CreateUserHabitMemoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreateUserHabitMemo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserHabitMemo(ctx, req.(*v12.CreateUserHabitMemoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.CreateUserHabitMemoReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdateUserHabitMemo0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.UpdateUserHabitMemoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdateUserHabitMemo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserHabitMemo(ctx, req.(*v12.UpdateUserHabitMemoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.UpdateUserHabitMemoReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_DeleteUserHabitMemo0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.DeleteUserHabitMemoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogDeleteUserHabitMemo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserHabitMemo(ctx, req.(*v12.DeleteUserHabitMemoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.DeleteUserHabitMemoReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_PunchUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.PunchUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogPunchUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PunchUserHabit(ctx, req.(*v12.PunchUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.PunchUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CancelPunchUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.CancelPunchUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCancelPunchUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CancelPunchUserHabit(ctx, req.(*v12.CancelPunchUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.CancelPunchUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdatePunchUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.UpdatePunchUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdatePunchUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdatePunchUserHabit(ctx, req.(*v12.UpdatePunchUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.UpdatePunchUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ReckonUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.ReckonUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogReckonUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReckonUserHabit(ctx, req.(*v12.ReckonUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.ReckonUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CancelReckonUserHabit0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.CancelReckonUserHabitRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCancelReckonUserHabit)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CancelReckonUserHabit(ctx, req.(*v12.CancelReckonUserHabitRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.CancelReckonUserHabitReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreateUserHabitReckon0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.CreateUserHabitReckonRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreateUserHabitReckon)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUserHabitReckon(ctx, req.(*v12.CreateUserHabitReckonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.CreateUserHabitReckonReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdateUserHabitReckon0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.UpdateUserHabitReckonRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdateUserHabitReckon)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateUserHabitReckon(ctx, req.(*v12.UpdateUserHabitReckonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.UpdateUserHabitReckonReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_DeleteUserHabitReckon0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.DeleteUserHabitReckonRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogDeleteUserHabitReckon)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteUserHabitReckon(ctx, req.(*v12.DeleteUserHabitReckonRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.DeleteUserHabitReckonReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreatePlanet0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.CreatePlanetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreatePlanet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePlanet(ctx, req.(*v13.CreatePlanetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.CreatePlanetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_GetPlanet0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.GetPlanetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogGetPlanet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPlanet(ctx, req.(*v13.GetPlanetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.GetPlanetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdatePlanet0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.UpdatePlanetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdatePlanet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdatePlanet(ctx, req.(*v13.UpdatePlanetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.UpdatePlanetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_DeletePlanet0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.DeletePlanetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogDeletePlanet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeletePlanet(ctx, req.(*v13.DeletePlanetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.DeletePlanetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreatePlanetTarget0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.CreatePlanetTargetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreatePlanetTarget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePlanetTarget(ctx, req.(*v13.CreatePlanetTargetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.CreatePlanetTargetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_GetPlanetTarget0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.GetPlanetTargetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogGetPlanetTarget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPlanetTarget(ctx, req.(*v13.GetPlanetTargetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.GetPlanetTargetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdatePlanetTarget0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.UpdatePlanetTargetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdatePlanetTarget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdatePlanetTarget(ctx, req.(*v13.UpdatePlanetTargetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.UpdatePlanetTargetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_DeletePlanetTarget0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.DeletePlanetTargetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogDeletePlanetTarget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeletePlanetTarget(ctx, req.(*v13.DeletePlanetTargetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.DeletePlanetTargetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ListPlanetTarget0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.ListPlanetTargetRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogListPlanetTarget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPlanetTarget(ctx, req.(*v13.ListPlanetTargetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.ListPlanetTargetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ListPlanetByUserID0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListPlanetByUserIDRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogListPlanetByUserID)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPlanetByUserID(ctx, req.(*v14.ListPlanetByUserIDRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListPlanetByUserIDReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_JoinPlanet0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.JoinPlanetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogJoinPlanet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JoinPlanet(ctx, req.(*v14.JoinPlanetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.JoinPlanetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_QuitPlanet0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.QuitPlanetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogQuitPlanet)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QuitPlanet(ctx, req.(*v14.QuitPlanetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.QuitPlanetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_JoinPlanetTarget0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.JoinPlanetTargetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogJoinPlanetTarget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.JoinPlanetTarget(ctx, req.(*v14.JoinPlanetTargetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.JoinPlanetTargetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_QuitPlanetTarget0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.QuitPlanetTargetRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogQuitPlanetTarget)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QuitPlanetTarget(ctx, req.(*v14.QuitPlanetTargetRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.QuitPlanetTargetReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_LikePlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.LikePlanetPostRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogLikePlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LikePlanetPost(ctx, req.(*v14.LikePlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.LikePlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CancelLikePlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CancelLikePlanetPostRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCancelLikePlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CancelLikePlanetPost(ctx, req.(*v14.CancelLikePlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CancelLikePlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_FavoritePlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.FavoritePlanetPostRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogFavoritePlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FavoritePlanetPost(ctx, req.(*v14.FavoritePlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.FavoritePlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CancelFavoritePlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CancelFavoritePlanetPostRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCancelFavoritePlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CancelFavoritePlanetPost(ctx, req.(*v14.CancelFavoritePlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CancelFavoritePlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreatePlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreatePlanetPostRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreatePlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePlanetPost(ctx, req.(*v14.CreatePlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreatePlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UpdatePlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.UpdatePlanetPostRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUpdatePlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdatePlanetPost(ctx, req.(*v14.UpdatePlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.UpdatePlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_DeletePlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.DeletePlanetPostRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogDeletePlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeletePlanetPost(ctx, req.(*v14.DeletePlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.DeletePlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ToppedPlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ToppedPlanetPostRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogToppedPlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ToppedPlanetPost(ctx, req.(*v14.ToppedPlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ToppedPlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ListPlanetPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListPlanetPostRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogListPlanetPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPlanetPost(ctx, req.(*v14.ListPlanetPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListPlanetPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ListPlanetTopPost0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListPlanetTopPostRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogListPlanetTopPost)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPlanetTopPost(ctx, req.(*v14.ListPlanetTopPostRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListPlanetTopPostReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_CreatePlanetPostComment0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreatePlanetPostCommentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogCreatePlanetPostComment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePlanetPostComment(ctx, req.(*v14.CreatePlanetPostCommentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreatePlanetPostCommentReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_DeletePlanetPostComment0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.DeletePlanetPostCommentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogDeletePlanetPostComment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeletePlanetPostComment(ctx, req.(*v14.DeletePlanetPostCommentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.DeletePlanetPostCommentReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_ListPlanetPostComment0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListPlanetPostCommentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogListPlanetPostComment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListPlanetPostComment(ctx, req.(*v14.ListPlanetPostCommentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListPlanetPostCommentReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_RunCronTask0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.RunCronTaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogRunCronTask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RunCronTask(ctx, req.(*v12.RunCronTaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.RunCronTaskReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_TodayStatisticFromHome0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v15.TodayStatisticFromHomeRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogTodayStatisticFromHome)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TodayStatisticFromHome(ctx, req.(*v15.TodayStatisticFromHomeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v15.TodayStatisticFromHomeReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UserHabitStatisticFromDetail0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v15.UserHabitStatisticFromDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUserHabitStatisticFromDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UserHabitStatisticFromDetail(ctx, req.(*v15.UserHabitStatisticFromDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v15.UserHabitStatisticFromDetailReply)
		return ctx.Result(200, reply)
	}
}

func _LifeLog_UnreadCount0_HTTP_Handler(srv LifeLogHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v16.UnreadCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationLifeLogUnreadCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UnreadCount(ctx, req.(*v16.UnreadCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v16.UnreadCountReply)
		return ctx.Result(200, reply)
	}
}

type LifeLogHTTPClient interface {
	ArchiveUserHabit(ctx context.Context, req *v12.ArchiveUserHabitRequest, opts ...http.CallOption) (rsp *v12.ArchiveUserHabitReply, err error)
	BuddyInvitation(ctx context.Context, req *v1.BuddyInvitationRequest, opts ...http.CallOption) (rsp *v1.BuddyInvitationReply, err error)
	BuddySearch(ctx context.Context, req *v1.BuddySearchRequest, opts ...http.CallOption) (rsp *v1.BuddySearchReply, err error)
	CancelFavoritePlanetPost(ctx context.Context, req *v14.CancelFavoritePlanetPostRequest, opts ...http.CallOption) (rsp *v14.CancelFavoritePlanetPostReply, err error)
	CancelLikePlanetPost(ctx context.Context, req *v14.CancelLikePlanetPostRequest, opts ...http.CallOption) (rsp *v14.CancelLikePlanetPostReply, err error)
	CancelPunchUserHabit(ctx context.Context, req *v12.CancelPunchUserHabitRequest, opts ...http.CallOption) (rsp *v12.CancelPunchUserHabitReply, err error)
	CancelReckonUserHabit(ctx context.Context, req *v12.CancelReckonUserHabitRequest, opts ...http.CallOption) (rsp *v12.CancelReckonUserHabitReply, err error)
	ChangeEmail(ctx context.Context, req *v1.ChangeEmailRequest, opts ...http.CallOption) (rsp *v1.ChangeEmailReply, err error)
	ChangePassword(ctx context.Context, req *v1.ChangePasswordRequest, opts ...http.CallOption) (rsp *v1.ChangePasswordReply, err error)
	ChangePhone(ctx context.Context, req *v1.ChangePhoneRequest, opts ...http.CallOption) (rsp *v1.ChangePhoneReply, err error)
	ChangeUserPrivacyPassword(ctx context.Context, req *v1.ChangeUserPrivacyPasswordRequest, opts ...http.CallOption) (rsp *v1.ChangeUserPrivacyPasswordReply, err error)
	CheckUserPrivacyPassword(ctx context.Context, req *v1.CheckUserPrivacyPasswordRequest, opts ...http.CallOption) (rsp *v1.CheckUserPrivacyPasswordReply, err error)
	CreateDownURL(ctx context.Context, req *v11.CreateDownURLRequest, opts ...http.CallOption) (rsp *v11.CreateDownURLReply, err error)
	CreateFeedback(ctx context.Context, req *v11.CreateFeedbackRequest, opts ...http.CallOption) (rsp *v11.CreateFeedbackReply, err error)
	CreatePlanet(ctx context.Context, req *v13.CreatePlanetRequest, opts ...http.CallOption) (rsp *v13.CreatePlanetReply, err error)
	CreatePlanetPost(ctx context.Context, req *v14.CreatePlanetPostRequest, opts ...http.CallOption) (rsp *v14.CreatePlanetPostReply, err error)
	CreatePlanetPostComment(ctx context.Context, req *v14.CreatePlanetPostCommentRequest, opts ...http.CallOption) (rsp *v14.CreatePlanetPostCommentReply, err error)
	CreatePlanetTarget(ctx context.Context, req *v13.CreatePlanetTargetRequest, opts ...http.CallOption) (rsp *v13.CreatePlanetTargetReply, err error)
	CreateUpToken(ctx context.Context, req *v11.CreateUpTokenRequest, opts ...http.CallOption) (rsp *v11.CreateUpTokenReply, err error)
	CreateUserHabit(ctx context.Context, req *v12.CreateUserHabitRequest, opts ...http.CallOption) (rsp *v12.CreateUserHabitReply, err error)
	CreateUserHabitMemo(ctx context.Context, req *v12.CreateUserHabitMemoRequest, opts ...http.CallOption) (rsp *v12.CreateUserHabitMemoReply, err error)
	CreateUserHabitReckon(ctx context.Context, req *v12.CreateUserHabitReckonRequest, opts ...http.CallOption) (rsp *v12.CreateUserHabitReckonReply, err error)
	DeletePlanet(ctx context.Context, req *v13.DeletePlanetRequest, opts ...http.CallOption) (rsp *v13.DeletePlanetReply, err error)
	DeletePlanetPost(ctx context.Context, req *v14.DeletePlanetPostRequest, opts ...http.CallOption) (rsp *v14.DeletePlanetPostReply, err error)
	DeletePlanetPostComment(ctx context.Context, req *v14.DeletePlanetPostCommentRequest, opts ...http.CallOption) (rsp *v14.DeletePlanetPostCommentReply, err error)
	DeletePlanetTarget(ctx context.Context, req *v13.DeletePlanetTargetRequest, opts ...http.CallOption) (rsp *v13.DeletePlanetTargetReply, err error)
	DeleteUser(ctx context.Context, req *v1.DeleteUserRequest, opts ...http.CallOption) (rsp *v1.DeleteUserReply, err error)
	DeleteUserHabit(ctx context.Context, req *v12.DeleteUserHabitRequest, opts ...http.CallOption) (rsp *v12.DeleteUserHabitReply, err error)
	DeleteUserHabitMemo(ctx context.Context, req *v12.DeleteUserHabitMemoRequest, opts ...http.CallOption) (rsp *v12.DeleteUserHabitMemoReply, err error)
	DeleteUserHabitReckon(ctx context.Context, req *v12.DeleteUserHabitReckonRequest, opts ...http.CallOption) (rsp *v12.DeleteUserHabitReckonReply, err error)
	FavoritePlanetPost(ctx context.Context, req *v14.FavoritePlanetPostRequest, opts ...http.CallOption) (rsp *v14.FavoritePlanetPostReply, err error)
	FollowUser(ctx context.Context, req *v1.FollowUserRequest, opts ...http.CallOption) (rsp *v1.FollowUserReply, err error)
	ForgetPassword(ctx context.Context, req *v1.ForgetPasswordRequest, opts ...http.CallOption) (rsp *v1.ForgetPasswordReply, err error)
	GetPlanet(ctx context.Context, req *v13.GetPlanetRequest, opts ...http.CallOption) (rsp *v13.GetPlanetReply, err error)
	GetPlanetTarget(ctx context.Context, req *v13.GetPlanetTargetRequest, opts ...http.CallOption) (rsp *v13.GetPlanetTargetReply, err error)
	GetSiteInfo(ctx context.Context, req *v11.GetSiteInfoRequest, opts ...http.CallOption) (rsp *v11.GetSiteInfoReply, err error)
	GetUserHabit(ctx context.Context, req *v12.GetUserHabitRequest, opts ...http.CallOption) (rsp *v12.GetUserHabitReply, err error)
	GetUserProfile(ctx context.Context, req *v1.GetUserProfileRequest, opts ...http.CallOption) (rsp *v1.GetUserProfileReply, err error)
	GetUserSetting(ctx context.Context, req *v1.GetUserSettingRequest, opts ...http.CallOption) (rsp *v1.GetUserSettingReply, err error)
	GetUserVipInfo(ctx context.Context, req *v1.GetUserVipInfoRequest, opts ...http.CallOption) (rsp *v1.GetUserVipInfoReply, err error)
	HealthCheck(ctx context.Context, req *v11.HealthCheckRequest, opts ...http.CallOption) (rsp *v11.HealthCheckReply, err error)
	JoinPlanet(ctx context.Context, req *v14.JoinPlanetRequest, opts ...http.CallOption) (rsp *v14.JoinPlanetReply, err error)
	JoinPlanetTarget(ctx context.Context, req *v14.JoinPlanetTargetRequest, opts ...http.CallOption) (rsp *v14.JoinPlanetTargetReply, err error)
	LikePlanetPost(ctx context.Context, req *v14.LikePlanetPostRequest, opts ...http.CallOption) (rsp *v14.LikePlanetPostReply, err error)
	ListMotiveMemo(ctx context.Context, req *v11.ListMotiveMemoRequest, opts ...http.CallOption) (rsp *v11.ListMotiveMemoReply, err error)
	ListPlanetByUserID(ctx context.Context, req *v14.ListPlanetByUserIDRequest, opts ...http.CallOption) (rsp *v14.ListPlanetByUserIDReply, err error)
	ListPlanetPost(ctx context.Context, req *v14.ListPlanetPostRequest, opts ...http.CallOption) (rsp *v14.ListPlanetPostReply, err error)
	ListPlanetPostComment(ctx context.Context, req *v14.ListPlanetPostCommentRequest, opts ...http.CallOption) (rsp *v14.ListPlanetPostCommentReply, err error)
	ListPlanetTarget(ctx context.Context, req *v13.ListPlanetTargetRequest, opts ...http.CallOption) (rsp *v13.ListPlanetTargetReply, err error)
	ListPlanetTopPost(ctx context.Context, req *v14.ListPlanetTopPostRequest, opts ...http.CallOption) (rsp *v14.ListPlanetTopPostReply, err error)
	ListUserHabit(ctx context.Context, req *v12.ListUserHabitRequest, opts ...http.CallOption) (rsp *v12.ListUserHabitReply, err error)
	ListUserHabitSnapshot(ctx context.Context, req *v12.ListUserHabitSnapshotRequest, opts ...http.CallOption) (rsp *v12.ListUserHabitSnapshotReply, err error)
	Login(ctx context.Context, req *v1.LoginRequest, opts ...http.CallOption) (rsp *v1.LoginReply, err error)
	Logout(ctx context.Context, req *v1.LogoutRequest, opts ...http.CallOption) (rsp *v1.LogoutReply, err error)
	PauseUserHabit(ctx context.Context, req *v12.PauseUserHabitRequest, opts ...http.CallOption) (rsp *v12.PauseUserHabitReply, err error)
	PunchUserHabit(ctx context.Context, req *v12.PunchUserHabitRequest, opts ...http.CallOption) (rsp *v12.PunchUserHabitReply, err error)
	PushToken(ctx context.Context, req *v1.PushTokenRequest, opts ...http.CallOption) (rsp *v1.PushTokenReply, err error)
	QuitPlanet(ctx context.Context, req *v14.QuitPlanetRequest, opts ...http.CallOption) (rsp *v14.QuitPlanetReply, err error)
	QuitPlanetTarget(ctx context.Context, req *v14.QuitPlanetTargetRequest, opts ...http.CallOption) (rsp *v14.QuitPlanetTargetReply, err error)
	ReckonUserHabit(ctx context.Context, req *v12.ReckonUserHabitRequest, opts ...http.CallOption) (rsp *v12.ReckonUserHabitReply, err error)
	RecoverUserHabit(ctx context.Context, req *v12.RecoverUserHabitRequest, opts ...http.CallOption) (rsp *v12.RecoverUserHabitReply, err error)
	RefreshToken(ctx context.Context, req *v1.RefreshTokenRequest, opts ...http.CallOption) (rsp *v1.LoginReply, err error)
	Register(ctx context.Context, req *v1.RegisterRequest, opts ...http.CallOption) (rsp *v1.RegisterReply, err error)
	RunCronTask(ctx context.Context, req *v12.RunCronTaskRequest, opts ...http.CallOption) (rsp *v12.RunCronTaskReply, err error)
	SendVerifyCode(ctx context.Context, req *v11.SendVerifyCodeRequest, opts ...http.CallOption) (rsp *v11.SendVerifyCodeReply, err error)
	TodayStatisticFromHome(ctx context.Context, req *v15.TodayStatisticFromHomeRequest, opts ...http.CallOption) (rsp *v15.TodayStatisticFromHomeReply, err error)
	ToppedPlanetPost(ctx context.Context, req *v14.ToppedPlanetPostRequest, opts ...http.CallOption) (rsp *v14.ToppedPlanetPostReply, err error)
	UnfollowUser(ctx context.Context, req *v1.UnfollowUserRequest, opts ...http.CallOption) (rsp *v1.UnfollowUserReply, err error)
	UnreadCount(ctx context.Context, req *v16.UnreadCountRequest, opts ...http.CallOption) (rsp *v16.UnreadCountReply, err error)
	UpdatePlanet(ctx context.Context, req *v13.UpdatePlanetRequest, opts ...http.CallOption) (rsp *v13.UpdatePlanetReply, err error)
	UpdatePlanetPost(ctx context.Context, req *v14.UpdatePlanetPostRequest, opts ...http.CallOption) (rsp *v14.UpdatePlanetPostReply, err error)
	UpdatePlanetTarget(ctx context.Context, req *v13.UpdatePlanetTargetRequest, opts ...http.CallOption) (rsp *v13.UpdatePlanetTargetReply, err error)
	UpdatePunchUserHabit(ctx context.Context, req *v12.UpdatePunchUserHabitRequest, opts ...http.CallOption) (rsp *v12.UpdatePunchUserHabitReply, err error)
	UpdateUserAward(ctx context.Context, req *v1.UpdateUserAwardRequest, opts ...http.CallOption) (rsp *v1.UpdateUserAwardReply, err error)
	UpdateUserHabit(ctx context.Context, req *v12.UpdateUserHabitRequest, opts ...http.CallOption) (rsp *v12.UpdateUserHabitReply, err error)
	UpdateUserHabitMemo(ctx context.Context, req *v12.UpdateUserHabitMemoRequest, opts ...http.CallOption) (rsp *v12.UpdateUserHabitMemoReply, err error)
	UpdateUserHabitReckon(ctx context.Context, req *v12.UpdateUserHabitReckonRequest, opts ...http.CallOption) (rsp *v12.UpdateUserHabitReckonReply, err error)
	UpdateUserProfile(ctx context.Context, req *v1.UpdateUserProfileRequest, opts ...http.CallOption) (rsp *v1.UpdateUserProfileReply, err error)
	UserHabitStatisticFromDetail(ctx context.Context, req *v15.UserHabitStatisticFromDetailRequest, opts ...http.CallOption) (rsp *v15.UserHabitStatisticFromDetailReply, err error)
	VersionCheck(ctx context.Context, req *v11.VersionCheckRequest, opts ...http.CallOption) (rsp *v11.VersionCheckReply, err error)
}

type LifeLogHTTPClientImpl struct {
	cc *http.Client
}

func NewLifeLogHTTPClient(client *http.Client) LifeLogHTTPClient {
	return &LifeLogHTTPClientImpl{client}
}

func (c *LifeLogHTTPClientImpl) ArchiveUserHabit(ctx context.Context, in *v12.ArchiveUserHabitRequest, opts ...http.CallOption) (*v12.ArchiveUserHabitReply, error) {
	var out v12.ArchiveUserHabitReply
	pattern := "/api/v1/user/habit/{id}/archive"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogArchiveUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) BuddyInvitation(ctx context.Context, in *v1.BuddyInvitationRequest, opts ...http.CallOption) (*v1.BuddyInvitationReply, error) {
	var out v1.BuddyInvitationReply
	pattern := "/api/v1/buddy/invitation"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogBuddyInvitation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) BuddySearch(ctx context.Context, in *v1.BuddySearchRequest, opts ...http.CallOption) (*v1.BuddySearchReply, error) {
	var out v1.BuddySearchReply
	pattern := "/api/v1/buddy/search"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogBuddySearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CancelFavoritePlanetPost(ctx context.Context, in *v14.CancelFavoritePlanetPostRequest, opts ...http.CallOption) (*v14.CancelFavoritePlanetPostReply, error) {
	var out v14.CancelFavoritePlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}/cancel-favorite"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCancelFavoritePlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CancelLikePlanetPost(ctx context.Context, in *v14.CancelLikePlanetPostRequest, opts ...http.CallOption) (*v14.CancelLikePlanetPostReply, error) {
	var out v14.CancelLikePlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}/cancel-like"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCancelLikePlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CancelPunchUserHabit(ctx context.Context, in *v12.CancelPunchUserHabitRequest, opts ...http.CallOption) (*v12.CancelPunchUserHabitReply, error) {
	var out v12.CancelPunchUserHabitReply
	pattern := "/api/v1/user/habit/{user_habit_id}/cancel-punch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCancelPunchUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CancelReckonUserHabit(ctx context.Context, in *v12.CancelReckonUserHabitRequest, opts ...http.CallOption) (*v12.CancelReckonUserHabitReply, error) {
	var out v12.CancelReckonUserHabitReply
	pattern := "/api/v1/user/habit/{user_habit_id}/cancel-reckon"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCancelReckonUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ChangeEmail(ctx context.Context, in *v1.ChangeEmailRequest, opts ...http.CallOption) (*v1.ChangeEmailReply, error) {
	var out v1.ChangeEmailReply
	pattern := "/api/v1/user/change-email"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogChangeEmail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ChangePassword(ctx context.Context, in *v1.ChangePasswordRequest, opts ...http.CallOption) (*v1.ChangePasswordReply, error) {
	var out v1.ChangePasswordReply
	pattern := "/api/v1/user/change-password"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogChangePassword))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ChangePhone(ctx context.Context, in *v1.ChangePhoneRequest, opts ...http.CallOption) (*v1.ChangePhoneReply, error) {
	var out v1.ChangePhoneReply
	pattern := "/api/v1/user/change-phone"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogChangePhone))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ChangeUserPrivacyPassword(ctx context.Context, in *v1.ChangeUserPrivacyPasswordRequest, opts ...http.CallOption) (*v1.ChangeUserPrivacyPasswordReply, error) {
	var out v1.ChangeUserPrivacyPasswordReply
	pattern := "/api/v1/user/privacy-password"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogChangeUserPrivacyPassword))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CheckUserPrivacyPassword(ctx context.Context, in *v1.CheckUserPrivacyPasswordRequest, opts ...http.CallOption) (*v1.CheckUserPrivacyPasswordReply, error) {
	var out v1.CheckUserPrivacyPasswordReply
	pattern := "/api/v1/user/privacy-password/check"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCheckUserPrivacyPassword))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreateDownURL(ctx context.Context, in *v11.CreateDownURLRequest, opts ...http.CallOption) (*v11.CreateDownURLReply, error) {
	var out v11.CreateDownURLReply
	pattern := "/api/v1/storage/download-url"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreateDownURL))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreateFeedback(ctx context.Context, in *v11.CreateFeedbackRequest, opts ...http.CallOption) (*v11.CreateFeedbackReply, error) {
	var out v11.CreateFeedbackReply
	pattern := "/api/v1/feedback"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreateFeedback))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreatePlanet(ctx context.Context, in *v13.CreatePlanetRequest, opts ...http.CallOption) (*v13.CreatePlanetReply, error) {
	var out v13.CreatePlanetReply
	pattern := "/api/v1/planet"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreatePlanet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreatePlanetPost(ctx context.Context, in *v14.CreatePlanetPostRequest, opts ...http.CallOption) (*v14.CreatePlanetPostReply, error) {
	var out v14.CreatePlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreatePlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreatePlanetPostComment(ctx context.Context, in *v14.CreatePlanetPostCommentRequest, opts ...http.CallOption) (*v14.CreatePlanetPostCommentReply, error) {
	var out v14.CreatePlanetPostCommentReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}/comment"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreatePlanetPostComment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreatePlanetTarget(ctx context.Context, in *v13.CreatePlanetTargetRequest, opts ...http.CallOption) (*v13.CreatePlanetTargetReply, error) {
	var out v13.CreatePlanetTargetReply
	pattern := "/api/v1/planet/{planet_id}/target"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreatePlanetTarget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreateUpToken(ctx context.Context, in *v11.CreateUpTokenRequest, opts ...http.CallOption) (*v11.CreateUpTokenReply, error) {
	var out v11.CreateUpTokenReply
	pattern := "/api/v1/storage/upload-token"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogCreateUpToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreateUserHabit(ctx context.Context, in *v12.CreateUserHabitRequest, opts ...http.CallOption) (*v12.CreateUserHabitReply, error) {
	var out v12.CreateUserHabitReply
	pattern := "/api/v1/user/habit"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreateUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreateUserHabitMemo(ctx context.Context, in *v12.CreateUserHabitMemoRequest, opts ...http.CallOption) (*v12.CreateUserHabitMemoReply, error) {
	var out v12.CreateUserHabitMemoReply
	pattern := "/api/v1/user/habit/{user_habit_id}/memo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreateUserHabitMemo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) CreateUserHabitReckon(ctx context.Context, in *v12.CreateUserHabitReckonRequest, opts ...http.CallOption) (*v12.CreateUserHabitReckonReply, error) {
	var out v12.CreateUserHabitReckonReply
	pattern := "/api/v1/user/habit/{user_habit_id}/reckon/save"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogCreateUserHabitReckon))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) DeletePlanet(ctx context.Context, in *v13.DeletePlanetRequest, opts ...http.CallOption) (*v13.DeletePlanetReply, error) {
	var out v13.DeletePlanetReply
	pattern := "/api/v1/planet/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogDeletePlanet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) DeletePlanetPost(ctx context.Context, in *v14.DeletePlanetPostRequest, opts ...http.CallOption) (*v14.DeletePlanetPostReply, error) {
	var out v14.DeletePlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogDeletePlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) DeletePlanetPostComment(ctx context.Context, in *v14.DeletePlanetPostCommentRequest, opts ...http.CallOption) (*v14.DeletePlanetPostCommentReply, error) {
	var out v14.DeletePlanetPostCommentReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}/comment/{comment_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogDeletePlanetPostComment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) DeletePlanetTarget(ctx context.Context, in *v13.DeletePlanetTargetRequest, opts ...http.CallOption) (*v13.DeletePlanetTargetReply, error) {
	var out v13.DeletePlanetTargetReply
	pattern := "/api/v1/planet/{planet_id}/target/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogDeletePlanetTarget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) DeleteUser(ctx context.Context, in *v1.DeleteUserRequest, opts ...http.CallOption) (*v1.DeleteUserReply, error) {
	var out v1.DeleteUserReply
	pattern := "/api/v1/user"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogDeleteUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) DeleteUserHabit(ctx context.Context, in *v12.DeleteUserHabitRequest, opts ...http.CallOption) (*v12.DeleteUserHabitReply, error) {
	var out v12.DeleteUserHabitReply
	pattern := "/api/v1/user/habit/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogDeleteUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) DeleteUserHabitMemo(ctx context.Context, in *v12.DeleteUserHabitMemoRequest, opts ...http.CallOption) (*v12.DeleteUserHabitMemoReply, error) {
	var out v12.DeleteUserHabitMemoReply
	pattern := "/api/v1/user/habit/{user_habit_id}/memo/{memo_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogDeleteUserHabitMemo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) DeleteUserHabitReckon(ctx context.Context, in *v12.DeleteUserHabitReckonRequest, opts ...http.CallOption) (*v12.DeleteUserHabitReckonReply, error) {
	var out v12.DeleteUserHabitReckonReply
	pattern := "/api/v1/user/habit/{user_habit_id}/reckon/{reckon_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogDeleteUserHabitReckon))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) FavoritePlanetPost(ctx context.Context, in *v14.FavoritePlanetPostRequest, opts ...http.CallOption) (*v14.FavoritePlanetPostReply, error) {
	var out v14.FavoritePlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}/favorite"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogFavoritePlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) FollowUser(ctx context.Context, in *v1.FollowUserRequest, opts ...http.CallOption) (*v1.FollowUserReply, error) {
	var out v1.FollowUserReply
	pattern := "/api/v1/user/{user_id}/follow"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogFollowUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ForgetPassword(ctx context.Context, in *v1.ForgetPasswordRequest, opts ...http.CallOption) (*v1.ForgetPasswordReply, error) {
	var out v1.ForgetPasswordReply
	pattern := "/api/v1/user/forget-password"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogForgetPassword))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) GetPlanet(ctx context.Context, in *v13.GetPlanetRequest, opts ...http.CallOption) (*v13.GetPlanetReply, error) {
	var out v13.GetPlanetReply
	pattern := "/api/v1/planet/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogGetPlanet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) GetPlanetTarget(ctx context.Context, in *v13.GetPlanetTargetRequest, opts ...http.CallOption) (*v13.GetPlanetTargetReply, error) {
	var out v13.GetPlanetTargetReply
	pattern := "/api/v1/planet/{planet_id}/target/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogGetPlanetTarget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) GetSiteInfo(ctx context.Context, in *v11.GetSiteInfoRequest, opts ...http.CallOption) (*v11.GetSiteInfoReply, error) {
	var out v11.GetSiteInfoReply
	pattern := "/api/v1/site-info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogGetSiteInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) GetUserHabit(ctx context.Context, in *v12.GetUserHabitRequest, opts ...http.CallOption) (*v12.GetUserHabitReply, error) {
	var out v12.GetUserHabitReply
	pattern := "/api/v1/user/habit/{id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogGetUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) GetUserProfile(ctx context.Context, in *v1.GetUserProfileRequest, opts ...http.CallOption) (*v1.GetUserProfileReply, error) {
	var out v1.GetUserProfileReply
	pattern := "/api/v1/user/profile"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogGetUserProfile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) GetUserSetting(ctx context.Context, in *v1.GetUserSettingRequest, opts ...http.CallOption) (*v1.GetUserSettingReply, error) {
	var out v1.GetUserSettingReply
	pattern := "/api/v1/user/setting"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogGetUserSetting))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) GetUserVipInfo(ctx context.Context, in *v1.GetUserVipInfoRequest, opts ...http.CallOption) (*v1.GetUserVipInfoReply, error) {
	var out v1.GetUserVipInfoReply
	pattern := "/api/v1/user/vip"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogGetUserVipInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) HealthCheck(ctx context.Context, in *v11.HealthCheckRequest, opts ...http.CallOption) (*v11.HealthCheckReply, error) {
	var out v11.HealthCheckReply
	pattern := "/api/v1/health"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogHealthCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) JoinPlanet(ctx context.Context, in *v14.JoinPlanetRequest, opts ...http.CallOption) (*v14.JoinPlanetReply, error) {
	var out v14.JoinPlanetReply
	pattern := "/api/v1/planet/{planet_id}/join"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogJoinPlanet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) JoinPlanetTarget(ctx context.Context, in *v14.JoinPlanetTargetRequest, opts ...http.CallOption) (*v14.JoinPlanetTargetReply, error) {
	var out v14.JoinPlanetTargetReply
	pattern := "/api/v1/planet/{planet_id}/target/{target_id}/join"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogJoinPlanetTarget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) LikePlanetPost(ctx context.Context, in *v14.LikePlanetPostRequest, opts ...http.CallOption) (*v14.LikePlanetPostReply, error) {
	var out v14.LikePlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}/like"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogLikePlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ListMotiveMemo(ctx context.Context, in *v11.ListMotiveMemoRequest, opts ...http.CallOption) (*v11.ListMotiveMemoReply, error) {
	var out v11.ListMotiveMemoReply
	pattern := "/api/v1/motive-memo"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogListMotiveMemo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ListPlanetByUserID(ctx context.Context, in *v14.ListPlanetByUserIDRequest, opts ...http.CallOption) (*v14.ListPlanetByUserIDReply, error) {
	var out v14.ListPlanetByUserIDReply
	pattern := "/api/v1/planet"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogListPlanetByUserID))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ListPlanetPost(ctx context.Context, in *v14.ListPlanetPostRequest, opts ...http.CallOption) (*v14.ListPlanetPostReply, error) {
	var out v14.ListPlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogListPlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ListPlanetPostComment(ctx context.Context, in *v14.ListPlanetPostCommentRequest, opts ...http.CallOption) (*v14.ListPlanetPostCommentReply, error) {
	var out v14.ListPlanetPostCommentReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}/comment"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogListPlanetPostComment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ListPlanetTarget(ctx context.Context, in *v13.ListPlanetTargetRequest, opts ...http.CallOption) (*v13.ListPlanetTargetReply, error) {
	var out v13.ListPlanetTargetReply
	pattern := "/api/v1/planet/{planet_id}/target"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogListPlanetTarget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ListPlanetTopPost(ctx context.Context, in *v14.ListPlanetTopPostRequest, opts ...http.CallOption) (*v14.ListPlanetTopPostReply, error) {
	var out v14.ListPlanetTopPostReply
	pattern := "/api/v1/planet/{planet_id}/top-post"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogListPlanetTopPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ListUserHabit(ctx context.Context, in *v12.ListUserHabitRequest, opts ...http.CallOption) (*v12.ListUserHabitReply, error) {
	var out v12.ListUserHabitReply
	pattern := "/api/v1/user/habit"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogListUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ListUserHabitSnapshot(ctx context.Context, in *v12.ListUserHabitSnapshotRequest, opts ...http.CallOption) (*v12.ListUserHabitSnapshotReply, error) {
	var out v12.ListUserHabitSnapshotReply
	pattern := "/api/v1/user/habit_snapshot"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogListUserHabitSnapshot))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) Login(ctx context.Context, in *v1.LoginRequest, opts ...http.CallOption) (*v1.LoginReply, error) {
	var out v1.LoginReply
	pattern := "/api/v1/user/login"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogLogin))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) Logout(ctx context.Context, in *v1.LogoutRequest, opts ...http.CallOption) (*v1.LogoutReply, error) {
	var out v1.LogoutReply
	pattern := "/api/v1/user/logout"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogLogout))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) PauseUserHabit(ctx context.Context, in *v12.PauseUserHabitRequest, opts ...http.CallOption) (*v12.PauseUserHabitReply, error) {
	var out v12.PauseUserHabitReply
	pattern := "/api/v1/user/habit/{id}/pause"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogPauseUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) PunchUserHabit(ctx context.Context, in *v12.PunchUserHabitRequest, opts ...http.CallOption) (*v12.PunchUserHabitReply, error) {
	var out v12.PunchUserHabitReply
	pattern := "/api/v1/user/habit/{user_habit_id}/punch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogPunchUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) PushToken(ctx context.Context, in *v1.PushTokenRequest, opts ...http.CallOption) (*v1.PushTokenReply, error) {
	var out v1.PushTokenReply
	pattern := "/api/v1/user/push-token"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogPushToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) QuitPlanet(ctx context.Context, in *v14.QuitPlanetRequest, opts ...http.CallOption) (*v14.QuitPlanetReply, error) {
	var out v14.QuitPlanetReply
	pattern := "/api/v1/planet/{planet_id}/quit"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogQuitPlanet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) QuitPlanetTarget(ctx context.Context, in *v14.QuitPlanetTargetRequest, opts ...http.CallOption) (*v14.QuitPlanetTargetReply, error) {
	var out v14.QuitPlanetTargetReply
	pattern := "/api/v1/planet/{planet_id}/target/{target_id}/quit"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogQuitPlanetTarget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ReckonUserHabit(ctx context.Context, in *v12.ReckonUserHabitRequest, opts ...http.CallOption) (*v12.ReckonUserHabitReply, error) {
	var out v12.ReckonUserHabitReply
	pattern := "/api/v1/user/habit/{user_habit_id}/reckon"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogReckonUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) RecoverUserHabit(ctx context.Context, in *v12.RecoverUserHabitRequest, opts ...http.CallOption) (*v12.RecoverUserHabitReply, error) {
	var out v12.RecoverUserHabitReply
	pattern := "/api/v1/user/habit/{id}/recover"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogRecoverUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) RefreshToken(ctx context.Context, in *v1.RefreshTokenRequest, opts ...http.CallOption) (*v1.LoginReply, error) {
	var out v1.LoginReply
	pattern := "/api/v1/user/refresh-token"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogRefreshToken))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) Register(ctx context.Context, in *v1.RegisterRequest, opts ...http.CallOption) (*v1.RegisterReply, error) {
	var out v1.RegisterReply
	pattern := "/api/v1/user/register"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogRegister))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) RunCronTask(ctx context.Context, in *v12.RunCronTaskRequest, opts ...http.CallOption) (*v12.RunCronTaskReply, error) {
	var out v12.RunCronTaskReply
	pattern := "/api/v1/cron/run"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogRunCronTask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) SendVerifyCode(ctx context.Context, in *v11.SendVerifyCodeRequest, opts ...http.CallOption) (*v11.SendVerifyCodeReply, error) {
	var out v11.SendVerifyCodeReply
	pattern := "/api/v1/sms/verify-code/send"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogSendVerifyCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) TodayStatisticFromHome(ctx context.Context, in *v15.TodayStatisticFromHomeRequest, opts ...http.CallOption) (*v15.TodayStatisticFromHomeReply, error) {
	var out v15.TodayStatisticFromHomeReply
	pattern := "/api/v1/user_statistic/today"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogTodayStatisticFromHome))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) ToppedPlanetPost(ctx context.Context, in *v14.ToppedPlanetPostRequest, opts ...http.CallOption) (*v14.ToppedPlanetPostReply, error) {
	var out v14.ToppedPlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}/topped"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogToppedPlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UnfollowUser(ctx context.Context, in *v1.UnfollowUserRequest, opts ...http.CallOption) (*v1.UnfollowUserReply, error) {
	var out v1.UnfollowUserReply
	pattern := "/api/v1/user/{user_id}/unfollow"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUnfollowUser))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UnreadCount(ctx context.Context, in *v16.UnreadCountRequest, opts ...http.CallOption) (*v16.UnreadCountReply, error) {
	var out v16.UnreadCountReply
	pattern := "/api/v1/message/unread-count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogUnreadCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdatePlanet(ctx context.Context, in *v13.UpdatePlanetRequest, opts ...http.CallOption) (*v13.UpdatePlanetReply, error) {
	var out v13.UpdatePlanetReply
	pattern := "/api/v1/planet/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdatePlanet))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdatePlanetPost(ctx context.Context, in *v14.UpdatePlanetPostRequest, opts ...http.CallOption) (*v14.UpdatePlanetPostReply, error) {
	var out v14.UpdatePlanetPostReply
	pattern := "/api/v1/planet/{planet_id}/post/{post_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdatePlanetPost))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdatePlanetTarget(ctx context.Context, in *v13.UpdatePlanetTargetRequest, opts ...http.CallOption) (*v13.UpdatePlanetTargetReply, error) {
	var out v13.UpdatePlanetTargetReply
	pattern := "/api/v1/planet/{planet_id}/target/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdatePlanetTarget))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdatePunchUserHabit(ctx context.Context, in *v12.UpdatePunchUserHabitRequest, opts ...http.CallOption) (*v12.UpdatePunchUserHabitReply, error) {
	var out v12.UpdatePunchUserHabitReply
	pattern := "/api/v1/user/habit/{user_habit_id}/punch/{punch_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdatePunchUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdateUserAward(ctx context.Context, in *v1.UpdateUserAwardRequest, opts ...http.CallOption) (*v1.UpdateUserAwardReply, error) {
	var out v1.UpdateUserAwardReply
	pattern := "/api/v1/user/award"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdateUserAward))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdateUserHabit(ctx context.Context, in *v12.UpdateUserHabitRequest, opts ...http.CallOption) (*v12.UpdateUserHabitReply, error) {
	var out v12.UpdateUserHabitReply
	pattern := "/api/v1/user/habit/{id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdateUserHabit))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdateUserHabitMemo(ctx context.Context, in *v12.UpdateUserHabitMemoRequest, opts ...http.CallOption) (*v12.UpdateUserHabitMemoReply, error) {
	var out v12.UpdateUserHabitMemoReply
	pattern := "/api/v1/user/habit/{user_habit_id}/memo/{memo_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdateUserHabitMemo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdateUserHabitReckon(ctx context.Context, in *v12.UpdateUserHabitReckonRequest, opts ...http.CallOption) (*v12.UpdateUserHabitReckonReply, error) {
	var out v12.UpdateUserHabitReckonReply
	pattern := "/api/v1/user/habit/{user_habit_id}/reckon/{reckon_id}"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdateUserHabitReckon))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UpdateUserProfile(ctx context.Context, in *v1.UpdateUserProfileRequest, opts ...http.CallOption) (*v1.UpdateUserProfileReply, error) {
	var out v1.UpdateUserProfileReply
	pattern := "/api/v1/user/profile"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationLifeLogUpdateUserProfile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "PUT", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) UserHabitStatisticFromDetail(ctx context.Context, in *v15.UserHabitStatisticFromDetailRequest, opts ...http.CallOption) (*v15.UserHabitStatisticFromDetailReply, error) {
	var out v15.UserHabitStatisticFromDetailReply
	pattern := "/api/v1/user_statistic/habit/{user_habit_id}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogUserHabitStatisticFromDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *LifeLogHTTPClientImpl) VersionCheck(ctx context.Context, in *v11.VersionCheckRequest, opts ...http.CallOption) (*v11.VersionCheckReply, error) {
	var out v11.VersionCheckReply
	pattern := "/api/v1/version/check"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationLifeLogVersionCheck))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
