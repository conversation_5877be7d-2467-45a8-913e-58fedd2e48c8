["Internal Server Error"]
description = "Internal Server Error"
one = "Internal Server Error"
other = "Internal Server Error"

["Params Error"]
description = "Params Error"
one = "Params Error"
other = "Params Error"

["password is not allow"]
description = "password is not allow"
one = "password is not allow"
other = "password is not allow"

["password is not same"]
description = "password is not same"
one = "password is not same"
other = "password is not same"

["password is wrong"]
description = "password is wrong"
one = "password is wrong"
other = "password is wrong"

["pause user habit error"]
description = "pause user habit error"
one = "pause user habit error"
other = "pause user habit error"

["phone is registered"]
description = "phone is registered"
one = "phone is registered"
other = "phone is registered"

["phone is used"]
description = "phone is used"
one = "phone is used"
other = "phone is used"

["reckon user habit error"]
description = "reckon user habit error"
one = "reckon user habit error"
other = "reckon user habit error"

["recover user habit error"]
description = "recover user habit error"
one = "recover user habit error"
other = "recover user habit error"

["user can not unfollow self"]
description = "user can not unfollow self"
one = "user can not unfollow self"
other = "user can not unfollow self"

["user is not vip"]
description = "user is not vip"
one = "user is not vip"
other = "user is not vip"

["user or password is wrong"]
description = "user or password is wrong"
one = "user or password is wrong"
other = "user or password is wrong"

["user_name is exited"]
description = "user_name is exited"
one = "{{.Name}} is exited"
other = "{{.Name}} is exited"

["verify_code is expired"]
description = "verify_code is expired"
one = "verify_code is expired"
other = "verify_code is expired"

["verify_code is limited"]
description = "verify_code is limited"
one = "verify_code is limited"
other = "verify_code is limited"

["verify_code is wrong"]
description = "verify_code is wrong"
one = "verify_code is wrong"
other = "verify_code is wrong"
