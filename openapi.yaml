# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: LifeLog API
    version: 0.0.1
paths:
    /api/v1/buddy/invitation:
        post:
            tags:
                - LifeLog
            description: 发送搭子邀请
            operationId: LifeLog_BuddyInvitation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.BuddyInvitationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.BuddyInvitationReply'
    /api/v1/buddy/search:
        get:
            tags:
                - LifeLog
            description: 搭子搜索
            operationId: LifeLog_BuddySearch
            parameters:
                - name: uid
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.BuddySearchReply'
    /api/v1/cron/run:
        post:
            tags:
                - LifeLog
            description: 运行定时任务
            operationId: LifeLog_RunCronTask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.RunCronTaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.RunCronTaskReply'
    /api/v1/feedback:
        post:
            tags:
                - LifeLog
            description: 添加意见反馈
            operationId: LifeLog_CreateFeedback
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/site_v1.CreateFeedbackRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/site_v1.CreateFeedbackReply'
    /api/v1/health:
        get:
            tags:
                - LifeLog
            description: 健康检查接口
            operationId: LifeLog_HealthCheck
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/site_v1.HealthCheckReply'
    /api/v1/message/unread-count:
        get:
            tags:
                - LifeLog
            operationId: LifeLog_UnreadCount
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_message_v1.UnreadCountReply'
    /api/v1/motive-memo:
        get:
            tags:
                - LifeLog
            description: 获取每日随想
            operationId: LifeLog_ListMotiveMemo
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/site_v1.ListMotiveMemoReply'
    /api/v1/planet:
        get:
            tags:
                - LifeLog
            description: 获取用户加入星球列表
            operationId: LifeLog_ListPlanetByUserID
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.ListPlanetByUserIDReply'
        post:
            tags:
                - LifeLog
            description: 创建星球
            operationId: LifeLog_CreatePlanet
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/planet_v1.CreatePlanetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.CreatePlanetReply'
    /api/v1/planet/{id}:
        get:
            tags:
                - LifeLog
            description: 获取星球详情
            operationId: LifeLog_GetPlanet
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.GetPlanetReply'
        put:
            tags:
                - LifeLog
            description: 更新星球
            operationId: LifeLog_UpdatePlanet
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/planet_v1.UpdatePlanetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.UpdatePlanetReply'
        delete:
            tags:
                - LifeLog
            description: 删除星球
            operationId: LifeLog_DeletePlanet
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.DeletePlanetReply'
    /api/v1/planet/{planetId}/join:
        post:
            tags:
                - LifeLog
            description: 加入星球
            operationId: LifeLog_JoinPlanet
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.JoinPlanetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.JoinPlanetReply'
    /api/v1/planet/{planetId}/post:
        get:
            tags:
                - LifeLog
            description: 获取星球动态列表
            operationId: LifeLog_ListPlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: labelType
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.ListPlanetPostReply'
        post:
            tags:
                - LifeLog
            description: 创建星球动态
            operationId: LifeLog_CreatePlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.CreatePlanetPostRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.CreatePlanetPostReply'
    /api/v1/planet/{planetId}/post/{postId}:
        put:
            tags:
                - LifeLog
            description: 修改星球动态
            operationId: LifeLog_UpdatePlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.UpdatePlanetPostRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.UpdatePlanetPostReply'
        delete:
            tags:
                - LifeLog
            description: 删除星球动态
            operationId: LifeLog_DeletePlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.DeletePlanetPostReply'
    /api/v1/planet/{planetId}/post/{postId}/cancel-favorite:
        post:
            tags:
                - LifeLog
            description: 取消收藏星球动态
            operationId: LifeLog_CancelFavoritePlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.CancelFavoritePlanetPostRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.CancelFavoritePlanetPostReply'
    /api/v1/planet/{planetId}/post/{postId}/cancel-like:
        post:
            tags:
                - LifeLog
            description: 取消点赞星球动态
            operationId: LifeLog_CancelLikePlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.CancelLikePlanetPostRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.CancelLikePlanetPostReply'
    /api/v1/planet/{planetId}/post/{postId}/comment:
        get:
            tags:
                - LifeLog
            description: 获取动态评论列表
            operationId: LifeLog_ListPlanetPostComment
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.ListPlanetPostCommentReply'
        post:
            tags:
                - LifeLog
            description: 创建动态评论
            operationId: LifeLog_CreatePlanetPostComment
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.CreatePlanetPostCommentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.CreatePlanetPostCommentReply'
    /api/v1/planet/{planetId}/post/{postId}/comment/{commentId}:
        delete:
            tags:
                - LifeLog
            description: 删除动态评论
            operationId: LifeLog_DeletePlanetPostComment
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: commentId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.DeletePlanetPostCommentReply'
    /api/v1/planet/{planetId}/post/{postId}/favorite:
        post:
            tags:
                - LifeLog
            description: 收藏星球动态
            operationId: LifeLog_FavoritePlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.FavoritePlanetPostRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.FavoritePlanetPostReply'
    /api/v1/planet/{planetId}/post/{postId}/like:
        post:
            tags:
                - LifeLog
            description: 点赞星球动态
            operationId: LifeLog_LikePlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.LikePlanetPostRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.LikePlanetPostReply'
    /api/v1/planet/{planetId}/post/{postId}/topped:
        post:
            tags:
                - LifeLog
            description: 置顶星球动态
            operationId: LifeLog_ToppedPlanetPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: postId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.ToppedPlanetPostRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.ToppedPlanetPostReply'
    /api/v1/planet/{planetId}/quit:
        post:
            tags:
                - LifeLog
            description: 退出星球
            operationId: LifeLog_QuitPlanet
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.QuitPlanetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.QuitPlanetReply'
    /api/v1/planet/{planetId}/target:
        get:
            tags:
                - LifeLog
            description: 获取星球目标列表
            operationId: LifeLog_ListPlanetTarget
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.ListPlanetTargetReply'
        post:
            tags:
                - LifeLog
            description: 创建星球目标
            operationId: LifeLog_CreatePlanetTarget
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/planet_v1.CreatePlanetTargetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.CreatePlanetTargetReply'
    /api/v1/planet/{planetId}/target/{id}:
        get:
            tags:
                - LifeLog
            description: 获取星球目标详情
            operationId: LifeLog_GetPlanetTarget
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.GetPlanetTargetReply'
        put:
            tags:
                - LifeLog
            description: 更新星球目标
            operationId: LifeLog_UpdatePlanetTarget
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/planet_v1.UpdatePlanetTargetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.UpdatePlanetTargetReply'
        delete:
            tags:
                - LifeLog
            description: 删除星球目标
            operationId: LifeLog_DeletePlanetTarget
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/planet_v1.DeletePlanetTargetReply'
    /api/v1/planet/{planetId}/target/{targetId}/join:
        post:
            tags:
                - LifeLog
            description: 加入星球目标
            operationId: LifeLog_JoinPlanetTarget
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: targetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.JoinPlanetTargetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.JoinPlanetTargetReply'
    /api/v1/planet/{planetId}/target/{targetId}/quit:
        post:
            tags:
                - LifeLog
            description: 退出星球目标
            operationId: LifeLog_QuitPlanetTarget
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: targetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_planet_v1.QuitPlanetTargetRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.QuitPlanetTargetReply'
    /api/v1/planet/{planetId}/top-post:
        get:
            tags:
                - LifeLog
            description: 获取星球热点动态列表
            operationId: LifeLog_ListPlanetTopPost
            parameters:
                - name: planetId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: page
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_planet_v1.ListPlanetTopPostReply'
    /api/v1/site-info:
        get:
            tags:
                - LifeLog
            description: 获取网站信息
            operationId: LifeLog_GetSiteInfo
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/site_v1.GetSiteInfoReply'
    /api/v1/sms/verify-code/send:
        post:
            tags:
                - LifeLog
            description: 发送验证码
            operationId: LifeLog_SendVerifyCode
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/site_v1.SendVerifyCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/site_v1.SendVerifyCodeReply'
    /api/v1/storage/download-url:
        post:
            tags:
                - LifeLog
            description: 获取七牛下载地址
            operationId: LifeLog_CreateDownURL
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/site_v1.CreateDownURLRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/site_v1.CreateDownURLReply'
    /api/v1/storage/upload-token:
        get:
            tags:
                - LifeLog
            description: 获取七牛上传凭证
            operationId: LifeLog_CreateUpToken
            parameters:
                - name: scene
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/site_v1.CreateUpTokenReply'
    /api/v1/user:
        post:
            tags:
                - LifeLog
            description: 删除用户
            operationId: LifeLog_DeleteUser
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.DeleteUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.DeleteUserReply'
    /api/v1/user/award:
        post:
            tags:
                - LifeLog
            description: 修改用户配置信息
            operationId: LifeLog_UpdateUserAward
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.UpdateUserAwardRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.UpdateUserAwardReply'
    /api/v1/user/change-email:
        post:
            tags:
                - LifeLog
            description: 修改邮箱
            operationId: LifeLog_ChangeEmail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.ChangeEmailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.ChangeEmailReply'
    /api/v1/user/change-password:
        post:
            tags:
                - LifeLog
            description: 修改密码
            operationId: LifeLog_ChangePassword
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.ChangePasswordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.ChangePasswordReply'
    /api/v1/user/change-phone:
        post:
            tags:
                - LifeLog
            description: 修改手机号
            operationId: LifeLog_ChangePhone
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.ChangePhoneRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.ChangePhoneReply'
    /api/v1/user/forget-password:
        post:
            tags:
                - LifeLog
            description: 忘记密码
            operationId: LifeLog_ForgetPassword
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.ForgetPasswordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.ForgetPasswordReply'
    /api/v1/user/habit:
        get:
            tags:
                - LifeLog
            description: 获取用户习惯列表
            operationId: LifeLog_ListUserHabit
            parameters:
                - name: status
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: currentDate
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.ListUserHabitReply'
        post:
            tags:
                - LifeLog
            description: 创建用户习惯
            operationId: LifeLog_CreateUserHabit
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.CreateUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.CreateUserHabitReply'
    /api/v1/user/habit/{id}:
        get:
            tags:
                - LifeLog
            description: 获取用户习惯详情
            operationId: LifeLog_GetUserHabit
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: currentDate
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.GetUserHabitReply'
        put:
            tags:
                - LifeLog
            description: 更新用户习惯
            operationId: LifeLog_UpdateUserHabit
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.UpdateUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.UpdateUserHabitReply'
        delete:
            tags:
                - LifeLog
            description: 删除用户习惯
            operationId: LifeLog_DeleteUserHabit
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.DeleteUserHabitReply'
    /api/v1/user/habit/{id}/archive:
        post:
            tags:
                - LifeLog
            description: 归档用户习惯
            operationId: LifeLog_ArchiveUserHabit
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.ArchiveUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.ArchiveUserHabitReply'
    /api/v1/user/habit/{id}/pause:
        post:
            tags:
                - LifeLog
            description: 暂停用户习惯
            operationId: LifeLog_PauseUserHabit
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.PauseUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.PauseUserHabitReply'
    /api/v1/user/habit/{id}/recover:
        post:
            tags:
                - LifeLog
            description: 恢复用户习惯
            operationId: LifeLog_RecoverUserHabit
            parameters:
                - name: id
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.RecoverUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.RecoverUserHabitReply'
    /api/v1/user/habit/{userHabitId}/cancel-punch:
        post:
            tags:
                - LifeLog
            description: 取消习惯打卡
            operationId: LifeLog_CancelPunchUserHabit
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.CancelPunchUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.CancelPunchUserHabitReply'
    /api/v1/user/habit/{userHabitId}/cancel-reckon:
        post:
            tags:
                - LifeLog
            description: 取消习惯计时
            operationId: LifeLog_CancelReckonUserHabit
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.CancelReckonUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.CancelReckonUserHabitReply'
    /api/v1/user/habit/{userHabitId}/memo:
        post:
            tags:
                - LifeLog
            description: 创建用户习惯想法
            operationId: LifeLog_CreateUserHabitMemo
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.CreateUserHabitMemoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.CreateUserHabitMemoReply'
    /api/v1/user/habit/{userHabitId}/memo/{memoId}:
        put:
            tags:
                - LifeLog
            description: 更新用户习惯想法
            operationId: LifeLog_UpdateUserHabitMemo
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: memoId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.UpdateUserHabitMemoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.UpdateUserHabitMemoReply'
        delete:
            tags:
                - LifeLog
            description: 删除用户习惯想法
            operationId: LifeLog_DeleteUserHabitMemo
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: memoId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.DeleteUserHabitMemoReply'
    /api/v1/user/habit/{userHabitId}/punch:
        post:
            tags:
                - LifeLog
            description: 习惯打卡
            operationId: LifeLog_PunchUserHabit
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.PunchUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.PunchUserHabitReply'
    /api/v1/user/habit/{userHabitId}/punch/{punchId}:
        put:
            tags:
                - LifeLog
            description: 更新习惯打卡
            operationId: LifeLog_UpdatePunchUserHabit
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: punchId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.UpdatePunchUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.UpdatePunchUserHabitReply'
    /api/v1/user/habit/{userHabitId}/reckon:
        post:
            tags:
                - LifeLog
            description: 习惯计时
            operationId: LifeLog_ReckonUserHabit
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.ReckonUserHabitRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.ReckonUserHabitReply'
    /api/v1/user/habit/{userHabitId}/reckon/save:
        post:
            tags:
                - LifeLog
            description: 创建用户习惯计时
            operationId: LifeLog_CreateUserHabitReckon
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.CreateUserHabitReckonRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.CreateUserHabitReckonReply'
    /api/v1/user/habit/{userHabitId}/reckon/{reckonId}:
        put:
            tags:
                - LifeLog
            description: 更新用户习惯计时
            operationId: LifeLog_UpdateUserHabitReckon
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: reckonId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_habit_v1.UpdateUserHabitReckonRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.UpdateUserHabitReckonReply'
        delete:
            tags:
                - LifeLog
            description: 删除用户习惯计时
            operationId: LifeLog_DeleteUserHabitReckon
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: reckonId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.DeleteUserHabitReckonReply'
    /api/v1/user/habit_snapshot:
        get:
            tags:
                - LifeLog
            description: 获取用户每日习惯列表
            operationId: LifeLog_ListUserHabitSnapshot
            parameters:
                - name: labelType
                  in: query
                  schema:
                    type: integer
                    format: int32
                - name: currentDate
                  in: query
                  description: 'eg: 2023-11-26T07:00:00+08:00'
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_habit_v1.ListUserHabitSnapshotReply'
    /api/v1/user/login:
        post:
            tags:
                - LifeLog
            description: 登录
            operationId: LifeLog_Login
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.LoginRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.LoginReply'
    /api/v1/user/logout:
        post:
            tags:
                - LifeLog
            description: 登出
            operationId: LifeLog_Logout
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.LogoutRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.LogoutReply'
    /api/v1/user/privacy-password:
        post:
            tags:
                - LifeLog
            description: 修改用户隐私密码
            operationId: LifeLog_ChangeUserPrivacyPassword
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.ChangeUserPrivacyPasswordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.ChangeUserPrivacyPasswordReply'
    /api/v1/user/privacy-password/check:
        post:
            tags:
                - LifeLog
            description: 检查用户隐私密码
            operationId: LifeLog_CheckUserPrivacyPassword
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.CheckUserPrivacyPasswordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.CheckUserPrivacyPasswordReply'
    /api/v1/user/profile:
        get:
            tags:
                - LifeLog
            description: 获取用户资料
            operationId: LifeLog_GetUserProfile
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.GetUserProfileReply'
        put:
            tags:
                - LifeLog
            description: 修改用户资料
            operationId: LifeLog_UpdateUserProfile
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.UpdateUserProfileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.UpdateUserProfileReply'
    /api/v1/user/push-token:
        post:
            tags:
                - LifeLog
            description: 推送 token 和设备信息
            operationId: LifeLog_PushToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.PushTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.PushTokenReply'
    /api/v1/user/refresh-token:
        post:
            tags:
                - LifeLog
            description: 刷新 token
            operationId: LifeLog_RefreshToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.RefreshTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.LoginReply'
    /api/v1/user/register:
        post:
            tags:
                - LifeLog
            description: 注册用户
            operationId: LifeLog_Register
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.RegisterRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.RegisterReply'
    /api/v1/user/setting:
        get:
            tags:
                - LifeLog
            description: 获取用户配置信息
            operationId: LifeLog_GetUserSetting
            parameters:
                - name: scene
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.GetUserSettingReply'
    /api/v1/user/vip:
        get:
            tags:
                - LifeLog
            description: 获取用户会员详情
            operationId: LifeLog_GetUserVipInfo
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.GetUserVipInfoReply'
    /api/v1/user/{userId}/follow:
        post:
            tags:
                - LifeLog
            description: 关注
            operationId: LifeLog_FollowUser
            parameters:
                - name: userId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.FollowUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.FollowUserReply'
    /api/v1/user/{userId}/unfollow:
        post:
            tags:
                - LifeLog
            description: 取消关注
            operationId: LifeLog_UnfollowUser
            parameters:
                - name: userId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user_v1.UnfollowUserRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user_v1.UnfollowUserReply'
    /api/v1/user_statistic/habit/{userHabitId}:
        get:
            tags:
                - LifeLog
            description: 获取用户习惯详情统计数据
            operationId: LifeLog_UserHabitStatisticFromDetail
            parameters:
                - name: userHabitId
                  in: path
                  required: true
                  schema:
                    type: integer
                    format: int32
                - name: currentDate
                  in: query
                  schema:
                    type: string
                - name: labelType
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.user_statistic_v1.UserHabitStatisticFromDetailReply'
    /api/v1/user_statistic/today:
        get:
            tags:
                - LifeLog
            description: |-
                数据统计
                 获取首页每日数据
            operationId: LifeLog_TodayStatisticFromHome
            parameters:
                - name: currentDate
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.user_statistic_v1.TodayStatisticFromHomeReply'
    /api/v1/version/check:
        get:
            tags:
                - LifeLog
            description: 获取版本更新
            operationId: LifeLog_VersionCheck
            parameters:
                - name: currentVersion
                  in: query
                  schema:
                    type: string
                - name: platform
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/site_v1.VersionCheckReply'
components:
    schemas:
        api.user_statistic_v1.TodayStatisticFromHomeReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/api.user_statistic_v1.TodayStatisticFromHomeReply_Data'
        api.user_statistic_v1.TodayStatisticFromHomeReply_Data:
            type: object
            properties:
                smallHabit:
                    $ref: '#/components/schemas/api.user_statistic_v1.TodayStatisticFromHomeReply_HabitDetail'
                normalHabit:
                    $ref: '#/components/schemas/api.user_statistic_v1.TodayStatisticFromHomeReply_HabitDetail'
                overComplete:
                    type: string
                unComplete:
                    type: string
        api.user_statistic_v1.TodayStatisticFromHomeReply_HabitDetail:
            type: object
            properties:
                per:
                    type: integer
                    format: int32
                doneCount:
                    type: integer
                    format: int32
                allCount:
                    type: integer
                    format: int32
        api.user_statistic_v1.UserHabitStatisticFromDetailReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/api.user_statistic_v1.UserHabitStatisticFromDetailReply_Data'
        api.user_statistic_v1.UserHabitStatisticFromDetailReply_AllStatistic:
            type: object
            properties:
                per:
                    type: integer
                    format: int32
                doneCount:
                    type: integer
                    format: int32
                allCount:
                    type: integer
                    format: int32
                persistLongDays:
                    type: integer
                    format: int32
                avgData:
                    type: string
                maxData:
                    type: string
                minData:
                    type: string
                persistStartDate:
                    type: string
                persistEndDate:
                    type: string
        api.user_statistic_v1.UserHabitStatisticFromDetailReply_Data:
            type: object
            properties:
                allStatistic:
                    $ref: '#/components/schemas/api.user_statistic_v1.UserHabitStatisticFromDetailReply_AllStatistic'
                weekStatistic:
                    $ref: '#/components/schemas/api.user_statistic_v1.UserHabitStatisticFromDetailReply_WeekStatistic'
                monthStatistic:
                    $ref: '#/components/schemas/api.user_statistic_v1.UserHabitStatisticFromDetailReply_MonthStatistic'
        api.user_statistic_v1.UserHabitStatisticFromDetailReply_Detail:
            type: object
            properties:
                day:
                    type: integer
                    format: int32
                doneCount:
                    type: integer
                    format: int32
                stages:
                    type: array
                    items:
                        type: integer
                        format: int32
        api.user_statistic_v1.UserHabitStatisticFromDetailReply_MonthStatistic:
            type: object
            properties:
                doneCount:
                    type: integer
                    format: int32
                allCount:
                    type: integer
                    format: int32
                stages:
                    type: array
                    items:
                        type: integer
                        format: int32
                detail:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.user_statistic_v1.UserHabitStatisticFromDetailReply_Detail'
                chartLeftCount:
                    type: array
                    items:
                        type: integer
                        format: int32
                avgData:
                    type: string
        api.user_statistic_v1.UserHabitStatisticFromDetailReply_WeekStatistic:
            type: object
            properties:
                doneCount:
                    type: integer
                    format: int32
                allCount:
                    type: integer
                    format: int32
                stages:
                    type: array
                    items:
                        type: integer
                        format: int32
                detail:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.user_statistic_v1.UserHabitStatisticFromDetailReply_Detail'
                chartLeftCount:
                    type: array
                    items:
                        type: integer
                        format: int32
                avgData:
                    type: string
        planet_v1.CreatePlanetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        planet_v1.CreatePlanetRequest:
            type: object
            properties:
                name:
                    type: string
                imgUrl:
                    type: string
                description:
                    type: string
        planet_v1.CreatePlanetTargetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        planet_v1.CreatePlanetTargetRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                name:
                    type: string
        planet_v1.DeletePlanetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        planet_v1.DeletePlanetTargetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        planet_v1.GetPlanetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/planet_v1.GetPlanetReply_Data'
        planet_v1.GetPlanetReply_Data:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                imgUrl:
                    type: string
                description:
                    type: string
        planet_v1.GetPlanetTargetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/planet_v1.GetPlanetTargetReply_Data'
        planet_v1.GetPlanetTargetReply_Data:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                planetId:
                    type: integer
                    format: int32
        planet_v1.ListPlanetTargetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/planet_v1.ListPlanetTargetReply_Data'
        planet_v1.ListPlanetTargetReply_Data:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                planetId:
                    type: integer
                    format: int32
        planet_v1.UpdatePlanetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        planet_v1.UpdatePlanetRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                imgUrl:
                    type: string
                description:
                    type: string
        planet_v1.UpdatePlanetTargetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        planet_v1.UpdatePlanetTargetRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                planetId:
                    type: integer
                    format: int32
        site_v1.CreateDownURLReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/site_v1.CreateDownURLReply_Data'
        site_v1.CreateDownURLReply_Data:
            type: object
            properties:
                url:
                    type: string
        site_v1.CreateDownURLRequest:
            type: object
            properties:
                key:
                    type: string
                scene:
                    type: string
        site_v1.CreateFeedbackReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        site_v1.CreateFeedbackRequest:
            type: object
            properties:
                content:
                    type: string
                email:
                    type: string
                imgs:
                    type: array
                    items:
                        type: string
                nickname:
                    type: string
        site_v1.CreateUpTokenReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/site_v1.CreateUpTokenReply_Data'
        site_v1.CreateUpTokenReply_Data:
            type: object
            properties:
                token:
                    type: string
        site_v1.GetSiteInfoReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/site_v1.GetSiteInfoReply_Data'
        site_v1.GetSiteInfoReply_Data:
            type: object
            properties:
                version:
                    type: string
                agreement:
                    type: string
                privacyPolicy:
                    type: string
        site_v1.HealthCheckReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/site_v1.HealthCheckReply_Data'
            description: 健康检查响应
        site_v1.HealthCheckReply_Data:
            type: object
            properties:
                timestamp:
                    type: string
                formattedTime:
                    type: string
        site_v1.ListMotiveMemoReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/site_v1.ListMotiveMemoReply_Data'
        site_v1.ListMotiveMemoReply_Data:
            type: object
            properties:
                motives:
                    type: array
                    items:
                        $ref: '#/components/schemas/site_v1.ListMotiveMemoReply_Motive'
        site_v1.ListMotiveMemoReply_Motive:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                content:
                    type: string
                from:
                    type: string
        site_v1.SendVerifyCodeReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        site_v1.SendVerifyCodeRequest:
            type: object
            properties:
                email:
                    type: string
                phone:
                    type: string
                scene:
                    type: string
        site_v1.VersionCheckReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/site_v1.VersionCheckReply_Data'
        site_v1.VersionCheckReply_Data:
            type: object
            properties:
                hasUpdate:
                    type: boolean
                forceUpdate:
                    type: boolean
                latestVersion:
                    type: string
                currentVersion:
                    type: string
                updateTitle:
                    type: string
                updateContent:
                    type: string
                downloadUrl:
                    type: string
                iosAppStoreUrl:
                    type: string
                androidDirectUrl:
                    type: string
                fileSize:
                    type: integer
                    format: uint32
        user_habit_v1.ArchiveUserHabitReply:
            type: object
            properties: {}
        user_habit_v1.ArchiveUserHabitRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
        user_habit_v1.CancelPunchUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.CancelPunchUserHabitRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                smallStageId:
                    type: integer
                    format: int32
                currentDate:
                    type: string
                punchId:
                    type: integer
                    format: int32
        user_habit_v1.CancelReckonUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.CancelReckonUserHabitRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                smallStageId:
                    type: integer
                    format: int32
                currentDate:
                    type: string
        user_habit_v1.CreateUserHabitMemoReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.CreateUserHabitMemoRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                content:
                    type: string
                images:
                    type: array
                    items:
                        type: string
                smallStageId:
                    type: integer
                    format: int32
                currentDate:
                    type: string
        user_habit_v1.CreateUserHabitReckonReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.CreateUserHabitReckonRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                reckonDuration:
                    type: integer
                    format: int32
                smallStageId:
                    type: integer
                    format: int32
                currentDate:
                    type: string
        user_habit_v1.CreateUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.CreateUserHabitRequest:
            type: object
            properties:
                name:
                    type: string
                config:
                    $ref: '#/components/schemas/user_habit_v1.UserHabitConfig'
                createDate:
                    type: string
                habitType:
                    type: integer
                    format: int32
                timezonePlace:
                    type: string
                timezone:
                    type: string
        user_habit_v1.DeleteUserHabitMemoReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.DeleteUserHabitReckonReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.DeleteUserHabitReply:
            type: object
            properties: {}
        user_habit_v1.GetUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_habit_v1.GetUserHabitReply_Data'
        user_habit_v1.GetUserHabitReply_Data:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                status:
                    type: integer
                    format: int32
                habitType:
                    type: integer
                    format: int32
                config:
                    $ref: '#/components/schemas/user_habit_v1.UserHabitConfig'
                punchedTotal:
                    type: integer
                    format: int32
                calendar:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_habit_v1.GetUserHabitReply_DayData'
                timeline:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_habit_v1.GetUserHabitReply_TimeLine'
                desc:
                    type: string
                isCompleted:
                    type: boolean
                createdAt:
                    type: integer
                    format: int32
        user_habit_v1.GetUserHabitReply_DayData:
            type: object
            properties:
                dateTimestamp:
                    type: integer
                    format: int32
                completeLevel:
                    type: integer
                    format: int32
        user_habit_v1.GetUserHabitReply_TimeLine:
            type: object
            properties:
                time:
                    type: integer
                    format: int32
                operateType:
                    type: string
                smallStageName:
                    type: string
                smallStageId:
                    type: integer
                    format: int32
                duration:
                    type: integer
                    format: int32
                memoContent:
                    type: string
                punchId:
                    type: integer
                    format: int32
                memoId:
                    type: integer
                    format: int32
                reckonId:
                    type: integer
                    format: int32
        user_habit_v1.ListUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_habit_v1.ListUserHabitReply_Data'
        user_habit_v1.ListUserHabitReply_Data:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                status:
                    type: integer
                    format: int32
                punchedDays:
                    type: integer
                    format: int32
                punchAllDays:
                    type: integer
                    format: int32
                punchCycleType:
                    type: integer
                    format: int32
                punchCycle:
                    type: array
                    items:
                        type: integer
                        format: int32
                streakDays:
                    type: integer
                    format: int32
                isSetPrivacy:
                    type: boolean
                createdAt:
                    type: integer
                    format: int32
                endAt:
                    type: integer
                    format: int32
                healthStatus:
                    type: string
        user_habit_v1.ListUserHabitSnapshotReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_habit_v1.ListUserHabitSnapshotReply_Data'
        user_habit_v1.ListUserHabitSnapshotReply_Data:
            type: object
            properties:
                todayStatisticData:
                    $ref: '#/components/schemas/user_habit_v1.TodayStatistic'
                topHabits:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_habit_v1.ListUserHabitSnapshotReply_HabitItem'
                lowHabits:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_habit_v1.ListUserHabitSnapshotReply_HabitItem'
                trackHabits:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_habit_v1.ListUserHabitSnapshotReply_HabitItem'
                isNeedShow:
                    type: boolean
                pendingMotivationHabits:
                    type: array
                    items:
                        type: integer
                        format: int32
        user_habit_v1.ListUserHabitSnapshotReply_HabitItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                habitType:
                    type: integer
                    format: int32
                smallStages:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_habit_v1.ListUserHabitSnapshotReply_SmallStageItem'
                isReckoning:
                    type: boolean
                duration:
                    type: integer
                    format: int32
                isAllowCancelPunch:
                    type: boolean
                isJoinAward:
                    type: boolean
                isNecessary:
                    type: boolean
                isSetPrivacy:
                    type: boolean
                createdAt:
                    type: integer
                    format: int32
                punchCycleType:
                    type: integer
                    format: int32
                completed:
                    type: boolean
                per:
                    type: integer
                    format: int32
                doneCount:
                    type: integer
                    format: int32
                allCount:
                    type: integer
                    format: int32
                endDate:
                    type: string
        user_habit_v1.ListUserHabitSnapshotReply_SmallStageItem:
            type: object
            properties:
                stageId:
                    type: integer
                    format: int32
                name:
                    type: string
                punchedCount:
                    type: integer
                    format: int32
                isReckoning:
                    type: boolean
        user_habit_v1.PauseUserHabitReply:
            type: object
            properties: {}
        user_habit_v1.PauseUserHabitRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
        user_habit_v1.PunchUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.PunchUserHabitRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                smallStageId:
                    type: integer
                    format: int32
                currentDate:
                    type: string
        user_habit_v1.ReckonUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_habit_v1.ReckonUserHabitReply_Data'
        user_habit_v1.ReckonUserHabitReply_Data:
            type: object
            properties:
                reckonDuration:
                    type: integer
                    format: int32
                isReckoning:
                    type: boolean
        user_habit_v1.ReckonUserHabitRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                reckonDuration:
                    type: integer
                    format: int32
                smallStageId:
                    type: integer
                    format: int32
                currentDate:
                    type: string
                isEnd:
                    type: boolean
        user_habit_v1.RecoverUserHabitReply:
            type: object
            properties: {}
        user_habit_v1.RecoverUserHabitRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
        user_habit_v1.RunCronTaskReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.RunCronTaskRequest:
            type: object
            properties:
                taskId:
                    type: integer
                    format: int32
                notRunTag:
                    type: boolean
        user_habit_v1.TodayStatistic:
            type: object
            properties:
                smallHabit:
                    $ref: '#/components/schemas/user_habit_v1.TodayStatistic_HabitDetail'
                normalHabit:
                    $ref: '#/components/schemas/user_habit_v1.TodayStatistic_HabitDetail'
                weekData:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_habit_v1.TodayStatistic_WeekData'
        user_habit_v1.TodayStatistic_HabitDetail:
            type: object
            properties:
                per:
                    type: integer
                    format: int32
                doneCount:
                    type: integer
                    format: int32
                allCount:
                    type: integer
                    format: int32
        user_habit_v1.TodayStatistic_WeekData:
            type: object
            properties:
                day:
                    type: string
                progress:
                    type: string
                isToday:
                    type: boolean
        user_habit_v1.UpdatePunchUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.UpdatePunchUserHabitRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                punchId:
                    type: integer
                    format: int32
                currentDate:
                    type: string
        user_habit_v1.UpdateUserHabitMemoReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.UpdateUserHabitMemoRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                content:
                    type: string
                memoId:
                    type: integer
                    format: int32
        user_habit_v1.UpdateUserHabitReckonReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.UpdateUserHabitReckonRequest:
            type: object
            properties:
                userHabitId:
                    type: integer
                    format: int32
                reckonDuration:
                    type: integer
                    format: int32
                reckonId:
                    type: integer
                    format: int32
                smallStageId:
                    type: integer
                    format: int32
        user_habit_v1.UpdateUserHabitReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_habit_v1.UpdateUserHabitRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                config:
                    $ref: '#/components/schemas/user_habit_v1.UserHabitConfig'
                updateDate:
                    type: string
                timezonePlace:
                    type: string
                timezone:
                    type: string
        user_habit_v1.UserHabitConfig:
            type: object
            properties:
                punchMaxCount:
                    type: integer
                    format: int32
                punchCycleType:
                    type: integer
                    format: int32
                punchCycle:
                    type: array
                    items:
                        type: integer
                        format: int32
                smallStages:
                    type: array
                    items:
                        type: string
                stagePunches:
                    type: array
                    items:
                        type: integer
                        format: int32
                isAllowCancelPunch:
                    type: boolean
                isJoinAward:
                    type: boolean
                isSetPrivacy:
                    type: boolean
                privacyDisplayMode:
                    type: string
                privacyDisplayContent:
                    type: string
                recordType:
                    type: integer
                    format: int32
                endDate:
                    type: string
                reminderTimes:
                    type: array
                    items:
                        type: string
                buddyList:
                    type: array
                    items:
                        type: integer
                        format: int32
        user_message_v1.UnreadCountReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_message_v1.UnreadCountReply_Data'
        user_message_v1.UnreadCountReply_Data:
            type: object
            properties:
                unreadCount:
                    type: integer
                    format: int32
        user_planet_v1.CancelFavoritePlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.CancelFavoritePlanetPostRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                postId:
                    type: integer
                    format: int32
        user_planet_v1.CancelLikePlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.CancelLikePlanetPostRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                postId:
                    type: integer
                    format: int32
        user_planet_v1.CreatePlanetPostCommentReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.CreatePlanetPostCommentRequest:
            type: object
            properties:
                commentId:
                    type: integer
                    format: int32
                postId:
                    type: integer
                    format: int32
                content:
                    type: string
                planetId:
                    type: integer
                    format: int32
        user_planet_v1.CreatePlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.CreatePlanetPostRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                content:
                    type: string
                images:
                    type: array
                    items:
                        type: string
        user_planet_v1.DeletePlanetPostCommentReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.DeletePlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.FavoritePlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.FavoritePlanetPostRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                postId:
                    type: integer
                    format: int32
        user_planet_v1.JoinPlanetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.JoinPlanetRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
        user_planet_v1.JoinPlanetTargetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.JoinPlanetTargetRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                targetId:
                    type: integer
                    format: int32
        user_planet_v1.LikePlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.LikePlanetPostRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                postId:
                    type: integer
                    format: int32
        user_planet_v1.ListPlanetByUserIDReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_planet_v1.ListPlanetByUserIDReply_Data'
        user_planet_v1.ListPlanetByUserIDReply_Data:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                name:
                    type: string
                imgUrl:
                    type: string
        user_planet_v1.ListPlanetPostCommentReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_planet_v1.ListPlanetPostCommentReply_Data'
        user_planet_v1.ListPlanetPostCommentReply_Data:
            type: object
            properties:
                page:
                    type: integer
                    format: int32
                pageSize:
                    type: integer
                    format: int32
                total:
                    type: integer
                    format: int32
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_planet_v1.ListPlanetPostCommentReply_Item'
        user_planet_v1.ListPlanetPostCommentReply_Item:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                postId:
                    type: integer
                    format: int32
                content:
                    type: string
                planetId:
                    type: integer
                    format: int32
                user:
                    $ref: '#/components/schemas/user_planet_v1.UserInfo'
                subItems:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_planet_v1.SubCommentItem'
                createdAt:
                    type: integer
                    format: int32
                subCount:
                    type: integer
                    format: int32
        user_planet_v1.ListPlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_planet_v1.ListPlanetPostReply_Data'
        user_planet_v1.ListPlanetPostReply_Data:
            type: object
            properties:
                page:
                    type: integer
                    format: int32
                pageSize:
                    type: integer
                    format: int32
                total:
                    type: integer
                    format: int32
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_planet_v1.ListPlanetPostReply_Item'
        user_planet_v1.ListPlanetPostReply_Item:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                user:
                    $ref: '#/components/schemas/user_planet_v1.UserInfo'
                content:
                    type: string
                planetId:
                    type: integer
                    format: int32
                likeCount:
                    type: integer
                    format: int32
                favoriteCount:
                    type: integer
                    format: int32
                commentCount:
                    type: integer
                    format: int32
                createdAt:
                    type: integer
                    format: int32
                isLike:
                    type: boolean
                isFavorite:
                    type: boolean
        user_planet_v1.ListPlanetTopPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_planet_v1.ListPlanetTopPostReply_Data'
        user_planet_v1.ListPlanetTopPostReply_Data:
            type: object
            properties:
                page:
                    type: integer
                    format: int32
                pageSize:
                    type: integer
                    format: int32
                total:
                    type: integer
                    format: int32
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_planet_v1.ListPlanetTopPostReply_Item'
        user_planet_v1.ListPlanetTopPostReply_Item:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                planetId:
                    type: integer
                    format: int32
                content:
                    type: string
        user_planet_v1.QuitPlanetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.QuitPlanetRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
        user_planet_v1.QuitPlanetTargetReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.QuitPlanetTargetRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                targetId:
                    type: integer
                    format: int32
        user_planet_v1.SubCommentItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                postId:
                    type: integer
                    format: int32
                content:
                    type: string
                planetId:
                    type: integer
                    format: int32
                createdAt:
                    type: integer
                    format: int32
                user:
                    $ref: '#/components/schemas/user_planet_v1.UserInfo'
                repliedUser:
                    $ref: '#/components/schemas/user_planet_v1.UserInfo'
        user_planet_v1.ToppedPlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.ToppedPlanetPostRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                postId:
                    type: integer
                    format: int32
        user_planet_v1.UpdatePlanetPostReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_planet_v1.UpdatePlanetPostRequest:
            type: object
            properties:
                planetId:
                    type: integer
                    format: int32
                content:
                    type: string
                images:
                    type: array
                    items:
                        type: string
                postId:
                    type: integer
                    format: int32
        user_planet_v1.UserInfo:
            type: object
            properties:
                id:
                    type: integer
                    format: int32
                nickname:
                    type: string
                avatarUrl:
                    type: string
        user_v1.BuddyInvitationReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                message:
                    type: string
                success:
                    type: boolean
        user_v1.BuddyInvitationRequest:
            type: object
            properties:
                toUserId:
                    type: string
                message:
                    type: string
            description: 搭子邀请接口
        user_v1.BuddySearchReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                message:
                    type: string
                data:
                    $ref: '#/components/schemas/user_v1.BuddySearchReply_Data'
        user_v1.BuddySearchReply_Data:
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/user_v1.BuddySearchReply_User'
                total:
                    type: integer
                    format: int32
                canAddBuddy:
                    type: boolean
                statusMessage:
                    type: string
        user_v1.BuddySearchReply_User:
            type: object
            properties:
                id:
                    type: string
                nickname:
                    type: string
                avatarUrl:
                    type: string
                description:
                    type: string
                habitCount:
                    type: integer
                    format: int32
                status:
                    type: string
        user_v1.ChangeEmailReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.ChangeEmailRequest:
            type: object
            properties:
                email:
                    type: string
                verifyCode:
                    type: string
        user_v1.ChangePasswordReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.ChangePasswordRequest:
            type: object
            properties:
                oldPassword:
                    type: string
                newPassword:
                    type: string
                confirmPassword:
                    type: string
        user_v1.ChangePhoneReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.ChangePhoneRequest:
            type: object
            properties:
                phone:
                    type: string
                verifyCode:
                    type: string
        user_v1.ChangeUserPrivacyPasswordReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.ChangeUserPrivacyPasswordRequest:
            type: object
            properties:
                oldPassword:
                    type: string
                newPassword:
                    type: string
                confirmPassword:
                    type: string
        user_v1.CheckUserPrivacyPasswordReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_v1.CheckUserPrivacyPasswordReply_Data'
        user_v1.CheckUserPrivacyPasswordReply_Data:
            type: object
            properties:
                isPass:
                    type: boolean
        user_v1.CheckUserPrivacyPasswordRequest:
            type: object
            properties:
                password:
                    type: string
        user_v1.DeleteUserReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.DeleteUserRequest:
            type: object
            properties:
                phone:
                    type: string
                password:
                    type: string
                email:
                    type: string
        user_v1.FollowUserReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.FollowUserRequest:
            type: object
            properties:
                userId:
                    type: integer
                    format: int32
        user_v1.ForgetPasswordReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.ForgetPasswordRequest:
            type: object
            properties:
                phone:
                    type: string
                verifyCode:
                    type: string
                password:
                    type: string
                confirmPassword:
                    type: string
                email:
                    type: string
        user_v1.GetUserProfileReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_v1.GetUserProfileReply_Data'
        user_v1.GetUserProfileReply_Data:
            type: object
            properties:
                username:
                    type: string
                phone:
                    type: string
                avatarUrl:
                    type: string
                desc:
                    type: string
                stats:
                    $ref: '#/components/schemas/user_v1.GetUserProfileReply_Stats'
                goals:
                    type: array
                    items:
                        type: string
                email:
                    type: string
        user_v1.GetUserProfileReply_Stats:
            type: object
            properties:
                allHabits:
                    type: integer
                    format: int32
                onTrackHabits:
                    type: integer
                    format: int32
                doneHabits:
                    type: integer
                    format: int32
        user_v1.GetUserSettingReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_v1.GetUserSettingReply_Data'
        user_v1.GetUserSettingReply_Data:
            type: object
            properties:
                awardImageUrl:
                    type: string
                awardImageUrlWithDomain:
                    type: string
        user_v1.GetUserVipInfoReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_v1.GetUserVipInfoReply_Data'
        user_v1.GetUserVipInfoReply_Data:
            type: object
            properties:
                isVip:
                    type: boolean
                remainDays:
                    type: integer
                    format: int32
                monthPrice:
                    $ref: '#/components/schemas/user_v1.GetUserVipInfoReply_VipDetail'
                quarterPrice:
                    $ref: '#/components/schemas/user_v1.GetUserVipInfoReply_VipDetail'
                yearPrice:
                    $ref: '#/components/schemas/user_v1.GetUserVipInfoReply_VipDetail'
        user_v1.GetUserVipInfoReply_VipDetail:
            type: object
            properties:
                price:
                    type: integer
                    format: int32
                discountPrice:
                    type: integer
                    format: int32
        user_v1.LoginReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_v1.LoginReply_Data'
        user_v1.LoginReply_Data:
            type: object
            properties:
                token:
                    type: string
                expireAt:
                    type: integer
                    format: int32
                userId:
                    type: integer
                    format: int32
                name:
                    type: string
                avatarUrl:
                    type: string
                isSystemAdmin:
                    type: boolean
                roleType:
                    type: integer
                    format: int32
                phone:
                    type: string
                email:
                    type: string
                status:
                    type: integer
                    format: int32
                desc:
                    type: string
                gender:
                    type: integer
                    format: int32
                isSetPwd:
                    type: boolean
                isVip:
                    type: boolean
                isSetPrivacy:
                    type: boolean
                uid:
                    type: string
        user_v1.LoginRequest:
            type: object
            properties:
                phone:
                    type: string
                    description: 核心认证参数（字段1-6）
                password:
                    type: string
                email:
                    type: string
                verifyCode:
                    type: string
                loginType:
                    type: integer
                    format: int32
                passType:
                    type: integer
                    format: int32
                thirdPartyToken:
                    type: string
                    description: Firebase认证参数（字段7）
                timezoneName:
                    type: string
                    description: 环境信息参数（字段8-10）
                timezoneOffset:
                    type: string
                locale:
                    type: string
                platform:
                    type: string
        user_v1.LogoutReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.LogoutRequest:
            type: object
            properties:
                deviceId:
                    type: string
                platform:
                    type: string
        user_v1.PushTokenReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.PushTokenRequest:
            type: object
            properties:
                timezoneName:
                    type: string
                    description: 环境信息参数（字段8-10）
                timezoneOffset:
                    type: string
                locale:
                    type: string
                deviceId:
                    type: string
                    description: 基础设备信息参数（字段11-15）
                platform:
                    type: string
                systemVersion:
                    type: string
                appVersion:
                    type: string
                deviceBrand:
                    type: string
                fcmToken:
                    type: string
                    description: 推送相关参数（字段16-21）
                apnsToken:
                    type: string
                pushServiceType:
                    type: string
                jpushRegistrationId:
                    type: string
                recommendedPushChannel:
                    type: string
                pushPermissionGranted:
                    type: boolean
        user_v1.RefreshTokenRequest:
            type: object
            properties:
                deviceId:
                    type: string
                platform:
                    type: string
                deviceBrand:
                    type: string
                fcmToken:
                    type: string
                apnsToken:
                    type: string
                pushServiceType:
                    type: string
                jpushRegistrationId:
                    type: string
                recommendedPushChannel:
                    type: string
        user_v1.RegisterReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_v1.RegisterReply_Data'
        user_v1.RegisterReply_Data:
            type: object
            properties:
                token:
                    type: string
                expireAt:
                    type: integer
                    format: int32
        user_v1.RegisterRequest:
            type: object
            properties:
                userName:
                    type: string
                password:
                    type: string
                confirmPassword:
                    type: string
                verifyCode:
                    type: string
                Phone:
                    type: string
        user_v1.UnfollowUserReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.UnfollowUserRequest:
            type: object
            properties:
                userId:
                    type: integer
                    format: int32
        user_v1.UpdateUserAwardReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
        user_v1.UpdateUserAwardRequest:
            type: object
            properties:
                awardImageUrl:
                    type: string
        user_v1.UpdateUserProfileReply:
            type: object
            properties:
                code:
                    type: integer
                    format: int32
                msg:
                    type: string
                data:
                    $ref: '#/components/schemas/user_v1.UpdateUserProfileReply_Data'
        user_v1.UpdateUserProfileReply_Data:
            type: object
            properties:
                username:
                    type: string
                avatarUrl:
                    type: string
                desc:
                    type: string
        user_v1.UpdateUserProfileRequest:
            type: object
            properties:
                username:
                    type: string
                avatarUrl:
                    type: string
                desc:
                    type: string
tags:
    - name: LifeLog
