package server

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	"github.com/go-kratos/kratos/v2/middleware/logging"
	"github.com/go-kratos/kratos/v2/middleware/selector"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	v1 "github.com/wlnil/life-log-be/api"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/core"
	"github.com/wlnil/life-log-be/internal/pkg/middleware/localize"
	"github.com/wlnil/life-log-be/internal/service"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	sentrykratos "github.com/go-kratos/sentry"
	jwtv4 "github.com/golang-jwt/jwt/v4"
	"github.com/gorilla/handlers"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, ac *conf.Auth, data *conf.Data, userService *service.LifeLogInterface, logger log.Logger) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			sentrykratos.Server(),
			logging.Server(logger),
			localize.I18N(data.LocalizePath),
			validate.Validator(),
			selector.Server(
				jwt.Server(func(token *jwtv4.Token) (interface{}, error) {
					return []byte(ac.SecretKey), nil
				}, jwt.WithSigningMethod(jwtv4.SigningMethodHS256), jwt.WithClaims(func() jwtv4.Claims {
					return &jwtv4.MapClaims{}
				})),
			).
				Match(NewWhiteListMatcher()).
				Build(),
		),
		http.Filter(handlers.CORS(
			handlers.AllowedHeaders([]string{"X-Requested-With", "Content-Type", "Authorization"}),
			handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS"}),
			handlers.AllowedOrigins([]string{"*"}),
		)),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	// 错误响应封装`
	opts = append(opts, http.ErrorEncoder(core.EncoderError()))
	srv := http.NewServer(opts...)
	v1.RegisterLifeLogHTTPServer(srv, userService)

	return srv
}

func NewWhiteListMatcher() selector.MatchFunc {

	whiteList := make(map[string]struct{})
	whiteList["/api.LifeLog/Register"] = struct{}{}
	whiteList["/api.LifeLog/Login"] = struct{}{}
	whiteList["/api.LifeLog/LoginWithFirebase"] = struct{}{}
	whiteList["/api.LifeLog/GetSiteInfo"] = struct{}{}
	whiteList["/api.LifeLog/HealthCheck"] = struct{}{}
	whiteList["/api.LifeLog/RunCronTask"] = struct{}{}
	whiteList["/api.LifeLog/ListPlanetPost"] = struct{}{}
	whiteList["/api.LifeLog/ListPlanetTopPost"] = struct{}{}
	whiteList["/api.LifeLog/SendVerifyCode"] = struct{}{}
	whiteList["/api.LifeLog/ListPlanetPostComment"] = struct{}{}
	whiteList["/api.LifeLog/ListMotiveMemo"] = struct{}{}
	whiteList["/api.LifeLog/GetUserVipInfo"] = struct{}{}
	whiteList["/api.LifeLog/VersionCheck"] = struct{}{}
	whiteList["/api.LifeLog/CreateFeedback"] = struct{}{}
	return func(ctx context.Context, operation string) bool {
		if _, ok := whiteList[operation]; ok {
			return false
		}
		return true
	}
}
