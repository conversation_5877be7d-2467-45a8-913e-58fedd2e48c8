package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"time"
)

type planetRepo struct {
	data *Data
	log  *log.Helper
}

func (p *planetRepo) CreatePlanet(ctx context.Context, planet *biz.Planet) error {
	return p.data.DB.Create(&planet).Error
}

func (p *planetRepo) UpdatePlanet(ctx context.Context, planet *biz.Planet) error {
	return p.data.DB.Save(&planet).Error
}

func (p *planetRepo) FindPlanetByUserID(ctx context.Context, planet *biz.Planet) error {
	return p.data.DB.Where("user_id = ? AND id = ? AND status = ?", planet.UserID, planet.ID, enums.PlanetStatusNormal).Take(&planet).Error
}

func (p *planetRepo) DeletePlanet(ctx context.Context, planet *biz.Planet) error {
	return p.data.DB.Where("user_id = ? AND id = ?", planet.UserID, planet.ID).Updates(map[string]interface{}{
		"status":     enums.PlanetStatusDelete,
		"updated_at": time.Now().Unix(),
	}).Error
}

func (p *planetRepo) FirstPlanetByID(ctx context.Context, planet *biz.Planet) error {
	return p.data.DB.Where("status = ?", enums.PlanetStatusNormal).Take(&planet, planet.ID).Error
}

func (p *planetRepo) CreatePlanetTarget(ctx context.Context, planetTarget *biz.PlanetTarget) error {
	return p.data.DB.Create(&planetTarget).Error
}

func (p *planetRepo) UpdatePlanetTarget(ctx context.Context, planetTarget *biz.PlanetTarget) error {
	return p.data.DB.Save(&planetTarget).Error
}

func (p *planetRepo) FindPlanetTargetByID(ctx context.Context, planetTarget *biz.PlanetTarget) error {
	return p.data.DB.Take(&planetTarget, planetTarget.ID).Error
}

func (p *planetRepo) DeletePlanetTarget(ctx context.Context, planetTarget *biz.PlanetTarget) error {
	return p.data.DB.Where("planet_id = ? AND id = ?", planetTarget.PlanetID, planetTarget.ID).Updates(map[string]interface{}{
		"status":     enums.PlanetTargetStatusDelete,
		"updated_at": time.Now().Unix(),
	}).Error
}

func (p *planetRepo) ListPlanetTargetByPlanetID(ctx context.Context, planetID int) ([]*biz.PlanetTarget, error) {
	pts := make([]*biz.PlanetTarget, 0)
	err := p.data.DB.Where("planet_id = ? AND status IN ?", planetID, []int{int(enums.PlanetTargetStatusNormal), int(enums.PlanetTargetStatusEnd)}).Find(&pts).Error
	return pts, err
}

// NewPlanetRepo .
func NewPlanetRepo(data *Data, logger log.Logger) biz.PlanetRepo {
	return &planetRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
