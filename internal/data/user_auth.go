package data

import (
	"context"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"time"

	"github.com/wlnil/life-log-be/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
)

type userAuthRepo struct {
	data *Data
	log  *log.Helper
}

func (r *userAuthRepo) Delete(ctx context.Context, userID int32, platform enums.UserTokenPlatformType) error {
	return r.data.DB.Model(&biz.UserToken{}).
		Where("user_id = ? and platform = ? and is_valid = ?", userID, platform, true).
		Updates(map[string]interface{}{"is_valid": false, "updated_at": time.Now().Unix()}).Error
}

func (r *userAuthRepo) UpdateValidByParams(ctx context.Context, ut *biz.UserToken) error {
	return r.data.DB.Model(&biz.UserToken{}).
		Where(&ut).
		Updates(map[string]interface{}{"is_valid": false, "updated_at": time.Now().Unix()}).Error
}

func (r *userAuthRepo) Create(ctx context.Context, ut *biz.UserToken) error {
	return r.data.DB.Create(&ut).Error
}

func (r *userAuthRepo) Update(ctx context.Context, ut *biz.UserToken) error {
	return r.data.DB.Save(&ut).Error
}

func (r *userAuthRepo) FindByID(context.Context, int32) (*biz.UserToken, error) {
	return nil, nil
}

// InvalidateUserTokens 使用户Token失效
func (r *userAuthRepo) InvalidateUserTokens(ctx context.Context, userID int32, platform enums.UserTokenPlatformType) error {
	return r.data.DB.Model(&biz.UserToken{}).
		Where("user_id = ? AND platform = ?", userID, platform).
		Update("is_valid", false).Error
}

func (r *userAuthRepo) FindBySearch(ctx context.Context, ut *biz.UserToken, where biz.Where) error {
	sql, vals, err := queryBuild(where)
	if err != nil {
		return err
	}
	return r.data.DB.Where(sql, vals...).Order("id DESC").Take(&ut).Error
}

// NewUserAuthRepo .
func NewUserAuthRepo(data *Data, logger log.Logger) biz.UserAuthRepo {
	return &userAuthRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
