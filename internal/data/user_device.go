package data

import (
	"context"
	"errors"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
	"gorm.io/gorm"
)

type userDeviceRepo struct {
	data *Data
	log  *log.Helper
}

func NewUserDeviceRepo(data *Data, logger log.Logger) biz.UserDeviceRepo {
	return &userDeviceRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}

// CreateOrUpdateDevice 创建或更新设备信息
func (r *userDeviceRepo) CreateOrUpdateDevice(ctx context.Context, device *biz.UserDevice) error {
	var existing biz.UserDevice
	err := r.data.DB.Where("user_id = ? AND device_id = ?", device.UserID, device.DeviceID).First(&existing).Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新设备记录
		return r.data.DB.Create(device).Error
	} else if err != nil {
		return err
	} else {
		// 更新现有设备记录
		device.ID = existing.ID
		return r.data.DB.Save(device).Error
	}
}

// GetUserDevices 获取用户设备列表
func (r *userDeviceRepo) GetUserDevices(ctx context.Context, userID int32) ([]*biz.UserDevice, error) {
	var devices []*biz.UserDevice
	err := r.data.DB.Where("user_id = ? AND is_active = ?", userID, true).
		Order("last_active_at DESC").
		Find(&devices).Error
	return devices, err
}

// GetDeviceByID 根据设备ID获取设备信息
func (r *userDeviceRepo) GetDeviceByID(ctx context.Context, userID int32, deviceID string) (*biz.UserDevice, error) {
	var device biz.UserDevice
	err := r.data.DB.Where("user_id = ? AND device_id = ?", userID, deviceID).First(&device).Error
	return &device, err
}

// UpdateDeviceActiveTime 更新设备活跃时间
func (r *userDeviceRepo) UpdateDeviceActiveTime(ctx context.Context, userID int32, deviceID string) error {
	now := time.Now().Unix()
	return r.data.DB.Model(&biz.UserDevice{}).
		Where("user_id = ? AND device_id = ?", userID, deviceID).
		Updates(map[string]interface{}{
			"last_active_at": now,
			"updated_at":     now,
		}).Error
}

// DeactivateDevice 停用设备
func (r *userDeviceRepo) DeactivateDevice(ctx context.Context, userID int32, deviceID string) error {
	now := time.Now().Unix()
	return r.data.DB.Model(&biz.UserDevice{}).
		Where("user_id = ? AND device_id = ?", userID, deviceID).
		Updates(map[string]interface{}{
			"is_active":  false,
			"updated_at": now,
		}).Error
}

// CreateAuditLog 创建审计日志
func (r *userDeviceRepo) CreateAuditLog(ctx context.Context, log *biz.AuthAuditLog) error {
	log.CreatedAt = time.Now().Unix()
	log.UpdatedAt = time.Now().Unix()
	return r.data.DB.Create(log).Error
}

// GetAuditLogs 获取审计日志
func (r *userDeviceRepo) GetAuditLogs(ctx context.Context, userID int32, limit int32) ([]*biz.AuthAuditLog, error) {
	var logs []*biz.AuthAuditLog
	err := r.data.DB.Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(int(limit)).
		Find(&logs).Error
	return logs, err
}
