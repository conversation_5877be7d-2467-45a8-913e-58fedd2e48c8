package data

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"gorm.io/gorm"
	"time"
)

type pushTaskRepo struct {
	data *Data
	log  *log.Helper
}

// gormTransaction 实现 biz.Transaction 接口
type gormTransaction struct {
	tx *gorm.DB
}

func (t *gormTransaction) Commit() error {
	return t.tx.Commit().Error
}

func (t *gormTransaction) Rollback() error {
	return t.tx.Rollback().Error
}

// CreatePushTask 创建推送任务
func (r *pushTaskRepo) CreatePushTask(ctx context.Context, task *biz.PushTask) error {
	return r.data.DB.Create(task).Error
}

// UpdatePushTask 更新推送任务
func (r *pushTaskRepo) UpdatePushTask(ctx context.Context, task *biz.PushTask) error {
	return r.data.DB.Save(task).Error
}

// GetPushTaskByID 根据ID获取推送任务
func (r *pushTaskRepo) GetPushTaskByID(ctx context.Context, taskID int64) (*biz.PushTask, error) {
	task := &biz.PushTask{}
	err := r.data.DB.Where("id = ?", taskID).Take(task).Error
	return task, err
}

// GetPendingTasks 获取待执行的推送任务
func (r *pushTaskRepo) GetPendingTasks(ctx context.Context, limit int32) ([]*biz.PushTask, error) {
	tasks := make([]*biz.PushTask, 0)
	err := r.data.DB.Where("status = ?", 0). // 0-待执行
							Order("scheduled_time ASC").
							Limit(int(limit)).
							Find(&tasks).Error
	return tasks, err
}

// GetPendingTasksPaginated 分页获取待执行的推送任务
func (r *pushTaskRepo) GetPendingTasksPaginated(ctx context.Context, limit, offset int) ([]*biz.PushTask, error) {
	tasks := make([]*biz.PushTask, 0)
	err := r.data.DB.WithContext(ctx).
		Where("status = ? AND scheduled_time > ?", 0, time.Now().Unix()-3600). // 排除过期任务（1小时前）
		Order("scheduled_time ASC").
		Limit(limit).
		Offset(offset).
		Find(&tasks).Error
	return tasks, err
}

// DeleteTasksByUserHabit 删除用户习惯相关的推送任务
func (r *pushTaskRepo) DeleteTasksByUserHabit(ctx context.Context, userID, userHabitID int32) error {
	return r.data.DB.Where("user_id = ? AND related_id = ? AND task_type = ?",
		userID, userHabitID, enums.PushTaskTypeHabitReminder).
		Update("status", 3). // 3-取消
		Error
}

// BeginTx 开始事务
func (r *pushTaskRepo) BeginTx(ctx context.Context) (biz.Transaction, error) {
	tx := r.data.DB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}
	return &gormTransaction{tx: tx}, nil
}

// UpdatePushTaskWithVersion 使用乐观锁更新推送任务
func (r *pushTaskRepo) UpdatePushTaskWithVersion(ctx context.Context, tx biz.Transaction, task *biz.PushTask, expectedVersion int32) error {
	var db *gorm.DB
	if tx != nil {
		gormTx, ok := tx.(*gormTransaction)
		if !ok {
			return fmt.Errorf("无效的事务类型")
		}
		db = gormTx.tx
	} else {
		db = r.data.DB.WithContext(ctx)
	}

	// 使用乐观锁更新，检查版本号
	result := db.Model(task).
		Where("id = ? AND version = ?", task.ID, expectedVersion).
		Updates(map[string]interface{}{
			"user_id":           task.UserID,
			"task_type":         task.TaskType,
			"related_id":        task.RelatedID,
			"title":             task.Title,
			"content":           task.Content,
			"scheduled_time":    task.ScheduledTime,
			"status":            task.Status,
			"is_recurring":      task.IsRecurring,
			"repeat_rule":       task.RepeatRule,
			"reminder_time":     task.ReminderTime,
			"execute_time":      task.ExecuteTime,
			"error_msg":         task.ErrorMsg,
			"failure_count":     task.FailureCount,
			"max_failures":      task.MaxFailures,
			"last_failure_time": task.LastFailureTime,
			"failure_reason":    task.FailureReason,
			"version":           task.Version,
			"updated_at":        time.Now(),
		})

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("乐观锁冲突：任务可能已被其他进程修改")
	}

	return nil
}

// CreatePushTaskInTx 在事务中创建推送任务
func (r *pushTaskRepo) CreatePushTaskInTx(ctx context.Context, tx biz.Transaction, task *biz.PushTask) error {
	var db *gorm.DB
	if tx != nil {
		gormTx, ok := tx.(*gormTransaction)
		if !ok {
			return fmt.Errorf("无效的事务类型")
		}
		db = gormTx.tx
	} else {
		db = r.data.DB.WithContext(ctx)
	}

	return db.Create(task).Error
}

// NewPushTaskRepo 创建简化推送任务仓库
func NewPushTaskRepo(data *Data, logger log.Logger) biz.PushNotificationRepo {
	return &pushTaskRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
