package data

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
	"gorm.io/gorm"
	"time"
)

type userHabitRepo struct {
	data *Data
	log  *log.Helper
}

func (u *userHabitRepo) SaveUserHabitStats(ctx context.Context, stats *biz.UserHabitStats) error {
	return u.data.DB.Save(stats).Error
}

func (u *userHabitRepo) GetUserHabitStats(ctx context.Context, userID, userHabitID int32) (*biz.UserHabitStats, error) {
	stats := &biz.UserHabitStats{}
	err := u.data.DB.Where("user_id = ? AND user_habit_id = ?", userID, userHabitID).Take(&stats).Error

	return stats, err
}

func (u *userHabitRepo) ListHabitLogByParams(ctx context.Context, where biz.Where) ([]*biz.UserHabitUpdateLog, error) {
	sql, vals, err := queryBuild(where)
	if err != nil {
		return nil, err
	}

	res := make([]*biz.UserHabitUpdateLog, 0)
	err = u.data.DB.Model(&biz.UserHabitUpdateLog{}).Where(sql, vals...).Order("created_at ASC").Find(&res).Error

	return res, err
}

func (u *userHabitRepo) CreateHabitLog(ctx context.Context, tx *gorm.DB, habitLog *biz.UserHabitUpdateLog) error {
	return tx.Create(habitLog).Error
}

func (u *userHabitRepo) UpdatePunchLog(ctx context.Context, punchLog *biz.UserPunchLog) error {
	return u.data.DB.Save(punchLog).Error
}

func (u *userHabitRepo) FirstPunchLogByParams(ctx context.Context, params map[string]interface{}) (*biz.UserPunchLog, error) {
	res := &biz.UserPunchLog{}
	err := u.data.DB.Model(&biz.UserPunchLog{}).Where(params).Take(&res).Error

	return res, err
}

func (u *userHabitRepo) ListPunchLogByParams(ctx context.Context, where biz.Where) ([]*biz.UserPunchLog, error) {
	sql, vals, err := queryBuild(where)
	if err != nil {
		return nil, err
	}

	res := make([]*biz.UserPunchLog, 0)
	err = u.data.DB.Model(&biz.UserPunchLog{}).Where(sql, vals...).Find(&res).Error

	return res, err
}

func (u *userHabitRepo) MapCountUserPunchByUserHabitIDs(ctx context.Context, where biz.Where) (map[int32]int32, error) {
	type Result struct {
		UserHabitID int32 `json:"user_habit_id"`
		Count       int32 `json:"count"`
	}

	sql, vals, err := queryBuild(where)
	if err != nil {
		return nil, err
	}

	ups := make([]*Result, 0)
	err = u.data.DB.Model(&biz.UserHabitSnapshot{}).
		Select("user_habit_id, count(*) as count").
		Where(sql, vals...).
		Group("user_habit_id").Scan(&ups).Error
	if err != nil {
		return nil, err
	}
	res := make(map[int32]int32)
	for _, item := range ups {
		res[item.UserHabitID] = item.Count
	}

	return res, nil
}

func (u *userHabitRepo) ListReckonTimeByParams(ctx context.Context, params map[string]interface{}) ([]*biz.UserReckonTime, error) {
	res := make([]*biz.UserReckonTime, 0)
	err := u.data.DB.Where(params).Find(&res).Error

	return res, err
}

func (u *userHabitRepo) ListSnapshotByUserHabitID(ctx context.Context, userHabitIDs []int32, params map[string]interface{}) ([]*biz.UserHabitSnapshot, error) {
	uhs := make([]*biz.UserHabitSnapshot, 0)
	err := u.data.DB.Model(&biz.UserHabitSnapshot{}).
		Where("user_habit_id IN ?", userHabitIDs).
		Where(params).
		Find(&uhs).Error

	return uhs, err
}

// CountUserPunchedDayByTime 统计用户习惯在某个时间段内的打卡完成天数
func (u *userHabitRepo) CountUserPunchedDayByTime(ctx context.Context, userHabitID int32, startTime, endTime int64) (int64, error) {
	var count int64
	err := u.data.DB.Model(&biz.UserHabitSnapshot{}).
		Where("user_habit_id = ? AND is_completed = ? AND today_time >= ? AND today_time <= ?", userHabitID, true, startTime, endTime).
		Count(&count).Error

	return count, err
}

func (u *userHabitRepo) BatchCreateSnapshot(ctx context.Context, habitSnaps []*biz.UserHabitSnapshot) error {
	return u.data.DB.Create(&habitSnaps).Error
}

func (u *userHabitRepo) CreateSnapshot(ctx context.Context, habitSnap *biz.UserHabitSnapshot) error {
	return u.data.DB.Create(&habitSnap).Error
}

func (u *userHabitRepo) CreateReckonTime(ctx context.Context, urt *biz.UserReckonTime) error {
	return u.data.DB.Create(&urt).Error
}

func (u *userHabitRepo) UpdateReckonTime(ctx context.Context, urt *biz.UserReckonTime) error {
	return u.data.DB.Where("user_id = ? AND habit_snap_id = ? AND is_valid = 1", urt.UserID, urt.HabitSnapID).Save(&urt).Error
}

func (u *userHabitRepo) FirstReckonTimeByID(ctx context.Context, urt *biz.UserReckonTime) error {
	return u.data.DB.Take(&urt, urt.ID).Error
}

func (u *userHabitRepo) MapCountUserPunchByTime(ctx context.Context, userHabitIds []int32, recordTime int64) (map[string]int32, error) {
	type Result struct {
		HabitSnapID  int32 `json:"habit_snap_id"`
		SmallStageID int32 `json:"small_stage_id"`
		Count        int32 `json:"count"`
	}

	ups := make([]*Result, 0)
	err := u.data.DB.Model(&biz.UserPunchLog{}).
		Select("habit_snap_id, small_stage_id, count(*) as count").
		Where("habit_snap_id in ? AND record_time = ?", userHabitIds, recordTime).
		Group("habit_snap_id, small_stage_id").Scan(&ups).Error
	if err != nil {
		return nil, err
	}
	res := make(map[string]int32)
	for _, item := range ups {
		res[fmt.Sprintf("%v-%v", item.HabitSnapID, item.SmallStageID)] = item.Count
	}

	return res, nil
}

func (u *userHabitRepo) CreatePunchLog(ctx context.Context, tx *gorm.DB, up *biz.UserPunchLog) error {
	return tx.Create(&up).Error
}

// DeletePunchLog 删除最新的一条打卡记录
func (u *userHabitRepo) DeletePunchLog(ctx context.Context, tx *gorm.DB, params map[string]interface{}) error {
	up := &biz.UserPunchLog{}
	if err := tx.Where(params).Order("id desc").Take(&up).Error; err != nil {
		return err
	}
	return tx.Delete(&biz.UserPunchLog{}, up.ID).Error
}

func (u *userHabitRepo) CountUserPunch(ctx context.Context, params map[string]interface{}) (count int64, err error) {
	err = u.data.DB.Model(&biz.UserPunchLog{}).Where(params).Count(&count).Error
	return
}

func (u *userHabitRepo) Create(ctx context.Context, tx *gorm.DB, habit *biz.UserHabit) error {
	return tx.Create(&habit).Error
}

func (u *userHabitRepo) Update(ctx context.Context, tx *gorm.DB, habit *biz.UserHabit) error {
	return tx.Where("user_id = ? AND id = ?", habit.UserID, habit.ID).Updates(&habit).Error
}

func (u *userHabitRepo) FirstByID(ctx context.Context, habit *biz.UserHabit) error {
	return u.data.DB.Where("user_id = ? AND id = ? AND is_deleted = 0", habit.UserID, habit.ID).Take(&habit).Error
}

func (u *userHabitRepo) FirstByParams(ctx context.Context, habit *biz.UserHabit, params map[string]interface{}) error {
	return u.data.DB.Where(params).Take(&habit).Error
}

func (u *userHabitRepo) Delete(ctx context.Context, habit *biz.UserHabit) error {
	return u.data.DB.Model(&biz.UserHabit{}).
		Where("user_id = ? AND id = ?", habit.UserID, habit.ID).
		Updates(map[string]interface{}{
			"is_deleted": true,
			"updated_at": time.Now().Unix(),
		}).Error
}

func (u *userHabitRepo) List(ctx context.Context, where biz.Where) ([]*biz.UserHabit, error) {
	sql, vals, err := queryBuild(where)
	if err != nil {
		return nil, err
	}

	uhs := make([]*biz.UserHabit, 0)
	err = u.data.DB.
		Model(&biz.UserHabit{}).
		Where("is_deleted = ?", false).
		Where(sql, vals...).
		Order("status asc, created_at desc").
		Find(&uhs).Error

	return uhs, err
}

func (u *userHabitRepo) ListSnapshotByParams(ctx context.Context, params map[string]interface{}) ([]*biz.UserHabitSnapshot, error) {
	uhs := make([]*biz.UserHabitSnapshot, 0)
	err := u.data.DB.Model(&biz.UserHabitSnapshot{}).Where(params).Order("today_time ASC").Find(&uhs).Error

	return uhs, err
}

func (u *userHabitRepo) FirstSnapshotByID(ctx context.Context, habitSnap *biz.UserHabitSnapshot) error {
	return u.data.DB.Where("user_id = ? AND id = ?", habitSnap.UserID, habitSnap.ID).Take(&habitSnap).Error
}

func (u *userHabitRepo) UpdateSnapshot(ctx context.Context, tx *gorm.DB, habitSnapID, userID int32, updateParams map[string]interface{}) error {
	return tx.WithContext(ctx).Model(&biz.UserHabitSnapshot{}).Where("user_id = ? AND id = ?", userID, habitSnapID).Updates(&updateParams).Error
}

func (u *userHabitRepo) UpdateSnapshotByUserHabitID(ctx context.Context, tx *gorm.DB, habitSnap *biz.UserHabitSnapshot) error {
	return tx.Where("user_habit_id", habitSnap.UserHabitID).Updates(&habitSnap).Error

}

// GetUserHabitDailyStatsByDateRange 获取指定日期范围内的用户习惯统计数据
func (u *userHabitRepo) GetUserHabitDailyStatsByDateRange(ctx context.Context, userID int32, startDateTime, endDateTime int64) ([]*biz.UserHabitDailyStats, error) {
	var stats []*biz.UserHabitDailyStats

	err := u.data.DB.Where("user_id = ? AND date_time >= ? AND date_time < ?",
		userID, startDateTime, endDateTime).
		Order("date_time ASC").
		Find(&stats).Error

	return stats, err
}

// SaveUserHabitDailyStats 保存用户习惯日统计数据
func (u *userHabitRepo) SaveUserHabitDailyStats(ctx context.Context, stats *biz.UserHabitDailyStats) error {
	// 尝试更新，如果不存在则创建
	result := u.data.DB.Model(&biz.UserHabitDailyStats{}).Where("user_id = ? AND date_time = ?", stats.UserID, stats.DateTime).
		Updates(map[string]interface{}{
			"total_habits":     stats.TotalHabits,
			"completed_habits": stats.CompletedHabits,
			"updated_at":       stats.UpdatedAt,
		})

	if result.RowsAffected == 0 {
		// 记录不存在，创建新记录
		return u.data.DB.Create(stats).Error
	}

	return result.Error
}

// GetUserHabitSnapshot 获取用户习惯快照
func (u *userHabitRepo) GetUserHabitSnapshot(ctx context.Context, habitID, userID, dateTime int64) (*biz.UserHabitSnapshot, error) {
	var snapshot biz.UserHabitSnapshot

	err := u.data.DB.Where("user_habit_id = ? AND user_id = ? AND today_time = ?",
		habitID, userID, dateTime).
		First(&snapshot).Error

	if err != nil {
		return nil, err
	}

	return &snapshot, nil
}

// GetHabitStatusCountByUserID 根据用户ID获取习惯状态统计
func (u *userHabitRepo) GetHabitStatusCountByUserID(ctx context.Context, userID int32) ([]*biz.StatusHabitCount, error) {
	var results []*biz.StatusHabitCount

	err := u.data.DB.Model(&biz.UserHabit{}).
		Select("status, count(*) as count").
		Where("user_id = ? AND is_deleted = 0", userID).
		Group("status").
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}

// NewUserHabitRepo .
func NewUserHabitRepo(data *Data, logger log.Logger) biz.UserHabitRepo {
	return &userHabitRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
