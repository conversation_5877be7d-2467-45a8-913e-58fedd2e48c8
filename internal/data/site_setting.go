package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
)

type siteSettingRepo struct {
	data *Data
	log  *log.Helper
}

func (s *siteSettingRepo) CreateFeedBack(ctx context.Context, feedback *biz.Feedback) error {
	return s.data.DB.Create(&feedback).Error
}

func (s *siteSettingRepo) ListMotiveMemo(ctx context.Context, params map[string]interface{}) ([]*biz.MotiveMemo, error) {
	memos := make([]*biz.MotiveMemo, 0)
	err := s.data.DB.Where(params).Find(&memos).Error

	return memos, err
}

func (s *siteSettingRepo) CreateSmsCode(ctx context.Context, sms *biz.UserSms) error {
	return s.data.DB.Create(&sms).Error
}

func (s *siteSettingRepo) Detail(ctx context.Context, setting *biz.SiteSetting) error {
	return s.data.DB.Take(&setting).Error
}

func (s *siteSettingRepo) GetLatestAppVersion(ctx context.Context, platform string) (*biz.AppVersion, error) {
	var version biz.AppVersion
	err := s.data.DB.Where("platform = ? AND is_active = ?", platform, true).
		Order("version_code DESC").
		First(&version).Error
	return &version, err
}

// NewSiteSettingRepo .
func NewSiteSettingRepo(data *Data, logger log.Logger) biz.SiteSettingRepo {
	return &siteSettingRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
