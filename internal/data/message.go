package data

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
)

type messageRepo struct {
	data *Data
	log  *log.Helper
}

func (m *messageRepo) Create(msg *biz.UserMessage) error {
	return m.data.DB.Create(&msg).Error
}

func NewMessageRepo(data *Data, logger log.Logger) biz.MessageRepo {
	return &messageRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
