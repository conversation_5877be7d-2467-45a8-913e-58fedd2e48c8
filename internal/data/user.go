package data

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/wlnil/life-log-be/internal/pkg/enums"

	"github.com/wlnil/life-log-be/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type userRepo struct {
	data *Data
	log  *log.Helper
}

func (r *userRepo) CreateUserSetting(ctx context.Context, us *biz.UserSetting) error {
	return r.data.DB.Create(&us).Error
}

func (r *userRepo) GetUserSetting(ctx context.Context, us *biz.UserSetting) error {
	return r.data.DB.Model(&biz.UserSetting{}).Where("user_id = ?", us.UserID).Take(&us).Error
}

func (r *userRepo) UpdateUserSetting(ctx context.Context, us *biz.UserSetting, params map[string]interface{}) error {
	return r.data.DB.Model(&biz.UserSetting{}).Where("user_id = ?", us.UserID).Updates(params).Error
}

func (r *userRepo) ListUserLikePostIDByPostID(ctx context.Context, userID int32, postIDs []int32) ([]string, error) {
	likePostIDs := make([]string, 0)
	err := r.data.DB.Model(&biz.UserLike{}).
		Where("third_id IN ? AND third_type = ? AND JSON_CONTAINS(user_ids, ?)", postIDs, enums.UserLikeTypePlanetPost, fmt.Sprintf("%v", userID)).
		Pluck("third_id", &likePostIDs).Error

	return likePostIDs, err
}

func (r *userRepo) ListUserFavoritePostIDByPostID(ctx context.Context, userID int32, postIDs []int32) ([]int32, error) {
	favoritePostIDs := make([]int32, 0)
	err := r.data.DB.Model(&biz.UserFavorite{}).
		Where("user_id = ? AND third_id IN ? AND third_type = ? AND status = ?", userID, postIDs, enums.UserFavoriteTypePlanetPost, enums.UserFavoriteStatusNormal).
		Pluck("third_id", &favoritePostIDs).Error

	return favoritePostIDs, err
}

func (r *userRepo) FirstUserRelation(ctx context.Context, ur *biz.UserRelation) error {
	return r.data.DB.Where(&ur).Take(&ur).Error
}

func (r *userRepo) UpdateUserRelationByID(ctx context.Context, ur *biz.UserRelation) error {
	return r.data.DB.Where("id = ?", ur.ID).Save(&ur).Error
}

func (r *userRepo) CreateUserRelation(ctx context.Context, ur *biz.UserRelation) error {
	return r.data.DB.Create(&ur).Error
}

func (r *userRepo) FindBySearch(ctx context.Context, user *biz.User, where biz.Where) error {
	sql, vals, err := queryBuild(where)
	if err != nil {
		return err
	}
	return r.data.DB.Where(sql, vals...).Take(&user).Error
}

func (r *userRepo) Create(ctx context.Context, user *biz.User) error {
	return r.data.DB.Create(&user).Error
}

func (r *userRepo) Update(ctx context.Context, user *biz.User) error {
	return r.data.DB.Save(&user).Error
}

func (r *userRepo) FindByID(ctx context.Context, user *biz.User) error {
	return r.data.DB.Where("status = ?", enums.UserStatusNormal).Take(&user, user.ID).Error
}

// 搭子相关方法实现

func (r *userRepo) GetBuddySetting(ctx context.Context, userID int32) (*biz.BuddySetting, error) {
	setting := &biz.BuddySetting{}
	err := r.data.DB.Where("user_id = ?", userID).First(setting).Error
	if err != nil {
		return nil, err
	}
	return setting, nil
}

// GetBuddyCount 获取已确认的搭子数量（从关系表）
func (r *userRepo) GetBuddyCount(ctx context.Context, userID int32) (int, error) {
	var count int64
	err := r.data.DB.Model(&biz.BuddyRelation{}).
		Where("user_id = ? AND status = ?", userID, enums.BuddyRelationStatusNormal).
		Count(&count).Error
	return int(count), err
}

// GetPendingInvitationCount 获取待处理的邀请数量（从邀请表）
func (r *userRepo) GetPendingInvitationCount(ctx context.Context, userID int32) (int, error) {
	var count int64
	err := r.data.DB.Model(&biz.BuddyInvitation{}).
		Where("from_user_id = ? AND status = ?", userID, enums.BuddyInvitationStatusPending).
		Count(&count).Error
	return int(count), err
}

func (r *userRepo) GetBuddyRelation(ctx context.Context, userID, buddyUserID int32, status enums.BuddyRelationStatus) (*biz.BuddyRelation, error) {
	relation := &biz.BuddyRelation{}
	err := r.data.DB.Where("user_id = ? AND buddy_user_id = ? AND status = ?",
		userID, buddyUserID, status).First(relation).Error
	if err != nil {
		return nil, err
	}
	return relation, nil
}

func (r *userRepo) GetBuddyInvitation(ctx context.Context, fromUserID, toUserID int32, status enums.BuddyInvitationStatus) (*biz.BuddyInvitation, error) {
	invitation := &biz.BuddyInvitation{}
	err := r.data.DB.Where("from_user_id = ? AND to_user_id = ? AND status = ?",
		fromUserID, toUserID, status).First(invitation).Error
	if err != nil {
		return nil, err
	}
	return invitation, nil
}

func (r *userRepo) CreateBuddyInvitation(ctx context.Context, invitation *biz.BuddyInvitation) error {
	return r.data.DB.Create(invitation).Error
}

func (r *userRepo) GetUserHabitCount(ctx context.Context, userID int32) (int, error) {
	var count int64
	err := r.data.DB.Table("tb_user_habit").
		Where("user_id = ? AND status = ?", userID, enums.UserHabitStatusNormal).
		Count(&count).Error
	return int(count), err
}

func (r *userRepo) CheckUIDExists(ctx context.Context, uid string) (bool, error) {
	var count int64
	err := r.data.DB.Model(&biz.User{}).Where("uid = ?", uid).Count(&count).Error
	return count > 0, err
}

// GetBuddySearchData 批量获取搭子搜索所需的所有数据
func (r *userRepo) GetBuddySearchData(ctx context.Context, currentUserID, targetUserID int32) (*biz.BuddySearchData, error) {
	data := &biz.BuddySearchData{}

	// 使用事务确保数据一致性
	err := r.data.DB.Transaction(func(tx *gorm.DB) error {
		// 1. 获取当前用户设置
		setting := &biz.BuddySetting{}
		err := tx.Where("user_id = ?", currentUserID).First(setting).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 使用默认设置
			setting = &biz.BuddySetting{
				MaxBuddyCount:   50,
				AllowInvitation: true,
			}
		}
		data.CurrentUserSetting = setting

		// 2. 获取当前用户的搭子数量
		var confirmedCount int64
		err = tx.Model(&biz.BuddyRelation{}).
			Where("user_id = ? AND status = ?", currentUserID, enums.BuddyRelationStatusNormal).
			Count(&confirmedCount).Error
		if err != nil {
			return err
		}
		data.ConfirmedBuddyCount = int(confirmedCount)

		// 3. 获取当前用户的待处理邀请数量
		var pendingCount int64
		err = tx.Model(&biz.BuddyInvitation{}).
			Where("from_user_id = ? AND status = ?", currentUserID, enums.BuddyInvitationStatusPending).
			Count(&pendingCount).Error
		if err != nil {
			return err
		}
		data.PendingInvitationCount = int(pendingCount)

		// 4. 获取目标用户的习惯数量
		var habitCount int64
		err = tx.Table("tb_user_habit").
			Where("user_id = ? AND status = ?", targetUserID, enums.UserHabitStatusNormal).
			Count(&habitCount).Error
		if err != nil {
			return err
		}
		data.TargetUserHabitCount = int(habitCount)

		// 5. 获取搭子关系（如果存在）
		relation := &biz.BuddyRelation{}
		err = tx.Where("user_id = ? AND buddy_user_id = ? AND status IN ?",
			currentUserID, targetUserID,
			[]enums.BuddyRelationStatus{enums.BuddyRelationStatusNormal, enums.BuddyRelationStatusBlocked}).
			First(relation).Error
		if err == nil {
			data.BuddyRelation = relation
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		// 6. 获取邀请记录（待处理和被拒绝的）
		var invitations []biz.BuddyInvitation
		err = tx.Where("from_user_id = ? AND to_user_id = ? AND status IN ?",
			currentUserID, targetUserID,
			[]enums.BuddyInvitationStatus{enums.BuddyInvitationStatusPending, enums.BuddyInvitationStatusRejected}).
			Find(&invitations).Error
		if err != nil {
			return err
		}

		// 分类邀请记录
		for i := range invitations {
			if invitations[i].Status == enums.BuddyInvitationStatusPending {
				data.PendingInvitation = &invitations[i]
			} else if invitations[i].Status == enums.BuddyInvitationStatusRejected {
				data.RejectedInvitation = &invitations[i]
			}
		}

		return nil
	})

	return data, err
}

// GetBuddyInvitationData 批量获取搭子邀请所需的所有数据
func (r *userRepo) GetBuddyInvitationData(ctx context.Context, currentUserID, targetUserID int32) (*biz.BuddyInvitationData, error) {
	data := &biz.BuddyInvitationData{}

	// 使用事务确保数据一致性
	err := r.data.DB.Transaction(func(tx *gorm.DB) error {
		// 1. 检查是否已经是搭子
		relation := &biz.BuddyRelation{}
		err := tx.Where("user_id = ? AND buddy_user_id = ? AND status = ?",
			currentUserID, targetUserID, enums.BuddyRelationStatusNormal).First(relation).Error
		if err == nil {
			data.ExistingRelation = relation
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		// 2. 检查是否已经发送过邀请
		invitation := &biz.BuddyInvitation{}
		err = tx.Where("from_user_id = ? AND to_user_id = ? AND status = ?",
			currentUserID, targetUserID, enums.BuddyInvitationStatusPending).First(invitation).Error
		if err == nil {
			data.ExistingInvitation = invitation
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}

		return nil
	})

	return data, err
}

// FindByFirebaseUID 根据Firebase UID查询用户
func (r *userRepo) FindByFirebaseUID(ctx context.Context, firebaseUID string) (*biz.User, error) {
	user := &biz.User{}
	err := r.data.DB.Where("firebase_uid = ? AND status != ?", firebaseUID, enums.UserStatusDelete).Take(user).Error
	return user, err
}

// Transaction 执行事务
func (r *userRepo) Transaction(ctx context.Context, fn func(tx interface{}) error) error {
	return r.data.DB.Transaction(func(tx *gorm.DB) error {
		return fn(tx)
	})
}

// InvalidateUserTokens 使用户Token失效
func (r *userRepo) InvalidateUserTokens(ctx context.Context, userID int32, platform enums.UserTokenPlatformType) error {
	return r.data.DB.Model(&biz.UserToken{}).
		Where("user_id = ? AND platform = ?", userID, platform).
		Update("is_valid", false).Error
}

// NewUserRepo .
func NewUserRepo(data *Data, logger log.Logger) biz.UserRepo {
	return &userRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
