package data

import (
	"context"
	"fmt"
	"github.com/wlnil/life-log-be/internal/pkg/enums"

	"github.com/wlnil/life-log-be/internal/biz"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"
)

type userRepo struct {
	data *Data
	log  *log.Helper
}

func (r *userRepo) CreateUserSetting(ctx context.Context, us *biz.UserSetting) error {
	return r.data.DB.Create(&us).Error
}

func (r *userRepo) GetUserSetting(ctx context.Context, us *biz.UserSetting) error {
	return r.data.DB.Model(&biz.UserSetting{}).Where("user_id = ?", us.UserID).Take(&us).Error
}

func (r *userRepo) UpdateUserSetting(ctx context.Context, us *biz.UserSetting, params map[string]interface{}) error {
	return r.data.DB.Model(&biz.UserSetting{}).Where("user_id = ?", us.UserID).Updates(params).Error
}

func (r *userRepo) ListUserLikePostIDByPostID(ctx context.Context, userID int32, postIDs []int32) ([]string, error) {
	likePostIDs := make([]string, 0)
	err := r.data.DB.Model(&biz.UserLike{}).
		Where("third_id IN ? AND third_type = ? AND JSON_CONTAINS(user_ids, ?)", postIDs, enums.UserLikeTypePlanetPost, fmt.Sprintf("%v", userID)).
		Pluck("third_id", &likePostIDs).Error

	return likePostIDs, err
}

func (r *userRepo) ListUserFavoritePostIDByPostID(ctx context.Context, userID int32, postIDs []int32) ([]int32, error) {
	favoritePostIDs := make([]int32, 0)
	err := r.data.DB.Model(&biz.UserFavorite{}).
		Where("user_id = ? AND third_id IN ? AND third_type = ? AND status = ?", userID, postIDs, enums.UserFavoriteTypePlanetPost, enums.UserFavoriteStatusNormal).
		Pluck("third_id", &favoritePostIDs).Error

	return favoritePostIDs, err
}

func (r *userRepo) FirstUserRelation(ctx context.Context, ur *biz.UserRelation) error {
	return r.data.DB.Where(&ur).Take(&ur).Error
}

func (r *userRepo) UpdateUserRelationByID(ctx context.Context, ur *biz.UserRelation) error {
	return r.data.DB.Where("id = ?", ur.ID).Save(&ur).Error
}

func (r *userRepo) CreateUserRelation(ctx context.Context, ur *biz.UserRelation) error {
	return r.data.DB.Create(&ur).Error
}

func (r *userRepo) FindBySearch(ctx context.Context, user *biz.User, where biz.Where) error {
	sql, vals, err := queryBuild(where)
	if err != nil {
		return err
	}
	return r.data.DB.Where(sql, vals...).Take(&user).Error
}

func (r *userRepo) Create(ctx context.Context, user *biz.User) error {
	return r.data.DB.Create(&user).Error
}

func (r *userRepo) Update(ctx context.Context, user *biz.User) error {
	return r.data.DB.Save(&user).Error
}

func (r *userRepo) FindByID(ctx context.Context, user *biz.User) error {
	return r.data.DB.Where("status = ?", enums.UserStatusNormal).Take(&user, user.ID).Error
}

func (r *userRepo) DB() *gorm.DB {
	return r.data.DB
}

// FindByFirebaseUID 根据Firebase UID查询用户
func (r *userRepo) FindByFirebaseUID(ctx context.Context, firebaseUID string) (*biz.User, error) {
	user := &biz.User{}
	err := r.data.DB.Where("firebase_uid = ? AND status != ?", firebaseUID, enums.UserStatusDelete).Take(user).Error
	return user, err
}

// Transaction 执行事务
func (r *userRepo) Transaction(ctx context.Context, fn func(tx interface{}) error) error {
	return r.data.DB.Transaction(func(tx *gorm.DB) error {
		return fn(tx)
	})
}

// InvalidateUserTokens 使用户Token失效
func (r *userRepo) InvalidateUserTokens(ctx context.Context, userID int32, platform enums.UserTokenPlatformType) error {
	return r.data.DB.Model(&biz.UserToken{}).
		Where("user_id = ? AND platform = ?", userID, platform).
		Update("is_valid", false).Error
}

// NewUserRepo .
func NewUserRepo(data *Data, logger log.Logger) biz.UserRepo {
	return &userRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
