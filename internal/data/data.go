package data

import (
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"github.com/wlnil/life-log-be/internal/biz"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/cache"
	"github.com/wlnil/life-log-be/internal/pkg/db"
	"gorm.io/gorm"
	"strings"
	"time"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewRedisClient,
	NewUserRepo,
	NewUserAuthRepo,
	NewSiteSettingRepo,
	NewUserHabitRepo,
	NewPlanetRepo,
	NewUserPlanetRepo,
	NewCronTaskRepo,
	NewMessageRepo,
	NewOrderRepo,
	NewUserDeviceRepo,
	NewPushTaskRepo,
)

type Data struct {
	DB *gorm.DB
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(c *conf.Data, logger log.Logger) *redis.Client {
	// 优化Redis连接池配置
	client := redis.NewClient(&redis.Options{
		Addr:     c.Redis.Addr,
		Username: c.Redis.Username,
		Password: c.Redis.Password,
		DB:       int(c.Redis.Db),

		// 连接池优化配置
		PoolSize:     20, // 连接池大小：默认10 → 20
		MinIdleConns: 5,  // 最小空闲连接数：新增
		MaxRetries:   3,  // 最大重试次数：新增

		// 超时配置优化
		DialTimeout:  5 * time.Second, // 连接超时
		ReadTimeout:  3 * time.Second, // 读取超时
		WriteTimeout: 3 * time.Second, // 写入超时

		// 连接生命周期
		PoolTimeout:     4 * time.Second, // 连接池超时
		ConnMaxIdleTime: 5 * time.Minute, // 空闲连接超时
	})

	log.NewHelper(logger).Info("Redis客户端初始化完成")
	return client
}

// NewData .
func NewData(c *conf.Data, authConf *conf.Auth, logger log.Logger) (*Data, func(), error) {
	auth.Secret = &auth.SecretInfo{
		Key:      authConf.SecretKey,
		Issuer:   authConf.Issuer,
		ExpireAt: authConf.ExpireAt,
	}

	// 初始化Redis连接 - 数据层核心职责
	redisX := &cache.RedisX{
		Addr:     c.Redis.Addr,
		Username: c.Redis.Username,
		Password: c.Redis.Password,
		DB:       int(c.Redis.Db),
	}
	redisX.Init()

	// 初始化数据库连接 - 数据层核心职责
	gormX := &db.GormX{
		Conn:         c.Database.Source,
		MaxIdleCount: int(c.Database.MaxIdleCount),
		MaxOpenCount: int(c.Database.MaxOpenCount),
	}
	gormX.Init()

	// 资源清理函数 - 只清理数据层资源
	cleanup := func() {
		gormX.Close()
		redisX.Close()
		log.NewHelper(logger).Info("closing the data resources")
	}
	return &Data{
		DB: db.GormClient,
	}, cleanup, nil
}

func Paginate(page, pageSize int) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if page <= 0 {
			page = 1
		}

		switch {
		case pageSize > 20:
			pageSize = 20
		case pageSize <= 0:
			pageSize = 10
		}

		offset := (page - 1) * pageSize
		return db.Offset(offset).Limit(pageSize)
	}
}

type NullType byte

const (
	_ NullType = iota
	IsNull
	IsNotNull
)

func queryBuild(where biz.Where) (querySQL string, vals []interface{}, err error) {
	for k, v := range where {
		ks := strings.Split(k, " ")
		if len(ks) > 2 {
			return "", nil, fmt.Errorf("Error in query condition: %s. ", k)
		}

		if querySQL != "" {
			querySQL += " AND "
		}
		strings.Join(ks, ",")
		switch len(ks) {
		case 1:
			switch v := v.(type) {
			case NullType:
				if v == IsNotNull {
					querySQL += fmt.Sprint(k, " IS NOT NULL")
				} else {
					querySQL += fmt.Sprint(k, " IS NULL")
				}
			default:
				querySQL += fmt.Sprint(k, " = ?")
				vals = append(vals, v)
			}
			break
		case 2:
			k = ks[0]
			switch ks[1] {
			case "=":
				querySQL += fmt.Sprint(k, " = ?")
				vals = append(vals, v)
				break
			case ">":
				querySQL += fmt.Sprint(k, " > ?")
				vals = append(vals, v)
				break
			case ">=":
				querySQL += fmt.Sprint(k, " >= ?")
				vals = append(vals, v)
				break
			case "<":
				querySQL += fmt.Sprint(k, " < ?")
				vals = append(vals, v)
				break
			case "<=":
				querySQL += fmt.Sprint(k, " <= ?")
				vals = append(vals, v)
				break
			case "!=":
				querySQL += fmt.Sprint(k, " != ?")
				vals = append(vals, v)
				break
			case "<>":
				querySQL += fmt.Sprint(k, " <> ?")
				vals = append(vals, v)
				break
			case "in":
				querySQL += fmt.Sprint(k, " IN (?)")
				vals = append(vals, v)
				break
			case "like":
				querySQL += fmt.Sprint(k, " LIKE ?")
				vals = append(vals, v)
				break
			case "not in":
				querySQL += fmt.Sprint(k, " NOT IN (?)")
				vals = append(vals, v)
			}
			break
		}
	}
	return
}
