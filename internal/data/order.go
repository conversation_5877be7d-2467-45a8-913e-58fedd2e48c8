package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
)

type orderRepo struct {
	data *Data
	log  *log.Helper
}

func (o *orderRepo) CreateUserOrder(ctx context.Context, order *biz.UserOrder) error {
	return o.data.DB.Create(&order).Error
}

func NewOrderRepo(data *Data, logger log.Logger) biz.OrderRepo {
	return &orderRepo{
		data: data,
		log:  log.<PERSON><PERSON><PERSON>per(logger),
	}
}
