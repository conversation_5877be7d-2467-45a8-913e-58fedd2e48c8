package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"time"
)

type cronTaskRepo struct {
	data *Data
	log  *log.Helper
}

func (c *cronTaskRepo) DeleteByUserID(ctx context.Context, userID int32) error {
	return c.data.DB.Model(&biz.CronTask{}).Where("user_id = ?", userID).Updates(map[string]interface{}{
		"status":     enums.CronTaskStatusTypeDelete,
		"updated_at": time.Now().Unix(),
	}).Error
}

func (c *cronTaskRepo) Update(ctx context.Context, cronTask *biz.CronTask) error {
	return c.data.DB.Save(&cronTask).Error
}

func (c *cronTaskRepo) BatchUpdateByID(ctx context.Context, IDs []int32, cronTask *biz.CronTask) error {
	return c.data.DB.Model(&biz.CronTask{}).Where("id IN ?", IDs).Updates(&cronTask).Error
}

func (c *cronTaskRepo) BatchCreate(ctx context.Context, cronTasks []*biz.CronTask) error {
	return c.data.DB.Create(&cronTasks).Error
}

func (c *cronTaskRepo) FirstByID(taskID int32, task *biz.CronTask) error {
	return c.data.DB.Take(&task, taskID).Error
}

func (c *cronTaskRepo) FindCronTaskByParams(where biz.Where) ([]*biz.CronTask, error) {
	sql, vals, err := queryBuild(where)
	if err != nil {
		return nil, err
	}

	tasks := make([]*biz.CronTask, 0)
	err = c.data.DB.Model(&biz.CronTask{}).Where(sql, vals...).Find(&tasks).Error
	return tasks, err
}

func NewCronTaskRepo(data *Data, logger log.Logger) biz.CronTaskRepo {
	return &cronTaskRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
