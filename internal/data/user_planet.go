package data

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
)

type userPlanetRepo struct {
	data *Data
	log  *log.Helper
}

// DeletePlantPost 删除动态时，仅修改状态为删除
func (u *userPlanetRepo) DeletePlantPost(ctx context.Context, planetPost *biz.PlanetPost) error {
	return u.data.DB.Model(&biz.PlanetPost{}).Where("user_id = ? AND id = ?", planetPost.UserID, planetPost.ID).Updates(map[string]interface{}{
		"status":     enums.PlanetPostStatusDelete,
		"updated_at": planetPost.UpdatedAt,
	}).Error
}

func (u *userPlanetRepo) ListPlanetPostByParams(ctx context.Context, params map[string]interface{}) ([]*biz.PlanetPost, error) {
	res := make([]*biz.PlanetPost, 0)
	err := u.data.DB.Model(&biz.PlanetPost{}).Where(params).Find(&res).Error

	return res, err
}

func (u *userPlanetRepo) UpdatePlanetPostCommentByParams(ctx context.Context, updateParams map[string]interface{}, filterParams map[string]interface{}) error {
	return u.data.DB.Model(&biz.PostComment{}).Where(filterParams).Updates(updateParams).Error
}

func (u *userPlanetRepo) UpdatePlanetPostByParams(ctx context.Context, updateParams map[string]interface{}, filterParams map[string]interface{}) error {
	return u.data.DB.Model(&biz.PlanetPost{}).Where(filterParams).Updates(updateParams).Error
}

func (u *userPlanetRepo) UpdateUserLike(ctx context.Context, like *biz.UserLike) error {
	return u.data.DB.Save(&like).Error
}

func (u *userPlanetRepo) CreateUserLike(ctx context.Context, like *biz.UserLike) error {
	return u.data.DB.Create(&like).Error
}

func (u *userPlanetRepo) FirstUserLikeByParams(ctx context.Context, like *biz.UserLike) error {
	return u.data.DB.Where(&like).Take(&like).Error
}

func (u *userPlanetRepo) FirstPostCommentByID(ctx context.Context, comment *biz.PostComment) error {
	return u.data.DB.Take(&comment, comment.ID).Error
}

func (u *userPlanetRepo) PaginatePostCommentByParams(ctx context.Context, params map[string]interface{}, page, pageSize int) ([]*biz.PostComment, int64, error) {
	comments := make([]*biz.PostComment, 0)
	var count int64

	countParams := make(map[string]interface{})
	for k, v := range params {
		countParams[k] = v
	}
	if rootID, ok := countParams["root_id"]; ok && rootID == 0 {
		delete(countParams, "root_id")
	}
	if err := u.data.DB.Model(&biz.PostComment{}).
		Where(countParams).
		Count(&count).Error; err != nil {
		return comments, count, err
	}
	err := u.data.DB.Model(&biz.PostComment{}).
		Where(params).
		Order("id ASC").
		Scopes(Paginate(page, pageSize)).
		Find(&comments).Error

	return comments, count, err
}

func (u *userPlanetRepo) CreatePostComment(ctx context.Context, comment *biz.PostComment) error {
	return u.data.DB.Create(&comment).Error
}

func (u *userPlanetRepo) UpdatePostComment(ctx context.Context, comment *biz.PostComment) error {
	return u.data.DB.Save(&comment).Error
}

func (u *userPlanetRepo) PaginatePlanetPostByParams(ctx context.Context, planetID int32, page, pageSize int, labelType enums.PlanetPostLabelType, userID int32) ([]*biz.PlanetPost, int64, error) {
	var count int64
	res := make([]*biz.PlanetPost, 0)

	qs := u.data.DB.Model(&biz.PlanetPost{}).
		Where("tb_planet_post.is_public = true AND tb_planet_post.status = ? AND tb_planet_post.planet_id = ?", enums.PlanetPostStatusNormal, planetID)
	if labelType == enums.PlanetPostLabelTypeTop {
		qs = qs.Where("is_top = ?", true)
	} else if labelType == enums.PlanetPostLabelTypeHot {
	} else if labelType == enums.PlanetPostLabelTypeMy {
		if userID == 0 {
			return res, count, nil
		}
		qs = qs.Where("user_id = ?", userID)
	} else if labelType == enums.PlanetPostLabelTypeFavorite {
		if userID == 0 {
			return res, count, nil
		}
		qs = qs.Joins("LEFT JOIN tb_user_favorite ON tb_user_favorite.third_id = tb_planet_post.id AND tb_user_favorite.third_type = ?", enums.UserFavoriteTypePlanetPost).
			Where("tb_user_favorite.user_id = ? AND tb_user_favorite.status = ?", userID, enums.UserFavoriteStatusNormal)
	}

	if err := qs.Count(&count).Error; err != nil {
		return res, count, err
	}
	qs = qs.Select("tb_planet_post.*")
	if labelType == enums.PlanetPostLabelTypeHot {
		// 最热，排序规则：评论数 > 点赞数
		qs = qs.Order("comment_count DESC, like_count DESC")
	} else if labelType == enums.PlanetPostLabelTypeFavorite {
		qs = qs.Order("tb_user_favorite.id DESC")
	} else {
		qs = qs.Order("id DESC")
	}
	err := qs.Scopes(Paginate(page, pageSize)).Find(&res).Error

	return res, count, err
}

func (u *userPlanetRepo) FirstPlanetPostByID(ctx context.Context, planetPost *biz.PlanetPost) error {
	return u.data.DB.Where("id = ?", planetPost.ID).Take(&planetPost).Error
}

func (u *userPlanetRepo) UpdatePlanetPost(ctx context.Context, planetPost *biz.PlanetPost) error {
	return u.data.DB.Save(&planetPost).Error
}

func (u *userPlanetRepo) FirstUserFavoriteByParams(ctx context.Context, favorite *biz.UserFavorite, params map[string]interface{}) error {
	return u.data.DB.Where(params).Take(&favorite).Error
}

func (u *userPlanetRepo) UpdateUserFavorite(ctx context.Context, favorite *biz.UserFavorite) error {
	return u.data.DB.Save(&favorite).Error
}

func (u *userPlanetRepo) CreateUserFavorite(ctx context.Context, favorite *biz.UserFavorite) error {
	return u.data.DB.Create(&favorite).Error
}

func (u *userPlanetRepo) UserJoinPlanet(ctx context.Context, planetMember *biz.PlanetMember) error {
	return u.data.DB.Create(&planetMember).Error
}

func (u *userPlanetRepo) UserQuitPlanet(ctx context.Context, planetMember *biz.PlanetMember) error {
	return u.data.DB.Where("user_id = ? AND planet_id = ?", planetMember.UserID, planetMember.PlanetID).Updates(map[string]interface{}{
		"status":     planetMember.Status,
		"quited_at":  planetMember.QuitedAt,
		"updated_at": planetMember.UpdatedAt,
	}).Error
}

func (u *userPlanetRepo) CreatePlanetPost(ctx context.Context, planetPost *biz.PlanetPost) error {
	return u.data.DB.Create(&planetPost).Error
}

func (u *userPlanetRepo) ListPlanetByUserID(ctx context.Context, userID int32) ([]*biz.Planet, error) {
	planets := make([]*biz.Planet, 0)
	err := u.data.DB.Model(&biz.Planet{}).
		Where("user_id = ? AND status = ?", userID, enums.PlanetStatusNormal).
		Find(&planets).Error
	return planets, err
}

func NewUserPlanetRepo(data *Data, logger log.Logger) biz.UserPlanetRepo {
	return &userPlanetRepo{
		data: data,
		log:  log.NewHelper(logger),
	}
}
