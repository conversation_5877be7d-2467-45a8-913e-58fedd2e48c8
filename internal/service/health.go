package service

import (
	"context"
	"github.com/wlnil/life-log-be/api/site/v1"
	"time"
)

// HealthCheck 实现健康检查接口
func (s *LifeLogInterface) HealthCheck(ctx context.Context, request *v1.HealthCheckRequest) (*v1.HealthCheckReply, error) {
	res := &v1.HealthCheckReply{
		Code: 0,
		Msg:  "success",
	}

	// 获取当前时间
	now := time.Now()
	
	// 构建响应数据
	res.Data = &v1.HealthCheckReply_Data{
		Timestamp:     now.Unix(),
		FormattedTime: now.Format("2006-01-02 15:04:05"),
	}

	return res, nil
}
