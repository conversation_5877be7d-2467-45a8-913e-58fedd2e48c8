package service

import (
	"context"
	v1 "github.com/wlnil/life-log-be/api/user_planet/v1"
)

func (s *LifeLogInterface) JoinPlanet(ctx context.Context, request *v1.JoinPlanetRequest) (*v1.JoinPlanetReply, error) {
	return s.userPlanet.JoinPlanet(ctx, request)
}

func (s *LifeLogInterface) QuitPlanet(ctx context.Context, request *v1.QuitPlanetRequest) (*v1.QuitPlanetReply, error) {
	return s.userPlanet.QuitPlanet(ctx, request)
}

func (s *LifeLogInterface) ListPlanetByUserID(ctx context.Context, request *v1.ListPlanetByUserIDRequest) (*v1.ListPlanetByUserIDReply, error) {
	return s.userPlanet.ListPlanetByUserID(ctx, request)
}

func (s *LifeLogInterface) JoinPlanetTarget(ctx context.Context, request *v1.JoinPlanetTargetRequest) (*v1.JoinPlanetTargetReply, error) {
	return s.userPlanet.JoinPlanetTarget(ctx, request)
}

func (s *LifeLogInterface) QuitPlanetTarget(ctx context.Context, request *v1.QuitPlanetTargetRequest) (*v1.QuitPlanetTargetReply, error) {
	return s.userPlanet.QuitPlanetTarget(ctx, request)
}

func (s *LifeLogInterface) CreatePlanetPost(ctx context.Context, request *v1.CreatePlanetPostRequest) (*v1.CreatePlanetPostReply, error) {
	return s.userPlanet.CreatePlanetPost(ctx, request)
}

func (s *LifeLogInterface) UpdatePlanetPost(ctx context.Context, request *v1.UpdatePlanetPostRequest) (*v1.UpdatePlanetPostReply, error) {
	return s.userPlanet.UpdatePlanetPost(ctx, request)
}

func (s *LifeLogInterface) DeletePlanetPost(ctx context.Context, request *v1.DeletePlanetPostRequest) (*v1.DeletePlanetPostReply, error) {
	return s.userPlanet.DeletePlanetPost(ctx, request)
}

func (s *LifeLogInterface) ToppedPlanetPost(ctx context.Context, request *v1.ToppedPlanetPostRequest) (*v1.ToppedPlanetPostReply, error) {
	return s.userPlanet.ToppedPlanetPost(ctx, request)
}

func (s *LifeLogInterface) LikePlanetPost(ctx context.Context, request *v1.LikePlanetPostRequest) (*v1.LikePlanetPostReply, error) {
	res, err := s.userPlanet.LikePlanetPost(ctx, request)
	if err != nil {
		s.log.Errorf("【点赞动态】失败，error: %v", err)
	}

	return res, nil
}

func (s *LifeLogInterface) CancelLikePlanetPost(ctx context.Context, request *v1.CancelLikePlanetPostRequest) (*v1.CancelLikePlanetPostReply, error) {
	res, err := s.userPlanet.CancelLikePlanetPost(ctx, request)
	if err != nil {
		s.log.Errorf("【取消点赞动态】失败，error: %v", err)
	}

	return res, nil
}

func (s *LifeLogInterface) FavoritePlanetPost(ctx context.Context, request *v1.FavoritePlanetPostRequest) (*v1.FavoritePlanetPostReply, error) {
	res, err := s.userPlanet.FavoritePlanetPost(ctx, request)
	if err != nil {
		s.log.Errorf("【收藏动态】失败，error: %v", err)
	}

	return res, nil
}

func (s *LifeLogInterface) CancelFavoritePlanetPost(ctx context.Context, request *v1.CancelFavoritePlanetPostRequest) (*v1.CancelFavoritePlanetPostReply, error) {
	res, err := s.userPlanet.CancelFavoritePlanetPost(ctx, request)
	if err != nil {
		s.log.Errorf("【取消收藏动态】失败，error: %v", err)
	}

	return res, nil
}

func (s *LifeLogInterface) ListPlanetPost(ctx context.Context, request *v1.ListPlanetPostRequest) (*v1.ListPlanetPostReply, error) {
	return s.userPlanet.ListPlanetPost(ctx, request)
}

func (s *LifeLogInterface) ListPlanetTopPost(ctx context.Context, request *v1.ListPlanetTopPostRequest) (*v1.ListPlanetTopPostReply, error) {
	return s.userPlanet.ListPlanetTopPost(ctx, request)
}

func (s *LifeLogInterface) CreatePlanetPostComment(ctx context.Context, request *v1.CreatePlanetPostCommentRequest) (*v1.CreatePlanetPostCommentReply, error) {
	res, err := s.userPlanet.CreatePlanetPostComment(ctx, request)
	if err != nil {
		s.log.Errorf("【创建星球动态评论】失败，error: %v", err)
	}

	return res, nil
}

func (s *LifeLogInterface) DeletePlanetPostComment(ctx context.Context, request *v1.DeletePlanetPostCommentRequest) (*v1.DeletePlanetPostCommentReply, error) {
	return s.userPlanet.DeletePlanetPostComment(ctx, request)
}

func (s *LifeLogInterface) ListPlanetPostComment(ctx context.Context, request *v1.ListPlanetPostCommentRequest) (*v1.ListPlanetPostCommentReply, error) {
	return s.userPlanet.ListPlanetPostComment(ctx, request)
}
