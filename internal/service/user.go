package service

import (
	"context"
	"github.com/wlnil/life-log-be/api/user/v1"
)

func (s *LifeLogInterface) Login(ctx context.Context, request *v1.LoginRequest) (*v1.LoginReply, error) {
	return s.user.Login(ctx, request)
}

func (s *LifeLogInterface) PushToken(ctx context.Context, request *v1.PushTokenRequest) (*v1.PushTokenReply, error) {
	return s.user.PushToken(ctx, request)
}

func (s *LifeLogInterface) Register(ctx context.Context, request *v1.RegisterRequest) (*v1.RegisterReply, error) {
	return s.user.Register(ctx, request)
}

func (s *LifeLogInterface) Logout(ctx context.Context, request *v1.LogoutRequest) (*v1.LogoutReply, error) {
	return s.user.Logout(ctx, request)
}

func (s *LifeLogInterface) ForgetPassword(ctx context.Context, request *v1.ForgetPasswordRequest) (*v1.ForgetPasswordReply, error) {
	return s.user.ForgetPassword(ctx, request)
}

func (s *LifeLogInterface) GetUserProfile(ctx context.Context, request *v1.GetUserProfileRequest) (*v1.GetUserProfileReply, error) {
	return s.user.GetUserProfile(ctx, request)
}

func (s *LifeLogInterface) UpdateUserProfile(ctx context.Context, request *v1.UpdateUserProfileRequest) (*v1.UpdateUserProfileReply, error) {
	return s.user.UpdateUserProfile(ctx, request)
}

func (s *LifeLogInterface) FollowUser(ctx context.Context, request *v1.FollowUserRequest) (*v1.FollowUserReply, error) {
	return s.user.FollowUser(ctx, request)
}

func (s *LifeLogInterface) UnfollowUser(ctx context.Context, request *v1.UnfollowUserRequest) (*v1.UnfollowUserReply, error) {
	return s.user.UnfollowUser(ctx, request)
}

func (s *LifeLogInterface) RefreshToken(ctx context.Context, request *v1.RefreshTokenRequest) (*v1.LoginReply, error) {
	return s.user.RefreshToken(ctx, request)
}

func (s *LifeLogInterface) GetUserSetting(ctx context.Context, request *v1.GetUserSettingRequest) (*v1.GetUserSettingReply, error) {
	return s.user.GetUserSetting(ctx, request)
}

func (s *LifeLogInterface) UpdateUserAward(ctx context.Context, request *v1.UpdateUserAwardRequest) (*v1.UpdateUserAwardReply, error) {
	return s.user.UpdateUserAward(ctx, request)
}

func (s *LifeLogInterface) ChangePhone(ctx context.Context, request *v1.ChangePhoneRequest) (*v1.ChangePhoneReply, error) {
	return s.user.ChangePhone(ctx, request)
}

func (s *LifeLogInterface) ChangeEmail(ctx context.Context, request *v1.ChangeEmailRequest) (*v1.ChangeEmailReply, error) {
	return s.user.ChangeEmail(ctx, request)
}

func (s *LifeLogInterface) ChangePassword(ctx context.Context, request *v1.ChangePasswordRequest) (*v1.ChangePasswordReply, error) {
	return s.user.ChangePassword(ctx, request)
}

func (s *LifeLogInterface) DeleteUser(ctx context.Context, request *v1.DeleteUserRequest) (*v1.DeleteUserReply, error) {
	return s.user.DeleteUser(ctx, request)
}

func (s *LifeLogInterface) ChangeUserPrivacyPassword(ctx context.Context, request *v1.ChangeUserPrivacyPasswordRequest) (*v1.ChangeUserPrivacyPasswordReply, error) {
	return s.user.ChangeUserPrivacyPassword(ctx, request)
}

func (s *LifeLogInterface) CheckUserPrivacyPassword(ctx context.Context, request *v1.CheckUserPrivacyPasswordRequest) (*v1.CheckUserPrivacyPasswordReply, error) {
	return s.user.CheckUserPrivacyPassword(ctx, request)
}

func (s *LifeLogInterface) GetUserVipInfo(ctx context.Context, request *v1.GetUserVipInfoRequest) (*v1.GetUserVipInfoReply, error) {
	return s.user.GetUserVipInfo(ctx, request)
}
