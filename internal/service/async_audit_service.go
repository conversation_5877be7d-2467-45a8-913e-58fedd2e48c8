package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/biz"
	"github.com/wlnil/life-log-be/internal/pkg/cache"
)

// AsyncAuditService 异步审计服务
type AsyncAuditService struct {
	userDeviceRepo biz.UserDeviceRepo
	log            *log.Helper
	
	// 本地缓冲区
	buffer     chan *biz.AuthAuditLog
	bufferSize int
	batchSize  int
	flushInterval time.Duration
	
	// 控制
	stopChan chan struct{}
	wg       sync.WaitGroup
	started  bool
	mu       sync.RWMutex
}

// NewAsyncAuditService 创建异步审计服务
func NewAsyncAuditService(
	userDeviceRepo biz.UserDeviceRepo,
	logger log.Logger,
) *AsyncAuditService {
	return &AsyncAuditService{
		userDeviceRepo: userDeviceRepo,
		log:            log.NewHelper(logger),
		
		// 配置参数
		bufferSize:    1000,  // 缓冲区大小
		batchSize:     50,    // 批量写入大小
		flushInterval: 5 * time.Second, // 刷新间隔
		
		buffer:   make(chan *biz.AuthAuditLog, 1000),
		stopChan: make(chan struct{}),
	}
}

// Start 启动异步审计服务
func (s *AsyncAuditService) Start() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if s.started {
		return
	}
	
	s.started = true
	s.log.Info("启动异步审计服务")
	
	// 启动处理协程
	s.wg.Add(2)
	go s.bufferProcessor()
	go s.redisProcessor()
}

// Stop 停止异步审计服务
func (s *AsyncAuditService) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	if !s.started {
		return
	}
	
	s.log.Info("停止异步审计服务")
	close(s.stopChan)
	s.wg.Wait()
	s.started = false
}

// LogAuthEvent 记录认证事件（异步，非阻塞）
func (s *AsyncAuditService) LogAuthEvent(event *biz.AuthAuditLog) {
	s.mu.RLock()
	started := s.started
	s.mu.RUnlock()
	
	if !started {
		s.log.Warn("审计服务未启动，忽略日志记录")
		return
	}
	
	// 设置创建时间
	if event.CreatedAt == 0 {
		event.CreatedAt = time.Now().Unix()
		event.UpdatedAt = event.CreatedAt
	}
	
	// 非阻塞写入缓冲区
	select {
	case s.buffer <- event:
		// 成功写入缓冲区
	default:
		// 缓冲区满，记录警告但不阻塞
		s.log.Warn("审计日志缓冲区已满，丢弃日志记录")
		// 可以考虑增加丢弃计数器
	}
}

// bufferProcessor 缓冲区处理器
func (s *AsyncAuditService) bufferProcessor() {
	defer s.wg.Done()
	
	batch := make([]*biz.AuthAuditLog, 0, s.batchSize)
	ticker := time.NewTicker(s.flushInterval)
	defer ticker.Stop()
	
	for {
		select {
		case event := <-s.buffer:
			batch = append(batch, event)
			
			// 达到批量大小，立即处理
			if len(batch) >= s.batchSize {
				s.processBatch(batch)
				batch = batch[:0] // 重置切片
			}
			
		case <-ticker.C:
			// 定时刷新，处理剩余的事件
			if len(batch) > 0 {
				s.processBatch(batch)
				batch = batch[:0]
			}
			
		case <-s.stopChan:
			// 服务停止，处理剩余事件
			if len(batch) > 0 {
				s.processBatch(batch)
			}
			
			// 处理缓冲区中剩余的事件
			for {
				select {
				case event := <-s.buffer:
					batch = append(batch, event)
					if len(batch) >= s.batchSize {
						s.processBatch(batch)
						batch = batch[:0]
					}
				default:
					if len(batch) > 0 {
						s.processBatch(batch)
					}
					return
				}
			}
		}
	}
}

// processBatch 处理批量事件
func (s *AsyncAuditService) processBatch(batch []*biz.AuthAuditLog) {
	if len(batch) == 0 {
		return
	}
	
	// 1. 优先写入Redis队列（快速）
	s.writeToRedis(batch)
	
	// 2. 直接写入数据库（可能较慢）
	s.writeToDatabase(batch)
}

// writeToRedis 写入Redis队列
func (s *AsyncAuditService) writeToRedis(batch []*biz.AuthAuditLog) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	pipe := cache.RedisClient.Pipeline()
	queueKey := "audit_log_queue"
	
	for _, event := range batch {
		data, err := json.Marshal(event)
		if err != nil {
			s.log.Errorf("序列化审计日志失败: %v", err)
			continue
		}
		pipe.RPush(ctx, queueKey, string(data))
	}
	
	// 设置队列过期时间（7天）
	pipe.Expire(ctx, queueKey, 7*24*time.Hour)
	
	if _, err := pipe.Exec(ctx); err != nil {
		s.log.Errorf("写入Redis审计队列失败: %v", err)
	} else {
		s.log.Debugf("成功写入 %d 条审计日志到Redis", len(batch))
	}
}

// writeToDatabase 写入数据库
func (s *AsyncAuditService) writeToDatabase(batch []*biz.AuthAuditLog) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	successCount := 0
	for _, event := range batch {
		if err := s.userDeviceRepo.CreateAuditLog(ctx, event); err != nil {
			s.log.Errorf("写入数据库审计日志失败: %v", err)
		} else {
			successCount++
		}
	}
	
	if successCount > 0 {
		s.log.Debugf("成功写入 %d/%d 条审计日志到数据库", successCount, len(batch))
	}
}

// redisProcessor Redis队列处理器（用于故障恢复）
func (s *AsyncAuditService) redisProcessor() {
	defer s.wg.Done()
	
	ticker := time.NewTicker(30 * time.Second) // 每30秒处理一次Redis队列
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			s.processRedisQueue()
		case <-s.stopChan:
			// 最后处理一次Redis队列
			s.processRedisQueue()
			return
		}
	}
}

// processRedisQueue 处理Redis队列中的审计日志
func (s *AsyncAuditService) processRedisQueue() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	queueKey := "audit_log_queue"

	// 批量从Redis队列中取出数据
	for i := 0; i < 10; i++ { // 最多处理10批
		results, err := cache.RedisClient.LPop(ctx, queueKey).Result()
		if err != nil && err.Error() == "redis: nil" {
			// 队列为空
			break
		} else if err != nil {
			s.log.Errorf("从Redis队列读取审计日志失败: %v", err)
			break
		}
		
		// 反序列化并写入数据库
		var event biz.AuthAuditLog
		if err := json.Unmarshal([]byte(results), &event); err != nil {
			s.log.Errorf("反序列化审计日志失败: %v", err)
			continue
		}
		
		if err := s.userDeviceRepo.CreateAuditLog(ctx, &event); err != nil {
			s.log.Errorf("从Redis恢复审计日志到数据库失败: %v", err)
			// 重新放回队列头部
			cache.RedisClient.LPush(ctx, queueKey, results)
			break
		}
	}
}

// GetStats 获取审计服务统计信息
func (s *AsyncAuditService) GetStats() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	stats := map[string]interface{}{
		"started":      s.started,
		"buffer_size":  len(s.buffer),
		"buffer_cap":   cap(s.buffer),
		"batch_size":   s.batchSize,
		"flush_interval": s.flushInterval.String(),
	}
	
	// 获取Redis队列长度
	if cache.RedisClient != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		defer cancel()

		queueLen, err := cache.RedisClient.LLen(ctx, "audit_log_queue").Result()
		if err == nil {
			stats["redis_queue_len"] = queueLen
		}
	}
	
	return stats
}

// HealthCheck 健康检查
func (s *AsyncAuditService) HealthCheck() error {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	if !s.started {
		return fmt.Errorf("审计服务未启动")
	}
	
	// 检查缓冲区是否接近满载
	bufferUsage := float64(len(s.buffer)) / float64(cap(s.buffer))
	if bufferUsage > 0.9 {
		return fmt.Errorf("审计日志缓冲区使用率过高: %.2f%%", bufferUsage*100)
	}
	
	return nil
}
