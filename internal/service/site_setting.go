package service

import (
	"context"

	"github.com/wlnil/life-log-be/api/site/v1"
)

func (s *LifeLogInterface) GetSiteInfo(ctx context.Context, request *v1.GetSiteInfoRequest) (*v1.GetSiteInfoReply, error) {
	return s.site.GetSiteInfo(ctx, request)
}

func (s *LifeLogInterface) CreateUpToken(ctx context.Context, request *v1.CreateUpTokenRequest) (*v1.CreateUpTokenReply, error) {
	return s.site.CreateUpToken(ctx, request)
}

func (s *LifeLogInterface) CreateDownURL(ctx context.Context, request *v1.CreateDownURLRequest) (*v1.CreateDownURLReply, error) {
	return s.site.CreateDownloadURL(ctx, request)
}

func (s *LifeLogInterface) SendVerifyCode(ctx context.Context, request *v1.SendVerifyCodeRequest) (*v1.SendVerifyCodeReply, error) {
	return s.site.SendVerifyCode(ctx, request)
}

func (s *LifeLogInterface) ListMotiveMemo(ctx context.Context, request *v1.ListMotiveMemoRequest) (*v1.ListMotiveMemoReply, error) {
	return s.site.ListMotiveMemo(ctx, request)
}

func (s *LifeLogInterface) CreateFeedback(ctx context.Context, request *v1.CreateFeedbackRequest) (*v1.CreateFeedbackReply, error) {
	return s.site.CreateFeedback(ctx, request)
}

func (s *LifeLogInterface) VersionCheck(ctx context.Context, request *v1.VersionCheckRequest) (*v1.VersionCheckReply, error) {
	return s.site.VersionCheck(ctx, request)
}
