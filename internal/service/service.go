package service

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/wlnil/life-log-be/internal/biz"
)

// ProviderSet is service providers.
var ProviderSet = wire.NewSet(
	NewLifeLogInterface,
	NewAsyncAuditService,
	wire.Bind(new(biz.AsyncAuditService), new(*AsyncAuditService)),
)

type LifeLogInterface struct {
	// v1.UnimplementedLifeLogServer // 暂时注释掉，因为没有生成gRPC服务
	user          *biz.UserUsecase
	site          *biz.SiteSettingUsecase
	userHabit     *biz.UserHabitUsecase
	planet        *biz.PlanetUsecase
	userPlanet    *biz.UserPlanetUsecase
	cronTask      *biz.CronTaskUsecase
	message       *biz.MessageUsecase
	userStatistic *biz.UserStatisticUsecase
	order         *biz.OrderUsecase

	log *log.Helper
}

func NewLifeLogInterface(
	user *biz.UserUsecase,
	site *biz.SiteSettingUsecase,
	userHabit *biz.UserHabitUsecase,
	planet *biz.PlanetUsecase,
	userPlanet *biz.UserPlanetUsecase,
	cronTask *biz.CronTaskUsecase,
	message *biz.MessageUsecase,
	userStatistic *biz.UserStatisticUsecase,
	order *biz.OrderUsecase,
	logger log.Logger) *LifeLogInterface {
	// 初始化任务
	cronTask.Init()

	return &LifeLogInterface{
		log:           log.NewHelper(log.With(logger, "module", "service/interface")),
		user:          user,
		site:          site,
		userHabit:     userHabit,
		planet:        planet,
		userPlanet:    userPlanet,
		cronTask:      cronTask,
		message:       message,
		userStatistic: userStatistic,
		order:         order,
	}
}
