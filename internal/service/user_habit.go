package service

import (
	"context"
	"github.com/wlnil/life-log-be/api/user_habit/v1"
)

func (s *LifeLogInterface) CreateUserHabit(ctx context.Context, request *v1.CreateUserHabitRequest) (*v1.CreateUserHabitReply, error) {
	return s.userHabit.CreateUserHabit(ctx, request)
}

func (s *LifeLogInterface) UpdateUserHabit(ctx context.Context, request *v1.UpdateUserHabitRequest) (*v1.UpdateUserHabitReply, error) {
	return s.userHabit.UpdateUserHabit(ctx, request)
}

func (s *LifeLogInterface) GetUserHabit(ctx context.Context, request *v1.GetUserHabitRequest) (*v1.GetUserHabitReply, error) {
	return s.userHabit.GetUserHabit(ctx, request)
}

func (s *LifeLogInterface) ListUserHabit(ctx context.Context, request *v1.ListUserHabitRequest) (*v1.ListUserHabitReply, error) {
	return s.userHabit.ListUserHabit(ctx, request)
}

func (s *LifeLogInterface) DeleteUserHabit(ctx context.Context, request *v1.DeleteUserHabitRequest) (*v1.DeleteUserHabitReply, error) {
	return s.userHabit.DeleteUserHabit(ctx, request)
}

func (s *LifeLogInterface) ListUserHabitSnapshot(ctx context.Context, request *v1.ListUserHabitSnapshotRequest) (*v1.ListUserHabitSnapshotReply, error) {
	return s.userHabit.ListUserHabitSnapshot(ctx, request)
}

func (s *LifeLogInterface) CreateUserHabitMemo(ctx context.Context, request *v1.CreateUserHabitMemoRequest) (*v1.CreateUserHabitMemoReply, error) {
	return s.userHabit.CreateUserHabitMemo(ctx, request)
}

func (s *LifeLogInterface) UpdateUserHabitMemo(ctx context.Context, request *v1.UpdateUserHabitMemoRequest) (*v1.UpdateUserHabitMemoReply, error) {
	return s.userHabit.UpdateUserHabitMemo(ctx, request)
}

func (s *LifeLogInterface) DeleteUserHabitMemo(ctx context.Context, request *v1.DeleteUserHabitMemoRequest) (*v1.DeleteUserHabitMemoReply, error) {
	return s.userHabit.DeleteUserHabitMemo(ctx, request)
}

func (s *LifeLogInterface) PunchUserHabit(ctx context.Context, request *v1.PunchUserHabitRequest) (*v1.PunchUserHabitReply, error) {
	return s.userHabit.PunchUserHabit(ctx, request)
}

func (s *LifeLogInterface) CancelPunchUserHabit(ctx context.Context, request *v1.CancelPunchUserHabitRequest) (*v1.CancelPunchUserHabitReply, error) {
	return s.userHabit.CancelPunchUserHabit(ctx, request)
}

func (s *LifeLogInterface) UpdatePunchUserHabit(ctx context.Context, request *v1.UpdatePunchUserHabitRequest) (*v1.UpdatePunchUserHabitReply, error) {
	return s.userHabit.UpdatePunchUserHabit(ctx, request)
}

func (s *LifeLogInterface) ReckonUserHabit(ctx context.Context, request *v1.ReckonUserHabitRequest) (*v1.ReckonUserHabitReply, error) {
	return s.userHabit.ReckonUserHabit(ctx, request)
}

func (s *LifeLogInterface) CancelReckonUserHabit(ctx context.Context, request *v1.CancelReckonUserHabitRequest) (*v1.CancelReckonUserHabitReply, error) {
	return s.userHabit.CancelReckonUserHabit(ctx, request)
}

func (s *LifeLogInterface) CreateUserHabitReckon(ctx context.Context, request *v1.CreateUserHabitReckonRequest) (*v1.CreateUserHabitReckonReply, error) {
	return s.userHabit.CreateUserHabitReckon(ctx, request)
}

func (s *LifeLogInterface) UpdateUserHabitReckon(ctx context.Context, request *v1.UpdateUserHabitReckonRequest) (*v1.UpdateUserHabitReckonReply, error) {
	return s.userHabit.UpdateUserHabitReckon(ctx, request)
}

func (s *LifeLogInterface) DeleteUserHabitReckon(ctx context.Context, request *v1.DeleteUserHabitReckonRequest) (*v1.DeleteUserHabitReckonReply, error) {
	return s.userHabit.DeleteUserHabitReckon(ctx, request)
}

func (s *LifeLogInterface) PauseUserHabit(ctx context.Context, request *v1.PauseUserHabitRequest) (*v1.PauseUserHabitReply, error) {
	return s.userHabit.PauseUserHabit(ctx, request)
}

func (s *LifeLogInterface) RecoverUserHabit(ctx context.Context, request *v1.RecoverUserHabitRequest) (*v1.RecoverUserHabitReply, error) {
	return s.userHabit.RecoverUserHabit(ctx, request)
}

func (s *LifeLogInterface) ArchiveUserHabit(ctx context.Context, request *v1.ArchiveUserHabitRequest) (*v1.ArchiveUserHabitReply, error) {
	return s.userHabit.ArchiveUserHabit(ctx, request)
}

func (s *LifeLogInterface) RunCronTask(ctx context.Context, request *v1.RunCronTaskRequest) (*v1.RunCronTaskReply, error) {
	return s.cronTask.RunCronTask(ctx, request)
}
