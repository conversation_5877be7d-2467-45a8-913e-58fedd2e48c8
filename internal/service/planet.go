package service

import (
	"context"
	v1 "github.com/wlnil/life-log-be/api/planet/v1"
)

func (s *LifeLogInterface) CreatePlanet(ctx context.Context, request *v1.CreatePlanetRequest) (*v1.CreatePlanetReply, error) {
	return s.planet.CreatePlanet(ctx, request)
}

func (s *LifeLogInterface) UpdatePlanet(ctx context.Context, request *v1.UpdatePlanetRequest) (*v1.UpdatePlanetReply, error) {
	return s.planet.UpdatePlanet(ctx, request)
}

func (s *LifeLogInterface) GetPlanet(ctx context.Context, request *v1.GetPlanetRequest) (*v1.GetPlanetReply, error) {
	return s.planet.GetPlanet(ctx, request)
}

func (s *LifeLogInterface) DeletePlanet(ctx context.Context, request *v1.DeletePlanetRequest) (*v1.DeletePlanetReply, error) {
	return s.planet.DeletePlanet(ctx, request)
}

func (s *LifeLogInterface) CreatePlanetTarget(ctx context.Context, request *v1.CreatePlanetTargetRequest) (*v1.CreatePlanetTargetReply, error) {
	return s.planet.CreatePlanetTarget(ctx, request)
}

func (s *LifeLogInterface) UpdatePlanetTarget(ctx context.Context, request *v1.UpdatePlanetTargetRequest) (*v1.UpdatePlanetTargetReply, error) {
	return s.planet.UpdatePlanetTarget(ctx, request)
}

func (s *LifeLogInterface) GetPlanetTarget(ctx context.Context, request *v1.GetPlanetTargetRequest) (*v1.GetPlanetTargetReply, error) {
	return s.planet.GetPlanetTarget(ctx, request)
}

func (s *LifeLogInterface) DeletePlanetTarget(ctx context.Context, request *v1.DeletePlanetTargetRequest) (*v1.DeletePlanetTargetReply, error) {
	return s.planet.DeletePlanetTarget(ctx, request)
}

func (s *LifeLogInterface) ListPlanetTarget(ctx context.Context, request *v1.ListPlanetTargetRequest) (*v1.ListPlanetTargetReply, error) {
	return s.planet.ListPlanetTarget(ctx, request)
}
