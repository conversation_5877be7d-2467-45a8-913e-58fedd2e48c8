package service

import (
	"context"
	v1 "github.com/wlnil/life-log-be/api/user_statistic/v1"
)

func (s *LifeLogInterface) TodayStatisticFromHome(ctx context.Context, request *v1.TodayStatisticFromHomeRequest) (*v1.TodayStatisticFromHomeReply, error) {
	return s.userStatistic.TodayStatisticFromHome(ctx, request)
}

func (s *LifeLogInterface) UserHabitStatisticFromDetail(ctx context.Context, request *v1.UserHabitStatisticFromDetailRequest) (*v1.UserHabitStatisticFromDetailReply, error) {
	return s.userStatistic.UserHabitStatisticFromDetail(ctx, request)
}
