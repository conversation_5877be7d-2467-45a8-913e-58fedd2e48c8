package biz

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
)

// HabitReminderHandler 习惯提醒处理器
type HabitReminderHandler struct {
	userHabitRepo UserHabitRepo
	userRepo      UserRepo
	log           *log.Helper
}

// ShouldSendNotification 判断是否需要发送推送
// 核心逻辑：复用 listDisplayUserHabit 中的 isNecessary 计算逻辑
func (h *HabitReminderHandler) ShouldSendNotification(ctx context.Context, userHabitID int32) bool {
	// 1. 获取用户习惯信息
	habit := &UserHabit{BasicModel: BasicModel{ID: userHabitID}}
	if err := h.userHabitRepo.FirstByID(ctx, habit); err != nil {
		h.log.Errorf("获取用户习惯失败: %v", err)
		return false
	}

	// 2. 检查习惯状态
	if habit.Status != enums.UserHabitStatusNormal {
		return false
	}

	// 3. 获取用户信息（用于时区计算）
	user := &User{BasicModel: BasicModel{ID: habit.UserID}}
	if err := h.userRepo.FindByID(ctx, user); err != nil {
		h.log.Errorf("获取用户信息失败: %v", err)
		return false
	}

	// 4. 检查用户推送权限
	if !h.checkUserPushPermission(ctx, habit.UserID) {
		return false
	}

	// 5. 计算用户当前时间
	userTime := h.getUserCurrentTime(user)

	// 6. 核心逻辑：复用 isNecessary 计算
	isNecessary := h.calculateIsNecessary(ctx, habit, userTime)

	return isNecessary
}

// calculateIsNecessary 计算 isNecessary - 完全复用现有逻辑
func (h *HabitReminderHandler) calculateIsNecessary(ctx context.Context, habit *UserHabit, userTime time.Time) bool {
	isNecessary := false

	// 完全复用 listDisplayUserHabit 中的逻辑
	if habit.Config.PunchCycleType == enums.PunchCycleFix { // 固定周期
		nowWeekDay := userTime.Weekday()
		if tool.Contains(habit.Config.PunchCycle, int32(nowWeekDay)) {
			isNecessary = true
		}
	} else if habit.Config.PunchCycleType == enums.PunchCycleWeek { // 按周，一周打卡几天
		// 获取本周开始时间和结束时间戳
		startTime, endTime := tool.GetWeekStartAndEndTime(userTime)
		// 获取本周已打卡天数
		punchedDayCount, err := h.userHabitRepo.CountUserPunchedDayByTime(ctx, habit.ID, startTime.Unix(), endTime.Unix())
		if err != nil {
			h.log.Errorf("获取用户习惯打卡天数失败，error: %v", err)
			return false
		}

		// 获取当日快照信息（用于判断是否已完成）
		habitSnap, err := h.getUserHabitSnapshot(ctx, habit.ID, userTime)
		if err != nil {
			h.log.Errorf("获取用户习惯快照失败，error: %v", err)
		}

		// 如果当周剩余天数小于待打卡天数，当天打卡状态返回为必须打卡
		remainDays := int64((endTime.Sub(userTime).Hours() / 24) + 1)
		if int64(habit.Config.PunchCycle[0])-punchedDayCount > remainDays ||
			(int64(habit.Config.PunchCycle[0])-punchedDayCount == remainDays && habitSnap != nil && habitSnap.IsCompleted) {
			isNecessary = true
		}
	} else if habit.Config.PunchCycleType == enums.PunchCycleMonth { // 按月，一月打卡几天
		// 获取本月开始时间和结束时间戳
		startTime, endTime := tool.GetMonthStartAndEndTime(userTime)
		// 获取本月已打卡天数
		punchedDayCount, err := h.userHabitRepo.CountUserPunchedDayByTime(ctx, habit.ID, startTime.Unix(), endTime.Unix())
		if err != nil {
			h.log.Errorf("获取用户习惯打卡天数失败，error: %v", err)
			return false
		}

		// 获取当日快照信息（用于判断是否已完成）
		habitSnap, err := h.getUserHabitSnapshot(ctx, habit.ID, userTime)
		if err != nil {
			h.log.Errorf("获取用户习惯快照失败，error: %v", err)
		}

		// 如果当月剩余天数小于待打卡天数，当天打卡状态返回为必须打卡
		remainDays := int64((endTime.Sub(userTime).Hours() / 24) + 1)
		if int64(habit.Config.PunchCycle[0])-punchedDayCount > remainDays ||
			(int64(habit.Config.PunchCycle[0])-punchedDayCount == remainDays && habitSnap != nil && habitSnap.IsCompleted) {
			isNecessary = true
		}
	}

	return isNecessary
}

// GenerateContent 生成推送内容
func (h *HabitReminderHandler) GenerateContent(ctx context.Context, userHabitID int32) (title, content string, err error) {
	// 1. 获取用户习惯信息
	habit := &UserHabit{BasicModel: BasicModel{ID: userHabitID}}
	if err := h.userHabitRepo.FirstByID(ctx, habit); err != nil {
		return "", "", fmt.Errorf("获取用户习惯失败: %w", err)
	}

	// 2. 获取用户信息
	user := &User{BasicModel: BasicModel{ID: habit.UserID}}
	if err := h.userRepo.FindByID(ctx, user); err != nil {
		return "", "", fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 3. 获取用户设置以检查隐私配置
	userSetting := &UserSetting{UserID: habit.UserID}
	if err := h.userRepo.GetUserSetting(ctx, userSetting); err != nil {
		h.log.Errorf("获取用户设置失败: %v", err)
		// 继续执行，不因为获取设置失败而中断
	}

	// 4. 处理隐私设置
	habitName := habit.Name
	isSetPrivacy := userSetting.PrivacyPassword != ""
	if habit.Config.IsSetPrivacy && isSetPrivacy {
		if habit.Config.PrivacyDisplayMode == enums.PrivacyDisplayModeTypeCustom {
			habitName = habit.Config.PrivacyDisplayContent
		} else {
			habitName = tool.MaskString(habitName)
		}
	}

	// 5. 根据习惯类型生成不同的推送内容
	switch habit.HabitType {
	case enums.UserHabitSmall:
		title = "微习惯提醒"
		content = fmt.Sprintf("该完成你的微习惯「%s」了，坚持就是胜利！", habitName)
	case enums.UserHabitNormal:
		title = "习惯提醒"
		content = fmt.Sprintf("记得完成今天的「%s」，每一次坚持都让你更强大！", habitName)
	case enums.UserHabitRecord:
		title = "记录提醒"
		content = fmt.Sprintf("别忘了记录你的「%s」，数据见证成长！", habitName)
	default:
		title = "习惯提醒"
		content = fmt.Sprintf("该完成你的习惯「%s」了！", habitName)
	}

	return title, content, nil
}

// CalculateNextScheduleTime 计算下次调度时间
func (h *HabitReminderHandler) CalculateNextScheduleTime(reminderTime, repeatRule string) int64 {
	now := time.Now()

	// 解析提醒时间 "07:30"
	reminderHour, reminderMinute, err := h.parseReminderTime(reminderTime)
	if err != nil {
		h.log.Errorf("解析提醒时间失败: %v", err)
		// 默认早上8点
		reminderHour, reminderMinute = 8, 0
	}

	// 根据重复规则计算下次执行时间
	switch repeatRule {
	case "daily":
		// 每日：如果今天的提醒时间已过，则安排明天
		nextTime := time.Date(now.Year(), now.Month(), now.Day(), reminderHour, reminderMinute, 0, 0, now.Location())
		if nextTime.Before(now) {
			nextTime = nextTime.AddDate(0, 0, 1)
		}
		return nextTime.Unix()

	case "weekly":
		// 每周：安排下周同一时间
		nextTime := time.Date(now.Year(), now.Month(), now.Day(), reminderHour, reminderMinute, 0, 0, now.Location())
		nextTime = nextTime.AddDate(0, 0, 7)
		return nextTime.Unix()

	case "monthly":
		// 每月：安排下月同一时间
		nextTime := time.Date(now.Year(), now.Month(), now.Day(), reminderHour, reminderMinute, 0, 0, now.Location())
		nextTime = nextTime.AddDate(0, 1, 0)
		return nextTime.Unix()

	default:
		// 默认每日
		nextTime := time.Date(now.Year(), now.Month(), now.Day(), reminderHour, reminderMinute, 0, 0, now.Location())
		if nextTime.Before(now) {
			nextTime = nextTime.AddDate(0, 0, 1)
		}
		return nextTime.Unix()
	}
}

// 辅助方法
func (h *HabitReminderHandler) checkUserPushPermission(ctx context.Context, userID int32) bool {
	// 检查用户推送权限设置
	userSetting := &UserSetting{UserID: userID}
	if err := h.userRepo.GetUserSetting(ctx, userSetting); err != nil {
		h.log.Errorf("获取用户设置失败: %v", err)
		return false
	}

	// 检查推送权限是否授权
	return userSetting.PushPermissionGranted
}

func (h *HabitReminderHandler) getUserCurrentTime(user *User) time.Time {
	// 根据用户时区计算当前时间
	if user.TimezoneName != "" {
		if loc, err := time.LoadLocation(user.TimezoneName); err == nil {
			return time.Now().In(loc)
		}
	}
	return time.Now()
}

func (h *HabitReminderHandler) getUserHabitSnapshot(ctx context.Context, userHabitID int32, userTime time.Time) (*UserHabitSnapshot, error) {
	// 获取指定日期的用户习惯快照
	timestamp := tool.GetZeroTimestampByDate(userTime).Unix()

	// 使用现有的 ListSnapshotByParams 方法
	params := map[string]interface{}{
		"user_habit_id": userHabitID,
		"today_time":    timestamp,
	}

	snapshots, err := h.userHabitRepo.ListSnapshotByParams(ctx, params)
	if err != nil {
		return nil, err
	}

	if len(snapshots) == 0 {
		return nil, nil // 没有找到快照
	}

	return snapshots[0], nil
}

func (h *HabitReminderHandler) parseReminderTime(reminderTime string) (hour, minute int, err error) {
	// 解析 "07:30" 格式的时间
	t, err := time.Parse("15:04", reminderTime)
	if err != nil {
		return 0, 0, err
	}
	return t.Hour(), t.Minute(), nil
}
