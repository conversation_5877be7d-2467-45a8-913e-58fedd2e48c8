package biz

import (
	"context"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
)

// TaskHandler 任务处理器接口
type TaskHandler interface {
	// 判断是否需要发送推送（基于最新业务数据状态）
	ShouldSendNotification(ctx context.Context, relatedID int32) bool

	// 生成推送内容（基于最新业务数据）
	GenerateContent(ctx context.Context, relatedID int32) (title, content string, err error)

	// 计算下次调度时间（用于周期任务）
	CalculateNextScheduleTime(reminderTime, repeatRule string) int64
}

// TaskHandlerRegistry 任务处理器注册表
type TaskHandlerRegistry struct {
	handlers map[enums.PushTaskType]TaskHandler
}

// RegisterHandler 注册处理器
func (r *TaskHandlerRegistry) RegisterHandler(taskType enums.PushTaskType, handler TaskHandler) {
	r.handlers[taskType] = handler
}

// GetHandler 获取处理器
func (r *TaskHandlerRegistry) GetHandler(taskType enums.PushTaskType) TaskHandler {
	return r.handlers[taskType]
}

// HasHandler 检查是否有对应的处理器
func (r *TaskHandlerRegistry) HasHandler(taskType enums.PushTaskType) bool {
	_, exists := r.handlers[taskType]
	return exists
}
