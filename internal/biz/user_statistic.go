package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	v1 "github.com/wlnil/life-log-be/api/user_statistic/v1"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
)

type UserStatisticUsecase struct {
	repo          UserRepo
	commonUseCase *CommonUsecase

	userHabitUseCase *UserHabitUsecase

	log *log.Helper
}

func (u *UserStatisticUsecase) TodayStatisticFromHome(ctx context.Context, request *v1.TodayStatisticFromHomeRequest) (*v1.TodayStatisticFromHomeReply, error) {
	res := &v1.TodayStatisticFromHomeReply{
		Data: &v1.TodayStatisticFromHomeReply_Data{
			SmallHabit: &v1.TodayStatisticFromHomeReply_HabitDetail{
				Per:       0,
				DoneCount: 0,
				AllCount:  10,
			},
			NormalHabit:  &v1.TodayStatisticFromHomeReply_HabitDetail{},
			OverComplete: "",
			UnComplete:   "",
		},
	}

	return res, nil
}

func (u *UserStatisticUsecase) UserHabitStatisticFromDetail(ctx context.Context, request *v1.UserHabitStatisticFromDetailRequest) (*v1.UserHabitStatisticFromDetailReply, error) {
	res := &v1.UserHabitStatisticFromDetailReply{
		Data: &v1.UserHabitStatisticFromDetailReply_Data{
			AllStatistic: &v1.UserHabitStatisticFromDetailReply_AllStatistic{},
			WeekStatistic: &v1.UserHabitStatisticFromDetailReply_WeekStatistic{
				ChartLeftCount: []int32{0, 2, 5},
			},
			MonthStatistic: &v1.UserHabitStatisticFromDetailReply_MonthStatistic{},
		},
	}

	// 处理全时间段数据
	userID := auth.GetUserIDFromCtx(ctx)
	err := u.userHabitUseCase.GetUserHabitCompleteDetail(ctx, request.UserHabitId, userID, request.CurrentDate, enums.HabitDetailLabelType(request.LabelType), res)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func NewUserStatisticUsecase(repo UserRepo, logger log.Logger, commonUseCase *CommonUsecase, userHabitUsecase *UserHabitUsecase) *UserStatisticUsecase {
	return &UserStatisticUsecase{
		repo:             repo,
		log:              log.NewHelper(logger),
		commonUseCase:    commonUseCase,
		userHabitUseCase: userHabitUsecase,
	}
}
