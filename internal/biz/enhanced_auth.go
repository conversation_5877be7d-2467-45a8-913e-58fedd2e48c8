package biz

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	v1 "github.com/wlnil/life-log-be/api/user/v1"
	"github.com/wlnil/life-log-be/internal/pkg/firebase"
)

// AsyncAuditService 异步审计服务接口
type AsyncAuditService interface {
	LogAuthEvent(event *AuthAuditLog)
	Start()
	Stop()
	GetStats() map[string]interface{}
	HealthCheck() error
}

// EnhancedAuthUsecase 增强的认证用例
type EnhancedAuthUsecase struct {
	userRepo       UserRepo
	userAuthRepo   UserAuthRepo
	userDeviceRepo UserDeviceRepo
	pushUsecase    *PushNotificationUsecase
	auditService   AsyncAuditService
	log            *log.Helper
}

// NewEnhancedAuthUsecase 创建增强认证用例
func NewEnhancedAuthUsecase(
	userRepo UserRepo,
	userAuthRepo UserAuthRepo,
	userDeviceRepo UserDeviceRepo,
	pushUsecase *PushNotificationUsecase,
	auditService AsyncAuditService,
	logger log.Logger,
) *EnhancedAuthUsecase {
	return &EnhancedAuthUsecase{
		userRepo:       userRepo,
		userAuthRepo:   userAuthRepo,
		userDeviceRepo: userDeviceRepo,
		pushUsecase:    pushUsecase,
		auditService:   auditService,
		log:            log.NewHelper(logger),
	}
}

// ProcessEnhancedLogin 处理增强登录逻辑（统一错误处理策略）
func (uc *EnhancedAuthUsecase) ProcessEnhancedLogin(ctx context.Context, req *v1.PushTokenRequest, userID int32) error {
	// 1. 注册/更新设备信息（关键操作）
	if err := uc.registerDeviceInfo(ctx, userID, req); err != nil {
		uc.log.Errorf("注册设备信息失败: %v", err)
	}

	// 3. 更新推送设置（关键操作）
	if err := uc.updatePushSettings(ctx, userID, req); err != nil {
		uc.log.Errorf("更新推送设置失败: %v", err)
	}

	return nil
}

// registerDeviceInfo 注册设备信息
func (uc *EnhancedAuthUsecase) registerDeviceInfo(ctx context.Context, userID int32, req *v1.PushTokenRequest) error {
	if req.DeviceId == "" {
		return nil
	}

	deviceInfo := &DeviceInfo{
		DeviceID:      req.DeviceId,
		Platform:      req.Platform,
		SystemVersion: req.SystemVersion,
		AppVersion:    req.AppVersion,
		DeviceBrand:   req.DeviceBrand,
	}

	deviceUsecase := &UserDeviceUsecase{
		repo: uc.userDeviceRepo,
		log:  uc.log,
	}

	return deviceUsecase.RegisterDevice(ctx, userID, deviceInfo)
}

// updatePushSettings 更新推送设置
func (uc *EnhancedAuthUsecase) updatePushSettings(ctx context.Context, userID int32, req *v1.PushTokenRequest) error {
	uc.log.Infof("更新用户 %d 的推送设置：权限=%v", userID, req.PushPermissionGranted)

	// 获取或创建用户设置
	userSetting := &UserSetting{UserID: userID}
	err := uc.userRepo.GetUserSetting(ctx, userSetting)
	if err != nil && err.Error() != "record not found" {
		uc.log.Errorf("获取用户设置失败: %v", err)
		return err
	}

	// 更新用户设置中的推送权限
	updates := map[string]interface{}{
		"push_permission_granted": req.PushPermissionGranted,
		"updated_at":              time.Now().Unix(),
	}

	if userSetting.ID == 0 {
		// 创建新的用户设置
		userSetting.PushPermissionGranted = req.PushPermissionGranted
		userSetting.AwardImageUrl = "award-imgs/default-1725106894969"
		userSetting.PrivacyPassword = "1234"
		userSetting.CreatedAt = time.Now().Unix()
		userSetting.UpdatedAt = time.Now().Unix()

		if err = uc.userRepo.CreateUserSetting(ctx, userSetting); err != nil {
			uc.log.Errorf("创建用户设置失败: %v", err)
			return err
		}
	} else {
		// 更新现有用户设置
		if err = uc.userRepo.UpdateUserSetting(ctx, userSetting, updates); err != nil {
			uc.log.Errorf("更新用户推送设置失败: %v", err)
			return err
		}
	}

	return nil
}

// LogAuthEventForAction 为特定操作记录审计日志
func (uc *EnhancedAuthUsecase) LogAuthEventForAction(userID int32, action string, platform string, deviceID string, clientIP string, success bool, errorCode string, errorMsg string) {
	if uc.auditService == nil {
		return
	}

	auditLog := &AuthAuditLog{
		UserID:    userID,
		Action:    action,
		DeviceID:  deviceID,
		Platform:  platform,
		ClientIP:  clientIP,
		Success:   success,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	uc.auditService.LogAuthEvent(auditLog)
}

// FirebaseTokenInfo Firebase token 解析后的信息
type FirebaseTokenInfo struct {
	UID           string `json:"uid"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	Name          string `json:"name"`
	Picture       string `json:"picture"`
	Provider      string `json:"firebase"`
}

// parseFirebaseToken 解析Firebase token获取用户信息
func (uc *EnhancedAuthUsecase) parseFirebaseToken(token string) (*FirebaseTokenInfo, error) {
	if token == "" {
		return nil, fmt.Errorf("token为空")
	}

	// 使用项目中已有的Firebase认证客户端
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 调用Firebase认证服务验证token
	firebaseUser, err := firebase.FirebaseAuthInstance.VerifyIDToken(ctx, token)
	if err != nil {
		uc.log.Errorf("Firebase token验证失败: %v", err)
		return nil, fmt.Errorf("Firebase token验证失败: %w", err)
	}

	// 转换为内部的FirebaseTokenInfo结构
	tokenInfo := &FirebaseTokenInfo{
		UID:           firebaseUser.UID,
		Email:         firebaseUser.Email,
		EmailVerified: firebaseUser.EmailVerified,
		Name:          firebaseUser.Name,
		Picture:       firebaseUser.Picture,
		Provider:      firebaseUser.Provider,
	}

	uc.log.Infof("Firebase token解析成功: uid=%s, email=%s, provider=%s",
		tokenInfo.UID, tokenInfo.Email, tokenInfo.Provider)

	return tokenInfo, nil
}
