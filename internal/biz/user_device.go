package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// UserDevice 用户设备信息模型
// 用于记录和管理用户的设备信息，支持多设备登录场景
type UserDevice struct {
	BasicModel              // 基础模型，包含ID、创建时间、更新时间
	UserID           int32  `json:"user_id" gorm:"column:user_id"` // 用户ID，关联tb_user表，建立用户与设备的关系
	DeviceID         string `json:"device_id" gorm:"column:device_id"` // 设备唯一标识符，由客户端生成，如：UUID、IMEI、设备指纹等，用于区分不同设备
	Platform         string `json:"platform" gorm:"column:platform"` // 设备平台类型，如：ios、android、web、windows、macos，用于推送服务选择和兼容性处理
	SystemVersion    string `json:"system_version" gorm:"column:system_version"` // 操作系统版本，如：iOS 17.0、Android 13、Windows 11，用于兼容性分析和功能适配
	AppVersion       string `json:"app_version" gorm:"column:app_version"` // 应用版本号，如：1.0.0、2.1.3，用于版本控制、功能推送和兼容性检查
	DeviceBrand      string `json:"device_brand" gorm:"column:device_brand"` // 设备品牌，如：Apple、Samsung、Huawei、Xiaomi，用于推送渠道选择和用户画像分析
	DeviceModel      string `json:"device_model" gorm:"column:device_model"` // 设备型号，如：iPhone 15 Pro、SM-G998B，用于详细的设备识别和适配优化
	ScreenResolution string `json:"screen_resolution" gorm:"column:screen_resolution"` // 屏幕分辨率，如：1179x2556、1080x2400，用于UI适配和用户体验优化
	LastActiveAt     int64  `json:"last_active_at" gorm:"column:last_active_at"` // 最后活跃时间戳，记录设备最后一次使用时间，用于设备活跃度统计和推送策略
	IsActive         bool   `json:"is_active" gorm:"column:is_active"` // 设备活跃状态，true-活跃，false-非活跃，用于控制是否向该设备推送通知
}

func (ud *UserDevice) TableName() string {
	return "tb_user_device"
}

// AuthAuditLog 认证审计日志
type AuthAuditLog struct {
	BasicModel
	UserID          int32  `json:"user_id" gorm:"column:user_id"`
	Action          string `json:"action" gorm:"column:action"`
	LoginType       string `json:"login_type" gorm:"column:login_type"`
	ThirdPartyType  string `json:"third_party_type" gorm:"column:third_party_type"`
	DeviceID        string `json:"device_id" gorm:"column:device_id"`
	Platform        string `json:"platform" gorm:"column:platform"`
	ClientIP        string `json:"client_ip" gorm:"column:client_ip"`
	UserAgent       string `json:"user_agent" gorm:"column:user_agent"`
	CountryCode     string `json:"country_code" gorm:"column:country_code"`
	Success         bool   `json:"success" gorm:"column:success"`
	ErrorCode       string `json:"error_code" gorm:"column:error_code"`
	ErrorMsg        string `json:"error_msg" gorm:"column:error_msg"`
	SessionDuration int32  `json:"session_duration" gorm:"column:session_duration"`
}

func (aal *AuthAuditLog) TableName() string {
	return "tb_auth_audit_log"
}

// UserDeviceRepo 用户设备仓库接口
type UserDeviceRepo interface {
	CreateOrUpdateDevice(ctx context.Context, device *UserDevice) error
	GetUserDevices(ctx context.Context, userID int32) ([]*UserDevice, error)
	GetDeviceByID(ctx context.Context, userID int32, deviceID string) (*UserDevice, error)
	UpdateDeviceActiveTime(ctx context.Context, userID int32, deviceID string) error
	DeactivateDevice(ctx context.Context, userID int32, deviceID string) error

	// 审计日志相关
	CreateAuditLog(ctx context.Context, log *AuthAuditLog) error
	GetAuditLogs(ctx context.Context, userID int32, limit int32) ([]*AuthAuditLog, error)
}

// UserDeviceUsecase 用户设备用例
type UserDeviceUsecase struct {
	repo UserDeviceRepo
	log  *log.Helper
}

// NewUserDeviceUsecase 创建用户设备用例
func NewUserDeviceUsecase(repo UserDeviceRepo, logger log.Logger) *UserDeviceUsecase {
	return &UserDeviceUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

// RegisterDevice 注册或更新设备信息
func (uc *UserDeviceUsecase) RegisterDevice(ctx context.Context, userID int32, deviceInfo *DeviceInfo) error {
	device := &UserDevice{
		BasicModel: BasicModel{
			CreatedAt: time.Now().Unix(),
			UpdatedAt: time.Now().Unix(),
		},
		UserID:           userID,
		DeviceID:         deviceInfo.DeviceID,
		Platform:         deviceInfo.Platform,
		SystemVersion:    deviceInfo.SystemVersion,
		AppVersion:       deviceInfo.AppVersion,
		DeviceBrand:      deviceInfo.DeviceBrand,
		DeviceModel:      deviceInfo.DeviceModel,
		ScreenResolution: deviceInfo.ScreenResolution,
		LastActiveAt:     time.Now().Unix(),
		IsActive:         true,
	}

	return uc.repo.CreateOrUpdateDevice(ctx, device)
}

// UpdateDeviceActivity 更新设备活跃时间
func (uc *UserDeviceUsecase) UpdateDeviceActivity(ctx context.Context, userID int32, deviceID string) error {
	return uc.repo.UpdateDeviceActiveTime(ctx, userID, deviceID)
}

// LogAuthEvent 记录认证事件
func (uc *UserDeviceUsecase) LogAuthEvent(ctx context.Context, event *AuthEvent) error {
	auditLog := &AuthAuditLog{
		UserID:          event.UserID,
		Action:          event.Action,
		LoginType:       event.LoginType,
		ThirdPartyType:  event.ThirdPartyType,
		DeviceID:        event.DeviceID,
		Platform:        event.Platform,
		ClientIP:        event.ClientIP,
		UserAgent:       event.UserAgent,
		CountryCode:     event.CountryCode,
		Success:         event.Success,
		ErrorCode:       event.ErrorCode,
		ErrorMsg:        event.ErrorMsg,
		SessionDuration: event.SessionDuration,
	}

	return uc.repo.CreateAuditLog(ctx, auditLog)
}

// DeviceInfo 设备信息结构
type DeviceInfo struct {
	DeviceID         string `json:"device_id"`
	Platform         string `json:"platform"`
	SystemVersion    string `json:"system_version"`
	AppVersion       string `json:"app_version"`
	DeviceBrand      string `json:"device_brand"`
	DeviceModel      string `json:"device_model"`
	ScreenResolution string `json:"screen_resolution"`
}

// AuthEvent 认证事件结构
type AuthEvent struct {
	UserID          int32  `json:"user_id"`
	Action          string `json:"action"`
	LoginType       string `json:"login_type"`
	ThirdPartyType  string `json:"third_party_type"`
	DeviceID        string `json:"device_id"`
	Platform        string `json:"platform"`
	ClientIP        string `json:"client_ip"`
	UserAgent       string `json:"user_agent"`
	CountryCode     string `json:"country_code"`
	Success         bool   `json:"success"`
	ErrorCode       string `json:"error_code"`
	ErrorMsg        string `json:"error_msg"`
	SessionDuration int32  `json:"session_duration"`
}

// ThirdPartyInfo 第三方账号信息结构
type ThirdPartyInfo struct {
	Provider       string `json:"provider"`
	ProviderUserID string `json:"provider_user_id"`
	ProviderEmail  string `json:"provider_email"`
	ProviderName   string `json:"provider_name"`
	AvatarUrl      string `json:"avatar_url"`
	EmailVerified  bool   `json:"email_verified"`
	Locale         string `json:"locale"`
	IsPrimary      bool   `json:"is_primary"`
}
