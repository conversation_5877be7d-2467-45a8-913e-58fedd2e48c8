package biz

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	stderr "errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	v1 "github.com/wlnil/life-log-be/api/user_habit/v1"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	httpClient "github.com/wlnil/life-log-be/internal/pkg/http_client"
	"github.com/wlnil/life-log-be/internal/pkg/task"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type CronTaskPayload struct {
	ApiUrl string `json:"api_url"` // 接口地址
	Method string `json:"method"`  // 请求方法
	Body   string `json:"body"`    // 请求体
}

func (c CronTaskPayload) Value() (driver.Value, error) {
	b, err := json.Marshal(c)
	return string(b), err
}

func (c *CronTaskPayload) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), c)
}

// CronTask 定时任务表
type CronTask struct {
	BasicModel
	UserID      int32                    `json:"user_id" gorm:"column:user_id"`           // 用户 id
	Payload     CronTaskPayload          `json:"payload" gorm:"column:payload;TYPE:json"` // 任务内容
	TriggerTime string                   `json:"trigger_time" gorm:"column:trigger_time"` // 任务触发时间
	Tag         string                   `json:"tag" gorm:"tag"`                          // 任务标签
	Status      enums.CronTaskStatusType `json:"status" gorm:"column:status"`             // 任务状态，1 进行中；2 暂停；3 结束；4 跳过；5 删除
	TodayTime   int64                    `json:"today_time" gorm:"today_time"`            // 当日时间戳
	ThirdID     string                   `json:"third_id" gorm:"third_id"`                // 外部 id，例如：习惯 id
	TaskType    enums.CronTaskType       `json:"task_type" gorm:"task_type"`              // 任务类型
	Remark      string                   `json:"remark" gorm:"remark"`                    // 备注
}

func (m *CronTask) TableName() string {
	return "tb_cron_task"
}

type CronTaskRepo interface {
	FindCronTaskByParams(where Where) ([]*CronTask, error)
	FirstByID(taskID int32, task *CronTask) error
	Update(ctx context.Context, cronTask *CronTask) error
	DeleteByUserID(ctx context.Context, userID int32) error
	BatchCreate(ctx context.Context, cronTasks []*CronTask) error
	BatchUpdateByID(ctx context.Context, IDs []int32, cronTask *CronTask) error
}

type CronTaskUsecase struct {
	commonUseCase *CommonUsecase

	repo          CronTaskRepo
	messageRepo   MessageRepo
	userHabitRepo UserHabitRepo
	userRepo      UserRepo

	log      *log.Helper
	confData *conf.Data
}

// Init 初始化定时任务
func (c *CronTaskUsecase) Init() {
	// TODO: 需要做电话通知管理员处理

	time.AfterFunc(30*time.Second, func() {
		// 1. 获取所有未执行任务
		where := Where{
			"status =": enums.CronTaskStatusTypeProcessing,
		}
		tasks, err := c.repo.FindCronTaskByParams(where)
		if err != nil {
			c.log.Errorf("cron_task_usecase: init cron task error: %v", err)
			return
		}

		// 2. 遍历任务，添加任务到 gocron
		for _, item := range tasks {
			if tool.Contains([]enums.CronTaskType{enums.CronTaskTypeUserHabit, enums.CronTaskTypeUserReckon}, item.TaskType) {
				_, err = task.Scheduler.Every(1).Day().At(item.TriggerTime).Tag(item.Tag).Do(func(taskID int32) {
					runCronTaskBody := map[string]interface{}{
						"task_id": taskID,
					}
					if err = httpClient.RunCronTask(runCronTaskBody, c.confData.Task.HttpAddr); err != nil {
						c.log.Errorf("cron_task_usecase: 初始化执行定时任务失败, error: %v, task_id: %v", err, taskID)
					}
				}, item.ID)
			} else if tool.Contains([]enums.CronTaskType{enums.CronTaskTypeUserVipTrial}, item.TaskType) {
				// 创建目标时间
				triggerTime, err := tool.ConvertLocalToUTC(item.TriggerTime)
				if err != nil {
					c.log.Errorf("cron_task_usecase: 初始化执行用户 vip 定时任务失败, error: %v, task_id: %v", err, item.ID)
					continue
				}
				// 如果当前时间大于等于目标时间，则直接执行
				if time.Now().UTC().Unix() >= triggerTime.Unix() {
					runCronTaskBody := map[string]interface{}{
						"task_id":     item.ID,
						"not_run_tag": true,
					}
					if err = httpClient.RunCronTask(runCronTaskBody, c.confData.Task.HttpAddr); err != nil {
						c.log.Errorf("cron_task_usecase: 执行用户 vip 定时任务失败, error: %v, task_id: %v", err, item.ID)
					}
				}
				_, err = task.Scheduler.Every(1).Day().At(triggerTime.Format("15:04")).Tag(item.Tag).Do(func(taskID int32) {
					now := time.Now().UTC()

					// 检查当前日期和时间是否匹配
					if now.Month() == triggerTime.Month() && now.Day() == triggerTime.Day() && now.Format("15:04") == triggerTime.Format("15:04") {
						runCronTaskBody := map[string]interface{}{
							"task_id": taskID,
						}
						if err = httpClient.RunCronTask(runCronTaskBody, c.confData.Task.HttpAddr); err != nil {
							c.log.Errorf("cron_task_usecase: 初始化执行用户 vip 定时任务失败, error: %v, task_id: %v", err, item.ID)
						}
					}

				}, item.ID)
			}

			if err != nil {
				c.log.Errorf("cron_task_usecase: 添加 scheduler 失败, error: %v", err)
			}
		}
		c.log.Infof("cron_task_usecase: 初始化添加定时任务成功，任务数量: %v", len(tasks))
	})
}

func (c *CronTaskUsecase) RunCronTask(ctx context.Context, request *v1.RunCronTaskRequest) (*v1.RunCronTaskReply, error) {
	res := &v1.RunCronTaskReply{}

	// 1. 获取用户任务，并判断是否需要执行
	cronTask := &CronTask{}
	err := c.repo.FirstByID(request.TaskId, cronTask)
	if err != nil {
		return nil, err
	}
	if cronTask.Status != enums.CronTaskStatusTypeProcessing {
		c.log.Errorf("cron_task 状态不是进行中，不需要执行，task_id: %v", request.TaskId)
		return res, nil
	}

	runCron, err := NewRunCronTask(cronTask.TaskType, c)
	if err != nil {
		return nil, err
	}

	// 执行任务
	if err = runCron.Run(ctx, cronTask, request.NotRunTag); err != nil {
		return nil, err
	}

	return res, nil
}

func (c *CronTaskUsecase) DeleteCronTaskByUserID(ctx context.Context, userID int32) error {
	where := Where{
		"user_id =": userID,
		"status in": []enums.CronTaskStatusType{enums.CronTaskStatusTypePause, enums.CronTaskStatusTypeProcessing},
	}
	cronTasks, err := c.repo.FindCronTaskByParams(where)
	if err != nil {
		return stderr.New(fmt.Sprintf("获取 cron_task 列表失败，error: %v, user_id: %v", err, userID))
	}
	if len(cronTasks) == 0 {
		return nil
	}
	cronTaskIDs := make([]int32, 0, len(cronTasks))
	for _, item := range cronTasks {
		cronTaskIDs = append(cronTaskIDs, item.ID)
	}
	cronTask := &CronTask{}
	cronTask.Status = enums.CronTaskStatusTypeDelete
	cronTask.UpdatedAt = time.Now().Unix()
	if err = c.repo.BatchUpdateByID(ctx, cronTaskIDs, cronTask); err != nil {
		return err
	}

	// 删除用户习惯定时任务
	for _, item := range cronTasks {
		if err = task.Scheduler.RemoveByTag(item.Tag); err != nil {
			c.log.Errorf("cron_usecase: 删除 scheduler 失败, error: %v, task_id: %v", err, item.ID)
		}
	}
	return nil
}

type RunCronTaskInterface interface {
	Run(ctx context.Context, cronTask *CronTask, notRunTag bool) error
}

type UserHabitCronTask struct {
	usecase *CronTaskUsecase
}

func (u *UserHabitCronTask) Run(ctx context.Context, cronTask *CronTask, notRunTag bool) error {
	schedulerNow := time.Now()

	// 2. 获取用户习惯，判断状态
	userHabit := &UserHabit{}
	if err := u.usecase.userHabitRepo.FirstByParams(ctx, userHabit, map[string]interface{}{"id": cast.ToInt32(cronTask.ThirdID)}); err != nil {
		return err
	}
	if userHabit.IsDeleted {
		u.usecase.log.Errorf("删除 scheduler 失败, error: 用户习惯已删除, task_id: %v", cronTask.ID)
		return nil
	}
	if userHabit.Status != enums.UserHabitStatusNormal {
		u.usecase.log.Errorf("user_habit 状态不是正常，不需要执行，user_habit_id: %v", userHabit.ID)
		// 移除定时任务
		if !notRunTag {
			if err := task.Scheduler.RemoveByTag(cronTask.Tag); err != nil {
				u.usecase.log.Errorf("删除 scheduler 失败, error: %v, task_id: %v", err, cronTask.ID)
			}
		}

		return nil
	}

	// 3. 判断今天是否需要提醒
	nowUserTime := time.Unix(cronTask.TodayTime, 0).AddDate(0, 0, 1)
	// 判断用户递增后的时间是否是 utc 当天时间（防止暂停后 today_time 未更新或者服务停止后 today_time 未更新）
	gapDay := (schedulerNow.Unix() - nowUserTime.Unix()) / 3600 / 24
	if gapDay > 0 {
		nowUserTime = nowUserTime.AddDate(0, 0, int(gapDay))
	}
	isNeed, err := u.usecase.commonUseCase.checkGenerateHabitSnap(ctx, userHabit, nowUserTime)
	if err != nil {
		return err
	}
	if !isNeed {
		u.usecase.log.Errorf("user_habit 今天不需要提醒，user_habit_id: %v", userHabit.ID)
		return nil
	}

	// 判断是否到时间，保证准时执行
	hour, _ := strconv.Atoi(cronTask.TriggerTime[:2])
	minute, _ := strconv.Atoi(cronTask.TriggerTime[3:5])
	if hour != schedulerNow.UTC().Hour() || minute != schedulerNow.UTC().Minute() {
		u.usecase.log.Errorf("cron_task 未到提醒时间，cron_task_id: %v", cronTask.ID)
		return nil
	}

	// 4. 发送提醒
	isSent := false
	msg := &UserMessage{
		UserID:    cronTask.UserID,
		MsgType:   enums.MessageTypeUserHabit,
		IsRead:    false,
		TodayTime: cronTask.TodayTime,
		ThirdID:   fmt.Sprintf("%v", cronTask.ID),
	}
	msg.CreatedAt = schedulerNow.Unix()
	msg.UpdatedAt = schedulerNow.Unix()
	if err = u.usecase.messageRepo.Create(msg); err != nil {
		// 如果消息已存在，则不予发送该消息到用户，但需要修改任务时间
		if strings.HasPrefix(err.Error(), "Error 1062") {
			u.usecase.log.Errorf("user_message 今天不需要发送消息，user_habit_id: %v，cron_task_id: %v", userHabit.ID, cronTask.ID)
			isSent = true
		} else {
			return err
		}
	}

	// TODO: 通知到用户手机
	if !isSent {
	}

	// 5. 更新用户任务当天为已执行
	cronTask.TodayTime = nowUserTime.Unix()
	cronTask.UpdatedAt = schedulerNow.Unix()
	if err = u.usecase.repo.Update(ctx, cronTask); err != nil {
		return err
	}

	return nil
}

type UserReckonCronTask struct {
	usecase *CronTaskUsecase
}

func (u *UserReckonCronTask) Run(ctx context.Context, cronTask *CronTask, notRunTag bool) error {
	now := time.Now()

	// 查询是否存在正在计时的记录
	urt := &UserReckonTime{}
	urt.ID = cast.ToInt32(cronTask.ThirdID)
	err := u.usecase.userHabitRepo.FirstReckonTimeByID(ctx, urt)
	if err != nil {
		u.usecase.log.Errorf("获取【用户计时】数据失败，error: %v, reckon_time_id: %v", err, cronTask.ThirdID)
	} else {
		// 该计时有效且未结束
		if urt.IsValid && urt.EndAt == 0 {
			// 结束计时
			endTime := time.Unix(cronTask.TodayTime, 0).AddDate(0, 0, 1)
			urt.Duration = int(endTime.Unix() - urt.StartAt)
			urt.EndAt = endTime.Unix()
			urt.UpdatedAt = now.Unix()
			if err = u.usecase.userHabitRepo.UpdateReckonTime(ctx, urt); err != nil {
				u.usecase.log.Errorf("更新【用户计时】数据失败，error: %v, reckon_time_id: %v", err, cronTask.ThirdID)
			}
		}
	}

	// 移除定时任务
	if !notRunTag {
		if err = task.Scheduler.RemoveByTag(cronTask.Tag); err != nil {
			u.usecase.log.Errorf("删除 scheduler 失败, error: %v, task_id: %v", err, cronTask.ID)
		}
	}

	// 修改定时任务状态为结束
	cronTask.Status = enums.CronTaskStatusTypeEnd
	cronTask.UpdatedAt = now.Unix()
	if err = u.usecase.repo.Update(ctx, cronTask); err != nil {
		return err
	}

	return nil
}

type UserVipCronTask struct {
	usecase *CronTaskUsecase
}

func (u *UserVipCronTask) Run(ctx context.Context, cronTask *CronTask, notRunTag bool) error {
	now := time.Now()

	// 查询用户 vip 是否过期
	user := &User{}
	user.ID = cronTask.UserID
	if err := u.usecase.userRepo.FindByID(ctx, user); err != nil {
		u.usecase.log.Errorf("获取【用户】数据失败，error: %v, user_id: %v", err, cronTask.UserID)
		return err
	} else {
		if user.ExpiredAt <= now.Unix() {
			user.RoleType = enums.UserRoleTypeNormal
			user.UpdatedAt = now.Unix()
			if err = u.usecase.userRepo.Update(ctx, user); err != nil {
				u.usecase.log.Errorf("更新【用户】数据失败，error: %v, user_id: %v", err, cronTask.UserID)
			}
			us := &UserSetting{
				UserID: user.ID,
			}
			if err = u.usecase.userRepo.GetUserSetting(ctx, us); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				u.usecase.log.Errorf("获取【用户设置】数据失败，error: %v, user_id: %v", err, cronTask.UserID)
			}
			if err = u.usecase.userRepo.UpdateUserSetting(ctx, us, map[string]interface{}{
				"privacy_password": "",
			}); err != nil {
				return err
			}
			isSetPrivacy := false
			if _, err = u.usecase.commonUseCase.CacheUserInfo(ctx, user.ID, user, &isSetPrivacy); err != nil {
				u.usecase.log.Errorf("更新用户缓存失败，error: %v, user_id: %v", err, cronTask.UserID)
			}
		}
	}

	// 移除定时任务
	if !notRunTag {
		if err := task.Scheduler.RemoveByTag(cronTask.Tag); err != nil {
			u.usecase.log.Errorf("删除 scheduler 失败, error: %v, task_id: %v", err, cronTask.ID)
		}
	}

	// 修改定时任务状态为结束
	cronTask.Status = enums.CronTaskStatusTypeEnd
	cronTask.UpdatedAt = now.Unix()
	if err := u.usecase.repo.Update(ctx, cronTask); err != nil {
		return err
	}

	return nil
}

func NewRunCronTask(taskType enums.CronTaskType, usecase *CronTaskUsecase) (RunCronTaskInterface, error) {
	switch taskType {
	case enums.CronTaskTypeUserHabit:
		return &UserHabitCronTask{
			usecase: usecase,
		}, nil
	case enums.CronTaskTypeUserReckon:
		return &UserReckonCronTask{
			usecase: usecase,
		}, nil
	case enums.CronTaskTypeUserVipTrial:
		return &UserVipCronTask{
			usecase: usecase,
		}, nil

	default:
		return nil, stderr.New("未知的任务类型")
	}
}

func NewCronTaskUsecase(repo CronTaskRepo, messageRepo MessageRepo, logger log.Logger, userHabitRepo UserHabitRepo, confData *conf.Data, commonUseCase *CommonUsecase, userRepo UserRepo) *CronTaskUsecase {
	return &CronTaskUsecase{
		repo:          repo,
		messageRepo:   messageRepo,
		userHabitRepo: userHabitRepo,
		userRepo:      userRepo,
		log:           log.NewHelper(logger),
		confData:      confData,
		commonUseCase: commonUseCase,
	}
}
