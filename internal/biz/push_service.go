package biz

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	firebase2 "github.com/wlnil/life-log-be/internal/pkg/firebase"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/conf"
	"google.golang.org/api/option"
)

// PushRequest 推送请求结构体
type PushRequest struct {
	UserID      int32             `json:"user_id"`
	Platform    string            `json:"platform"`     // ios, android, web
	DeviceToken string            `json:"device_token"` // 设备推送token
	Title       string            `json:"title"`        // 推送标题
	Content     string            `json:"content"`      // 推送内容
	ExtraData   map[string]string `json:"extra_data"`   // 额外数据
}

// PushResponse 推送响应结构体
type PushResponse struct {
	Success   bool   `json:"success"`    // 是否成功
	MessageID string `json:"message_id"` // 消息ID
	Error     string `json:"error"`      // 错误信息
}

// 推送服务错误定义
var (
	ErrInvalidRequest     = errors.New("无效的推送请求")
	ErrEmptyDeviceToken   = errors.New("设备Token不能为空")
	ErrEmptyContent       = errors.New("推送内容不能为空")
	ErrContentTooLong     = errors.New("推送内容过长")
	ErrNetworkError       = errors.New("网络请求失败")
	ErrAuthFailed         = errors.New("认证失败")
	ErrInvalidResponse    = errors.New("无效的响应格式")
	ErrServiceUnavailable = errors.New("推送服务不可用")
)

// 推送内容长度限制
const (
	MaxTitleLength   = 100  // 标题最大长度
	MaxContentLength = 1000 // 内容最大长度
	MaxExtraDataSize = 50   // 额外数据最大键值对数量
)

// validatePushRequest 验证推送请求参数
func validatePushRequest(req *PushRequest) error {
	if req == nil {
		return ErrInvalidRequest
	}

	if strings.TrimSpace(req.DeviceToken) == "" {
		return ErrEmptyDeviceToken
	}

	if strings.TrimSpace(req.Title) == "" && strings.TrimSpace(req.Content) == "" {
		return ErrEmptyContent
	}

	if len(req.Title) > MaxTitleLength {
		return fmt.Errorf("标题长度超过限制(%d字符): %w", MaxTitleLength, ErrContentTooLong)
	}

	if len(req.Content) > MaxContentLength {
		return fmt.Errorf("内容长度超过限制(%d字符): %w", MaxContentLength, ErrContentTooLong)
	}

	if len(req.ExtraData) > MaxExtraDataSize {
		return fmt.Errorf("额外数据超过限制(%d个键值对): %w", MaxExtraDataSize, ErrContentTooLong)
	}

	return nil
}

// HTTPClient 统一的HTTP客户端配置
type HTTPClient struct {
	client  *http.Client
	timeout time.Duration
	retries int
}

// NewHTTPClient 创建HTTP客户端
func NewHTTPClient(timeout time.Duration, retries int) *HTTPClient {
	return NewHTTPClientWithProxy(timeout, retries, "")
}

// NewHTTPClientWithProxy 创建带代理的HTTP客户端
func NewHTTPClientWithProxy(timeout time.Duration, retries int, proxyURL string) *HTTPClient {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
		},
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
	}

	// 配置代理
	if proxyURL != "" {
		if proxy, err := url.Parse(proxyURL); err == nil {
			transport.Proxy = http.ProxyURL(proxy)
		}
	}

	return &HTTPClient{
		client: &http.Client{
			Timeout:   timeout,
			Transport: transport,
		},
		timeout: timeout,
		retries: retries,
	}
}

// DoRequest 执行HTTP请求（带重试）
func (hc *HTTPClient) DoRequest(ctx context.Context, method, url string, headers map[string]string, body []byte) (*http.Response, error) {
	var lastErr error

	for attempt := 0; attempt <= hc.retries; attempt++ {
		if attempt > 0 {
			// 指数退避重试
			backoff := time.Duration(1<<uint(attempt-1)) * time.Second
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(backoff):
			}
		}

		req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewReader(body))
		if err != nil {
			lastErr = fmt.Errorf("创建请求失败: %w", err)
			continue
		}

		// 设置请求头
		for key, value := range headers {
			req.Header.Set(key, value)
		}

		resp, err := hc.client.Do(req)
		if err != nil {
			lastErr = fmt.Errorf("请求执行失败: %w", err)
			continue
		}

		// 检查HTTP状态码
		if resp.StatusCode >= 200 && resp.StatusCode < 300 {
			return resp, nil
		}

		// 读取错误响应
		respBody, _ := io.ReadAll(resp.Body)
		resp.Body.Close()

		lastErr = fmt.Errorf("HTTP错误 %d: %s", resp.StatusCode, string(respBody))

		// 对于某些错误码不重试
		if resp.StatusCode == 401 || resp.StatusCode == 403 {
			break
		}
	}

	return nil, fmt.Errorf("请求失败，已重试%d次: %w", hc.retries, lastErr)
}

// JPushService 极光推送服务
type JPushService struct {
	appKey     string
	appSecret  string
	apiURL     string
	httpClient *HTTPClient
	log        *log.Helper
	testMode   bool // 测试模式标志
}

func NewJPushService(conf *conf.Data, logger log.Logger) *JPushService {
	var appKey, appSecret, apiURL string
	var timeout time.Duration = 30 * time.Second
	var retries int = 3

	if conf.Jpush != nil {
		appKey = conf.Jpush.AppKey
		appSecret = conf.Jpush.AppSecret
		if conf.Jpush.ApiEndpoint != "" {
			apiURL = conf.Jpush.ApiEndpoint
		} else {
			apiURL = "https://api.jpush.cn/v3/push"
		}
		if conf.Jpush.Timeout > 0 {
			timeout = time.Duration(conf.Jpush.Timeout) * time.Second
		}
		if conf.Jpush.MaxRetry > 0 {
			retries = int(conf.Jpush.MaxRetry)
		}
	} else {
		apiURL = "https://api.jpush.cn/v3/push"
	}

	return &JPushService{
		appKey:     appKey,
		appSecret:  appSecret,
		apiURL:     apiURL,
		httpClient: NewHTTPClient(timeout, retries),
		log:        log.NewHelper(logger),
		testMode:   false, // 默认非测试模式
	}
}

func (j *JPushService) Send(ctx context.Context, req *PushRequest) (*PushResponse, error) {
	// 验证请求参数
	if err := validatePushRequest(req); err != nil {
		j.log.Errorf("JPush请求参数验证失败: %v", err)
		return &PushResponse{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 测试模式下跳过配置检查
	if !j.testMode {
		// 检查配置
		if j.appKey == "" || j.appSecret == "" {
			j.log.Error("JPush配置不完整：缺少AppKey或AppSecret")
			return &PushResponse{
				Success: false,
				Error:   "JPush配置不完整",
			}, nil
		}
	}

	// 构建极光推送请求
	payload := map[string]interface{}{
		"platform": "all",
		"audience": map[string]interface{}{
			"registration_id": []string{req.DeviceToken},
		},
		"notification": map[string]interface{}{
			"alert": req.Content,
			"android": map[string]interface{}{
				"title":  req.Title,
				"extras": req.ExtraData,
			},
			"ios": map[string]interface{}{
				"alert":  req.Content,
				"extras": req.ExtraData,
			},
		},
		"options": map[string]interface{}{
			"time_to_live": 86400, // 24小时
		},
	}

	// 发送请求到极光推送API
	resp, err := j.sendRequest(ctx, payload)
	if err != nil {
		j.log.Errorf("JPush发送失败: %v", err)
		return &PushResponse{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 安全地获取消息ID
	messageID := ""
	if msgID, ok := resp["msg_id"]; ok {
		if msgIDStr, ok := msgID.(string); ok {
			messageID = msgIDStr
		}
	}

	j.log.Infof("JPush发送成功，消息ID: %s", messageID)
	return &PushResponse{
		Success:   true,
		MessageID: messageID,
	}, nil
}

func (j *JPushService) GetPlatformName() string {
	return "jpush"
}

func (j *JPushService) sendRequest(ctx context.Context, payload map[string]interface{}) (map[string]interface{}, error) {
	// 测试模式下返回模拟响应
	if j.testMode {
		return map[string]interface{}{
			"msg_id": fmt.Sprintf("jpush_test_%d", time.Now().Unix()),
		}, nil
	}
	// 序列化请求体
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 构建请求头
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Basic " + j.getBasicAuth(),
	}

	j.log.Debugf("JPush请求URL: %s", j.apiURL)
	j.log.Debugf("JPush请求体: %s", string(jsonData))

	// 发送HTTP请求
	resp, err := j.httpClient.DoRequest(ctx, "POST", j.apiURL, headers, jsonData)
	if err != nil {
		return nil, fmt.Errorf("JPush API请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	j.log.Debugf("JPush响应: %s", string(respBody))

	// 解析响应
	var result map[string]interface{}
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查业务错误
	if errorCode, exists := result["error"]; exists {
		return nil, fmt.Errorf("JPush业务错误: %v", errorCode)
	}

	return result, nil
}

// getBasicAuth 生成Basic认证字符串
func (j *JPushService) getBasicAuth() string {
	// Base64编码的Basic认证
	// 格式: base64(appKey:appSecret)
	auth := j.appKey + ":" + j.appSecret
	return base64.StdEncoding.EncodeToString([]byte(auth))
}

// FCMService Firebase云消息服务 (使用Firebase Admin SDK)
type FCMService struct {
	projectID       string                     // Firebase项目ID
	credentialsFile string                     // 服务账户JSON文件路径
	messagingClient *messaging.Client          // Firebase Messaging客户端
	customClient    *firebase2.CustomFCMClient // 自定义FCM客户端（支持代理）
	log             *log.Helper
	testMode        bool // 测试模式标志
}

func NewFCMService(confData *conf.Data, logger log.Logger) *FCMService {
	logHelper := log.NewHelper(logger)

	projectID := confData.Firebase.ProjectId
	credentialsFile := confData.Firebase.CredentialsFile

	// 初始化Firebase Messaging客户端
	var messagingClient *messaging.Client
	var customClient *firebase2.CustomFCMClient

	if confData.Env == conf.EnvType_Test {
		logHelper.Infof("🌐 检测到需要代理环境，使用自定义FCM客户端")

		// 初始化自定义FCM客户端
		if credentialsFile != "" && projectID != "" {
			proxyURL := tool.GetProxyURL()
			if client, err := firebase2.NewCustomFCMClient(credentialsFile, proxyURL, logger); err != nil {
				logHelper.Errorf("❌ 自定义FCM客户端初始化失败: %v", err)
			} else {
				customClient = client
				logHelper.Infof("✅ 自定义FCM客户端初始化成功，使用代理: %s", proxyURL)
			}
		}
	} else {
		logHelper.Infof("🌍 使用标准Firebase Admin SDK客户端")

		// 使用标准Firebase Admin SDK
		if credentialsFile != "" && projectID != "" {
			ctx := context.Background()
			config := &firebase.Config{
				ProjectID: projectID,
			}

			// 检查凭据文件是否存在
			if _, err := os.Stat(credentialsFile); err == nil {
				logHelper.Infof("🔑 使用FCM凭据文件: %s, 项目ID: %s", credentialsFile, projectID)

				opt := option.WithCredentialsFile(credentialsFile)
				app, err := firebase.NewApp(ctx, config, opt)
				if err != nil {
					logHelper.Errorf("❌ Firebase应用初始化失败: %v", err)
				} else {
					messagingClient, err = app.Messaging(ctx)
					if err != nil {
						logHelper.Errorf("❌ Firebase Messaging客户端初始化失败: %v", err)
					} else {
						logHelper.Infof("✅ Firebase Messaging客户端初始化成功")
					}
				}
			} else {
				logHelper.Warnf("⚠️  FCM凭据文件不存在: %s", credentialsFile)
			}
		} else {
			logHelper.Warnf("⚠️  FCM配置不完整: projectID=%s, credentialsFile=%s", projectID, credentialsFile)
		}
	}

	return &FCMService{
		projectID:       projectID,
		credentialsFile: credentialsFile,
		messagingClient: messagingClient,
		customClient:    customClient,
		log:             logHelper,
		testMode:        false,
	}
}

func (f *FCMService) Send(ctx context.Context, req *PushRequest) (*PushResponse, error) {
	// 验证请求参数
	if err := validatePushRequest(req); err != nil {
		f.log.Errorf("FCM请求参数验证失败: %v", err)
		return &PushResponse{
			Success: false,
			Error:   err.Error(),
		}, nil
	}

	// 测试模式下返回模拟响应
	if f.testMode {
		f.log.Infof("FCM测试模式：模拟发送推送到设备 %s", req.DeviceToken)
		return &PushResponse{
			Success:   true,
			MessageID: fmt.Sprintf("fcm_admin_sdk_test_%d", time.Now().Unix()),
		}, nil
	}

	// 优先使用自定义客户端（代理环境）
	if f.customClient != nil {
		return f.sendWithCustomClient(ctx, req)
	}

	// 使用Firebase Admin SDK客户端
	return f.sendWithFirebaseSDK(ctx, req)
}

// sendWithCustomClient 使用自定义客户端发送
func (f *FCMService) sendWithCustomClient(ctx context.Context, req *PushRequest) (*PushResponse, error) {
	f.log.Infof("🌐 使用自定义FCM客户端发送消息到设备: %s", req.DeviceToken)

	// 构建FCM消息
	message := &firebase2.FCMMessage{
		Token: req.DeviceToken,
		Notification: &firebase2.FCMNotification{
			Title: req.Title,
			Body:  req.Content,
		},
		Data: req.ExtraData,
		Android: &firebase2.FCMAndroidConfig{
			Priority: "high",
		},
		APNS: &firebase2.FCMAPNSConfig{
			Headers: map[string]string{
				"apns-priority": "10",
			},
		},
	}

	// 发送消息
	resp, err := f.customClient.SendMessage(ctx, message)
	if err != nil {
		f.log.Errorf("自定义FCM客户端发送失败: %v", err)
		return &PushResponse{
			Success: false,
			Error:   fmt.Sprintf("FCM发送失败: %v", err),
		}, nil
	}

	return &PushResponse{
		Success:   resp.Success,
		MessageID: resp.MessageID,
		Error:     resp.Error,
	}, nil
}

// sendWithFirebaseSDK 使用Firebase Admin SDK发送
func (f *FCMService) sendWithFirebaseSDK(ctx context.Context, req *PushRequest) (*PushResponse, error) {
	// 检查Firebase Messaging客户端
	if f.messagingClient == nil {
		f.log.Error("FCM配置不完整：Firebase Messaging客户端未初始化")
		return &PushResponse{
			Success: false,
			Error:   "FCM配置不完整：缺少有效的凭据文件",
		}, nil
	}

	f.log.Infof("🔥 使用Firebase Admin SDK发送消息到设备: %s", req.DeviceToken)

	// 构建Firebase Admin SDK消息
	message := &messaging.Message{
		Token: req.DeviceToken,
		Notification: &messaging.Notification{
			Title: req.Title,
			Body:  req.Content,
		},
	}

	// 添加自定义数据
	if req.ExtraData != nil && len(req.ExtraData) > 0 {
		message.Data = req.ExtraData
	}

	// 添加Android特定配置
	ttl := time.Duration(24) * time.Hour
	message.Android = &messaging.AndroidConfig{
		Priority: "high",
		TTL:      &ttl, // 24小时
	}

	// 添加APNs特定配置
	message.APNS = &messaging.APNSConfig{
		Headers: map[string]string{
			"apns-priority": "10",
		},
	}

	// 使用Firebase Admin SDK发送消息
	messageID, err := f.messagingClient.Send(ctx, message)
	if err != nil {
		f.log.Errorf("FCM Admin SDK发送失败: %v", err)
		return &PushResponse{
			Success: false,
			Error:   fmt.Sprintf("FCM发送失败: %v", err),
		}, nil
	}

	f.log.Infof("FCM Admin SDK发送成功，消息ID: %s", messageID)
	return &PushResponse{
		Success:   true,
		MessageID: messageID,
	}, nil
}

func (f *FCMService) GetPlatformName() string {
	return "fcm"
}
