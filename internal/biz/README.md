# Biz

## 业务

### 用户习惯
1. 用户获取每日习惯列表，依然从 `tb_user_habit` 表中获取。仅对每日习惯进行实际操作时，才生成快照数据，存储到 `tb_user_habit_snapshot` 表中。
   1. 减少无效数据产生，节约成本
   2. 保证用户习惯列表的实时性
   3. 待办：**需要对用户行为数据进行归档**
2. 创建用户习惯时，传入用户时区，生成提醒时间时，使用用户时区进行计算。
3. 打卡周期解读
   1. 固定：根据勾选的星期，展示习惯，否则当天展示
   2. 每周：根据配置的每周打卡几天进行展示，统计打卡完成天数达到每周的打卡天数，则后面不用展示
   3. 每月：根据配置的每月打卡几天进行展示，统计打卡完成天数达到每月的打卡天数，则后面不用展示
4. 首页习惯展示
   1. 选择全部时，展示所有正常状态下的习惯
   2. 选择未完成时，展示未打卡完成的所有习惯
   3. 对于当日不需要打卡的习惯，不进行展示
5. 打卡计数规则
   1. 对于微习惯而言，统计每个微习惯的打卡总次数。由于每个习惯设置难度不同，允许用户多次完成不同难度的习惯


### 统计

需要打卡总次数
```text
统计原则：
1. 如果该月最新配置为按月打卡，则当月只能查看「当月」「所有」的数据，且打卡天数为按月配置的天数
2. 如果该月最新配置为固定、按周打卡，则可以查看「当月」「当周」「所有」的数据。但是如果点击『当周』时，选择的日期在当时配置为按月打卡，则按照只显示「当月」「所有」的数据
3. 如果由按月修改为固定、按周打卡，统计当月数据时，按月影响的有效范围为当月天数/影响的天数*配置的天数，且取整
```
