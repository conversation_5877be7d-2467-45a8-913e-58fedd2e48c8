package biz

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	taskpkg "github.com/wlnil/life-log-be/internal/pkg/task"
)

// ============================================================================
// 任务创建器接口和请求结构
// ============================================================================

// 失败处理相关常量
const (
	MaxConsecutiveFailures = 5    // 最大连续失败次数
	MaxRetries            = 3     // 单次推送最大重试次数
	DelayedRetryInterval  = 2     // 延迟重试间隔（小时）
	ExtendedRetryInterval = 24    // 扩展重试间隔（小时）
)

// FailureType 失败类型
type FailureType string

const (
	FailureTypeTemporary   FailureType = "temporary"    // 临时错误（网络、服务暂时不可用）
	FailureTypeUserIssue   FailureType = "user_issue"   // 用户问题（token无效、应用卸载）
	FailureTypeSystemIssue FailureType = "system_issue" // 系统问题（配置错误、服务异常）
	FailureTypePermanent   FailureType = "permanent"    // 永久错误（用户禁用推送）
)

// DistributedLock 分布式锁
type DistributedLock struct {
	key    string
	value  string
	client *redis.Client
}

// Release 释放锁
func (l *DistributedLock) Release() error {
	// 使用Lua脚本确保原子性
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`
	return l.client.Eval(context.Background(), script, []string{l.key}, l.value).Err()
}

// TaskCreateRequest 通用任务创建请求接口
type TaskCreateRequest interface {
	GetUserID() int32
	GetRelatedID() int32
	GetTaskType() enums.PushTaskType
	Validate() error
}

// TaskCreator 任务创建器接口
type TaskCreator interface {
	CreateTask(ctx context.Context, req TaskCreateRequest) (*PushTask, error)
	ValidateRequest(req TaskCreateRequest) error
	GetTaskType() enums.PushTaskType
}

// HabitReminderCreateRequest 习惯提醒创建请求
type HabitReminderCreateRequest struct {
	UserID       int32  `json:"user_id" validate:"required,gt=0"`
	RelatedID    int32  `json:"related_id" validate:"required,gt=0"`
	ReminderTime string `json:"reminder_time" validate:"required"`
	RepeatRule   string `json:"repeat_rule" validate:"required,oneof=daily weekly monthly"`
}

func (r *HabitReminderCreateRequest) GetUserID() int32 {
	return r.UserID
}

func (r *HabitReminderCreateRequest) GetRelatedID() int32 {
	return r.RelatedID
}

func (r *HabitReminderCreateRequest) GetTaskType() enums.PushTaskType {
	return enums.PushTaskTypeHabitReminder
}

func (r *HabitReminderCreateRequest) Validate() error {
	if r.UserID <= 0 {
		return fmt.Errorf("用户ID必须大于0")
	}
	if r.RelatedID <= 0 {
		return fmt.Errorf("关联ID必须大于0")
	}
	if r.ReminderTime == "" {
		return fmt.Errorf("提醒时间不能为空")
	}
	if _, err := time.Parse("15:04", r.ReminderTime); err != nil {
		return fmt.Errorf("提醒时间格式错误，应为 HH:MM 格式")
	}
	validRules := map[string]bool{"daily": true, "weekly": true, "monthly": true}
	if !validRules[r.RepeatRule] {
		return fmt.Errorf("重复规则必须是 daily, weekly, monthly 之一")
	}
	return nil
}

// SystemNoticeCreateRequest 系统通知创建请求
type SystemNoticeCreateRequest struct {
	UserID        int32  `json:"user_id" validate:"required,gt=0"`
	Title         string `json:"title" validate:"required"`
	Content       string `json:"content" validate:"required"`
	ScheduledTime int64  `json:"scheduled_time" validate:"required,gt=0"`
}

func (r *SystemNoticeCreateRequest) GetUserID() int32 {
	return r.UserID
}

func (r *SystemNoticeCreateRequest) GetRelatedID() int32 {
	return 0 // 系统通知通常没有关联ID
}

func (r *SystemNoticeCreateRequest) GetTaskType() enums.PushTaskType {
	return enums.PushTaskTypeSystemNotice
}

func (r *SystemNoticeCreateRequest) Validate() error {
	if r.UserID <= 0 {
		return fmt.Errorf("用户ID必须大于0")
	}
	if r.Title == "" {
		return fmt.Errorf("标题不能为空")
	}
	if r.Content == "" {
		return fmt.Errorf("内容不能为空")
	}
	if r.ScheduledTime <= 0 {
		return fmt.Errorf("计划执行时间必须大于0")
	}
	return nil
}

// PushTask 简化的推送任务
type PushTask struct {
	BasicModel
	UserID        int32                `json:"user_id" gorm:"column:user_id"`               // 用户ID
	TaskType      enums.PushTaskType   `json:"task_type" gorm:"column:task_type"`           // 任务类型：habit_reminder, system_notice
	RelatedID     int32                `json:"related_id" gorm:"column:related_id"`         // 关联数据ID（如 user_habit_id）
	Title         string               `json:"title" gorm:"column:title"`                   // 推送标题（可为空，执行时生成）
	Content       string               `json:"content" gorm:"column:content"`               // 推送内容（可为空，执行时生成）
	ScheduledTime int64                `json:"scheduled_time" gorm:"column:scheduled_time"` // 计划执行时间戳
	Status        enums.PushTaskStatus `json:"status" gorm:"column:status"`                 // 状态：0-待执行，1-成功，2-失败，3-取消
	IsRecurring   bool                 `json:"is_recurring" gorm:"column:is_recurring"`     // 是否周期任务
	RepeatRule    string               `json:"repeat_rule" gorm:"column:repeat_rule"`       // 重复规则：daily, weekly, monthly
	ReminderTime  string               `json:"reminder_time" gorm:"column:reminder_time"`   // 提醒时间 "07:30"
	ExecuteTime   int64                `json:"execute_time" gorm:"column:execute_time"`     // 实际执行时间
	ErrorMsg      string               `json:"error_msg" gorm:"column:error_msg"`           // 错误信息

	// 失败处理相关字段
	FailureCount    int32  `json:"failure_count" gorm:"column:failure_count"`         // 连续失败次数
	MaxFailures     int32  `json:"max_failures" gorm:"column:max_failures"`           // 最大失败次数
	LastFailureTime int64  `json:"last_failure_time" gorm:"column:last_failure_time"` // 最后失败时间
	FailureReason   string `json:"failure_reason" gorm:"column:failure_reason"`       // 失败原因分类
	Version         int32  `json:"version" gorm:"column:version"`                     // 乐观锁版本号
}

func (p *PushTask) TableName() string {
	return "tb_push_task"
}

// PushNotificationRepo 简化推送任务仓库接口
// Transaction 事务接口
type Transaction interface {
	Commit() error
	Rollback() error
}

type PushNotificationRepo interface {
	CreatePushTask(ctx context.Context, task *PushTask) error
	UpdatePushTask(ctx context.Context, task *PushTask) error
	GetPushTaskByID(ctx context.Context, taskID int64) (*PushTask, error)
	GetPendingTasks(ctx context.Context, limit int32) ([]*PushTask, error)
	GetPendingTasksPaginated(ctx context.Context, limit, offset int) ([]*PushTask, error)
	DeleteTasksByUserHabit(ctx context.Context, userID, userHabitID int32) error

	// 新增：事务支持
	BeginTx(ctx context.Context) (Transaction, error)
	UpdatePushTaskWithVersion(ctx context.Context, tx Transaction, task *PushTask, expectedVersion int32) error
	CreatePushTaskInTx(ctx context.Context, tx Transaction, task *PushTask) error
}

// PushService 推送服务接口
type PushService interface {
	SendNotification(ctx context.Context, userID int32, title, content string) error
	GetServiceName() string
}

// PushNotificationUsecase 简化的推送通知用例
type PushNotificationUsecase struct {
	repo              PushNotificationRepo
	registry          *TaskHandlerRegistry
	creators          map[enums.PushTaskType]TaskCreator // 新增：任务创建器注册表
	scheduler         *taskpkg.DistributedScheduler
	pushServices      map[string]PushService
	pushServicesMutex sync.RWMutex                       // 保护pushServices的并发访问
	redisClient       *redis.Client
	instanceID        string                             // 实例ID，用于分布式锁
	log               *log.Helper
}

// NewPushNotificationUsecase 创建简化的推送通知用例
func NewPushNotificationUsecase(
	repo PushNotificationRepo,
	userHabitRepo UserHabitRepo,
	userRepo UserRepo,
	redisClient *redis.Client,
	logger log.Logger,
) *PushNotificationUsecase {
	usecase := &PushNotificationUsecase{
		repo: repo,
		registry: &TaskHandlerRegistry{
			handlers: make(map[enums.PushTaskType]TaskHandler),
		},
		creators:     make(map[enums.PushTaskType]TaskCreator), // 新增：初始化创建器注册表
		pushServices: make(map[string]PushService),
		redisClient:  redisClient,
		instanceID:   fmt.Sprintf("push_instance_%d", time.Now().UnixNano()), // 生成唯一实例ID
		log:          log.NewHelper(logger),
	}

	// 创建分布式调度器
	usecase.scheduler = taskpkg.NewDistributedScheduler(redisClient, logger)

	// 直接创建并注册习惯提醒处理器
	habitHandler := &HabitReminderHandler{
		userHabitRepo: userHabitRepo,
		userRepo:      userRepo,
		log:           log.NewHelper(logger),
	}
	usecase.registry.RegisterHandler(enums.PushTaskTypeHabitReminder, habitHandler)

	// 注册习惯提醒创建器
	habitCreator := NewHabitReminderCreator(habitHandler, logger)
	usecase.creators[enums.PushTaskTypeHabitReminder] = habitCreator

	// 注册系统通知创建器
	systemCreator := NewSystemNoticeCreator(logger)
	usecase.creators[enums.PushTaskTypeSystemNotice] = systemCreator

	return usecase
}

// RegisterTaskHandler 注册任务处理器
func (u *PushNotificationUsecase) RegisterTaskHandler(taskType enums.PushTaskType, handler TaskHandler) {
	u.registry.RegisterHandler(taskType, handler)
}

// SetPushServices 设置推送服务
func (u *PushNotificationUsecase) SetPushServices(services map[string]PushService) {
	u.pushServicesMutex.Lock()
	defer u.pushServicesMutex.Unlock()
	u.pushServices = services
}

// acquireDistributedLock 获取分布式锁
func (u *PushNotificationUsecase) acquireDistributedLock(ctx context.Context, key string, expiration time.Duration) (*DistributedLock, error) {
	lockValue := fmt.Sprintf("%s_%d", u.instanceID, time.Now().UnixNano())

	// 使用Redis SET NX EX命令
	success, err := u.redisClient.SetNX(ctx, key, lockValue, expiration).Result()
	if err != nil {
		return nil, err
	}

	if !success {
		return nil, fmt.Errorf("获取锁失败，任务可能正在执行")
	}

	return &DistributedLock{
		key:    key,
		value:  lockValue,
		client: u.redisClient,
	}, nil
}

// classifyFailure 分类失败类型
func (u *PushNotificationUsecase) classifyFailure(err error) FailureType {
	errMsg := strings.ToLower(err.Error())
	switch {
	case strings.Contains(errMsg, "network") || strings.Contains(errMsg, "timeout") || strings.Contains(errMsg, "connection"):
		return FailureTypeTemporary
	case strings.Contains(errMsg, "invalid token") || strings.Contains(errMsg, "not registered") || strings.Contains(errMsg, "unregistered"):
		return FailureTypeUserIssue
	case strings.Contains(errMsg, "permission denied") || strings.Contains(errMsg, "disabled") || strings.Contains(errMsg, "forbidden"):
		return FailureTypePermanent
	default:
		return FailureTypeSystemIssue
	}
}

// shouldCountFailure 判断是否应该计入失败次数
func (u *PushNotificationUsecase) shouldCountFailure(failureType FailureType) bool {
	switch failureType {
	case FailureTypeTemporary:
		return false // 临时错误不计入失败次数
	case FailureTypePermanent:
		return true // 永久错误立即计入
	default:
		return true
	}
}

// shouldRetry 判断是否应该重试
func (u *PushNotificationUsecase) shouldRetry(err error) bool {
	errMsg := strings.ToLower(err.Error())
	// 网络错误、超时错误可以重试
	retryableErrors := []string{"timeout", "network", "connection", "temporary", "unavailable"}

	for _, retryable := range retryableErrors {
		if strings.Contains(errMsg, retryable) {
			return true
		}
	}

	// 用户token无效、权限问题不重试
	nonRetryableErrors := []string{"invalid token", "permission denied", "not registered", "forbidden", "disabled"}
	for _, nonRetryable := range nonRetryableErrors {
		if strings.Contains(errMsg, nonRetryable) {
			return false
		}
	}

	return true // 默认重试
}

// calculateNextScheduleStrategy 计算下次调度策略
func (u *PushNotificationUsecase) calculateNextScheduleStrategy(task *PushTask, handler TaskHandler) (nextTime int64, shouldContinue bool) {
	// 检查是否达到最大失败次数
	if task.FailureCount >= task.MaxFailures {
		return 0, false
	}

	// 根据失败次数调整策略
	switch {
	case task.FailureCount <= 2:
		// 轻微失败：正常周期
		return handler.CalculateNextScheduleTime(task.ReminderTime, task.RepeatRule), true

	case task.FailureCount <= 4:
		// 中度失败：延迟2小时
		normalNext := handler.CalculateNextScheduleTime(task.ReminderTime, task.RepeatRule)
		delayedNext := time.Now().Add(DelayedRetryInterval * time.Hour).Unix()
		if delayedNext > normalNext {
			return delayedNext, true
		}
		return normalNext, true

	default:
		// 严重失败：延迟24小时
		normalNext := handler.CalculateNextScheduleTime(task.ReminderTime, task.RepeatRule)
		delayedNext := time.Now().Add(ExtendedRetryInterval * time.Hour).Unix()
		if delayedNext > normalNext {
			return delayedNext, true
		}
		return normalNext, true
	}
}



// ============================================================================
// 统一的任务创建接口
// ============================================================================

// CreateTask 统一的任务创建入口
func (u *PushNotificationUsecase) CreateTask(ctx context.Context, req TaskCreateRequest) error {
	// 1. 参数验证
	if err := req.Validate(); err != nil {
		u.log.Errorf("任务创建参数验证失败: taskType=%s, userID=%d, error=%v",
			req.GetTaskType(), req.GetUserID(), err)
		return fmt.Errorf("参数验证失败: %w", err)
	}

	// 2. 获取任务创建器
	creator := u.creators[req.GetTaskType()]
	if creator == nil {
		u.log.Errorf("不支持的任务类型: %s", req.GetTaskType())
		return fmt.Errorf("不支持的任务类型: %s", req.GetTaskType())
	}

	// 3. 创建任务
	task, err := creator.CreateTask(ctx, req)
	if err != nil {
		u.log.Errorf("创建任务失败: taskType=%s, userID=%d, error=%v",
			req.GetTaskType(), req.GetUserID(), err)
		return fmt.Errorf("创建任务失败: %w", err)
	}

	// 4. 保存并调度
	return u.saveAndScheduleTask(ctx, task)
}

// CreateTasksBatch 批量创建任务
func (u *PushNotificationUsecase) CreateTasksBatch(ctx context.Context, requests []TaskCreateRequest) error {
	if len(requests) == 0 {
		return fmt.Errorf("请求列表不能为空")
	}

	for _, req := range requests {
		if err := u.CreateTask(ctx, req); err != nil {
			u.log.Errorf("批量创建任务失败: taskType=%s, userID=%d, error=%v",
				req.GetTaskType(), req.GetUserID(), err)
			return err
		}
	}

	u.log.Infof("批量创建任务完成: 总数=%d", len(requests))
	return nil
}

// DeleteTasksByUserHabit 删除用户习惯相关的任务
func (u *PushNotificationUsecase) DeleteTasksByUserHabit(ctx context.Context, userID, relatedID int32) error {
	// 如果配置了分布式调度器，先从调度器中移除任务
	if u.scheduler != nil {
		taskType := string(enums.PushTaskTypeHabitReminder)
		if err := u.scheduler.RemovePushTask(taskType, userID, relatedID); err != nil {
			u.log.Warnf("从调度器移除任务失败: %v", err)
			// 继续执行，不中断流程
		} else {
			u.log.Infof("成功从调度器移除任务: type=%s, userID=%d, relatedID=%d",
				taskType, userID, relatedID)
		}
	}

	// 从数据库中删除任务记录
	return u.repo.DeleteTasksByUserHabit(ctx, userID, relatedID)
}

// DeleteAllUserTasks 删除用户的所有推送任务
func (u *PushNotificationUsecase) DeleteAllUserTasks(ctx context.Context, userID int32) error {
	// 如果配置了分布式调度器，先从调度器中移除用户的所有任务
	if u.scheduler != nil {
		if err := u.scheduler.RemoveUserPushTasks(userID); err != nil {
			u.log.Warnf("从调度器移除用户任务失败: %v", err)
			// 继续执行，不中断流程
		} else {
			u.log.Infof("成功从调度器移除用户所有任务: userID=%d", userID)
		}
	}

	// 从数据库中删除用户的所有任务记录
	// 注意：这里需要在 repo 中实现 DeleteTasksByUser 方法
	// return u.repo.DeleteTasksByUser(ctx, userID)

	u.log.Infof("用户任务删除完成: userID=%d", userID)
	return nil
}



// ============================================================================
// 内部辅助方法
// ============================================================================

// saveAndScheduleTask 保存并调度任务
func (u *PushNotificationUsecase) saveAndScheduleTask(ctx context.Context, task *PushTask) error {
	// 1. 保存任务
	if err := u.repo.CreatePushTask(ctx, task); err != nil {
		u.log.Errorf("保存任务失败: taskID=%d, error=%v", task.ID, err)
		return fmt.Errorf("保存任务失败: %w", err)
	}

	// 2. 调度任务
	if err := u.scheduleTask(ctx, task); err != nil {
		u.log.Errorf("调度任务失败: taskID=%d, error=%v", task.ID, err)
		return fmt.Errorf("调度任务失败: %w", err)
	}

	u.log.Infof("成功创建并调度任务: taskID=%d, userID=%d, taskType=%s, scheduledTime=%d",
		task.ID, task.UserID, task.TaskType, task.ScheduledTime)

	return nil
}

// scheduleTask 调度单个任务
func (u *PushNotificationUsecase) scheduleTask(ctx context.Context, task *PushTask) error {
	// 计算距离执行时间的间隔
	executeTime := time.Unix(task.ScheduledTime, 0)
	delay := time.Until(executeTime)

	if delay > 0 {
		// 如果是未来时间，创建一次性延迟任务
		config := taskpkg.ScheduleConfig{
			Name:        fmt.Sprintf("push_task_%d", task.ID),
			Interval:    delay,
			LockTimeout: 5 * time.Minute,
			TaskFunc: func() error {
				// 使用新的context，避免原context过期
				newCtx := context.Background()
				return u.ExecutePushTask(newCtx, int64(task.ID))
			},
			Tags: []string{
				fmt.Sprintf("user_%d", task.UserID),
				fmt.Sprintf("type_%s", task.TaskType),
				fmt.Sprintf("related_%d", task.RelatedID),
				"push_task",
			},
		}

		return u.scheduler.AddTask(config)
	} else {
		// 如果是过去时间，立即执行
		return u.ExecutePushTask(ctx, int64(task.ID))
	}
}

// ExecutePushTask 执行推送任务（重构版本，支持事务、重试、防重复）
func (u *PushNotificationUsecase) ExecutePushTask(ctx context.Context, taskID int64) error {
	// 1. 分布式锁防重复执行
	lockKey := fmt.Sprintf("push_task_lock_%d", taskID)
	lock, err := u.acquireDistributedLock(ctx, lockKey, 5*time.Minute)
	if err != nil {
		u.log.Warnf("获取任务锁失败: %v", err)
		return nil // 不返回错误，避免重复调度
	}
	defer lock.Release()

	// 2. 获取任务信息
	task, err := u.repo.GetPushTaskByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("获取推送任务失败: %w", err)
	}

	if task.Status != enums.PushTaskStatusPending {
		return nil // 任务已被处理
	}

	// 3. 获取处理器
	handler := u.registry.GetHandler(task.TaskType)
	if handler == nil {
		return u.handleTaskError(ctx, task, fmt.Errorf("未找到任务类型 %s 的处理器", task.TaskType), false)
	}

	// 4. 检查是否需要推送
	shouldSend, err := u.checkShouldSendNotification(ctx, handler, task.RelatedID)
	if err != nil {
		return u.handleTaskError(ctx, task, fmt.Errorf("检查推送条件失败: %w", err), true)
	}

	if !shouldSend {
		return u.handleTaskSkip(ctx, task, handler)
	}

	// 5. 生成推送内容
	title, content, err := handler.GenerateContent(ctx, task.RelatedID)
	if err != nil {
		return u.handleTaskError(ctx, task, fmt.Errorf("生成推送内容失败: %w", err), true)
	}

	// 6. 执行推送（带重试）
	pushErr := u.sendPushNotificationWithRetry(ctx, task.UserID, title, content)

	// 7. 处理结果（使用事务）
	return u.handleTaskResult(ctx, task, handler, title, content, pushErr)
}

// checkShouldSendNotification 检查是否需要推送（包装错误处理）
func (u *PushNotificationUsecase) checkShouldSendNotification(ctx context.Context, handler TaskHandler, relatedID int32) (bool, error) {
	// 这里可以添加更多的错误处理逻辑
	return handler.ShouldSendNotification(ctx, relatedID), nil
}

// handleTaskSkip 处理任务跳过
func (u *PushNotificationUsecase) handleTaskSkip(ctx context.Context, task *PushTask, handler TaskHandler) error {
	u.log.Infof("任务 %d 不需要推送，跳过执行", task.ID)

	// 更新任务状态为取消
	task.Status = enums.PushTaskStatusCancel
	task.ExecuteTime = time.Now().Unix()
	task.Version++

	if err := u.repo.UpdatePushTask(ctx, task); err != nil {
		u.log.Errorf("更新任务状态失败: %v", err)
		return err
	}

	// 处理周期任务（跳过的任务也要继续周期）
	if task.IsRecurring {
		nextTime, shouldContinue := u.calculateNextScheduleStrategy(task, handler)
		if shouldContinue {
			return u.createNextRecurringTask(ctx, task, nextTime)
		}
	}
	return nil
}

// handleTaskError 处理任务错误
func (u *PushNotificationUsecase) handleTaskError(ctx context.Context, task *PushTask, err error, shouldContinueCycle bool) error {
	u.log.Errorf("任务 %d 执行错误: %v", task.ID, err)

	// 更新任务状态为失败
	task.Status = enums.PushTaskStatusFailed
	task.ExecuteTime = time.Now().Unix()
	task.ErrorMsg = err.Error()
	task.Version++

	if updateErr := u.repo.UpdatePushTask(ctx, task); updateErr != nil {
		u.log.Errorf("更新任务状态失败: %v", updateErr)
	}

	// 根据参数决定是否继续周期
	if shouldContinueCycle && task.IsRecurring {
		handler := u.registry.GetHandler(task.TaskType)
		if handler != nil {
			nextTime, shouldContinue := u.calculateNextScheduleStrategy(task, handler)
			if shouldContinue {
				u.createNextRecurringTask(ctx, task, nextTime)
			}
		}
	}

	return err
}

// sendPushNotificationWithRetry 带重试的推送发送
func (u *PushNotificationUsecase) sendPushNotificationWithRetry(ctx context.Context, userID int32, title, content string) error {
	var lastErr error

	for i := 0; i < MaxRetries; i++ {
		if i > 0 {
			// 指数退避
			delay := time.Duration(math.Pow(2, float64(i-1))) * time.Second
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		err := u.sendPushNotification(ctx, userID, title, content)
		if err == nil {
			if i > 0 {
				u.log.Infof("推送重试成功: userID=%d, retries=%d", userID, i)
			}
			return nil
		}

		lastErr = err

		// 判断是否应该重试
		if !u.shouldRetry(err) {
			u.log.Warnf("推送错误不可重试: %v", err)
			break
		}

		u.log.Warnf("推送重试 %d/%d: %v", i+1, MaxRetries, err)
	}

	return fmt.Errorf("推送最终失败，重试 %d 次: %w", MaxRetries, lastErr)
}

// handleTaskResult 使用事务处理任务结果
func (u *PushNotificationUsecase) handleTaskResult(ctx context.Context, task *PushTask, handler TaskHandler, title, content string, pushErr error) error {
	// 开始事务
	tx, err := u.repo.BeginTx(ctx)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 更新任务状态
	originalVersion := task.Version
	task.ExecuteTime = time.Now().Unix()
	task.Version++ // 乐观锁版本递增

	if pushErr != nil {
		// 推送失败处理
		failureType := u.classifyFailure(pushErr)
		task.Status = enums.PushTaskStatusFailed
		task.ErrorMsg = pushErr.Error()
		task.FailureReason = string(failureType)
		task.LastFailureTime = time.Now().Unix()

		if u.shouldCountFailure(failureType) {
			task.FailureCount++
		}

		u.log.Errorf("推送失败: taskID=%d, type=%s, count=%d, error=%v",
			task.ID, failureType, task.FailureCount, pushErr)
	} else {
		// 推送成功处理
		task.Status = enums.PushTaskStatusSuccess
		task.Title = title
		task.Content = content
		task.FailureCount = 0 // 重置失败计数
		task.FailureReason = ""

		u.log.Infof("推送成功: taskID=%d", task.ID)
	}

	// 乐观锁更新任务
	if err := u.repo.UpdatePushTaskWithVersion(ctx, tx, task, originalVersion); err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	// 处理周期任务（关键：无论成功失败都处理）
	if task.IsRecurring {
		nextTime, shouldContinue := u.calculateNextScheduleStrategy(task, handler)

		if shouldContinue {
			if err := u.createNextRecurringTaskInTx(ctx, tx, task, nextTime); err != nil {
				return fmt.Errorf("创建下次周期任务失败: %w", err)
			}
		} else {
			// 记录任务暂停
			u.log.Warnf("周期任务暂停: taskID=%d, failureCount=%d", task.ID, task.FailureCount)
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	// 事务外的操作：发送告警（失败不影响主流程）
	if task.FailureCount >= task.MaxFailures {
		go u.sendFailureAlert(context.Background(), task)
	}

	return pushErr // 返回原始推送错误（用于日志）
}

// sendFailureAlert 发送失败告警
func (u *PushNotificationUsecase) sendFailureAlert(ctx context.Context, task *PushTask) {
	u.log.Errorf("推送任务达到最大失败次数: userID=%d, taskType=%s, taskID=%d, failureCount=%d",
		task.UserID, task.TaskType, task.ID, task.FailureCount)

	// 这里可以集成告警系统，比如发送邮件、短信、钉钉等
	// 暂时只记录日志
}

// handleRecurringTask 处理周期任务（已废弃，使用新的方法）
func (u *PushNotificationUsecase) handleRecurringTask(ctx context.Context, task *PushTask, handler TaskHandler) error {
	nextTime, shouldContinue := u.calculateNextScheduleStrategy(task, handler)
	if !shouldContinue {
		return nil
	}
	return u.createNextRecurringTask(ctx, task, nextTime)
}

// createNextRecurringTask 创建下次周期任务
func (u *PushNotificationUsecase) createNextRecurringTask(ctx context.Context, task *PushTask, nextTime int64) error {
	// 创建新的任务记录
	newTask := &PushTask{
		UserID:          task.UserID,
		TaskType:        task.TaskType,
		RelatedID:       task.RelatedID,
		ScheduledTime:   nextTime,
		Status:          enums.PushTaskStatusPending,
		IsRecurring:     true,
		RepeatRule:      task.RepeatRule,
		ReminderTime:    task.ReminderTime,
		FailureCount:    0,                    // 新任务重置失败计数
		MaxFailures:     task.MaxFailures,     // 继承最大失败次数
		LastFailureTime: 0,
		FailureReason:   "",
		Version:         1,
	}

	if err := u.repo.CreatePushTask(ctx, newTask); err != nil {
		return fmt.Errorf("创建下次推送任务失败: %w", err)
	}

	// 调度新任务
	return u.scheduleTask(ctx, newTask)
}

// createNextRecurringTaskInTx 在事务中创建下次周期任务
func (u *PushNotificationUsecase) createNextRecurringTaskInTx(ctx context.Context, tx Transaction, task *PushTask, nextTime int64) error {
	// 创建新的任务记录
	newTask := &PushTask{
		UserID:          task.UserID,
		TaskType:        task.TaskType,
		RelatedID:       task.RelatedID,
		ScheduledTime:   nextTime,
		Status:          enums.PushTaskStatusPending,
		IsRecurring:     true,
		RepeatRule:      task.RepeatRule,
		ReminderTime:    task.ReminderTime,
		FailureCount:    0,
		MaxFailures:     task.MaxFailures,
		LastFailureTime: 0,
		FailureReason:   "",
		Version:         1,
	}

	if err := u.repo.CreatePushTaskInTx(ctx, tx, newTask); err != nil {
		return fmt.Errorf("在事务中创建下次推送任务失败: %w", err)
	}

	// 事务提交后异步调度任务
	go func() {
		if err := u.scheduleTask(context.Background(), newTask); err != nil {
			u.log.Errorf("调度新周期任务失败: taskID=%d, error=%v", newTask.ID, err)
		}
	}()

	return nil
}

// sendPushNotification 发送推送通知（并发安全版本）
func (u *PushNotificationUsecase) sendPushNotification(ctx context.Context, userID int32, title, content string) error {
	// 复制map避免并发修改
	u.pushServicesMutex.RLock()
	services := make(map[string]PushService)
	for k, v := range u.pushServices {
		services[k] = v
	}
	u.pushServicesMutex.RUnlock()

	if len(services) == 0 {
		return fmt.Errorf("没有可用的推送服务")
	}

	for serviceName, service := range services {
		if err := service.SendNotification(ctx, userID, title, content); err != nil {
			u.log.Errorf("推送服务 %s 发送失败: %v", serviceName, err)
			continue
		}
		u.log.Infof("推送服务 %s 发送成功", serviceName)
		return nil
	}

	return fmt.Errorf("所有推送服务都发送失败")
}

// StartScheduler 启动调度器
func (u *PushNotificationUsecase) StartScheduler(ctx context.Context) error {
	// 1. 启动分布式调度器
	u.scheduler.Start()

	// 2. 加载待执行的推送任务
	if err := u.LoadPendingTasks(ctx); err != nil {
		u.log.Errorf("加载待执行任务失败: %v", err)
		return err
	}

	u.log.Info("简化推送调度器启动成功")
	return nil
}

// StopScheduler 停止调度器
func (u *PushNotificationUsecase) StopScheduler() {
	u.scheduler.Stop()
}

// LoadPendingTasks 启动时加载待执行的推送任务 - 使用分布式分片加载
func (u *PushNotificationUsecase) LoadPendingTasks(ctx context.Context) error {
	const batchSize = 1000 // 每个分片的大小

	// 初始化分布式加载状态
	if err := u.initDistributedLoadState(ctx); err != nil {
		return fmt.Errorf("初始化分布式加载状态失败: %w", err)
	}

	instanceProcessed := 0 // 当前实例处理的任务数
	instanceSuccess := 0   // 当前实例成功的任务数

	u.log.Info("开始分布式加载待执行推送任务...")

	for {
		// 获取当前实例应该处理的分片
		shardInfo, hasMore, err := u.claimNextShard(ctx, batchSize)
		if err != nil {
			return fmt.Errorf("获取分片失败: %w", err)
		}

		if !hasMore {
			break // 没有更多分片需要处理
		}

		// 处理当前分片
		tasks, err := u.repo.GetPendingTasksPaginated(ctx, shardInfo.Size, shardInfo.Offset)
		if err != nil {
			u.log.Errorf("获取分片任务失败 (offset=%d, size=%d): %v", shardInfo.Offset, shardInfo.Size, err)
			continue
		}

		if len(tasks) == 0 {
			u.log.Infof("分片 [%d-%d] 无任务", shardInfo.Offset, shardInfo.Offset+shardInfo.Size)
			continue
		}

		// 并发处理当前分片
		batchSuccess := u.processTaskBatch(ctx, tasks)
		instanceSuccess += batchSuccess
		instanceProcessed += len(tasks)

		u.log.Infof("分片 [%d-%d] 处理完成: %d/%d 成功",
			shardInfo.Offset, shardInfo.Offset+len(tasks), batchSuccess, len(tasks))

		// 更新全局进度（包含全局统计）
		u.updateGlobalProgress(ctx, len(tasks), batchSuccess)
	}

	// 获取并显示最终的全局统计
	globalStats, err := u.getGlobalLoadStats(ctx)
	if err != nil {
		u.log.Errorf("获取全局统计失败: %v", err)
	} else {
		u.log.Infof("当前实例完成: 处理 %d 个任务，成功 %d 个", instanceProcessed, instanceSuccess)
		u.log.Infof("全局统计: 总处理 %s 个任务，总成功 %s 个，活跃加载器 %s 个",
			globalStats.TotalProcessed, globalStats.TotalSuccess, globalStats.ActiveLoaders)
	}

	return nil
}

// processTaskBatch 并发处理任务批次
func (u *PushNotificationUsecase) processTaskBatch(ctx context.Context, tasks []*PushTask) int {
	const maxConcurrency = 20 // 最大并发数
	taskChan := make(chan *PushTask, len(tasks))
	resultChan := make(chan bool, len(tasks))

	// 启动worker goroutines
	for i := 0; i < maxConcurrency && i < len(tasks); i++ {
		go func() {
			for task := range taskChan {
				success := u.loadSingleTaskWithDistributedLock(ctx, task)
				resultChan <- success
			}
		}()
	}

	// 发送任务到channel
	for _, task := range tasks {
		taskChan <- task
	}
	close(taskChan)

	// 收集结果
	successCount := 0
	for i := 0; i < len(tasks); i++ {
		if <-resultChan {
			successCount++
		}
	}

	return successCount
}

// loadSingleTaskWithDistributedLock 使用分布式锁加载单个任务，防止重复
func (u *PushNotificationUsecase) loadSingleTaskWithDistributedLock(ctx context.Context, task *PushTask) bool {
	lockKey := fmt.Sprintf("task_load_lock_%d", task.ID)
	taskName := fmt.Sprintf("push_task_%d", task.ID)

	// 获取分布式锁，防止多个实例重复加载
	lock, err := u.redisClient.SetNX(ctx, lockKey, "loading", 30*time.Second).Result()
	if err != nil {
		u.log.Errorf("获取任务 %d 的分布式锁失败: %v", task.ID, err)
		return false
	}

	if !lock {
		u.log.Debugf("任务 %d 正在被其他实例加载，跳过", task.ID)
		return true // 被其他实例处理也算成功
	}

	// 确保释放锁
	defer func() {
		if err := u.redisClient.Del(ctx, lockKey).Err(); err != nil {
			u.log.Errorf("释放任务 %d 的分布式锁失败: %v", task.ID, err)
		}
	}()

	// 检查调度器中是否已存在该任务
	if u.scheduler.TaskExists(taskName) {
		u.log.Debugf("任务 %s 已存在于调度器中，跳过", taskName)
		return true
	}

	// 检查任务是否已过期（超过1小时）
	if time.Now().Unix()-task.ScheduledTime > 3600 {
		u.log.Infof("任务 %d 已过期，标记为取消", task.ID)
		return u.markTaskAsExpired(ctx, task)
	}

	// 添加任务到调度器
	return u.addTaskToScheduler(ctx, task)
}

// markTaskAsExpired 标记任务为过期
func (u *PushNotificationUsecase) markTaskAsExpired(ctx context.Context, task *PushTask) bool {
	task.Status = enums.PushTaskStatusCancel // 取消状态
	task.ExecuteTime = time.Now().Unix()
	task.ErrorMsg = "任务已过期"

	if err := u.repo.UpdatePushTask(ctx, task); err != nil {
		u.log.Errorf("标记任务 %d 为过期失败: %v", task.ID, err)
		return false
	}

	return true
}

// addTaskToScheduler 添加任务到调度器
func (u *PushNotificationUsecase) addTaskToScheduler(ctx context.Context, task *PushTask) bool {
	executeTime := time.Unix(task.ScheduledTime, 0)
	delay := time.Until(executeTime)
	taskName := fmt.Sprintf("push_task_%d", task.ID)

	if delay > 0 {
		// 未来时间，添加到调度器
		config := taskpkg.ScheduleConfig{
			Name:        taskName,
			Interval:    delay,
			LockTimeout: 5 * time.Minute,
			TaskFunc: func() error {
				return u.ExecutePushTask(ctx, int64(task.ID))
			},
			Tags: []string{
				fmt.Sprintf("user_%d", task.UserID),
				fmt.Sprintf("type_%s", task.TaskType),
				fmt.Sprintf("related_%d", task.RelatedID),
				"push_task",
			},
		}

		if err := u.scheduler.AddTask(config); err != nil {
			u.log.Errorf("添加任务 %d 到调度器失败: %v", task.ID, err)
			return false
		}

		u.log.Debugf("任务 %d 已添加到调度器，将在 %v 后执行", task.ID, delay)
	} else {
		// 过去时间，立即执行
		u.log.Infof("任务 %d 为过去时间，立即执行", task.ID)
		go func(taskID int64) {
			if err := u.ExecutePushTask(ctx, taskID); err != nil {
				u.log.Errorf("立即执行任务 %d 失败: %v", taskID, err)
			}
		}(int64(task.ID))
	}

	return true
}



// ShardInfo 分片信息
type ShardInfo struct {
	Offset int // 分片起始位置
	Size   int // 分片大小
}

// initDistributedLoadState 初始化分布式加载状态
func (u *PushNotificationUsecase) initDistributedLoadState(ctx context.Context) error {
	loadStateKey := "load_state"
	initLockKey := "load_state_init_lock"

	// 使用分布式锁确保只有一个实例初始化
	acquired, err := u.redisClient.SetNX(ctx, initLockKey, "initializing", 30*time.Second).Result()
	if err != nil {
		return fmt.Errorf("获取初始化锁失败: %w", err)
	}

	if acquired {
		// 当前实例负责初始化
		defer u.redisClient.Del(ctx, initLockKey)

		// 检查是否已经初始化过
		exists, err := u.redisClient.Exists(ctx, loadStateKey).Result()
		if err != nil {
			return fmt.Errorf("检查加载状态失败: %w", err)
		}

		if exists == 0 {
			// 初始化加载状态
			err = u.redisClient.HMSet(ctx, loadStateKey,
				"current_offset", 0,
				"total_processed", 0,
				"total_success", 0,
				"active_loaders", 0,
				"start_time", time.Now().Unix(),
			).Err()
			if err != nil {
				return fmt.Errorf("初始化加载状态失败: %w", err)
			}

			// 设置过期时间
			u.redisClient.Expire(ctx, loadStateKey, time.Hour)
			u.log.Info("初始化分布式加载状态成功")
		} else {
			u.log.Info("分布式加载状态已存在")
		}
	} else {
		// 等待其他实例完成初始化
		u.log.Info("等待其他实例完成初始化...")
		time.Sleep(100 * time.Millisecond)
	}

	return nil
}

// claimNextShard 获取下一个分片
func (u *PushNotificationUsecase) claimNextShard(ctx context.Context, batchSize int) (*ShardInfo, bool, error) {
	loadStateKey := "load_state"
	shardLockKey := "shard_allocation_lock"

	// 使用分布式锁保证分片分配的原子性
	acquired, err := u.redisClient.SetNX(ctx, shardLockKey, "allocating", 10*time.Second).Result()
	if err != nil {
		return nil, false, fmt.Errorf("获取分片分配锁失败: %w", err)
	}

	if !acquired {
		// 等待其他实例完成分片分配
		time.Sleep(10 * time.Millisecond)
		return u.claimNextShard(ctx, batchSize) // 重试
	}

	// 确保释放锁
	defer u.redisClient.Del(ctx, shardLockKey)

	// 获取当前offset
	currentOffsetStr, err := u.redisClient.HGet(ctx, loadStateKey, "current_offset").Result()
	if err != nil && err != redis.Nil {
		return nil, false, fmt.Errorf("获取当前offset失败: %w", err)
	}

	currentOffset := 0
	if currentOffsetStr != "" {
		currentOffset, _ = strconv.Atoi(currentOffsetStr)
	}

	// 检查是否还有数据需要处理
	tasks, err := u.repo.GetPendingTasksPaginated(ctx, 1, currentOffset)
	if err != nil {
		return nil, false, fmt.Errorf("检查数据是否存在失败: %w", err)
	}

	if len(tasks) == 0 {
		// 没有更多数据
		return nil, false, nil
	}

	// 增加活跃加载器计数
	u.redisClient.HIncrBy(ctx, loadStateKey, "active_loaders", 1)

	// 更新offset
	newOffset := currentOffset + batchSize
	u.redisClient.HSet(ctx, loadStateKey, "current_offset", newOffset)

	return &ShardInfo{
		Offset: currentOffset,
		Size:   batchSize,
	}, true, nil
}

// updateGlobalProgress 更新全局进度
func (u *PushNotificationUsecase) updateGlobalProgress(ctx context.Context, processed, success int) {
	loadStateKey := "load_state"
	progressLockKey := "progress_update_lock"

	// 使用分布式锁保证进度更新的原子性
	acquired, err := u.redisClient.SetNX(ctx, progressLockKey, "updating", 5*time.Second).Result()
	if err != nil {
		u.log.Errorf("获取进度更新锁失败: %v", err)
		return
	}

	if !acquired {
		// 如果获取锁失败，直接返回（进度更新不是关键操作）
		u.log.Debug("其他实例正在更新进度，跳过")
		return
	}

	// 确保释放锁
	defer u.redisClient.Del(ctx, progressLockKey)

	// 更新统计数据
	pipe := u.redisClient.Pipeline()
	pipe.HIncrBy(ctx, loadStateKey, "total_processed", int64(processed))
	pipe.HIncrBy(ctx, loadStateKey, "total_success", int64(success))
	pipe.HIncrBy(ctx, loadStateKey, "active_loaders", -1)

	_, err = pipe.Exec(ctx)
	if err != nil {
		u.log.Errorf("更新全局进度失败: %v", err)
		return
	}

	// 获取最新统计数据用于日志
	stats, err := u.redisClient.HMGet(ctx, loadStateKey,
		"total_processed", "total_success", "active_loaders").Result()
	if err != nil {
		u.log.Errorf("获取统计数据失败: %v", err)
		return
	}

	totalProcessed := stats[0].(string)
	totalSuccess := stats[1].(string)
	activeLoaders := stats[2].(string)

	u.log.Infof("全局进度更新: 已处理 %s 个任务，成功 %s 个，活跃加载器 %s 个",
		totalProcessed, totalSuccess, activeLoaders)
}

// GlobalLoadStats 全局加载统计
type GlobalLoadStats struct {
	TotalProcessed string
	TotalSuccess   string
	ActiveLoaders  string
	StartTime      string
}

// getGlobalLoadStats 获取全局加载统计
func (u *PushNotificationUsecase) getGlobalLoadStats(ctx context.Context) (*GlobalLoadStats, error) {
	loadStateKey := "load_state"

	stats, err := u.redisClient.HMGet(ctx, loadStateKey,
		"total_processed", "total_success", "active_loaders", "start_time").Result()
	if err != nil {
		return nil, fmt.Errorf("获取全局统计失败: %w", err)
	}

	return &GlobalLoadStats{
		TotalProcessed: getStringValue(stats[0]),
		TotalSuccess:   getStringValue(stats[1]),
		ActiveLoaders:  getStringValue(stats[2]),
		StartTime:      getStringValue(stats[3]),
	}, nil
}

// getStringValue 安全地获取字符串值
func getStringValue(val interface{}) string {
	if val == nil {
		return "0"
	}
	if str, ok := val.(string); ok {
		return str
	}
	return "0"
}
