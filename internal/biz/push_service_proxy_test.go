package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/wlnil/life-log-be/internal/pkg/firebase"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/wlnil/life-log-be/internal/conf"
)

// 代理测试配置
// 这些测试需要真实的推送服务配置和代理环境
// 适用于使用shadowsocks等VPN工具的开发环境

// getProxyURL 获取代理URL
// 支持从环境变量读取代理配置
func getProxyURL() string {
	// 优先从环境变量读取
	if proxy := os.Getenv("PUSH_TEST_PROXY"); proxy != "" {
		return proxy
	}

	// 常见的shadowsocks代理配置
	// 用户需要根据自己的shadowsocks配置修改
	return "socks5://127.0.0.1:1086" // 默认shadowsocks本地代理
}

// 真实推送服务配置（带代理支持）
// 用户需要替换为自己的真实配置

// getRealJPushConfigWithProxy 获取带代理的真实JPush配置
func getRealJPushConfigWithProxy() *conf.Data {
	return &conf.Data{
		Jpush: &conf.Data_JPush{
			AppKey:      "", // 从环境变量读取
			AppSecret:   "", // 从环境变量读取
			ApiEndpoint: "https://api.jpush.cn/v3/push",
			Timeout:     30,
			MaxRetry:    3,
			Enabled:     true,
		},
	}
}

// NewJPushServiceWithProxy 创建带代理的JPush服务
func NewJPushServiceWithProxy(conf *conf.Data, logger log.Logger, proxyURL string) *JPushService {
	var appKey, appSecret, apiURL string
	var timeout time.Duration = 30 * time.Second
	var retries int = 3

	if conf.Jpush != nil {
		appKey = conf.Jpush.AppKey
		appSecret = conf.Jpush.AppSecret
		if conf.Jpush.ApiEndpoint != "" {
			apiURL = conf.Jpush.ApiEndpoint
		} else {
			apiURL = "https://api.jpush.cn/v3/push"
		}
		if conf.Jpush.Timeout > 0 {
			timeout = time.Duration(conf.Jpush.Timeout) * time.Second
		}
		if conf.Jpush.MaxRetry > 0 {
			retries = int(conf.Jpush.MaxRetry)
		}
	} else {
		apiURL = "https://api.jpush.cn/v3/push"
	}

	return &JPushService{
		appKey:     appKey,
		appSecret:  appSecret,
		apiURL:     apiURL,
		httpClient: NewHTTPClientWithProxy(timeout, retries, proxyURL),
		log:        log.NewHelper(logger),
		testMode:   false, // 真实模式
	}
}

// NewFCMServiceWithProxy 创建带代理的FCM服务
func NewFCMServiceWithProxy(logger log.Logger, proxyURL string) *FCMService {
	logHelper := log.NewHelper(logger)

	projectID := "lifehabit-2dd0d"
	credentialsFile := "/Users/<USER>/my_pro/life-log-be/configs/firebase-service-account.json"

	// 强制使用自定义FCM客户端（测试环境）
	var customClient *firebase.CustomFCMClient

	if credentialsFile != "" && projectID != "" {
		// 检查凭据文件是否存在
		if _, err := os.Stat(credentialsFile); err == nil {
			logHelper.Infof("🔑 使用FCM凭据文件: %s, 项目ID: %s", credentialsFile, projectID)

			// 创建自定义FCM客户端
			if client, err := firebase.NewCustomFCMClient(credentialsFile, proxyURL, logger); err != nil {
				logHelper.Errorf("❌ 自定义FCM客户端初始化失败: %v", err)
			} else {
				customClient = client
				logHelper.Infof("✅ 自定义FCM客户端初始化成功，使用代理: %s", proxyURL)
			}
		} else {
			logHelper.Warnf("⚠️  FCM凭据文件不存在: %s", credentialsFile)
		}
	} else {
		logHelper.Warnf("⚠️  FCM配置不完整: projectID=%s, credentialsFile=%s", projectID, credentialsFile)
	}

	return &FCMService{
		projectID:       projectID,
		credentialsFile: credentialsFile,
		messagingClient: nil, // 测试环境不使用Firebase Admin SDK
		customClient:    customClient,
		log:             logHelper,
		testMode:        false,
	}
}

// TestPushServiceWithProxy_RealEnvironment 代理环境下的真实推送测试
// 注意：这些测试需要真实的推送服务配置和代理环境
func TestPushServiceWithProxy_RealEnvironment(t *testing.T) {
	// 检查是否启用代理测试
	//if os.Getenv("ENABLE_PROXY_TESTS") != "true" {
	//	t.Skip("跳过代理测试：设置环境变量 ENABLE_PROXY_TESTS=true 来启用")
	//}

	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)

	proxyURL := getProxyURL()
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	t.Run("JPush代理测试", func(t *testing.T) {
		config := getRealJPushConfigWithProxy()

		// 检查配置是否完整
		if config.Jpush.AppKey == "" || config.Jpush.AppSecret == "" {
			t.Skip("跳过JPush代理测试：缺少环境变量 JPUSH_APP_KEY 或 JPUSH_APP_SECRET")
		}

		service := NewJPushServiceWithProxy(config, logger, proxyURL)

		request := &PushRequest{
			UserID:      99999,
			Platform:    "ios",
			DeviceToken: "101d8559087b358af76", // 从环境变量读取测试设备Token
			Title:       "代理测试推送",
			Content:     "这是一条通过代理发送的JPush测试消息",
			ExtraData: map[string]string{
				"test_type": "proxy_test",
				"timestamp": time.Now().Format(time.RFC3339),
			},
		}

		if request.DeviceToken == "" {
			t.Skip("跳过JPush代理测试：缺少环境变量 TEST_JPUSH_DEVICE_TOKEN")
		}

		resp, err := service.Send(ctx, request)
		assert.NoError(t, err)
		assert.NotNil(t, resp)

		// 真实环境可能会因为各种原因失败，这里主要测试代理连接
		t.Logf("JPush代理测试结果: Success=%v, MessageID=%s, Error=%s",
			resp.Success, resp.MessageID, resp.Error)
	})

	t.Run("FCM代理测试", func(t *testing.T) {
		service := NewFCMServiceWithProxy(logger, proxyURL)

		request := &PushRequest{
			UserID:      99999,
			Platform:    "ios",
			DeviceToken: "edbmyiG4lUk-q9174zSvS-:APA91bGMVQ7OT6oEx6K2phroUkQ-CxNbh_HROTnP4N1yLDyfAnr2fFJcVyGs34tMmsR0YrBXYDksbQmxtLc8STyzTXpYbI4__fg9zPm_tKwXQrfwU67J4FA",
			Title:       "代理测试推送",
			Content:     "这是一条通过代理发送的FCM测试消息",
			ExtraData: map[string]string{
				"test_type": "proxy_test",
				"timestamp": time.Now().Format(time.RFC3339),
			},
		}

		if request.DeviceToken == "" {
			t.Skip("跳过FCM代理测试：缺少环境变量 TEST_FCM_DEVICE_TOKEN")
		}

		resp, err := service.Send(ctx, request)
		assert.NoError(t, err)
		assert.NotNil(t, resp)

		// 真实环境可能会因为各种原因失败，这里主要测试代理连接
		t.Logf("FCM代理测试结果: Success=%v, MessageID=%s, Error=%s",
			resp.Success, resp.MessageID, resp.Error)
	})
}
