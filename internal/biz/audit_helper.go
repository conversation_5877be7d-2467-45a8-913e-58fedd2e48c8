package biz

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	v1 "github.com/wlnil/life-log-be/api/user/v1"
)

// AuditHelper 审计日志辅助工具
type AuditHelper struct {
	auditService AsyncAuditService
	log          *log.Helper
}

// NewAuditHelper 创建审计辅助工具
func NewAuditHelper(auditService AsyncAuditService, logger log.Logger) *AuditHelper {
	return &AuditHelper{
		auditService: auditService,
		log:          log.NewHelper(logger),
	}
}

// LogLogin 记录登录事件
func (h *AuditHelper) LogLogin(userID int32, req *v1.LoginRequest, success bool, errorCode, errorMsg string) {
	if h.auditService == nil {
		return
	}

	loginType := h.getLoginTypeString(req.LoginType)
	thirdPartyType := h.getThirdPartyTypeString(req.LoginType)

	auditLog := &AuthAuditLog{
		UserID:         userID,
		Action:         "login",
		LoginType:      loginType,
		ThirdPartyType: thirdPartyType,
		DeviceID:       "",
		Platform:       req.Platform,
		ClientIP:       "", // 服务器端自动获取
		CountryCode:    req.Locale,
		Success:        success,
		ErrorCode:      errorCode,
		ErrorMsg:       errorMsg,
	}

	h.auditService.LogAuthEvent(auditLog)
}

// 可以，但是通知这种哈，比如习惯提醒通知，都超过预定的时间，还去通知，是不是已经没用了，但是对于周总结，日总结这种，即使过了系统设置的时间，延迟一定时间也允许通知，是不是应该划分下时间范围

// LogRegister 记录注册事件
func (h *AuditHelper) LogRegister(userID int32, phone, email, platform, deviceID, clientIP string, success bool, errorCode, errorMsg string) {
	if h.auditService == nil {
		return
	}

	auditLog := &AuthAuditLog{
		UserID:    userID,
		Action:    "register",
		DeviceID:  deviceID,
		Platform:  platform,
		ClientIP:  clientIP,
		Success:   success,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	h.auditService.LogAuthEvent(auditLog)
}

// LogLogout 记录登出事件
func (h *AuditHelper) LogLogout(userID int32, platform, deviceID, clientIP string, sessionDuration int32) {
	if h.auditService == nil {
		return
	}

	auditLog := &AuthAuditLog{
		UserID:          userID,
		Action:          "logout",
		DeviceID:        deviceID,
		Platform:        platform,
		ClientIP:        clientIP,
		Success:         true,
		SessionDuration: sessionDuration,
	}

	h.auditService.LogAuthEvent(auditLog)
}

// LogTokenRefresh 记录token刷新事件
func (h *AuditHelper) LogTokenRefresh(userID int32, platform, deviceID, clientIP string, success bool, errorCode, errorMsg string) {
	if h.auditService == nil {
		return
	}

	auditLog := &AuthAuditLog{
		UserID:    userID,
		Action:    "refresh_token",
		DeviceID:  deviceID,
		Platform:  platform,
		ClientIP:  clientIP,
		Success:   success,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	h.auditService.LogAuthEvent(auditLog)
}

// LogPasswordChange 记录密码修改事件
func (h *AuditHelper) LogPasswordChange(userID int32, platform, deviceID, clientIP string, success bool, errorCode, errorMsg string) {
	if h.auditService == nil {
		return
	}

	auditLog := &AuthAuditLog{
		UserID:    userID,
		Action:    "change_password",
		DeviceID:  deviceID,
		Platform:  platform,
		ClientIP:  clientIP,
		Success:   success,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	h.auditService.LogAuthEvent(auditLog)
}

// LogSecurityEvent 记录安全事件
func (h *AuditHelper) LogSecurityEvent(userID int32, action, platform, deviceID, clientIP string, success bool, errorCode, errorMsg string) {
	if h.auditService == nil {
		return
	}

	auditLog := &AuthAuditLog{
		UserID:    userID,
		Action:    action,
		DeviceID:  deviceID,
		Platform:  platform,
		ClientIP:  clientIP,
		Success:   success,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	h.auditService.LogAuthEvent(auditLog)
}

// getLoginTypeString 获取登录类型字符串
func (h *AuditHelper) getLoginTypeString(loginType int32) string {
	switch loginType {
	case 0:
		return "password"
	case 1:
		return "verify_code"
	case 2:
		return "google"
	case 3:
		return "apple"
	case 4:
		return "firebase"
	default:
		return "unknown"
	}
}

// getThirdPartyTypeString 获取第三方类型字符串
func (h *AuditHelper) getThirdPartyTypeString(loginType int32) string {
	switch loginType {
	case 2:
		return "google"
	case 3:
		return "apple"
	case 4:
		return "firebase"
	default:
		return ""
	}
}

// BatchLogEvents 批量记录事件（用于批量操作）
func (h *AuditHelper) BatchLogEvents(events []*AuthAuditLog) {
	if h.auditService == nil {
		return
	}

	for _, event := range events {
		h.auditService.LogAuthEvent(event)
	}
}

// GetAuditStats 获取审计服务统计信息
func (h *AuditHelper) GetAuditStats() map[string]interface{} {
	if h.auditService == nil {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	stats := h.auditService.GetStats()
	stats["enabled"] = true
	return stats
}

// HealthCheck 审计服务健康检查
func (h *AuditHelper) HealthCheck() error {
	if h.auditService == nil {
		return nil // 审计服务未启用，不算错误
	}

	return h.auditService.HealthCheck()
}

// WithContext 从context中提取审计信息的辅助方法
func (h *AuditHelper) WithContext(ctx context.Context) *ContextualAuditHelper {
	return &ContextualAuditHelper{
		helper: h,
		ctx:    ctx,
	}
}

// ContextualAuditHelper 带上下文的审计辅助工具
type ContextualAuditHelper struct {
	helper *AuditHelper
	ctx    context.Context
}

// LogEvent 记录事件（自动从context提取信息）
func (ch *ContextualAuditHelper) LogEvent(userID int32, action string, success bool, errorCode, errorMsg string) {
	// 从context中提取客户端信息
	clientIP := ch.getClientIPFromContext()
	userAgent := ch.getUserAgentFromContext()
	platform := ch.getPlatformFromContext()
	deviceID := ch.getDeviceIDFromContext()

	auditLog := &AuthAuditLog{
		UserID:    userID,
		Action:    action,
		DeviceID:  deviceID,
		Platform:  platform,
		ClientIP:  clientIP,
		UserAgent: userAgent,
		Success:   success,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}

	ch.helper.auditService.LogAuthEvent(auditLog)
}

// 从context中提取信息的辅助方法
func (ch *ContextualAuditHelper) getClientIPFromContext() string {
	// 这里可以从context中提取客户端IP
	// 具体实现取决于你的HTTP中间件如何设置context
	if ip, ok := ch.ctx.Value("client_ip").(string); ok {
		return ip
	}
	return ""
}

func (ch *ContextualAuditHelper) getUserAgentFromContext() string {
	if ua, ok := ch.ctx.Value("user_agent").(string); ok {
		return ua
	}
	return ""
}

func (ch *ContextualAuditHelper) getPlatformFromContext() string {
	if platform, ok := ch.ctx.Value("platform").(string); ok {
		return platform
	}
	return ""
}

func (ch *ContextualAuditHelper) getDeviceIDFromContext() string {
	if deviceID, ok := ch.ctx.Value("device_id").(string); ok {
		return deviceID
	}
	return ""
}
