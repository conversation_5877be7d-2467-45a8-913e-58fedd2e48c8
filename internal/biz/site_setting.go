package biz

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	stdErr "errors"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/api/site/v1"
	"github.com/wlnil/life-log-be/internal/conf"
	confV1 "github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/cache"
	"github.com/wlnil/life-log-be/internal/pkg/code"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"github.com/wlnil/life-log-be/internal/pkg/middleware/localize"
	"github.com/wlnil/life-log-be/internal/pkg/sms"
	"github.com/wlnil/life-log-be/internal/pkg/store/qiniu"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
	"gorm.io/gorm"
	"time"
)

type SiteSetting struct {
	Version       string `json:"version" gorm:"column:version"`               // 版本号
	Agreement     string `json:"agreement" gorm:"column:agreement"`           // 用户协议
	PrivacyPolicy string `json:"privacy_policy" gorm:"column:privacy_policy"` // 隐私政策
}

func (s *SiteSetting) TableName() string {
	return "tb_site_setting"
}

// UserSms 用户短信验证码
type UserSms struct {
	BasicModel
	UserID      int32                     `json:"user_id" gorm:"column:user_id"`           // 用户 ID，可能为空
	Code        string                    `json:"code" gorm:"column:code"`                 // 验证码
	SceneType   enums.VerifyCodeSceneType `json:"scene_type" gorm:"column:scene_type"`     // 使用场景类型，login 登录
	SendChannel enums.SmsChannelType      `json:"send_channel" gorm:"column:send_channel"` // 发送渠道，tx 腾讯，ali 阿里云
	Target      string                    `json:"target" gorm:"column:target"`             // 发送对象，可能是手机号，可能是邮箱
	ExpiredAt   int64                     `json:"expired_at" gorm:"column:expired_at"`     // 过期时间
}

func (m *UserSms) TableName() string {
	return "tb_user_sms"
}

type MotiveMemoConfig struct {
	Content string `json:"content"` // 内容
	From    string `json:"from"`    // 来源
}

func (c MotiveMemoConfig) Value() (driver.Value, error) {
	b, err := json.Marshal(c)
	return string(b), err
}

func (c *MotiveMemoConfig) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), c)
}

type MotiveMemo struct {
	BasicModel
	UserID int32                  `json:"user_id" gorm:"column:user_id"`
	Config MotiveMemoConfig       `json:"config" gorm:"column:config"`
	Status enums.MotiveMemoStatus `json:"status" gorm:"column:status"` // 状态（1 正常，2 删除，3 封禁）
	Type   enums.MotiveMemoType   `json:"type" gorm:"column:type"`     // 类型（1 公共，2 私人）
}

func (m *MotiveMemo) TableName() string {
	return "tb_motive_memo"
}

type Feedback struct {
	BasicModel
	Content  string                   `json:"content" gorm:"column:content"` // 内容
	Email    string                   `json:"email" gorm:"column:email"`     // 邮箱地址
	Imgs     ListStringModel          `json:"imgs" gorm:"column:imgs"`       // 图片列表
	UserID   int32                    `json:"user_id" gorm:"column:user_id"`
	Status   enums.FeedBackStatusType `json:"status" gorm:"column:status"`     // 处理状态，1 待处理，2 已处理
	Nickname string                   `json:"nickname" gorm:"column:nickname"` // 昵称
}

func (f *Feedback) TableName() string {
	return "tb_feedback"
}

// AppVersion 应用版本管理
type AppVersion struct {
	BasicModel
	Platform         string `json:"platform" gorm:"column:platform"`                     // 平台：ios, android, web
	Version          string `json:"version" gorm:"column:version"`                       // 版本号，如：1.0.0
	VersionCode      int32  `json:"version_code" gorm:"column:version_code"`             // 版本代码，用于版本比较
	IsForceUpdate    bool   `json:"is_force_update" gorm:"column:is_force_update"`       // 是否强制更新
	UpdateTitle      string `json:"update_title" gorm:"column:update_title"`             // 更新标题
	UpdateContent    string `json:"update_content" gorm:"column:update_content"`         // 更新内容
	DownloadUrl      string `json:"download_url" gorm:"column:download_url"`             // 下载链接
	IosAppStoreUrl   string `json:"ios_app_store_url" gorm:"column:ios_app_store_url"`   // iOS App Store链接
	AndroidDirectUrl string `json:"android_direct_url" gorm:"column:android_direct_url"` // Android直接下载链接
	FileSize         uint32 `json:"file_size" gorm:"column:file_size"`                   // 文件大小（字节）
	IsActive         bool   `json:"is_active" gorm:"column:is_active"`                   // 是否启用
}

func (a *AppVersion) TableName() string {
	return "tb_app_version"
}

type SiteSettingRepo interface {
	Detail(context.Context, *SiteSetting) error

	CreateSmsCode(context.Context, *UserSms) error
	ListMotiveMemo(ctx context.Context, params map[string]interface{}) ([]*MotiveMemo, error)

	CreateFeedBack(context.Context, *Feedback) error

	GetLatestAppVersion(ctx context.Context, platform string) (*AppVersion, error)
}

type SiteSettingUsecase struct {
	repo SiteSettingRepo
	log  *log.Helper

	confData *conf.Data
}

func (u *SiteSettingUsecase) GetSiteInfo(ctx context.Context, request *v1.GetSiteInfoRequest) (*v1.GetSiteInfoReply, error) {
	res := &v1.GetSiteInfoReply{}

	siteSetting := &SiteSetting{}
	if err := u.repo.Detail(ctx, siteSetting); err != nil && !stdErr.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	res.Data = &v1.GetSiteInfoReply_Data{
		Version:       siteSetting.Version,
		Agreement:     siteSetting.Agreement,
		PrivacyPolicy: siteSetting.PrivacyPolicy,
	}

	return res, nil
}

func (u *SiteSettingUsecase) CreateUpToken(ctx context.Context, request *v1.CreateUpTokenRequest) (*v1.CreateUpTokenReply, error) {
	res := &v1.CreateUpTokenReply{}

	token := qiniu.Client.CreateUptoken(enums.StorageVisibleType(request.Scene))
	res.Data = &v1.CreateUpTokenReply_Data{
		Token: token,
	}

	return res, nil
}

func (u *SiteSettingUsecase) CreateDownloadURL(ctx context.Context, request *v1.CreateDownURLRequest) (*v1.CreateDownURLReply, error) {
	res := &v1.CreateDownURLReply{}

	url := qiniu.Client.CreateDnURL(request.Key, enums.FileSceneType(request.Scene), 60)
	res.Data = &v1.CreateDownURLReply_Data{
		Url: url,
	}

	return res, nil
}

func (u *SiteSettingUsecase) SendVerifyCode(ctx context.Context, request *v1.SendVerifyCodeRequest) (*v1.SendVerifyCodeReply, error) {
	res := &v1.SendVerifyCodeReply{}
	var err error

	switch request.Scene {
	case string(enums.VerifyCodeSceneTypeLogin):
		err = u.sendLoginCode(ctx, request.Phone, request.Email)
	case string(enums.VerifyCodeSceneTypeChangePhone):
		redisPrefix := enums.RedisPhoneChangePrefix
		if request.Email != "" {
			redisPrefix = enums.RedisEmailChangePrefix
		}
		err = u.sendVerifyCode(ctx, request.Phone, request.Email, enums.VerifyCodeSceneTypeChangePhone, redisPrefix)
	case string(enums.VerifyCodeSceneTypeForgetPassword):
		redisPrefix := enums.RedisPhoneForgetPasswordPrefix
		if request.Email != "" {
			redisPrefix = enums.RedisEmailForgetPasswordPrefix
		}
		err = u.sendVerifyCode(ctx, request.Phone, request.Email, enums.VerifyCodeSceneTypeForgetPassword, redisPrefix)
	default:
		err = stdErr.New("scene not support")
	}

	return res, err
}

// sendLoginCode 发送登录验证码
// TODO: 限制用户每天发送验证码数量
func (u *SiteSettingUsecase) sendLoginCode(ctx context.Context, phone, email string) error {
	// 判断当前是否可发送短信
	target := phone
	loginType := enums.UserLoginTypePhone
	redisKey := enums.RedisPhoneLoginPrefix + phone
	if email != "" {
		redisKey = enums.RedisEmailLoginPrefix + email
		loginType = enums.UserLoginTypeEmail
		target = email
	}
	verifyCode := tool.GenerateVerifyCode()
	// 测试环境默认为 123456
	if u.confData.Env == confV1.EnvType_Test {
		verifyCode = "123456"
	}
	expiredAt := enums.SMS_EXPIRES_DEFAULT
	if u.confData.SmsCodeExpires > 0 {
		expiredAt = int(u.confData.SmsCodeExpires)
	}
	// 创建一个频率限制的 key，用于检查上次发送时间
	limitKey := redisKey + ":limit"

	// 检查是否存在频率限制
	exists, err := cache.RedisClient.Exists(ctx, limitKey).Result()
	if err != nil {
		return err
	}

	// 如果频率限制存在（即 1 分钟内已发送过验证码）
	if exists == 1 {
		return errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeLimitedMsg, nil))
	}

	// 设置验证码，允许覆盖之前的值
	err = cache.RedisClient.Set(ctx, redisKey, verifyCode, time.Duration(expiredAt)*time.Second).Err()
	if err != nil {
		return err
	}

	// 设置频率限制，1 分钟内不能再次请求
	err = cache.RedisClient.Set(ctx, limitKey, 1, time.Minute).Err()
	if err != nil {
		// 如果设置限制失败，尝试回滚验证码设置
		cache.RedisClient.Del(ctx, redisKey)
		return err
	}
	defer func() {
		if err != nil {
			if err = cache.RedisClient.Del(ctx, redisKey).Err(); err != nil {
				u.log.Errorf("redis del err: %v", err)
			}
		}
	}()

	// 发送验证码
	if u.confData.Env != confV1.EnvType_Test {
		if loginType == enums.UserLoginTypeEmail {
			if err = sms.Client.SendEmail(email, verifyCode, expiredAt); err != nil {
				return err
			}
		} else {
			if err = sms.Client.Send(phone, verifyCode, expiredAt); err != nil {
				return err
			}
		}
	}

	// 落库
	now := time.Now()
	userSms := &UserSms{
		Target:      target,
		Code:        verifyCode,
		SceneType:   enums.VerifyCodeSceneTypeLogin,
		SendChannel: sms.Client.ChannelType,
		ExpiredAt:   now.Unix() + int64(expiredAt),
	}
	userSms.CreatedAt = now.Unix()
	userSms.UpdatedAt = now.Unix()
	if err = u.repo.CreateSmsCode(ctx, userSms); err != nil {
		return err
	}

	return nil
}

// sendVerifyCode 发送验证码
func (u *SiteSettingUsecase) sendVerifyCode(ctx context.Context, phone, email string, sceneType enums.VerifyCodeSceneType, redisPrefix string) error {
	// 判断当前是否可发送短信
	target := phone
	targetType := enums.UserLoginTypePhone
	redisKey := redisPrefix + phone
	if email != "" {
		redisKey = redisPrefix + email
		target = email
		targetType = enums.UserLoginTypeEmail
	}
	verifyCode := tool.GenerateVerifyCode()
	// 测试环境默认为 123456
	if u.confData.Env == confV1.EnvType_Test {
		verifyCode = "123456"
	}
	expiredAt := enums.SMS_EXPIRES_DEFAULT
	if u.confData.SmsCodeExpires > 0 {
		expiredAt = int(u.confData.SmsCodeExpires)
	}
	// 创建一个频率限制的 key，用于检查上次发送时间
	limitKey := redisKey + ":limit"

	// 检查是否存在频率限制
	exists, err := cache.RedisClient.Exists(ctx, limitKey).Result()
	if err != nil {
		return err
	}

	// 如果频率限制存在（即 1 分钟内已发送过验证码）
	if exists == 1 {
		return errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeLimitedMsg, nil))
	}

	// 设置验证码，允许覆盖之前的值
	err = cache.RedisClient.Set(ctx, redisKey, verifyCode, time.Duration(expiredAt)*time.Second).Err()
	if err != nil {
		return err
	}

	// 设置频率限制，1 分钟内不能再次请求
	err = cache.RedisClient.Set(ctx, limitKey, 1, time.Minute).Err()
	if err != nil {
		// 如果设置限制失败，尝试回滚验证码设置
		cache.RedisClient.Del(ctx, redisKey)
		return err
	}
	defer func() {
		if err != nil {
			if err = cache.RedisClient.Del(ctx, redisKey).Err(); err != nil {
				u.log.Errorf("redis del err: %v", err)
			}
		}
	}()

	// 发送验证码
	// 测试环境默认为 123456
	if u.confData.Env != confV1.EnvType_Test {
		if targetType == enums.UserLoginTypeEmail {
			if err = sms.Client.SendEmail(email, verifyCode, expiredAt); err != nil {
				return err
			}
		} else {
			if err = sms.Client.Send(phone, verifyCode, expiredAt); err != nil {
				return err
			}
		}
	}

	// 落库
	now := time.Now()
	userSms := &UserSms{
		Target:      target,
		Code:        verifyCode,
		SceneType:   sceneType,
		SendChannel: sms.Client.ChannelType,
		ExpiredAt:   now.Unix() + int64(expiredAt),
	}
	userSms.CreatedAt = now.Unix()
	userSms.UpdatedAt = now.Unix()
	if err = u.repo.CreateSmsCode(ctx, userSms); err != nil {
		return err
	}

	return nil
}

func (u *SiteSettingUsecase) ListMotiveMemo(ctx context.Context, request *v1.ListMotiveMemoRequest) (*v1.ListMotiveMemoReply, error) {
	res := &v1.ListMotiveMemoReply{
		Data: &v1.ListMotiveMemoReply_Data{},
	}

	// 每个用户每次随机 6 条，缓存 4 个小时
	userID := auth.GetUserIDFromCtxByNoAuth(ctx)
	if userID > 0 {
	}

	params := make(map[string]interface{})
	motiveMemos, err := u.repo.ListMotiveMemo(ctx, params)
	if err != nil {
		return nil, err
	}

	for _, item := range motiveMemos {
		if item.Config.Content == "" {
			continue
		}
		res.Data.Motives = append(res.Data.Motives, &v1.ListMotiveMemoReply_Motive{
			Id:      item.ID,
			Content: item.Config.Content,
			From:    item.Config.From,
		})
	}

	return res, nil
}

func (u *SiteSettingUsecase) CreateFeedback(ctx context.Context, request *v1.CreateFeedbackRequest) (*v1.CreateFeedbackReply, error) {
	res := &v1.CreateFeedbackReply{}

	userID := auth.GetUserIDFromCtxByNoAuth(ctx)
	imgs := make([]string, 0)
	if len(request.Imgs) > 0 {
		imgs = request.Imgs
	}
	feedback := &Feedback{
		Content:  request.Content,
		Email:    request.Email,
		UserID:   userID,
		Status:   enums.FeedBackStatusTypeUndo,
		Imgs:     imgs,
		Nickname: request.Nickname,
	}
	feedback.CreatedAt = time.Now().Unix()
	feedback.UpdatedAt = time.Now().Unix()
	if err := u.repo.CreateFeedBack(ctx, feedback); err != nil {
		return nil, err
	}

	return res, nil
}

// VersionCheck 版本检查
func (u *SiteSettingUsecase) VersionCheck(ctx context.Context, request *v1.VersionCheckRequest) (*v1.VersionCheckReply, error) {
	res := &v1.VersionCheckReply{
		Data: &v1.VersionCheckReply_Data{},
	}

	// 获取最新版本信息
	latestVersion, err := u.repo.GetLatestAppVersion(ctx, request.Platform)
	if err != nil {
		if stdErr.Is(err, gorm.ErrRecordNotFound) {
			// 没有找到版本信息，返回无更新
			res.Data.HasUpdate = false
			res.Data.CurrentVersion = request.CurrentVersion
			return res, nil
		}
		return nil, err
	}

	// 解析当前版本和最新版本
	currentVersionCode := u.parseVersionToCode(request.CurrentVersion)
	latestVersionCode := latestVersion.VersionCode

	// 判断是否需要更新
	hasUpdate := latestVersionCode > currentVersionCode

	res.Data.HasUpdate = hasUpdate
	res.Data.ForceUpdate = hasUpdate && latestVersion.IsForceUpdate
	res.Data.LatestVersion = latestVersion.Version
	res.Data.CurrentVersion = request.CurrentVersion
	res.Data.UpdateTitle = latestVersion.UpdateTitle
	res.Data.UpdateContent = latestVersion.UpdateContent
	res.Data.DownloadUrl = latestVersion.DownloadUrl
	res.Data.IosAppStoreUrl = latestVersion.IosAppStoreUrl
	res.Data.AndroidDirectUrl = latestVersion.AndroidDirectUrl
	res.Data.FileSize = latestVersion.FileSize

	return res, nil
}

// parseVersionToCode 将版本号转换为版本代码用于比较
// 例如：1.2.3 -> 10203
func (u *SiteSettingUsecase) parseVersionToCode(version string) int32 {
	if version == "" {
		return 0
	}

	// 简单的版本号解析，支持 x.y.z 格式
	parts := tool.SplitString(version, ".")
	if len(parts) == 0 {
		return 0
	}

	var versionCode int32 = 0
	multiplier := int32(10000) // 主版本号权重

	for i, part := range parts {
		if i >= 3 { // 最多支持三位版本号
			break
		}

		num := tool.StringToInt32(part)
		if num < 0 {
			num = 0
		}
		if num > 99 { // 每位最大99
			num = 99
		}

		versionCode += num * multiplier
		multiplier /= 100
	}

	return versionCode
}

func NewSiteSettingUsecase(repo SiteSettingRepo, logger log.Logger, confData *conf.Data) *SiteSettingUsecase {
	return &SiteSettingUsecase{
		repo:     repo,
		log:      log.NewHelper(logger),
		confData: confData,
	}
}
