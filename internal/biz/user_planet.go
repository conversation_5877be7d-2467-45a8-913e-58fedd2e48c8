package biz

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cast"
	v1 "github.com/wlnil/life-log-be/api/user_planet/v1"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
	"gorm.io/gorm"
	"time"
)

type PlanetPostImgUrls []string

func (c PlanetPostImgUrls) Value() (driver.Value, error) {
	b, err := json.Marshal(c)
	return string(b), err
}

func (c *PlanetPostImgUrls) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), c)
}

type PlanetPost struct {
	BasicModel
	PlanetID       int32                  `json:"planet_id" gorm:"column:planet_id"`               // 星球 ID
	UserID         int32                  `json:"user_id" gorm:"column:user_id"`                   // 用户 ID
	PlanetTargetID int32                  `json:"planet_target_id" gorm:"column:planet_target_id"` // 星球目标 ID
	Content        string                 `json:"content" gorm:"column:content"`                   // 内容
	Status         enums.PlanetPostStatus `json:"status" gorm:"column:status"`                     // 状态
	ImgUrls        PlanetPostImgUrls      `json:"img_urls" gorm:"column:img_urls;type:json"`       // 图片地址列表
	HabitSnapID    int32                  `json:"habit_snap_id" gorm:"column:habit_snap_id"`       // 用户习惯快照 ID
	IsPublic       bool                   `json:"is_public" gorm:"is_public"`                      // 是否公开
	LikeCount      int32                  `json:"like_count" gorm:"column:like_count"`             // 点赞数量
	FavoriteCount  int32                  `json:"favorite_count" gorm:"favorite_count"`            // 收藏数量
	CommentCount   int32                  `json:"comment_count" gorm:"comment_count"`              // 评论数量
	SmallStageID   int32                  `json:"small_stage_id" gorm:"column:small_stage_id"`     // 微习惯阶段 ID
	IsTop          bool                   `json:"is_top" gorm:"column:is_top"`                     // 是否置顶
}

func (m *PlanetPost) TableName() string {
	return "tb_planet_post"
}

type LikeUsers []int32

func (c LikeUsers) Value() (driver.Value, error) {
	b, err := json.Marshal(c)
	return string(b), err
}

func (c *LikeUsers) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), c)
}

type UserLike struct {
	BasicModel
	ThirdID   string             `json:"third_id" gorm:"column:third_id"`           // 外部 ID
	ThirdType enums.UserLikeType `json:"third_type" gorm:"column:third_type"`       // 外部类型
	UserIDs   LikeUsers          `json:"user_ids" gorm:"column:user_ids;type:json"` // 用户 ID 列表
}

func (m *UserLike) TableName() string {
	return "tb_user_like"
}

type PlanetMember struct {
	BasicModel
	PlanetID int32                    `json:"planet_id" gorm:"column:planet_id"` // 星球 ID
	UserID   int32                    `json:"user_id" gorm:"column:user_id"`     // 成员用户 ID
	Status   enums.PlanetMemberStatus `json:"status" gorm:"column:status"`       // 状态
	QuitedAt int64                    `json:"quited_at" gorm:"column:quited_at"` // 退出时间
	JoinedAt int64                    `json:"joined_at" gorm:"column:joined_at"` // 加入时间
	Remark   string                   `json:"remark" gorm:"column:remark"`       // 备注，例如：封禁原因
}

func (m *PlanetMember) TableName() string {
	return "tb_planet_member"
}

type PostComment struct {
	BasicModel
	PostID       int32                   `json:"post_id" gorm:"column:post_id"`        // 星球帖子 ID
	RootID       int32                   `json:"root_id" gorm:"column:root_id"`        // 根评论 ID
	ParentID     int32                   `json:"parent_id" gorm:"column:parent_id"`    // 父级评论 ID
	ParentUserID int32                   `json:"parent_user_id" gorm:"parent_user_id"` // 父级评论的用户 ID
	UserID       int32                   `json:"user_id" gorm:"column:user_id"`        // 用户 ID
	Content      string                  `json:"content" gorm:"column:content"`        // 评论内容
	Status       enums.PostCommentStatus `json:"status" gorm:"column:status"`          // 状态
	Remark       string                  `json:"remark" gorm:"column:remark"`
}

func (m *PostComment) TableName() string {
	return "tb_post_comment"
}

type PostLike struct {
	BasicModel
	PostID  int32 `json:"post_id" gorm:"column:post_id"`   // 星球帖子 ID
	UserID  int32 `json:"user_id" gorm:"column:user_id"`   // 用户 ID
	IsValid bool  `json:"is_valid" gorm:"column:is_valid"` // 是否有效
}

func (m *PostLike) TableName() string {
	return "tb_post_like"
}

type TargetMember struct {
	BasicModel
	PlantTargetID int32                    `json:"plant_target_id" gorm:"column:plant_target_id"` // 星球目标 ID
	UserID        int32                    `json:"user_id" gorm:"column:user_id"`                 // 用户 ID
	JoinedAt      int64                    `json:"joined_at" gorm:"column:joined_at"`             // 加入时间
	QuitedAt      int64                    `json:"quited_at" gorm:"column:quited_at"`             // 退出时间
	Status        enums.TargetMemberStatus `json:"status" gorm:"column:status"`                   // 状态
}

func (m *TargetMember) TableName() string {
	return "tb_target_member"
}

type UserFavorite struct {
	BasicModel
	UserID    int32                    `json:"user_id" gorm:"column:user_id"`       // 用户 id
	ThirdID   int32                    `json:"third_id" gorm:"column:third_id"`     // 外部 id，例如：动态 id
	ThirdType enums.UserFavoriteType   `json:"third_type" gorm:"column:third_type"` // 外部类型，planet_post 星球动态
	Status    enums.UserFavoriteStatus `json:"status" gorm:"column:status"`         // 状态
}

func (m *UserFavorite) TableName() string {
	return "tb_user_favorite"
}

type UserPlanetRepo interface {
	CreatePlanetPost(ctx context.Context, planetPost *PlanetPost) error
	ListPlanetByUserID(ctx context.Context, userID int32) ([]*Planet, error)
	UserJoinPlanet(ctx context.Context, planetMember *PlanetMember) error
	UserQuitPlanet(ctx context.Context, planetMember *PlanetMember) error
	PaginatePlanetPostByParams(ctx context.Context, planetID int32, page, pageSize int, labelType enums.PlanetPostLabelType, userID int32) ([]*PlanetPost, int64, error)
	FirstPlanetPostByID(ctx context.Context, planetPost *PlanetPost) error
	UpdatePlanetPost(ctx context.Context, planetPost *PlanetPost) error
	UpdatePlanetPostByParams(ctx context.Context, updateParams map[string]interface{}, filterParams map[string]interface{}) error
	ListPlanetPostByParams(ctx context.Context, params map[string]interface{}) ([]*PlanetPost, error)
	DeletePlantPost(ctx context.Context, planetPost *PlanetPost) error

	FirstUserFavoriteByParams(ctx context.Context, favorite *UserFavorite, params map[string]interface{}) error
	UpdateUserFavorite(ctx context.Context, favorite *UserFavorite) error
	CreateUserFavorite(ctx context.Context, favorite *UserFavorite) error

	UpdatePostComment(ctx context.Context, comment *PostComment) error
	CreatePostComment(ctx context.Context, comment *PostComment) error
	PaginatePostCommentByParams(ctx context.Context, params map[string]interface{}, page, pageSize int) ([]*PostComment, int64, error)
	FirstPostCommentByID(ctx context.Context, comment *PostComment) error
	UpdatePlanetPostCommentByParams(ctx context.Context, update map[string]interface{}, filterParams map[string]interface{}) error

	FirstUserLikeByParams(ctx context.Context, like *UserLike) error
	UpdateUserLike(ctx context.Context, like *UserLike) error
	CreateUserLike(ctx context.Context, like *UserLike) error
}

type UserPlanetUsecase struct {
	commonUseCase *CommonUsecase

	repo       UserPlanetRepo
	planetRepo PlanetRepo
	userRepo   UserRepo

	log      *log.Helper
	confData *conf.Data
}

func (u *UserPlanetUsecase) ListPlanetByUserID(ctx context.Context, request *v1.ListPlanetByUserIDRequest) (*v1.ListPlanetByUserIDReply, error) {
	res := &v1.ListPlanetByUserIDReply{
		Data: make([]*v1.ListPlanetByUserIDReply_Data, 0),
	}

	userID := auth.GetUserIDFromCtx(ctx)
	planets, err := u.repo.ListPlanetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	for _, item := range planets {
		res.Data = append(res.Data, &v1.ListPlanetByUserIDReply_Data{
			Id:     item.ID,
			Name:   item.Name,
			ImgUrl: item.ImgUrl,
		})
	}

	return res, nil
}

func (u *UserPlanetUsecase) JoinPlanet(ctx context.Context, request *v1.JoinPlanetRequest) (*v1.JoinPlanetReply, error) {
	res := &v1.JoinPlanetReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	planet := &Planet{}
	planet.ID = request.PlanetId
	if err := u.planetRepo.FirstPlanetByID(ctx, planet); err != nil {
		return nil, err
	}

	now := time.Now()
	planetMember := &PlanetMember{
		PlanetID: planet.ID,
		UserID:   userID,
		Status:   enums.PlanetMemberStatusNormal,
		JoinedAt: now.Unix(),
	}
	planetMember.UpdatedAt = now.Unix()
	planetMember.CreatedAt = now.Unix()
	if err := u.repo.UserJoinPlanet(ctx, planetMember); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) QuitPlanet(ctx context.Context, request *v1.QuitPlanetRequest) (*v1.QuitPlanetReply, error) {
	res := &v1.QuitPlanetReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	now := time.Now()
	planetMember := &PlanetMember{
		PlanetID: request.PlanetId,
		UserID:   userID,
		Status:   enums.PlanetMemberStatusQuite,
		QuitedAt: now.Unix(),
	}
	planetMember.UpdatedAt = now.Unix()
	if err := u.repo.UserQuitPlanet(ctx, planetMember); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) JoinPlanetTarget(ctx context.Context, request *v1.JoinPlanetTargetRequest) (*v1.JoinPlanetTargetReply, error) {
	res := &v1.JoinPlanetTargetReply{}

	return res, nil
}

func (u *UserPlanetUsecase) QuitPlanetTarget(ctx context.Context, request *v1.QuitPlanetTargetRequest) (*v1.QuitPlanetTargetReply, error) {
	res := &v1.QuitPlanetTargetReply{}

	return res, nil
}

func (u *UserPlanetUsecase) LikePlanetPost(ctx context.Context, request *v1.LikePlanetPostRequest) (*v1.LikePlanetPostReply, error) {
	res := &v1.LikePlanetPostReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	planetPost := &PlanetPost{}
	planetPost.ID = request.PostId
	if err := u.repo.FirstPlanetPostByID(ctx, planetPost); err != nil {
		return res, err
	}

	userLike := &UserLike{
		ThirdID:   fmt.Sprintf("%v", planetPost.ID),
		ThirdType: enums.UserLikeTypePlanetPost,
	}
	if err := u.repo.FirstUserLikeByParams(ctx, userLike); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return res, err
	}
	now := time.Now()
	if userLike.ID > 0 {
		// 判断用户是否已经点赞
		if tool.Contains(userLike.UserIDs, userID) {
			return res, nil
		}
		userLike.UserIDs = append(userLike.UserIDs, userID)
		userLike.UpdatedAt = now.Unix()
		if err := u.repo.UpdateUserLike(ctx, userLike); err != nil {
			return res, err
		}
	} else {
		userLike.UserIDs = []int32{userID}
		userLike.CreatedAt = now.Unix()
		userLike.UpdatedAt = now.Unix()
		if err := u.repo.CreateUserLike(ctx, userLike); err != nil {
			return res, err
		}
	}
	planetPost.LikeCount += 1
	if err := u.repo.UpdatePlanetPost(ctx, planetPost); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) CancelLikePlanetPost(ctx context.Context, request *v1.CancelLikePlanetPostRequest) (*v1.CancelLikePlanetPostReply, error) {
	res := &v1.CancelLikePlanetPostReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	planetPost := &PlanetPost{}
	planetPost.ID = request.PostId
	if err := u.repo.FirstPlanetPostByID(ctx, planetPost); err != nil {
		return res, err
	}

	userLike := &UserLike{
		ThirdID:   fmt.Sprintf("%v", planetPost.ID),
		ThirdType: enums.UserLikeTypePlanetPost,
	}
	if err := u.repo.FirstUserLikeByParams(ctx, userLike); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return res, err
	}

	if userLike.ID > 0 {
		oldLikeCount := len(userLike.UserIDs)
		userLike.UserIDs = tool.RemoveElement(userLike.UserIDs, userID)
		if len(userLike.UserIDs) == oldLikeCount {
			return res, nil
		}
		userLike.UpdatedAt = time.Now().Unix()
		if err := u.repo.UpdateUserLike(ctx, userLike); err != nil {
			return res, err
		}
		if planetPost.LikeCount > 0 {
			planetPost.LikeCount -= 1
			if err := u.repo.UpdatePlanetPost(ctx, planetPost); err != nil {
				return res, err
			}
		}
	}

	return res, nil
}

func (u *UserPlanetUsecase) FavoritePlanetPost(ctx context.Context, request *v1.FavoritePlanetPostRequest) (*v1.FavoritePlanetPostReply, error) {
	res := &v1.FavoritePlanetPostReply{}
	now := time.Now()

	userID := auth.GetUserIDFromCtx(ctx)
	planetPost := &PlanetPost{}
	planetPost.ID = request.PostId
	if err := u.repo.FirstPlanetPostByID(ctx, planetPost); err != nil {
		return res, err
	}

	// 判断用户是否已经收藏
	userFavorite := &UserFavorite{}
	params := map[string]interface{}{
		"user_id":  userID,
		"third_id": planetPost.ID,
	}
	if err := u.repo.FirstUserFavoriteByParams(ctx, userFavorite, params); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if userFavorite.ID > 0 {
		if userFavorite.Status == enums.UserFavoriteStatusDelete {
			userFavorite.Status = enums.UserFavoriteStatusNormal
			userFavorite.UpdatedAt = now.Unix()
			if err := u.repo.UpdateUserFavorite(ctx, userFavorite); err != nil {
				return res, err
			}
		}
	} else {
		userFavorite.UserID = userID
		userFavorite.ThirdID = planetPost.ID
		userFavorite.ThirdType = enums.UserFavoriteTypePlanetPost
		userFavorite.Status = enums.UserFavoriteStatusNormal
		userFavorite.UpdatedAt = now.Unix()
		userFavorite.CreatedAt = now.Unix()
		if err := u.repo.CreateUserFavorite(ctx, userFavorite); err != nil {
			return res, err
		}
	}
	planetPost.FavoriteCount = planetPost.FavoriteCount + 1
	if err := u.repo.UpdatePlanetPost(ctx, planetPost); err != nil {
		return res, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) CancelFavoritePlanetPost(ctx context.Context, request *v1.CancelFavoritePlanetPostRequest) (*v1.CancelFavoritePlanetPostReply, error) {
	res := &v1.CancelFavoritePlanetPostReply{}
	now := time.Now()

	userID := auth.GetUserIDFromCtx(ctx)
	planetPost := &PlanetPost{}
	planetPost.ID = request.PostId
	if err := u.repo.FirstPlanetPostByID(ctx, planetPost); err != nil {
		return res, err
	}

	// 判断用户是否已经收藏
	userFavorite := &UserFavorite{}
	params := map[string]interface{}{
		"user_id":  userID,
		"third_id": planetPost.ID,
	}
	if err := u.repo.FirstUserFavoriteByParams(ctx, userFavorite, params); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if userFavorite.ID > 0 {
		if userFavorite.Status == enums.UserFavoriteStatusNormal {
			userFavorite.Status = enums.UserFavoriteStatusDelete
			userFavorite.UpdatedAt = now.Unix()
			if err := u.repo.UpdateUserFavorite(ctx, userFavorite); err != nil {
				return res, err
			}
		}
		if planetPost.FavoriteCount > 0 {
			planetPost.FavoriteCount = planetPost.FavoriteCount - 1
			if err := u.repo.UpdatePlanetPost(ctx, planetPost); err != nil {
				return res, err
			}
		}
	}

	return res, nil
}

func (u *UserPlanetUsecase) CreatePlanetPost(ctx context.Context, request *v1.CreatePlanetPostRequest) (*v1.CreatePlanetPostReply, error) {
	res := &v1.CreatePlanetPostReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	planet := &Planet{}
	planet.ID = request.PlanetId
	if err := u.planetRepo.FirstPlanetByID(ctx, planet); err != nil {
		return nil, err
	}

	now := time.Now()
	planetPost := &PlanetPost{
		UserID:   userID,
		PlanetID: planet.ID,
		Content:  request.Content,
		ImgUrls:  make([]string, 0),
		Status:   enums.PlanetPostStatusNormal,
		IsPublic: true,
	}
	planetPost.UpdatedAt = now.Unix()
	planetPost.CreatedAt = now.Unix()
	if err := u.repo.CreatePlanetPost(ctx, planetPost); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) ListPlanetPost(ctx context.Context, request *v1.ListPlanetPostRequest) (*v1.ListPlanetPostReply, error) {
	res := &v1.ListPlanetPostReply{
		Data: &v1.ListPlanetPostReply_Data{
			Page:     request.Page,
			PageSize: request.PageSize,
			Items:    make([]*v1.ListPlanetPostReply_Item, 0),
		},
	}

	// 通过配置文件控制动态是否展示（防止公开动态内容不合规，不可控）
	if !u.confData.IsDisplayPlanet {
		return res, nil
	}

	userID := auth.GetUserIDFromCtxByNoAuth(ctx)

	planetPosts, count, err := u.repo.PaginatePlanetPostByParams(ctx, request.PlanetId, int(request.Page), int(request.PageSize), enums.PlanetPostLabelType(request.LabelType), userID)
	if err != nil {
		return nil, err
	}
	res.Data.Total = int32(count)

	if len(planetPosts) == 0 {
		return res, nil
	}

	planetPostIDs := make([]int32, 0, len(planetPosts))
	for _, item := range planetPosts {
		planetPostIDs = append(planetPostIDs, item.ID)
	}
	favoritePostIDs := make([]int32, 0)
	likePostIDs := make([]string, 0)
	if userID > 0 {
		favoritePostIDs, err = u.userRepo.ListUserFavoritePostIDByPostID(ctx, userID, planetPostIDs)
		if err != nil {
			u.log.Errorf("获取【用户收藏帖子】列表失败，error: %v", err)
		}
		likePostIDs, err = u.userRepo.ListUserLikePostIDByPostID(ctx, userID, planetPostIDs)
		if err != nil {
			u.log.Errorf("获取【用户点赞帖子】列表失败，error: %v", err)
		}
	}

	for _, item := range planetPosts {
		user, err := u.commonUseCase.GetUserInfoByUserID(ctx, item.UserID)
		if err != nil {
			continue
		}
		isLike := false
		isFavorite := false
		if userID > 0 {
			isLike = tool.Contains(likePostIDs, cast.ToString(item.ID))
			isFavorite = tool.Contains(favoritePostIDs, item.ID)
		}
		res.Data.Items = append(res.Data.Items, &v1.ListPlanetPostReply_Item{
			Id:            item.ID,
			PlanetId:      item.PlanetID,
			Content:       item.Content,
			LikeCount:     item.LikeCount,
			FavoriteCount: item.FavoriteCount,
			CommentCount:  item.CommentCount,
			IsLike:        isLike,
			IsFavorite:    isFavorite,
			User: &v1.UserInfo{
				Id:        user.ID,
				Nickname:  user.Name,
				AvatarUrl: user.GetAvatarUrl(),
			},
			CreatedAt: int32(item.CreatedAt),
		})
	}

	return res, nil
}

func (u *UserPlanetUsecase) ListPlanetTopPost(ctx context.Context, request *v1.ListPlanetTopPostRequest) (*v1.ListPlanetTopPostReply, error) {
	res := &v1.ListPlanetTopPostReply{
		Data: &v1.ListPlanetTopPostReply_Data{
			Page:     request.Page,
			PageSize: request.PageSize,
			Items:    make([]*v1.ListPlanetTopPostReply_Item, 0),
		},
	}

	// 通过配置文件控制动态是否展示（防止公开动态内容不合规，不可控）
	if !u.confData.IsDisplayPlanet {
		return res, nil
	}

	// 从 redis 中获取热点动态
	// 获取不到，则从返回空，并加锁开启 goroutine 写入数据

	userID := auth.GetUserIDFromCtxByNoAuth(ctx)

	planetPosts, count, err := u.repo.PaginatePlanetPostByParams(ctx, request.PlanetId, int(request.Page), int(request.PageSize), enums.PlanetPostLabelTypeTop, userID)
	if err != nil {
		return nil, err
	}
	res.Data.Total = int32(count)

	if len(planetPosts) == 0 {
		return res, nil
	}

	for _, item := range planetPosts {
		res.Data.Items = append(res.Data.Items, &v1.ListPlanetTopPostReply_Item{
			Id:       item.ID,
			PlanetId: item.PlanetID,
			Content:  item.Content,
		})
	}

	return res, nil
}

func (u *UserPlanetUsecase) CreatePlanetPostComment(ctx context.Context, request *v1.CreatePlanetPostCommentRequest) (*v1.CreatePlanetPostCommentReply, error) {
	res := &v1.CreatePlanetPostCommentReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	now := time.Now()
	// TODO: 检查是否有权限评论

	planetPost := &PlanetPost{}
	planetPost.ID = request.PostId
	if err := u.repo.FirstPlanetPostByID(ctx, planetPost); err != nil {
		return res, err
	}

	parentPostComment := &PostComment{}
	parentPostComment.ID = request.CommentId
	if err := u.repo.FirstPostCommentByID(ctx, parentPostComment); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return res, err
	}
	var rootID int32
	var parentID int32
	var parentUserID int32
	if parentPostComment.RootID > 0 {
		rootID = parentPostComment.RootID
		parentID = parentPostComment.ID
		parentUserID = parentPostComment.UserID
	} else {
		rootID = parentPostComment.ID
	}

	postComment := &PostComment{
		PostID:       request.PostId,
		RootID:       rootID,
		ParentID:     parentID,
		ParentUserID: parentUserID,
		UserID:       userID,
		Content:      request.Content,
		Status:       enums.PostCommentStatusNormal,
		Remark:       "",
	}
	postComment.CreatedAt = now.Unix()
	postComment.UpdatedAt = now.Unix()
	if err := u.repo.CreatePostComment(ctx, postComment); err != nil {
		return res, err
	}
	planetPost.CommentCount += 1
	if err := u.repo.UpdatePlanetPost(ctx, planetPost); err != nil {
		return res, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) ListPlanetPostComment(ctx context.Context, request *v1.ListPlanetPostCommentRequest) (*v1.ListPlanetPostCommentReply, error) {
	res := &v1.ListPlanetPostCommentReply{
		Data: &v1.ListPlanetPostCommentReply_Data{
			Page:     request.Page,
			PageSize: request.PageSize,
			Items:    make([]*v1.ListPlanetPostCommentReply_Item, 0),
		},
	}

	params := map[string]interface{}{
		"post_id": request.PostId,
		"status":  enums.PostCommentStatusNormal,
		"root_id": 0,
	}
	postComments, count, err := u.repo.PaginatePostCommentByParams(ctx, params, int(request.Page), int(request.PageSize))
	if err != nil {
		return nil, err
	}
	res.Data.Total = int32(count)

	for _, item := range postComments {
		subComments, subCount, err := u.listSubCommentByParentID(ctx, item.ID)
		if err != nil {
			u.log.Errorf("获取【子评论】列表失败，error: %v", err)
		}
		ownerUser, _ := u.commonUseCase.GetUserInfoByUserID(ctx, item.UserID)
		res.Data.Items = append(res.Data.Items, &v1.ListPlanetPostCommentReply_Item{
			Id:        item.ID,
			PostId:    item.PostID,
			PlanetId:  request.PlanetId,
			Content:   item.Content,
			CreatedAt: int32(item.CreatedAt),
			User: &v1.UserInfo{
				Id:        ownerUser.ID,
				Nickname:  ownerUser.Name,
				AvatarUrl: ownerUser.GetAvatarUrl(),
			},
			SubItems: subComments,
			SubCount: int32(subCount),
		})
	}

	return res, nil
}

func (u *UserPlanetUsecase) listSubCommentByParentID(ctx context.Context, parentID int32) ([]*v1.SubCommentItem, int64, error) {
	res := make([]*v1.SubCommentItem, 0)

	// 获取子评论
	subCommentParams := map[string]interface{}{
		"status":  enums.PostCommentStatusNormal,
		"root_id": parentID,
	}
	subComments, count, err := u.repo.PaginatePostCommentByParams(ctx, subCommentParams, 1, 3)
	if err != nil {
		return nil, count, err
	}
	for _, item := range subComments {
		ownerUser, _ := u.commonUseCase.GetUserInfoByUserID(ctx, item.UserID)
		repliedUser, _ := u.commonUseCase.GetUserInfoByUserID(ctx, item.ParentUserID)
		res = append(res, &v1.SubCommentItem{
			Id:        item.ID,
			PostId:    item.PostID,
			Content:   item.Content,
			CreatedAt: int32(item.CreatedAt),
			User: &v1.UserInfo{
				Id:       ownerUser.ID,
				Nickname: ownerUser.Name,
			},
			RepliedUser: &v1.UserInfo{
				Id:       repliedUser.ID,
				Nickname: repliedUser.Name,
			},
		})
	}

	return res, count, nil
}

func (u *UserPlanetUsecase) UpdatePlanetPost(ctx context.Context, request *v1.UpdatePlanetPostRequest) (*v1.UpdatePlanetPostReply, error) {
	res := &v1.UpdatePlanetPostReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	now := time.Now()

	filterParams := map[string]interface{}{
		"user_id": userID,
		"id":      request.PostId,
	}
	updateParams := map[string]interface{}{
		"content":    request.Content,
		"updated_at": now.Unix(),
	}
	if err := u.repo.UpdatePlanetPostByParams(ctx, updateParams, filterParams); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) DeletePlanetPost(ctx context.Context, request *v1.DeletePlanetPostRequest) (*v1.DeletePlanetPostReply, error) {
	res := &v1.DeletePlanetPostReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	now := time.Now()

	filterParams := map[string]interface{}{
		"user_id": userID,
		"id":      request.PostId,
	}
	updateParams := map[string]interface{}{
		"status":     enums.PlanetPostStatusDelete,
		"updated_at": now.Unix(),
	}
	if err := u.repo.UpdatePlanetPostByParams(ctx, updateParams, filterParams); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) ToppedPlanetPost(ctx context.Context, request *v1.ToppedPlanetPostRequest) (*v1.ToppedPlanetPostReply, error) {
	res := &v1.ToppedPlanetPostReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	user, err := u.commonUseCase.GetUserInfoByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if !user.IsSystemAdmin {
		return res, nil
	}

	planetPost := &PlanetPost{}
	planetPost.ID = request.PostId
	if err = u.repo.FirstPlanetPostByID(ctx, planetPost); err != nil {
		return nil, err
	}

	filterParams := map[string]interface{}{
		"id": request.PostId,
	}
	updateParams := map[string]interface{}{
		"is_top": !planetPost.IsTop,
	}
	if err = u.repo.UpdatePlanetPostByParams(ctx, updateParams, filterParams); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserPlanetUsecase) DeletePlanetPostComment(ctx context.Context, request *v1.DeletePlanetPostCommentRequest) (*v1.DeletePlanetPostCommentReply, error) {
	res := &v1.DeletePlanetPostCommentReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	now := time.Now()

	filterParams := map[string]interface{}{
		"id": request.PostId,
	}
	// 系统管理员删除则不用校验权限
	user, err := u.commonUseCase.GetUserInfoByUserID(ctx, userID)
	if err != nil || !user.IsSystemAdmin {
		filterParams["user_id"] = userID
	}
	updateParams := map[string]interface{}{
		"status":     enums.PostCommentStatusDelete,
		"updated_at": now.Unix(),
	}
	if err = u.repo.UpdatePlanetPostCommentByParams(ctx, updateParams, filterParams); err != nil {
		return nil, err
	}

	// TODO: 判断是否由管理员删除的，如果是，判断是否给与站内通知警告

	return res, nil
}

func NewUserPlanetUsecase(repo UserPlanetRepo, logger log.Logger, planetRepo PlanetRepo, userRepo UserRepo, confData *conf.Data, commonUseCase *CommonUsecase) *UserPlanetUsecase {
	return &UserPlanetUsecase{
		repo:          repo,
		log:           log.NewHelper(logger),
		planetRepo:    planetRepo,
		userRepo:      userRepo,
		confData:      confData,
		commonUseCase: commonUseCase,
	}
}
