package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	v1 "github.com/wlnil/life-log-be/api/user_message/v1"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
)

// UserMessage 消息表
type UserMessage struct {
	BasicModel
	UserID    int32             `json:"user_id" gorm:"column:user_id"` // 用户 id
	MsgType   enums.MessageType `json:"msg_type" gorm:"msg_type"`      // 消息类型
	Content   string            `json:"content" gorm:"content"`        // 消息内容
	IsRead    bool              `json:"is_read" gorm:"is_read"`        // 是否已读
	TodayTime int64             `json:"today_time" gorm:"today_time"`  // 当日时间戳
	ThirdID   string            `json:"third_id" gorm:"third_id"`      // 外部 id，例如：定时任务 id
}

func (m *UserMessage) TableName() string {
	return "tb_user_message"
}

type MessageRepo interface {
	Create(msg *UserMessage) error
}

type MessageUsecase struct {
	repo MessageRepo
	log  *log.Helper
}

// Init 初始化消息
func (c *MessageUsecase) Init() {}

func (c *MessageUsecase) UnreadCount(ctx context.Context, request *v1.UnreadCountRequest) (*v1.UnreadCountReply, error) {
	res := &v1.UnreadCountReply{}

	res.Data = &v1.UnreadCountReply_Data{
		UnreadCount: 0,
	}

	return res, nil
}

func NewMessageUsecase(repo MessageRepo, logger log.Logger) *MessageUsecase {
	return &MessageUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}
