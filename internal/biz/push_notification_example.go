package biz

import (
	"context"
	"time"
)

// ============================================================================
// 使用示例：展示优化后的推送通知系统API
// ============================================================================

// ExampleUsage 展示如何使用推送通知系统
func ExampleUsage(usecase *PushNotificationUsecase) {
	ctx := context.Background()

	// 示例1：创建习惯提醒任务
	habitReq := &HabitReminderCreateRequest{
		UserID:       12345,
		RelatedID:    67890,
		ReminderTime: "07:30",
		RepeatRule:   "daily",
	}

	if err := usecase.CreateTask(ctx, habitReq); err != nil {
		// 处理错误
		return
	}

	// 示例2：创建系统通知任务
	noticeReq := &SystemNoticeCreateRequest{
		UserID:        12345,
		Title:         "系统维护通知",
		Content:       "系统将于今晚23:00-01:00进行维护，请提前保存数据。",
		ScheduledTime: time.Now().Add(2 * time.Hour).Unix(),
	}

	if err := usecase.CreateTask(ctx, noticeReq); err != nil {
		// 处理错误
		return
	}

	// 示例3：批量创建任务
	requests := []TaskCreateRequest{
		&HabitReminderCreateRequest{
			UserID:       12345,
			RelatedID:    11111,
			ReminderTime: "08:00",
			RepeatRule:   "daily",
		},
		&SystemNoticeCreateRequest{
			UserID:        12345,
			Title:         "新功能上线",
			Content:       "我们上线了新的习惯追踪功能，快来体验吧！",
			ScheduledTime: time.Now().Add(1 * time.Hour).Unix(),
		},
	}

	if err := usecase.CreateTasksBatch(ctx, requests); err != nil {
		// 处理错误
		return
	}
}


