package biz

import (
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/conf"
)

// PushConfigManager 简化的推送配置管理器
type PushConfigManager struct {
	config *conf.Data
	log    *log.Helper
}

// PushServiceConfig 推送服务配置
type PushServiceConfig struct {
	ServiceName string                 `json:"service_name"`
	Config      map[string]interface{} `json:"config"`
	Enabled     bool                   `json:"enabled"`
	Timeout     int32                  `json:"timeout"`
	MaxRetry    int32                  `json:"max_retry"`
}

// NewPushConfigManager 创建简化的推送配置管理器
func NewPushConfigManager(config *conf.Data, logger log.Logger) *PushConfigManager {
	return &PushConfigManager{
		config: config,
		log:    log.NewHelper(logger),
	}
}

// GetEnabledServices 获取启用的推送服务列表
func (pcm *PushConfigManager) GetEnabledServices() []string {
	if pcm.config.PushConfig == nil {
		return []string{"jpush"} // 默认启用极光推送
	}
	return pcm.config.PushConfig.EnabledServices
}

// GetDefaultService 获取默认推送服务
func (pcm *PushConfigManager) GetDefaultService() string {
	if pcm.config.PushConfig == nil {
		return "jpush"
	}
	if pcm.config.PushConfig.DefaultService == "" {
		return "jpush"
	}
	return pcm.config.PushConfig.DefaultService
}

// GetServiceForUser 根据用户信息获取推送服务
func (pcm *PushConfigManager) GetServiceForUser(user *User, platform string) string {
	// 如果没有启用智能路由，返回默认服务
	if pcm.config.PushConfig == nil || !pcm.config.PushConfig.EnableDomesticRouting {
		return pcm.GetDefaultService()
	}

	// 根据用户区域选择推送服务
	if user != nil && user.IsChineseUser() {
		if pcm.config.PushConfig.DomesticService != "" {
			return pcm.config.PushConfig.DomesticService
		}
		return "jpush"
	}

	// 海外用户根据平台选择
	switch strings.ToLower(platform) {
	case "ios":
		if pcm.isServiceEnabled("apns") {
			return "apns"
		}
	case "android":
		if pcm.isServiceEnabled("fcm") {
			return "fcm"
		}
	}

	// 备用服务
	if pcm.config.PushConfig.OverseasService != "" {
		return pcm.config.PushConfig.OverseasService
	}
	return "fcm"
}

// GetFallbackService 获取备用推送服务
func (pcm *PushConfigManager) GetFallbackService(currentService string) string {
	if pcm.config.PushConfig == nil || !pcm.config.PushConfig.EnableFallback {
		return ""
	}

	// 简单的备用策略
	enabledServices := pcm.GetEnabledServices()
	for _, service := range enabledServices {
		if service != currentService {
			return service
		}
	}
	return ""
}

// IsServiceEnabled 检查服务是否启用
func (pcm *PushConfigManager) IsServiceEnabled(serviceName string) bool {
	return pcm.isServiceEnabled(serviceName)
}

// isServiceEnabled 内部方法检查服务是否启用
func (pcm *PushConfigManager) isServiceEnabled(serviceName string) bool {
	switch strings.ToLower(serviceName) {
	case "jpush":
		return pcm.config.Jpush != nil && pcm.config.Jpush.Enabled
	case "apns":
		return pcm.config.Firebase != nil && pcm.config.Firebase.Enabled
	case "fcm":
		return pcm.config.Firebase != nil && pcm.config.Firebase.Enabled
	default:
		return false
	}
}

// getJPushConfig 获取极光推送配置
func (pcm *PushConfigManager) getJPushConfig() *PushServiceConfig {
	if pcm.config.Jpush == nil {
		return nil
	}

	config := map[string]interface{}{
		"app_key":      pcm.config.Jpush.AppKey,
		"app_secret":   pcm.config.Jpush.AppSecret,
		"api_endpoint": pcm.config.Jpush.ApiEndpoint,
	}

	return &PushServiceConfig{
		ServiceName: "jpush",
		Config:      config,
		Enabled:     pcm.config.Jpush.Enabled,
		Timeout:     pcm.getTimeout(pcm.config.Jpush.Timeout),
		MaxRetry:    pcm.getMaxRetry(pcm.config.Jpush.MaxRetry),
	}
}

// getTimeout 获取超时时间，提供默认值
func (pcm *PushConfigManager) getTimeout(timeout int32) int32 {
	if timeout <= 0 {
		return 30 // 默认30秒
	}
	return timeout
}

// getMaxRetry 获取最大重试次数，提供默认值
func (pcm *PushConfigManager) getMaxRetry(maxRetry int32) int32 {
	if maxRetry <= 0 {
		return 3 // 默认重试3次
	}
	return maxRetry
}

// GetHealthCheckInterval 获取健康检查间隔
func (pcm *PushConfigManager) GetHealthCheckInterval() time.Duration {
	if pcm.config.PushConfig == nil || pcm.config.PushConfig.HealthCheckInterval <= 0 {
		return 5 * time.Minute // 默认5分钟
	}
	return time.Duration(pcm.config.PushConfig.HealthCheckInterval) * time.Second
}

// LogConfigInfo 记录配置信息（用于调试）
func (pcm *PushConfigManager) LogConfigInfo() {
	enabledServices := pcm.GetEnabledServices()
	defaultService := pcm.GetDefaultService()

	pcm.log.Infof("推送服务配置加载完成:")
	pcm.log.Infof("- 启用的服务: %v", enabledServices)
	pcm.log.Infof("- 默认服务: %s", defaultService)

	if pcm.config.PushConfig != nil {
		pcm.log.Infof("- 启用备用服务: %v", pcm.config.PushConfig.EnableFallback)
		pcm.log.Infof("- 启用智能路由: %v", pcm.config.PushConfig.EnableDomesticRouting)
		if pcm.config.PushConfig.EnableDomesticRouting {
			pcm.log.Infof("- 国内服务: %s", pcm.config.PushConfig.DomesticService)
			pcm.log.Infof("- 海外服务: %s", pcm.config.PushConfig.OverseasService)
		}
	}
}
