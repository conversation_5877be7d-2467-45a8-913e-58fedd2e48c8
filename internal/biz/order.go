package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	v1 "github.com/wlnil/life-log-be/api/order/v1"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/code"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"github.com/wlnil/life-log-be/internal/pkg/middleware/localize"
	"github.com/wlnil/life-log-be/internal/pkg/pay"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
	"time"
)

type UserOrder struct {
	BasicModel
	SerialNumber string                    `json:"serial_number" gorm:"column:serial_number"` // 订单编号
	UserID       int32                     `json:"user_id" gorm:"column:user_id"`             // 用户 ID
	Amount       int32                     `json:"amount" gorm:"column:amount"`               // 应付金额
	RealAmount   int32                     `json:"real_amount" gorm:"column:real_amount"`     // 实付金额
	Status       enums.UserOrderStatusType `json:"status" gorm:"column:status"`               // 状态（expired 过期，pending 待付款，paid 已付款，failed 失败）
	Remark       string                    `json:"remark" gorm:"column:remark"`
	ThirdType    enums.UserOrderThirdType  `json:"third_type" gorm:"column:third_type"` // 关联三方数据类型（user_vip 用户 vip）
	ThirdID      int32                     `json:"third_id" gorm:"column:third_id"`     // 关联三方数据 id
}

func (m *UserOrder) TableName() string {
	return "tb_user_order"
}

type OrderPayment struct {
	BasicModel
	UserID        int32                    `json:"user_id" gorm:"column:user_id"`
	OrderID       int32                    `json:"order_id" gorm:"column:order_id"`
	PaymentMethod enums.OrderPaymentType   `json:"payment_method" gorm:"column:payment_method"` // 支付方式，（apple_pay 苹果支付，google_pay 谷歌支付，wx_pay 微信支付，ali_pay 支付宝支付）
	TransactionID string                   `json:"transaction_id" gorm:"column:transaction_id"` // 支付平台返回的交易 id
	Status        enums.OrderPaymentStatus `json:"status" gorm:"column:status"`                 // 支付状态（pending 支付中，paid 完成，failed 失败）
}

func (m *OrderPayment) TableName() string {
	return "tb_order_payment"
}

type PaymentCallback struct {
	ID           int32  `json:"id" gorm:"column:id"`
	PaymentID    int32  `json:"payment_id" gorm:"column:payment_id"`
	CallbackData string `json:"callback_data" gorm:"column:callback_data"`
	ReceivedAt   int64  `json:"received_at" gorm:"column:received_at"`
}

func (m *PaymentCallback) TableName() string {
	return "tb_payment_callback"
}

type OrderRepo interface {
	CreateUserOrder(ctx context.Context, order *UserOrder) error
}

type OrderUsecase struct {
	commonUseCase *CommonUsecase

	repo     OrderRepo
	userRepo UserRepo

	log      *log.Helper
	confData *conf.Data
}

func (o *OrderUsecase) CreateUserOrder(ctx context.Context, request *v1.CreateUserOrderRequest) (*v1.CreateUserOrderReply, error) {
	res := &v1.CreateUserOrderReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	cacheUser, err := o.commonUseCase.GetUserInfoByUserID(ctx, userID)
	if err != nil || cacheUser.ID == 0 {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidUserMsg, nil))
	}

	var payment pay.PaymentInterface
	switch request.PaymentMethod {
	case string(enums.OrderPaymentTypeAlipay):
		payment = pay.AliPayInstance
	}

	if payment == nil {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidPaymentMsg, nil))
	}
	if err = payment.Init(ctx); err != nil {
		o.log.Warnf("【订单服务】初始化支付错误, user_id: %v, err: %v", cacheUser.ID, err)
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidPaymentMsg, nil))
	}

	if !tool.Contains([]enums.OrderPaymentType{enums.OrderPaymentTypeAlipay}, enums.OrderPaymentType(request.PaymentMethod)) {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrParamsMsg, nil))
	}

	if request.PurchaseType == string(enums.UserOrderThirdTypeUserVip) && !tool.Contains([]enums.UserRoleType{enums.UserRoleTypeMonthVip, enums.UserRoleTypeQuarterVip, enums.UserRoleTypeYearVip}, enums.UserRoleType(request.Category)) {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrParamsMsg, nil))
	}

	// 默认为 1，等分库分表可以重新设计
	generatorOrder := tool.NewOrderIDGenerator(1)
	tradeNo := generatorOrder.GenerateOrderID()
	amount, realAmount := o.getPurchasePrice(enums.UserOrderThirdType(request.PurchaseType), enums.UserRoleType(request.Category))
	// 插入订单表
	order := &UserOrder{
		SerialNumber: tradeNo,
		UserID:       cacheUser.ID,
		Amount:       amount,
		RealAmount:   realAmount,
		Status:       enums.UserOrderStatusTypePending,
		ThirdType:    enums.UserOrderThirdTypeUserVip,
		ThirdID:      request.Category,
	}
	now := time.Now()
	order.CreatedAt = now.Unix()
	order.UpdatedAt = now.Unix()
	if err = o.repo.CreateUserOrder(ctx, order); err != nil {
		return nil, err
	}

	url, err := payment.Pay(ctx, float64(order.RealAmount/100), tradeNo)
	if err != nil {
		o.log.Warnf("【订单服务】生成支付 url 失败, user_id: %v, err: %v", cacheUser.ID, err)
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrCreateOrderMsg, nil))
	}

	res.Data = &v1.CreateUserOrderReply_Data{
		Url: url,
	}

	return res, nil
}

func (o *OrderUsecase) getPurchasePrice(purchaseType enums.UserOrderThirdType, category enums.UserRoleType) (int32, int32) {
	// 价格以分为单位
	var price int32
	var realAmount int32

	switch purchaseType {
	case enums.UserOrderThirdTypeUserVip:
		switch category {
		case enums.UserRoleTypeMonthVip:
			price = o.confData.UserVipPrice.MonthPrice
			realAmount = o.confData.UserVipPrice.DiscountMonthPrice
		case enums.UserRoleTypeQuarterVip:
			price = o.confData.UserVipPrice.QuarterPrice
			realAmount = o.confData.UserVipPrice.DiscountQuarterPrice
		case enums.UserRoleTypeYearVip:
			price = o.confData.UserVipPrice.YearPrice
			realAmount = o.confData.UserVipPrice.DiscountYearPrice
		}
	}

	return price, realAmount
}

func NewOrderUsecase(repo OrderRepo, logger log.Logger, confData *conf.Data, commonUseCase *CommonUsecase, userRepo UserRepo) *OrderUsecase {
	return &OrderUsecase{
		repo:          repo,
		userRepo:      userRepo,
		log:           log.NewHelper(logger),
		confData:      confData,
		commonUseCase: commonUseCase,
	}
}
