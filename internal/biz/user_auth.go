package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"time"
)

type UserToken struct {
	BasicModel
	UserID                 int32                       `json:"user_id" gorm:"column:user_id"`                                   // 用户ID
	Token                  string                      `json:"token" gorm:"column:token"`                                       // JWT token
	Platform               enums.UserTokenPlatformType `json:"platform" gorm:"column:platform"`                                 // 平台类型（0 网站，1 iOS，2 Android）
	ExpireAt               int64                       `json:"expire_at" gorm:"column:expire_at"`                               // 过期时间
	IsValid                bool                        `json:"is_valid" gorm:"column:is_valid"`                                 // 是否有效
	DeviceToken            string                      `json:"device_token" gorm:"column:device_token"`                         // 设备推送token
	JpushRegistrationId    string                      `json:"jpush_registration_id" gorm:"column:jpush_registration_id"`       // 极光推送设备注册ID
	DeviceID               string                      `json:"device_id" gorm:"column:device_id"`                               // 设备唯一标识
	TokenType              string                      `json:"token_type" gorm:"column:token_type"`                             // Token类型：fcm, apns, jpush
	LastVerifiedAt         int64                       `json:"last_verified_at" gorm:"column:last_verified_at"`                 // 最后验证时间
	RecommendedPushChannel string                      `json:"recommended_push_channel" gorm:"column:recommended_push_channel"` // 极光推送推荐渠道
	DeviceBrand            string                      `json:"device_brand" gorm:"column:device_brand"`                         // 设备品牌，用于推送策略选择，如：Apple、Samsung、Huawei等
}

func (ut *UserToken) TableName() string {
	return "tb_user_token"
}

type UserAuthRepo interface {
	Create(context.Context, *UserToken) error
	Delete(ctx context.Context, userID int32, platform enums.UserTokenPlatformType) error
	Update(context.Context, *UserToken) error
	UpdateValidByParams(context.Context, *UserToken) error
	InvalidateUserTokens(ctx context.Context, userID int32, platform enums.UserTokenPlatformType) error
	FindBySearch(context.Context, *UserToken, Where) error
}

type UserAuthUsecase struct {
	repo UserAuthRepo
	log  *log.Helper
}

func NewUserAuthUsecase(repo UserAuthRepo, logger log.Logger) *UserAuthUsecase {
	return &UserAuthUsecase{repo: repo, log: log.NewHelper(logger)}
}

// CreateUserToken 创建用户Token（优化版本）
func (ua *UserAuthUsecase) CreateUserToken(ctx context.Context, userID int32, platform, deviceId, fcmToken, apnsToken, jpushRegistrationId, recommendedPushChannel, deviceBrand string) (*UserToken, error) {
	// 1. 使旧Token失效
	if err := ua.repo.InvalidateUserTokens(ctx, userID, enums.UserTokenPlatformType(platform)); err != nil {
		ua.log.Warnf("使旧Token失效失败: %v", err)
	}

	// 2. 创建新Token
	expireAt := time.Now().Add(time.Second * time.Duration(auth.Secret.ExpireAt))
	tokenString, err := auth.GenerateToken(userID, expireAt)
	if err != nil {
		return nil, err
	}

	userToken := &UserToken{
		UserID:                 userID,
		Token:                  tokenString,
		ExpireAt:               expireAt.Unix(),
		Platform:               enums.UserTokenPlatformType(platform),
		IsValid:                true,
		DeviceID:               deviceId,
		DeviceBrand:            deviceBrand,
		JpushRegistrationId:    jpushRegistrationId,
		RecommendedPushChannel: recommendedPushChannel,
	}

	// 根据推送服务类型和平台确定Token信息
	if fcmToken != "" {
		userToken.DeviceToken = fcmToken
		userToken.TokenType = "fcm"
	} else if jpushRegistrationId != "" {
		userToken.DeviceToken = jpushRegistrationId
		userToken.TokenType = "jpush"
	}

	// 设置最后验证时间
	now := time.Now().Unix()
	userToken.CreatedAt = now
	userToken.UpdatedAt = now
	userToken.LastVerifiedAt = now

	if err = ua.repo.Create(ctx, userToken); err != nil {
		return nil, err
	}

	return userToken, nil
}

// UpdateUserToken 创建用户Token（优化版本）
func (ua *UserAuthUsecase) UpdateUserToken(ctx context.Context, userID int32, platform, deviceId, fcmToken, apnsToken, jpushRegistrationId, recommendedPushChannel, deviceBrand string) error {
	userToken := &UserToken{}
	where := Where{
		"user_id =":  userID,
		"platform =": enums.UserTokenPlatformType(platform),
		"is_valid =": 1,
	}

	err := ua.repo.FindBySearch(ctx, userToken, where)
	if err != nil {
		return err
	}

	userToken.Platform = enums.UserTokenPlatformType(platform)
	userToken.DeviceID = deviceId
	userToken.DeviceBrand = deviceBrand
	userToken.JpushRegistrationId = jpushRegistrationId
	userToken.RecommendedPushChannel = recommendedPushChannel

	// 根据推送服务类型和平台确定Token信息
	if fcmToken != "" {
		userToken.DeviceToken = fcmToken
		userToken.TokenType = "fcm"
	} else if jpushRegistrationId != "" {
		userToken.DeviceToken = jpushRegistrationId
		userToken.TokenType = "jpush"
	}

	// 设置最后验证时间
	now := time.Now().Unix()
	userToken.UpdatedAt = now
	userToken.LastVerifiedAt = now

	if err = ua.repo.Update(ctx, userToken); err != nil {
		return err
	}

	return nil
}

func (ua *UserAuthUsecase) Delete(ctx context.Context, userID int32, platform enums.UserTokenPlatformType) error {
	return ua.repo.Delete(ctx, userID, platform)
}
