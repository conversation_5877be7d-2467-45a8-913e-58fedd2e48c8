package biz

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
)

// ============================================================================
// 习惯提醒任务创建器
// ============================================================================

// HabitReminderCreator 习惯提醒任务创建器
type HabitReminderCreator struct {
	handler TaskHandler
	log     *log.Helper
}

// NewHabitReminderCreator 创建习惯提醒任务创建器
func NewHabitReminderCreator(handler TaskHandler, logger log.Logger) *HabitReminderCreator {
	return &HabitReminderCreator{
		handler: handler,
		log:     log.NewHelper(logger),
	}
}

func (c *HabitReminderCreator) GetTaskType() enums.PushTaskType {
	return enums.PushTaskTypeHabitReminder
}

func (c *HabitReminderCreator) ValidateRequest(req TaskCreateRequest) error {
	habitReq, ok := req.(*HabitReminderCreateRequest)
	if !ok {
		return fmt.Errorf("无效的请求类型，期望 HabitReminderCreateRequest")
	}
	return habitReq.Validate()
}

func (c *HabitReminderCreator) CreateTask(ctx context.Context, req TaskCreateRequest) (*PushTask, error) {
	habitReq := req.(*HabitReminderCreateRequest)

	scheduledTime := c.handler.CalculateNextScheduleTime(habitReq.ReminderTime, habitReq.RepeatRule)

	task := &PushTask{
		UserID:        habitReq.UserID,
		TaskType:      enums.PushTaskTypeHabitReminder,
		RelatedID:     habitReq.RelatedID,
		ScheduledTime: scheduledTime,
		Status:        enums.PushTaskStatusPending,
		IsRecurring:   true,
		RepeatRule:    habitReq.RepeatRule,
		ReminderTime:  habitReq.ReminderTime,
	}

	c.log.Infof("创建习惯提醒任务: userID=%d, relatedID=%d, scheduledTime=%d",
		habitReq.UserID, habitReq.RelatedID, scheduledTime)

	return task, nil
}

// ============================================================================
// 系统通知任务创建器
// ============================================================================

// SystemNoticeCreator 系统通知任务创建器
type SystemNoticeCreator struct {
	log *log.Helper
}

// NewSystemNoticeCreator 创建系统通知任务创建器
func NewSystemNoticeCreator(logger log.Logger) *SystemNoticeCreator {
	return &SystemNoticeCreator{
		log: log.NewHelper(logger),
	}
}

func (c *SystemNoticeCreator) GetTaskType() enums.PushTaskType {
	return enums.PushTaskTypeSystemNotice
}

func (c *SystemNoticeCreator) ValidateRequest(req TaskCreateRequest) error {
	noticeReq, ok := req.(*SystemNoticeCreateRequest)
	if !ok {
		return fmt.Errorf("无效的请求类型，期望 SystemNoticeCreateRequest")
	}
	return noticeReq.Validate()
}

func (c *SystemNoticeCreator) CreateTask(ctx context.Context, req TaskCreateRequest) (*PushTask, error) {
	noticeReq := req.(*SystemNoticeCreateRequest)

	task := &PushTask{
		UserID:        noticeReq.UserID,
		TaskType:      enums.PushTaskTypeSystemNotice,
		RelatedID:     0, // 系统通知没有关联ID
		Title:         noticeReq.Title,
		Content:       noticeReq.Content,
		ScheduledTime: noticeReq.ScheduledTime,
		Status:        enums.PushTaskStatusPending,
		IsRecurring:   false,
	}

	c.log.Infof("创建系统通知任务: userID=%d, title=%s, scheduledTime=%d",
		noticeReq.UserID, noticeReq.Title, noticeReq.ScheduledTime)

	return task, nil
}
