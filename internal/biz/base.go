package biz

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	stderr "errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/cache"
	"github.com/wlnil/life-log-be/internal/pkg/db"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"github.com/wlnil/life-log-be/internal/pkg/store/qiniu"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
	"gorm.io/gorm"
	"math/rand"
	"time"
)

type Where map[string]interface{}

type BasicModel struct {
	ID        int32 `gorm:"column:id" json:"id"`
	CreatedAt int64 `gorm:"created_at" json:"created_at"`
	UpdatedAt int64 `gorm:"updated_at" json:"updated_at"`
}

type CommonUsecase struct {
	log      *log.Helper
	db       *gorm.DB
	confData *conf.Data

	userPlanetRepo UserPlanetRepo
	planetRepo     PlanetRepo
	userRepo       UserRepo
	userHabitRepo  UserHabitRepo
}

type UserInfo struct {
	ID             int32                `json:"id"`
	Name           string               `json:"name"`
	AvatarUrl      string               `json:"avatar_url"`
	Phone          string               `json:"phone"`
	Email          string               `json:"email"`
	Status         enums.UserStatus     `json:"status"`
	Gender         enums.UserGenderType `json:"gender"`
	RoleType       enums.UserRoleType   `json:"role_type"`
	IsVip          bool                 `json:"is_vip"`
	ExpiredAt      int64                `json:"expired_at"`
	IsSystemAdmin  bool                 `json:"is_system_admin"`
	Desc           string               `json:"desc"`
	IsSetPrivacy   bool                 `json:"is_set_privacy"`
	IsSetPwd       bool                 `json:"is_set_pwd"`
	TimezoneName   string               `json:"timezone_name"`
	TimezoneOffset string               `json:"timezone_offset"`
}

type ListStringModel []string

func (c ListStringModel) Value() (driver.Value, error) {
	b, err := json.Marshal(c)
	return string(b), err
}

type StatusHabitCount struct {
	Status enums.UserHabitStatus
	Count  int32
}

func (u UserInfo) GetAvatarUrl() string {
	if u.AvatarUrl == "" {
		return ""
	}
	return fmt.Sprintf("%v/%v", qiniu.Client.PublicAddr, u.AvatarUrl)
}

func (c *CommonUsecase) checkGenerateHabitSnap(ctx context.Context, uh *UserHabit, userTime time.Time) (bool, error) {
	// 根据习惯配置的选择的周期生成习惯快照
	isNeed := false
	// 固定周期
	if uh.Config.PunchCycleType == enums.PunchCycleFix {
		nowWeekDay := userTime.Weekday()
		if tool.Contains(uh.Config.PunchCycle, int32(nowWeekDay)) {
			isNeed = true
		}
	} else if uh.Config.PunchCycleType == enums.PunchCycleWeek { // 按周，一周打卡几天
		// 获取本周开始时间和结束时间戳
		startTime, endTime := tool.GetWeekStartAndEndTime(userTime)
		// 获取本周已打卡天数
		punchedDayCount, err := c.userHabitRepo.CountUserPunchedDayByTime(ctx, uh.ID, startTime.Unix(), endTime.Unix())
		if err != nil {
			return false, stderr.New(fmt.Sprintf("获取用户习惯打卡天数失败，error: %v", err))
		}
		if punchedDayCount < int64(uh.Config.PunchCycle[0]) {
			isNeed = true
		}
	} else if uh.Config.PunchCycleType == enums.PunchCycleMonth { // 按月，一月打卡几天
		// 获取本月开始时间和结束时间戳
		startTime, endTime := tool.GetMonthStartAndEndTime(userTime)
		// 获取本月已打卡天数
		punchedDayCount, err := c.userHabitRepo.CountUserPunchedDayByTime(ctx, uh.ID, startTime.Unix(), endTime.Unix())
		if err != nil {
			return false, stderr.New(fmt.Sprintf("获取用户习惯打卡天数失败，error: %v", err))
		}
		if punchedDayCount < int64(uh.Config.PunchCycle[0]) {
			isNeed = true
		}
	}

	return isNeed, nil
}

func (c *CommonUsecase) GetUserInfoByUserID(ctx context.Context, userID int32) (UserInfo, error) {
	res := UserInfo{}
	if userID == 0 {
		return res, nil
	}

	// 先从 redis 中获取
	redisKey := fmt.Sprintf("%v%v", enums.RedisUserInfoPrefix, userID)
	cacheUser, err := cache.RedisClient.Get(ctx, redisKey).Result()
	if err != nil && !stderr.Is(err, redis.Nil) {
		c.log.Errorf("从 redis 中获取用户信息失败，error: %v", err)
		return res, err
	}
	if cacheUser != "" {
		if err = json.Unmarshal([]byte(cacheUser), &res); err != nil {
			c.log.Errorf("解析 cache_user 失败，error: %v", err)
		} else {
			return res, nil
		}
	} else {
		// 获取不到则从数据库中获取，并写入 redis
		return c.CacheUserInfo(ctx, userID, nil, nil)
	}

	return res, err
}

func (c *CommonUsecase) CacheUserInfo(ctx context.Context, userID int32, user *User, isSetPrivacy *bool) (UserInfo, error) {
	res := UserInfo{}

	// 先从 redis 中获取
	redisKey := fmt.Sprintf("%v%v", enums.RedisUserInfoPrefix, userID)
	cacheUser, err := cache.RedisClient.Get(ctx, redisKey).Result()
	if err != nil && !stderr.Is(err, redis.Nil) {
		c.log.Errorf("从 redis 中获取用户信息失败，error: %v", err)
	}
	if cacheUser != "" {
		if err = json.Unmarshal([]byte(cacheUser), &res); err != nil {
			c.log.Errorf("解析 cache_user 失败，error: %v", err)
		}
	}

	// 如果缓存为空，则所有数据全部重新获取
	if res.ID == 0 {
		user = &User{}
		user.ID = userID
		if err = c.userRepo.FindByID(ctx, user); err != nil {
			return res, err
		}

		us := &UserSetting{
			UserID: userID,
		}
		if err = c.userRepo.GetUserSetting(ctx, us); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return res, err
		}
		if us != nil && us.PrivacyPassword != "" {
			res.IsSetPrivacy = true
		}
	}

	// 更新用户信息
	if user != nil {
		res.ID = user.ID
		res.Name = user.Name
		res.AvatarUrl = user.AvatarUrl
		res.Phone = user.Phone
		res.Email = user.Email
		res.Status = user.Status
		res.IsSystemAdmin = user.IsSystemAdmin
		res.RoleType = user.RoleType
		res.Gender = user.Gender
		res.Desc = user.Desc
		res.IsSetPwd = len(user.Password) > 0
		res.IsVip = false
		res.ExpiredAt = user.ExpiredAt
		res.TimezoneName = user.TimezoneName
		res.TimezoneOffset = user.TimezoneOffset
		if user.IsSystemAdmin || user.ExpiredAt > time.Now().Unix() {
			res.IsVip = true
		}
	}

	// 更新是否设置隐私
	if isSetPrivacy != nil {
		res.IsSetPrivacy = *isSetPrivacy
	}

	// 写入 redis
	byteUser, err := json.Marshal(res)
	if err != nil {
		c.log.Errorf("序列化 user 失败，error: %v", err)
	} else {
		// redis 缓存用户信息过期时间，固定时长 + 随机时长，防止大批量 token 失效
		rand.New(rand.NewSource(time.Now().UnixNano()))
		expiration := time.Duration(enums.INVALID_REDIS_USERINFO_SECONDS+rand.Intn(10*60)) * time.Second
		if err = cache.RedisClient.Set(ctx, redisKey, string(byteUser), expiration).Err(); err != nil {
			c.log.Errorf("写入 redis 失败，error: %v", err)
		}
	}

	return res, nil
}

func (c *CommonUsecase) GetUserHabitStats(ctx context.Context, userID int32) (allHabits, onTracks, doneHabits int32, err error) {
	stats, err := c.userHabitRepo.GetHabitStatusCountByUserID(ctx, userID)
	if err != nil {
		return
	}

	for _, item := range stats {
		allHabits += item.Count
		if item.Status == enums.UserHabitStatusNormal {
			onTracks += item.Count
		} else if item.Status == enums.UserHabitStatusEnd {
			doneHabits += item.Count
		}
	}

	return
}

// InitUserData 用于用户注册账号后的数据初始化
func (c *CommonUsecase) InitUserData(ctx context.Context, userID int32, useEnglish bool, timeZoneStr string) error {
	var err error

	// 1. 初始化用户习惯
	if err = c.initHabitData(ctx, userID, useEnglish, timeZoneStr); err != nil {
		return err
	}

	return nil
}

func (c *CommonUsecase) initHabitData(ctx context.Context, userID int32, useEnglish bool, timeZoneStr string) error {
	var err error
	userTime := time.Now()

	// 1. 初始化用户习惯
	userHabits := make([]*UserHabit, 0)
	habitLogs := make([]*UserHabitUpdateLog, 0)
	normalHabit := "隐私密码 - 1234"
	smallHabit := "我是微习惯-先完成小目标"
	smallHabitStage1 := "读 1 页书"
	smallHabitStage2 := "读 10 页书"
	punchHabit := "左滑可以点击撤回打卡"
	cancelHabit := "完成我可以展示激励功能"
	aheadHabit := "我启用了隐私保护"
	detailHabit := "点击我进入习惯详情页"
	if useEnglish {
		normalHabit = "Privacy PIN - 1234"
		smallHabit = "I'm a micro habit - start small"
		punchHabit = "Swipe left to cli undo check-ins"
		cancelHabit = "Complete me for a sweet reward"
		aheadHabit = "I'm protected by privacy mode"
		detailHabit = "Tap me to explore habit details"
		smallHabitStage1 = "Read 1 page"
		smallHabitStage2 = "Read 10 pages"
	}

	userHabits = append(userHabits, &UserHabit{
		BasicModel: BasicModel{
			UpdatedAt: userTime.Unix(),
			CreatedAt: userTime.Unix(),
		},
		UserID: userID,
		Name:   detailHabit,
		Config: UserHabitConfig{
			PunchMaxCount:      1,
			PunchCycleType:     enums.PunchCycleFix,
			PunchCycle:         []int32{0, 1, 2, 3, 4, 5, 6},
			IsAllowCancelPunch: true,
			IsJoinAward:        false,
			IsSetPrivacy:       false,
		},
		Status:    enums.UserHabitStatusNormal,
		HabitType: enums.UserHabitNormal,
		IsDeleted: false,
		TimeZone:  timeZoneStr,
		Desc:      "",
	})
	userHabits = append(userHabits, &UserHabit{
		BasicModel: BasicModel{
			UpdatedAt: userTime.Unix(),
			CreatedAt: userTime.Unix(),
		},
		UserID: userID,
		Name:   punchHabit,
		Config: UserHabitConfig{
			PunchMaxCount:      1,
			PunchCycleType:     enums.PunchCycleFix,
			PunchCycle:         []int32{0, 1, 2, 3, 4, 5, 6},
			IsAllowCancelPunch: true,
			IsJoinAward:        false,
			IsSetPrivacy:       false,
		},
		Status:    enums.UserHabitStatusNormal,
		HabitType: enums.UserHabitNormal,
		IsDeleted: false,
		TimeZone:  timeZoneStr,
		Desc:      "",
	})
	userHabits = append(userHabits, &UserHabit{
		BasicModel: BasicModel{
			UpdatedAt: userTime.Unix(),
			CreatedAt: userTime.Unix(),
		},
		UserID: userID,
		Name:   smallHabit,
		Config: UserHabitConfig{
			PunchMaxCount:      1,
			PunchCycleType:     enums.PunchCycleFix,
			PunchCycle:         []int32{0, 1, 2, 3, 4, 5, 6},
			SmallStages:        []string{smallHabitStage1, smallHabitStage2},
			IsAllowCancelPunch: true,
			IsJoinAward:        false,
			IsSetPrivacy:       false,
		},
		Status:    enums.UserHabitStatusNormal,
		HabitType: enums.UserHabitSmall,
		IsDeleted: false,
		TimeZone:  timeZoneStr,
		Desc:      "",
	})
	userHabits = append(userHabits, &UserHabit{
		BasicModel: BasicModel{
			UpdatedAt: userTime.Unix(),
			CreatedAt: userTime.Unix(),
		},
		UserID: userID,
		Name:   cancelHabit,
		Config: UserHabitConfig{
			PunchMaxCount:      1,
			PunchCycleType:     enums.PunchCycleFix,
			PunchCycle:         []int32{0, 1, 2, 3, 4, 5, 6},
			IsAllowCancelPunch: true,
			IsJoinAward:        true,
			IsSetPrivacy:       false,
		},
		Status:    enums.UserHabitStatusNormal,
		HabitType: enums.UserHabitNormal,
		IsDeleted: false,
		TimeZone:  timeZoneStr,
		Desc:      "",
	})
	userHabits = append(userHabits, &UserHabit{
		BasicModel: BasicModel{
			UpdatedAt: userTime.Unix(),
			CreatedAt: userTime.Unix(),
		},
		UserID: userID,
		Name:   normalHabit,
		Config: UserHabitConfig{
			PunchMaxCount:      1,
			PunchCycleType:     enums.PunchCycleFix,
			PunchCycle:         []int32{0, 1, 2, 3, 4, 5, 6},
			IsAllowCancelPunch: true,
			IsJoinAward:        false,
			IsSetPrivacy:       false,
		},
		Status:    enums.UserHabitStatusNormal,
		HabitType: enums.UserHabitNormal,
		IsDeleted: false,
		TimeZone:  timeZoneStr,
		Desc:      "",
	})
	userHabits = append(userHabits, &UserHabit{
		BasicModel: BasicModel{
			UpdatedAt: userTime.Unix(),
			CreatedAt: userTime.Unix(),
		},
		UserID: userID,
		Name:   aheadHabit,
		Config: UserHabitConfig{
			PunchMaxCount:      1,
			PunchCycleType:     enums.PunchCycleFix,
			PunchCycle:         []int32{0, 1, 2, 3, 4, 5, 6},
			IsAllowCancelPunch: false,
			IsJoinAward:        false,
			IsSetPrivacy:       true,
		},
		Status:    enums.UserHabitStatusNormal,
		HabitType: enums.UserHabitNormal,
		IsDeleted: false,
		TimeZone:  timeZoneStr,
		Desc:      "",
	})
	tx := c.db.Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	if err = tx.Model(&UserHabit{}).Create(userHabits).Error; err != nil {
		return err
	}

	for _, item := range userHabits {
		habitLogs = append(habitLogs, &UserHabitUpdateLog{
			BasicModel: BasicModel{
				UpdatedAt: userTime.Unix(),
				CreatedAt: userTime.Unix(),
			},
			UserHabitID: item.ID,
			UserID:      userID,
			Config:      item.Config,
		})
	}
	if err = tx.Model(&UserHabitUpdateLog{}).Create(habitLogs).Error; err != nil {
		return err
	}

	return nil
}

func NewCommonUsecase(logger log.Logger, confData *conf.Data, userPlanetRepo UserPlanetRepo, planetRepo PlanetRepo, userRepo UserRepo, userHabitRepo UserHabitRepo) *CommonUsecase {
	return &CommonUsecase{
		log:            log.NewHelper(logger),
		db:             db.GormClient,
		confData:       confData,
		userPlanetRepo: userPlanetRepo,
		planetRepo:     planetRepo,
		userRepo:       userRepo,
		userHabitRepo:  userHabitRepo,
	}
}
