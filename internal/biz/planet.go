package biz

import (
	"context"
	"github.com/go-kratos/kratos/v2/log"
	v1 "github.com/wlnil/life-log-be/api/planet/v1"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"time"
)

type Planet struct {
	BasicModel
	Name             string             `json:"name" gorm:"column:name"`                             // 星球名称
	ImgUrl           string             `json:"img_url" gorm:"column:img_url"`                       // 星球头图
	UserID           int32              `json:"user_id" gorm:"column:user_id"`                       // 星主用户 ID
	LimitMemberCount int                `json:"limit_member_count" gorm:"column:limit_member_count"` // 限制人数
	MemberCount      int                `json:"member_count" gorm:"column:member_count"`             // 当前人数
	Status           enums.PlanetStatus `json:"status" gorm:"column:status"`                         // 状态
	Description      string             `json:"description" gorm:"column:description"`               // 描述
	Remark           string             `json:"remark" gorm:"column:remark"`                         // 备注，例如：封禁原因
}

func (p *Planet) TableName() string {
	return "tb_planet"
}

type PlanetTarget struct {
	BasicModel
	PlanetID int                      `json:"planet_id" gorm:"column:planet_id"` // 星球 ID
	Title    string                   `json:"title" gorm:"column:title"`         // 目标标题
	RuleDesc string                   `json:"rule_desc" gorm:"column:rule_desc"` // 目标规则
	StartAt  int64                    `json:"start_at" gorm:"column:start_at"`   // 开始时间
	EndAt    int64                    `json:"end_at" gorm:"column:end_at"`       // 结束时间
	Status   enums.PlanetTargetStatus `json:"status" gorm:"column:status"`       // 状态
	Remark   string                   `json:"remark" gorm:"column:remark"`       // 备注，例如：封禁原因
}

func (m *PlanetTarget) TableName() string {
	return "tb_planet_target"
}

type PlanetRepo interface {
	CreatePlanet(ctx context.Context, planet *Planet) error
	UpdatePlanet(ctx context.Context, planet *Planet) error
	FindPlanetByUserID(ctx context.Context, planet *Planet) error
	DeletePlanet(ctx context.Context, planet *Planet) error
	FirstPlanetByID(ctx context.Context, planet *Planet) error

	CreatePlanetTarget(ctx context.Context, planetTarget *PlanetTarget) error
	UpdatePlanetTarget(ctx context.Context, planetTarget *PlanetTarget) error
	FindPlanetTargetByID(ctx context.Context, planetTarget *PlanetTarget) error
	DeletePlanetTarget(ctx context.Context, planetTarget *PlanetTarget) error
	ListPlanetTargetByPlanetID(ctx context.Context, planetID int) ([]*PlanetTarget, error)
}

type PlanetUsecase struct {
	repo PlanetRepo
	log  *log.Helper
}

func (u *PlanetUsecase) CreatePlanet(ctx context.Context, request *v1.CreatePlanetRequest) (*v1.CreatePlanetReply, error) {
	res := &v1.CreatePlanetReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	planet := &Planet{
		Name:             request.Name,
		ImgUrl:           request.ImgUrl,
		UserID:           userID,
		LimitMemberCount: enums.DEFAULT_PLANET_MEMBER_COUNT,
		Status:           enums.PlanetStatusNormal,
		Description:      request.Description,
	}
	if err := u.repo.CreatePlanet(ctx, planet); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *PlanetUsecase) UpdatePlanet(ctx context.Context, request *v1.UpdatePlanetRequest) (*v1.UpdatePlanetReply, error) {
	res := &v1.UpdatePlanetReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	planet := &Planet{}
	planet.ID = request.Id
	planet.UserID = userID
	if err := u.repo.FindPlanetByUserID(ctx, planet); err != nil {
		return nil, err
	}
	now := time.Now()
	planet.Name = request.Name
	planet.ImgUrl = request.ImgUrl
	planet.Description = request.Description
	planet.UpdatedAt = now.Unix()
	if err := u.repo.UpdatePlanet(ctx, planet); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *PlanetUsecase) GetPlanet(ctx context.Context, request *v1.GetPlanetRequest) (*v1.GetPlanetReply, error) {
	res := &v1.GetPlanetReply{}

	planet := &Planet{}
	planet.ID = request.Id
	if err := u.repo.FirstPlanetByID(ctx, planet); err != nil {
		return nil, err
	}
	res.Data = &v1.GetPlanetReply_Data{
		Id:          planet.ID,
		Name:        planet.Name,
		ImgUrl:      planet.ImgUrl,
		Description: planet.Description,
	}

	return res, nil
}

func (u *PlanetUsecase) DeletePlanet(ctx context.Context, request *v1.DeletePlanetRequest) (*v1.DeletePlanetReply, error) {
	res := &v1.DeletePlanetReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	planet := &Planet{}
	planet.ID = request.Id
	planet.UserID = userID
	if err := u.repo.DeletePlanet(ctx, planet); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *PlanetUsecase) CreatePlanetTarget(ctx context.Context, request *v1.CreatePlanetTargetRequest) (*v1.CreatePlanetTargetReply, error) {
	res := &v1.CreatePlanetTargetReply{}

	return res, nil
}

func (u *PlanetUsecase) UpdatePlanetTarget(ctx context.Context, request *v1.UpdatePlanetTargetRequest) (*v1.UpdatePlanetTargetReply, error) {
	res := &v1.UpdatePlanetTargetReply{}

	return res, nil
}

func (u *PlanetUsecase) GetPlanetTarget(ctx context.Context, request *v1.GetPlanetTargetRequest) (*v1.GetPlanetTargetReply, error) {
	res := &v1.GetPlanetTargetReply{}

	return res, nil
}

func (u *PlanetUsecase) DeletePlanetTarget(ctx context.Context, request *v1.DeletePlanetTargetRequest) (*v1.DeletePlanetTargetReply, error) {
	res := &v1.DeletePlanetTargetReply{}

	return res, nil
}

func (u *PlanetUsecase) ListPlanetTarget(ctx context.Context, request *v1.ListPlanetTargetRequest) (*v1.ListPlanetTargetReply, error) {
	res := &v1.ListPlanetTargetReply{}

	return res, nil
}

func NewPlanetUsecase(repo PlanetRepo, logger log.Logger) *PlanetUsecase {
	return &PlanetUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}
