package biz

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	stderr "errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/api/user_habit/v1"
	statisticv1 "github.com/wlnil/life-log-be/api/user_statistic/v1"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/code"
	"github.com/wlnil/life-log-be/internal/pkg/db"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	httpClient "github.com/wlnil/life-log-be/internal/pkg/http_client"
	"github.com/wlnil/life-log-be/internal/pkg/middleware/localize"
	"github.com/wlnil/life-log-be/internal/pkg/task"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
	"gorm.io/gorm"
	"math"
	"sort"
	"strings"
	"time"
)

type UserHabitConfig struct {
	PunchMaxCount         int32                        `json:"punch_max_count"`         // 打卡最大次数
	PunchCycleType        enums.PunchCycleType         `json:"punch_cycle_type"`        // 打卡周期类型
	PunchCycle            []int32                      `json:"punch_cycle"`             // 打卡周期（固定：0-6，按周：每周打卡几天，按月：每月打卡几天）
	SmallStages           []string                     `json:"small_stages"`            // 微习惯阶段
	IsAllowTimeWarn       bool                         `json:"is_allow_time_warn"`      // 是否允许时间提醒
	IsJoinAward           bool                         `json:"is_join_award"`           // 是否参与奖励
	AwardImageUrl         string                       `json:"award_image_url"`         // 奖励图片
	IsAllowCancelPunch    bool                         `json:"is_allow_cancel_punch"`   // 是否允许取消打卡
	IsSetPrivacy          bool                         `json:"is_set_privacy"`          // 是否设置隐私
	PrivacyDisplayMode    enums.PrivacyDisplayModeType `json:"privacy_display_mode"`    // 隐私显示模式
	PrivacyDisplayContent string                       `json:"privacy_display_content"` // 隐私显示内容
	RecordType            enums.RecordType             `json:"record_type"`             // 统计方式
	EndDate               string                       `json:"end_date"`                // 结束时间
	ReminderTimes         []string                     `json:"reminder_times"`          // 提醒时间列表，格式：["09:00", "18:00", "21:30"]
	BuddyList             []int32                      `json:"buddy_list"`              // 伙伴列表
}

func (c UserHabitConfig) Value() (driver.Value, error) {
	b, err := json.Marshal(c)
	return string(b), err
}

func (c *UserHabitConfig) Scan(input interface{}) error {
	return json.Unmarshal(input.([]byte), c)
}

// UserHabit 用户习惯表
type UserHabit struct {
	BasicModel
	UserID    int32                 `json:"user_id" gorm:"column:user_id"`         // 用户id
	Name      string                `json:"name" gorm:"column:name"`               // 习惯名称
	Config    UserHabitConfig       `json:"config" gorm:"column:config;TYPE:json"` // 配置信息
	IsDeleted bool                  `json:"is_deleted" gorm:"column:is_deleted"`   // 是否删除（0 否, 1 是）
	Status    enums.UserHabitStatus `json:"status" gorm:"column:status"`           // 状态（1 正常，2 暂停，3 结束）
	HabitType enums.UserHabitType   `json:"habit_type" gorm:"column:habit_type"`   // 习惯类型
	Desc      string                `json:"desc" gorm:"column:desc"`               // 习惯描述
	TimeZone  string                `json:"time_zone" gorm:"column:time_zone"`     // 时区

	IsNecessary bool `json:"is_necessary" gorm:"-"` // 当日是否必须完成
	IsDisplay   bool `json:"is_display" gorm:"-"`   // 当日是否展示
}

func (uh *UserHabit) TableName() string {
	return "tb_user_habit"
}

// UserHabitSnapshot 用户习惯快照表
type UserHabitSnapshot struct {
	BasicModel
	UserHabitID int32                 `json:"user_habit_id" gorm:"column:user_habit_id"` // 用户习惯 ID
	UserID      int32                 `json:"user_id" gorm:"column:user_id"`             // 用户id
	Name        string                `json:"name" gorm:"column:name"`                   // 习惯名称
	Config      UserHabitConfig       `json:"config" gorm:"column:config;TYPE:json"`     // 配置信息
	IsCompleted bool                  `json:"is_completed" gorm:"column:is_completed"`   // 是否完成
	HabitType   enums.UserHabitType   `json:"habit_type" gorm:"column:habit_type"`       // 习惯类型
	TodayTime   int64                 `json:"today_time" gorm:"column:today_time"`       // 当日时间戳
	Status      enums.UserHabitStatus `json:"status" gorm:"column:status"`               // 状态（1 正常，2 暂停，3 结束）
	TimeZone    string                `json:"time_zone" gorm:"column:time_zone"`         // 时区
	Desc        string                `json:"desc" gorm:"column:desc"`                   // 习惯描述
}

func (uh *UserHabitSnapshot) TableName() string {
	return "tb_user_habit_snapshot"
}

// UserPunchLog 用户打卡记录表
type UserPunchLog struct {
	BasicModel
	UserHabitID  int32 `json:"user_habit_id" gorm:"column:user_habit_id"`   // 用户习惯 ID
	HabitSnapID  int32 `json:"habit_snap_id" gorm:"column:habit_snap_id"`   // 用户习惯快照 ID
	UserID       int32 `json:"user_id" gorm:"column:user_id"`               // 用户 ID
	RecordAt     int64 `json:"record_at" gorm:"column:record_at"`           // 打卡时间
	RecordTime   int64 `json:"record_time" gorm:"column:record_time"`       // 打卡时间戳
	SmallStageID int32 `json:"small_stage_id" gorm:"column:small_stage_id"` // 微习惯阶段 ID
}

func (ur *UserPunchLog) TableName() string {
	return "tb_user_punch_log"
}

// UserReckonTime 用户习惯时长统计表
type UserReckonTime struct {
	BasicModel
	UserHabitID  int32 `json:"user_habit_id" gorm:"column:user_habit_id"`   // 用户习惯 ID
	HabitSnapID  int32 `json:"habit_snap_id" gorm:"column:habit_snap_id"`   // 用户习惯快照 ID
	UserID       int32 `json:"user_id" gorm:"column:user_id"`               // 用户 ID
	Duration     int   `json:"duration" gorm:"column:duration"`             // 时长/秒
	StartAt      int64 `json:"start_at" gorm:"column:start_at"`             // 创建时间
	EndAt        int64 `json:"end_at" gorm:"column:end_at"`                 // 结束时间
	IsValid      bool  `json:"is_valid" gorm:"column:is_valid"`             // 是否有效
	SmallStageID int32 `json:"small_stage_id" gorm:"column:small_stage_id"` // 微习惯阶段 ID
}

func (urt *UserReckonTime) TableName() string {
	return "tb_user_reckon_time"
}

type UserHabitUpdateLog struct {
	BasicModel
	UserHabitID int32           `json:"user_habit_id" gorm:"column:user_habit_id"` // 用户习惯 id
	UserID      int32           `json:"user_id" gorm:"column:user_id"`             // 用户 ID
	Config      UserHabitConfig `json:"config" gorm:"column:config"`               // 习惯配置信息
}

func (uh *UserHabitUpdateLog) TableName() string {
	return "tb_user_habit_update_log"
}

// UserHabitDailyStats 用户习惯日统计数据
type UserHabitDailyStats struct {
	BasicModel
	UserID          int32 `json:"user_id" gorm:"column:user_id"`                   // 用户ID
	DateTime        int64 `json:"date_time" gorm:"column:date_time"`               // 日期时间戳，精确到天
	TotalHabits     int   `json:"total_habits" gorm:"column:total_habits"`         // 总习惯数
	CompletedHabits int   `json:"completed_habits" gorm:"column:completed_habits"` // 已完成习惯数
}

func (m *UserHabitDailyStats) TableName() string {
	return "tb_user_habit_daily_stats"
}

type UserHabitStats struct {
	BasicModel
	UserID             int32 `json:"user_id" gorm:"column:user_id"`
	UserHabitID        int32 `json:"user_habit_id" gorm:"column:user_habit_id"`
	AllCount           int   `json:"all_count" gorm:"column:all_count"`                       // 总待打卡天数
	DoneCount          int   `json:"done_count" gorm:"column:done_count"`                     // 已经打卡天数
	PersistLongDays    int   `json:"persist_long_days" gorm:"column:persist_long_days"`       // 坚持最长天数
	PersistStartDate   int32 `json:"persist_start_date" gorm:"column:persist_start_date"`     // 开始日期
	PersistEndDate     int32 `json:"persist_end_date" gorm:"column:persist_end_date"`         // 结束日期
	CurrentPersistDays int   `json:"current_persist_days" gorm:"column:current_persist_days"` // 当前坚持天数
}

func (m *UserHabitStats) TableName() string {
	return "tb_user_habit_stats"
}

type RecordResult struct {
	AvgData float64 `json:"avg_data"`
	MaxData int     `json:"max_data"`
	MinData int     `json:"min_data"`
}

type HabitStatisticAll struct {
	PunchCount       int32  `json:"punch_count"`
	MaxPersistDays   int32  `json:"max_persist_days"`
	PersistStartDate string `json:"persist_start_date"`
	PersistEndDate   string `json:"persist_end_date"`
}

type UserHabitRepo interface {
	Create(ctx context.Context, tx *gorm.DB, habit *UserHabit) error
	Update(ctx context.Context, tx *gorm.DB, habit *UserHabit) error
	FirstByID(ctx context.Context, habit *UserHabit) error
	FirstByParams(ctx context.Context, habit *UserHabit, params map[string]interface{}) error
	Delete(ctx context.Context, habit *UserHabit) error
	List(ctx context.Context, where Where) ([]*UserHabit, error)
	GetHabitStatusCountByUserID(ctx context.Context, userID int32) ([]*StatusHabitCount, error)

	ListSnapshotByParams(ctx context.Context, params map[string]interface{}) ([]*UserHabitSnapshot, error)
	ListSnapshotByUserHabitID(ctx context.Context, userHabitIDs []int32, params map[string]interface{}) ([]*UserHabitSnapshot, error)
	FirstSnapshotByID(ctx context.Context, habitSnap *UserHabitSnapshot) error
	UpdateSnapshot(ctx context.Context, tx *gorm.DB, habitSnapID, userID int32, updateParams map[string]interface{}) error
	UpdateSnapshotByUserHabitID(ctx context.Context, tx *gorm.DB, habitSnap *UserHabitSnapshot) error
	BatchCreateSnapshot(ctx context.Context, habitSnaps []*UserHabitSnapshot) error
	CreateSnapshot(ctx context.Context, habitSna *UserHabitSnapshot) error

	FirstPunchLogByParams(ctx context.Context, params map[string]interface{}) (*UserPunchLog, error)
	CreatePunchLog(ctx context.Context, tx *gorm.DB, up *UserPunchLog) error
	DeletePunchLog(ctx context.Context, tx *gorm.DB, params map[string]interface{}) error
	CountUserPunch(ctx context.Context, params map[string]interface{}) (int64, error)
	MapCountUserPunchByTime(ctx context.Context, habitSnapIds []int32, recordTime int64) (map[string]int32, error)
	CountUserPunchedDayByTime(ctx context.Context, userHabitID int32, startTime, endTime int64) (int64, error)
	MapCountUserPunchByUserHabitIDs(ctx context.Context, where Where) (map[int32]int32, error)
	ListPunchLogByParams(ctx context.Context, where Where) ([]*UserPunchLog, error)
	UpdatePunchLog(ctx context.Context, punchLog *UserPunchLog) error

	CreateReckonTime(ctx context.Context, urt *UserReckonTime) error
	UpdateReckonTime(ctx context.Context, urt *UserReckonTime) error
	FirstReckonTimeByID(ctx context.Context, urt *UserReckonTime) error
	ListReckonTimeByParams(ctx context.Context, params map[string]interface{}) ([]*UserReckonTime, error)

	CreateHabitLog(ctx context.Context, tx *gorm.DB, habitLog *UserHabitUpdateLog) error
	ListHabitLogByParams(ctx context.Context, where Where) ([]*UserHabitUpdateLog, error)
	SaveUserHabitDailyStats(ctx context.Context, stat *UserHabitDailyStats) error
	GetUserHabitDailyStatsByDateRange(ctx context.Context, userID int32, startTimestamp int64, endTimestamp int64) ([]*UserHabitDailyStats, error)

	SaveUserHabitStats(ctx context.Context, stats *UserHabitStats) error
	GetUserHabitStats(ctx context.Context, userID, userHabitID int32) (*UserHabitStats, error)
}

type UserHabitUsecase struct {
	log      *log.Helper
	db       *gorm.DB
	confData *conf.Data

	commonUsecase *CommonUsecase

	repo           UserHabitRepo
	userRepo       UserRepo
	userPlanetRepo UserPlanetRepo
	cronTaskRepo   CronTaskRepo
	messageRepo    MessageRepo
	pushUsecase    *PushNotificationUsecase
}

type CronTaskScheduler struct {
	AtStrings []string `json:"at_strings"` // 定时
	Tag       string   `json:"tag"`        // 标签
}

func (u *UserHabitUsecase) CreateUserHabit(ctx context.Context, request *v1.CreateUserHabitRequest) (*v1.CreateUserHabitReply, error) {
	res := &v1.CreateUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	userTime, err := time.Parse(time.RFC3339, request.CreateDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return res, nil
	}
	if err = u.userRepo.FindByID(ctx, &User{BasicModel: BasicModel{ID: userID}}); err != nil {
		return nil, err
	}
	// 每天打卡次数不小于 1
	punchMaxCount := int32(1)
	if request.Config.PunchMaxCount > 0 {
		punchMaxCount = request.Config.PunchMaxCount
	}
	// 每周、每月打卡天数不小于 1
	punchCycle := request.Config.PunchCycle
	if enums.PunchCycleType(request.Config.PunchCycleType) == enums.PunchCycleWeek {
		if len(punchCycle) == 0 || punchCycle[0] < 1 {
			punchCycle = []int32{1}
		} else if punchCycle[0] > 7 {
			punchCycle = []int32{7}
		}
	} else if enums.PunchCycleType(request.Config.PunchCycleType) == enums.PunchCycleMonth {
		if len(punchCycle) == 0 || punchCycle[0] < 1 {
			punchCycle = []int32{1}
		} else if punchCycle[0] > 31 {
			punchCycle = []int32{31}
		}
	}
	uh := &UserHabit{
		BasicModel: BasicModel{
			UpdatedAt: userTime.Unix(),
			CreatedAt: userTime.Unix(),
		},
		UserID: userID,
		Name:   request.Name,
		Config: UserHabitConfig{
			PunchMaxCount:         punchMaxCount,
			PunchCycleType:        enums.PunchCycleType(request.Config.PunchCycleType),
			PunchCycle:            punchCycle,
			SmallStages:           request.Config.SmallStages,
			IsAllowCancelPunch:    request.Config.IsAllowCancelPunch,
			IsJoinAward:           request.Config.IsJoinAward,
			IsSetPrivacy:          request.Config.IsSetPrivacy,
			PrivacyDisplayMode:    enums.PrivacyDisplayModeType(request.Config.PrivacyDisplayMode),
			PrivacyDisplayContent: request.Config.PrivacyDisplayContent,
			RecordType:            enums.RecordType(request.Config.RecordType),
			EndDate:               request.Config.EndDate,
			ReminderTimes:         request.Config.ReminderTimes,
			BuddyList:             request.Config.BuddyList,
		},
		Status:    enums.UserHabitStatusNormal,
		HabitType: enums.UserHabitType(request.HabitType),
		IsDeleted: false,
		TimeZone:  fmt.Sprintf("%v;%v", request.TimezonePlace, request.Timezone),
		Desc:      "",
	}
	// 对于记录类型，允许取消打卡
	if enums.UserHabitType(request.HabitType) == enums.UserHabitRecord {
		uh.Config.RecordType = enums.RecordType(request.Config.RecordType)
	}

	tx := u.db.Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	if err = u.repo.Create(ctx, tx, uh); err != nil {
		return nil, err
	}

	habitLog := &UserHabitUpdateLog{
		BasicModel: BasicModel{
			UpdatedAt: userTime.Unix(),
			CreatedAt: userTime.Unix(),
		},
		UserHabitID: uh.ID,
		UserID:      userID,
		Config:      uh.Config,
	}
	if err = u.repo.CreateHabitLog(ctx, tx, habitLog); err != nil {
		return nil, err
	}

	// 创建习惯关联的行为
	go func() {
		// 创建推送提醒任务
		if u.pushUsecase != nil {
			req := &HabitReminderCreateRequest{
				UserID:       userID,
				RelatedID:    uh.ID,
				ReminderTime: "09:00", // 默认上午9点提醒
				RepeatRule:   "daily",
			}
			if err = u.pushUsecase.CreateTask(ctx, req); err != nil {
				u.log.Errorf("创建习惯推送提醒任务失败，error: %v，user_habit_id: %v", err, uh.ID)
			}
		}
	}()

	return res, nil
}

func (u *UserHabitUsecase) UpdateUserHabit(ctx context.Context, request *v1.UpdateUserHabitRequest) (*v1.UpdateUserHabitReply, error) {
	res := &v1.UpdateUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	uh := &UserHabit{}
	uh.ID = request.Id
	uh.UserID = userID
	if err := u.repo.FirstByID(ctx, uh); err != nil {
		return nil, err
	}
	if uh.IsDeleted {
		return nil, stderr.New("习惯已删除，不能修改")
	}
	if uh.Status == enums.UserHabitStatusEnd {
		return nil, stderr.New("习惯已结束，不能修改")
	}
	oldConfig := uh.Config

	userTime, err := time.Parse(time.RFC3339, request.UpdateDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return res, nil
	}
	// 每天打卡次数不小于 1
	punchMaxCount := int32(1)
	if request.Config.PunchMaxCount > 0 {
		punchMaxCount = request.Config.PunchMaxCount
	}
	// 每周、每月打卡天数不小于 1
	punchCycle := request.Config.PunchCycle
	if enums.PunchCycleType(request.Config.PunchCycleType) == enums.PunchCycleWeek {
		if len(punchCycle) == 0 || punchCycle[0] < 1 {
			punchCycle = []int32{1}
		} else if punchCycle[0] > 7 {
			punchCycle = []int32{7}
		}
	} else if enums.PunchCycleType(request.Config.PunchCycleType) == enums.PunchCycleMonth {
		if len(punchCycle) == 0 || punchCycle[0] < 1 {
			punchCycle = []int32{1}
		} else if punchCycle[0] > 31 {
			punchCycle = []int32{31}
		}
	}
	uh.Config.PunchMaxCount = punchMaxCount
	uh.Config.PunchCycleType = enums.PunchCycleType(request.Config.PunchCycleType)
	uh.Config.PunchCycle = punchCycle
	uh.Config.SmallStages = request.Config.SmallStages
	uh.Config.IsJoinAward = request.Config.IsJoinAward
	uh.Config.IsSetPrivacy = request.Config.IsSetPrivacy
	uh.Config.PrivacyDisplayMode = enums.PrivacyDisplayModeType(request.Config.PrivacyDisplayMode)
	uh.Config.PrivacyDisplayContent = request.Config.PrivacyDisplayContent
	uh.Config.EndDate = request.Config.EndDate
	uh.Config.ReminderTimes = request.Config.ReminderTimes
	uh.Config.BuddyList = request.Config.BuddyList
	if uh.Config.IsAllowCancelPunch {
		uh.Config.IsAllowCancelPunch = request.Config.IsAllowCancelPunch
	}
	if uh.HabitType == enums.UserHabitRecord {
		uh.Config.RecordType = enums.RecordType(request.Config.RecordType)
	}
	uh.TimeZone = fmt.Sprintf("%v;%v", request.TimezonePlace, request.Timezone)
	now := time.Now()
	uh.UpdatedAt = now.Unix()

	tx := u.db.Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()

	if err = u.repo.Update(ctx, u.db, uh); err != nil {
		return nil, err
	}

	// 修改当日习惯快照数据
	if err = u.updateUserHabitSnapshot(ctx, userTime, uh, tx); err != nil {
		return nil, err
	}

	// 同时写入用户习惯日志，仅在修改打卡周期时
	if request.Config.PunchCycleType != int32(oldConfig.PunchCycleType) || !tool.CompareSlices(request.Config.PunchCycle, oldConfig.PunchCycle) || request.Config.PunchMaxCount != oldConfig.PunchMaxCount {
		habitLog := &UserHabitUpdateLog{
			BasicModel: BasicModel{
				UpdatedAt: userTime.Unix(),
				CreatedAt: userTime.Unix(),
			},
			UserHabitID: uh.ID,
			UserID:      userID,
			Config:      uh.Config,
		}
		if err = u.repo.CreateHabitLog(ctx, tx, habitLog); err != nil {
			return nil, err
		}
	}

	go func() {
		// 更新推送提醒任务
		if u.pushUsecase != nil {
			req := &HabitReminderCreateRequest{
				UserID:       userID,
				RelatedID:    uh.ID,
				ReminderTime: "09:00", // 默认上午9点提醒
				RepeatRule:   "daily",
			}
			if err = u.pushUsecase.CreateTask(ctx, req); err != nil {
				u.log.Errorf("更新习惯推送提醒任务失败，error: %v，user_habit_id: %v", err, uh.ID)
			}
		}
	}()

	return res, nil
}

// updateUserHabitSnapshot 修改当日习惯快照数据
func (u *UserHabitUsecase) updateUserHabitSnapshot(ctx context.Context, userTime time.Time, uh *UserHabit, tx *gorm.DB) error {
	userTimestamp := tool.GetZeroTimestampByDate(userTime).Unix()
	habitSnaps, err := u.repo.ListSnapshotByParams(ctx, map[string]interface{}{
		"user_habit_id": uh.ID,
		"user_id":       uh.UserID,
		"today_time":    userTimestamp,
	})
	if err != nil {
		return err
	}
	if len(habitSnaps) > 0 {
		uhs := habitSnaps[0]
		uhs.Config = uh.Config
		// 获取当日打卡次数（微习惯类型统计所有阶段打卡）
		punchCount, err := u.repo.CountUserPunch(ctx, map[string]interface{}{
			"habit_snap_id": uhs.ID,
			"record_time":   userTimestamp,
		})
		if err != nil {
			return err
		}
		// 如果当日打卡次数大于等于最大打卡次数，则更新习惯状态为已完成
		if int32(punchCount) >= uhs.Config.PunchMaxCount {
			uhs.IsCompleted = true
		} else {
			uhs.IsCompleted = false
		}
		if err = u.repo.UpdateSnapshot(ctx, tx, uhs.ID, uh.UserID, map[string]interface{}{
			"config":       uhs.Config,
			"is_completed": uhs.IsCompleted,
			"updated_at":   time.Now().Unix(),
		}); err != nil {
			return err
		}
	}

	return nil
}

func (u *UserHabitUsecase) GetUserHabit(ctx context.Context, request *v1.GetUserHabitRequest) (*v1.GetUserHabitReply, error) {
	res := &v1.GetUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	// 获取习惯详情
	uh := &UserHabit{}
	uh.ID = request.Id
	uh.UserID = userID
	if err := u.repo.FirstByID(ctx, uh); err != nil {
		return nil, err
	}

	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		return nil, err
	}
	// 获取指定日期的零点
	userTimestamp := tool.GetZeroTimestampByDate(userTime).Unix()

	// 获取用户习惯快照
	params := map[string]interface{}{
		"user_habit_id": request.Id,
		"user_id":       userID,
		"today_time":    userTimestamp,
	}
	habitSnaps, err := u.repo.ListSnapshotByParams(ctx, params)
	if err != nil {
		return nil, err
	}
	if len(habitSnaps) > 0 {
		uhs := habitSnaps[0]

		// 根据习惯快照 id 获取当日打卡次数
		punchedCountMap, err := u.repo.MapCountUserPunchByTime(ctx, []int32{uhs.ID}, userTimestamp)
		if err != nil {
			u.log.Errorf("获取【用户习惯快照打卡次数】失败，error: %v, user_id: %v", err, userID)
		}
		var punchedTotal int32
		stagePunches := make([]int32, len(uhs.Config.SmallStages)+1)
		if uhs.HabitType == enums.UserHabitSmall {
			for stageID := range uhs.Config.SmallStages {
				punchedKey := fmt.Sprintf("%v-%v", uhs.ID, stageID)
				punchedCount, _ := punchedCountMap[punchedKey]
				stagePunches[stageID] = punchedCount
				punchedTotal += punchedCount
			}
		} else {
			punchedKey := fmt.Sprintf("%v-%v", uhs.ID, 0)
			punchedTotal, _ = punchedCountMap[punchedKey]
		}

		// 获取当月打卡数据
		calendar, err := u.getUserHabitCalendarData(ctx, userID, request.CurrentDate, uhs)
		if err != nil {
			u.log.Errorf("【获取用户习惯详情】获取当月打卡数据失败，err: %v", err)
		}

		// 获取当天时间轴数据
		timeline, err := u.getUserHabitTimelineData(ctx, userID, request.CurrentDate, uhs)
		if err != nil {
			u.log.Errorf("【获取用户习惯详情】获取当天时间轴数据失败，err: %v", err)
		}

		res.Data = &v1.GetUserHabitReply_Data{
			Id:   request.Id,
			Name: uhs.Name,
			Config: &v1.UserHabitConfig{
				PunchMaxCount:         uhs.Config.PunchMaxCount,
				PunchCycleType:        int32(uhs.Config.PunchCycleType),
				PunchCycle:            uhs.Config.PunchCycle,
				SmallStages:           uhs.Config.SmallStages,
				StagePunches:          stagePunches,
				IsAllowCancelPunch:    uhs.Config.IsAllowCancelPunch,
				IsJoinAward:           uhs.Config.IsJoinAward,
				IsSetPrivacy:          uhs.Config.IsSetPrivacy,
				RecordType:            int32(uhs.Config.RecordType),
				PrivacyDisplayMode:    string(uhs.Config.PrivacyDisplayMode),
				PrivacyDisplayContent: uhs.Config.PrivacyDisplayContent,
				EndDate:               uhs.Config.EndDate,
				ReminderTimes:         uhs.Config.ReminderTimes,
				BuddyList:             uhs.Config.BuddyList,
			},
			PunchedTotal: punchedTotal,
			Status:       int32(uh.Status),
			HabitType:    int32(uh.HabitType),
			IsCompleted:  uhs.IsCompleted,
			Desc:         uh.Desc,
			Calendar:     calendar,
			Timeline:     timeline,
			CreatedAt:    int32(uh.CreatedAt),
		}
	} else {
		// 由于日志更新记录仅记录打卡周期变化，所以其它配置依然从最新配置中获取
		currentConfig := uh.Config
		// 获取当月的最新配置，从习惯更新日志中获取习惯配置
		_, endTime := tool.GetMonthStartAndEndTime(userTime)
		where := Where{
			"created_at <":    endTime.AddDate(0, 0, 1).Unix(),
			"user_habit_id =": uh.ID,
		}
		habitLogs, err := u.repo.ListHabitLogByParams(ctx, where)
		if err != nil {
			u.log.Errorf("获取习惯日志失败，error: %v", err)
		}
		// 如果最新配置为按月，则将最新的日志配置作为当前配置
		if len(habitLogs) > 0 && habitLogs[len(habitLogs)-1].Config.PunchCycleType == enums.PunchCycleMonth {
			currentConfig = habitLogs[len(habitLogs)-1].Config
		} else if len(habitLogs) > 0 {
			// 否则获取最近一周的配置
			_, weekEndTime := tool.GetWeekStartAndEndTime(userTime)
			for i := len(habitLogs) - 1; i >= 0; i-- {
				habitLog := habitLogs[i]
				// 从有效日志的开始统计
				if habitLog.CreatedAt < weekEndTime.AddDate(0, 0, 1).Unix() {
					currentConfig = habitLog.Config
					break
				}
			}
		}
		res.Data = &v1.GetUserHabitReply_Data{
			Id:   request.Id,
			Name: uh.Name,
			Config: &v1.UserHabitConfig{
				PunchMaxCount:         currentConfig.PunchMaxCount,
				PunchCycleType:        int32(currentConfig.PunchCycleType),
				PunchCycle:            currentConfig.PunchCycle,
				SmallStages:           uh.Config.SmallStages,
				StagePunches:          make([]int32, len(uh.Config.SmallStages)+1),
				IsAllowCancelPunch:    uh.Config.IsAllowCancelPunch,
				IsJoinAward:           uh.Config.IsJoinAward,
				IsSetPrivacy:          uh.Config.IsSetPrivacy,
				RecordType:            int32(uh.Config.RecordType),
				PrivacyDisplayMode:    string(uh.Config.PrivacyDisplayMode),
				PrivacyDisplayContent: uh.Config.PrivacyDisplayContent,
				EndDate:               uh.Config.EndDate,
				ReminderTimes:         uh.Config.ReminderTimes,
				BuddyList:             uh.Config.BuddyList,
			},
			PunchedTotal: 0,
			Status:       int32(uh.Status),
			HabitType:    int32(uh.HabitType),
			Desc:         uh.Desc,
			Calendar:     make([]*v1.GetUserHabitReply_DayData, 0),
			Timeline:     make([]*v1.GetUserHabitReply_TimeLine, 0),
			CreatedAt:    int32(uh.CreatedAt),
		}
	}

	return res, nil
}

func (u *UserHabitUsecase) getUserHabitCalendarData(ctx context.Context, userID int32, currentDate string, uhs *UserHabitSnapshot) ([]*v1.GetUserHabitReply_DayData, error) {
	calendar := make([]*v1.GetUserHabitReply_DayData, 0, 31)

	return calendar, nil
}

// getUserHabitTimelineData 获取用户当天时间轴数据
// 包含：打卡记录、随记、计时时长
func (u *UserHabitUsecase) getUserHabitTimelineData(ctx context.Context, userID int32, currentDate string, uhs *UserHabitSnapshot) ([]*v1.GetUserHabitReply_TimeLine, error) {
	timeline := make([]*v1.GetUserHabitReply_TimeLine, 0)
	smallStageMap := make(map[int32]string)
	if uhs.HabitType == enums.UserHabitSmall {
		for stageID, stageName := range uhs.Config.SmallStages {
			smallStageMap[int32(stageID)] = stageName
		}
	}

	// 获取打卡记录
	punchLogs, err := u.repo.ListPunchLogByParams(ctx, Where{
		"user_id =":       userID,
		"habit_snap_id =": uhs.ID,
	})
	if err != nil {
		return nil, err
	}
	for _, item := range punchLogs {
		smallStageName, _ := smallStageMap[item.SmallStageID]
		timeline = append(timeline, &v1.GetUserHabitReply_TimeLine{
			Time:           int32(item.RecordAt),
			OperateType:    string(enums.TimelineOperatePunch),
			SmallStageName: smallStageName,
			PunchId:        item.ID,
		})
	}

	// 获取计时记录
	reckons, err := u.repo.ListReckonTimeByParams(ctx, map[string]interface{}{
		"user_id":       userID,
		"habit_snap_id": uhs.ID,
		"is_valid":      true,
	})
	if err != nil {
		return nil, err
	}
	for _, item := range reckons {
		// 计时未结束，不进行展示
		if item.EndAt <= 0 {
			continue
		}
		smallStageName, _ := smallStageMap[item.SmallStageID]
		timeline = append(timeline, &v1.GetUserHabitReply_TimeLine{
			Time:           int32(item.StartAt),
			OperateType:    string(enums.TimelineOperateReckon),
			SmallStageName: smallStageName,
			SmallStageId:   item.SmallStageID,
			Duration:       int32(item.Duration),
			ReckonId:       item.ID,
		})
	}

	// 获取随记记录
	memos, err := u.userPlanetRepo.ListPlanetPostByParams(ctx, map[string]interface{}{
		"user_id":       userID,
		"habit_snap_id": uhs.ID,
		"status":        enums.PlanetPostStatusNormal,
	})
	if err != nil {
		return nil, err
	}
	for _, item := range memos {
		smallStageName, _ := smallStageMap[item.SmallStageID]
		timeline = append(timeline, &v1.GetUserHabitReply_TimeLine{
			Time:           int32(item.CreatedAt),
			OperateType:    string(enums.TimelineOperateMemo),
			SmallStageName: smallStageName,
			MemoContent:    item.Content,
			MemoId:         item.ID,
		})
	}

	// 按时间正序排序
	sort.Slice(timeline, func(i, j int) bool {
		return timeline[i].Time < timeline[j].Time
	})

	return timeline, nil
}

func (u *UserHabitUsecase) ListUserHabit(ctx context.Context, request *v1.ListUserHabitRequest) (*v1.ListUserHabitReply, error) {
	res := &v1.ListUserHabitReply{
		Data: make([]*v1.ListUserHabitReply_Data, 0),
	}

	userID := auth.GetUserIDFromCtx(ctx)
	where := Where{
		"user_id =": userID,
	}
	if request.Status != 0 {
		where["status ="] = request.Status
	}
	uhs, err := u.repo.List(ctx, where)
	if err != nil {
		return nil, err
	}
	if len(uhs) == 0 {
		return res, nil
	}

	// 获取用户所在时区的时间
	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return res, nil
	}

	// 获取打卡天数
	userHabitIds := make([]int32, 0, len(uhs))
	for _, item := range uhs {
		userHabitIds = append(userHabitIds, item.ID)
	}
	where = Where{
		"user_habit_id in": userHabitIds,
		"is_completed =":   true,
	}
	mapPunchDayCount, err := u.repo.MapCountUserPunchByUserHabitIDs(ctx, where)
	if err != nil {
		return nil, err
	}

	// 获取用户习惯完成详情
	overCompleteIds, unCompleteIds := u.sortUserHabitByDone(ctx, userHabitIds, tool.GetZeroTimestampByDate(userTime))

	// 获取总打卡天数

	var statusNormalData []*v1.ListUserHabitReply_Data

	// 获取缓存用户信息
	cacheUser, err := u.commonUsecase.GetUserInfoByUserID(ctx, userID)
	if err != nil {
		u.log.Errorf("获取用户信息失败，error: %v, user_id: %v", err, userID)
	}

	// 单次遍历构建数据
	for _, item := range uhs {
		punchedDayCount, _ := mapPunchDayCount[item.ID]
		var name = item.Name
		if item.Config.IsSetPrivacy && cacheUser.IsSetPrivacy {
			if item.Config.PrivacyDisplayMode == enums.PrivacyDisplayModeTypeCustom {
				name = item.Config.PrivacyDisplayContent
			} else {
				name = tool.MaskString(name)
			}
		}
		var endAt int32
		if item.Status == enums.UserHabitStatusEnd {
			endAt = int32(item.UpdatedAt)
		}
		if item.Status == enums.UserHabitStatusPause {
			endAt = int32(item.UpdatedAt)
		}
		healthStatus := "normal"
		category := u.categorizaHabit(item, overCompleteIds, unCompleteIds)
		switch category {
		case "top":
			healthStatus = "exceed"
		case "low":
			healthStatus = "poor"
		case "track":
			healthStatus = "normal"
		}
		data := &v1.ListUserHabitReply_Data{
			Id:             item.ID,
			Name:           name,
			Status:         int32(item.Status),
			PunchCycleType: int32(item.Config.PunchCycleType),
			PunchCycle:     item.Config.PunchCycle,
			PunchedDays:    punchedDayCount,
			PunchAllDays:   item.Config.PunchMaxCount,
			IsSetPrivacy:   item.Config.IsSetPrivacy && cacheUser.IsSetPrivacy,
			CreatedAt:      int32(item.CreatedAt),
			EndAt:          endAt,
			StreakDays:     3,
			HealthStatus:   healthStatus,
		}
		if item.Status == enums.UserHabitStatusNormal {
			statusNormalData = append(statusNormalData, data)
		} else {
			res.Data = append(res.Data, data)
		}
	}

	// 合并结果
	res.Data = append(statusNormalData, res.Data...)

	return res, nil
}

func (u *UserHabitUsecase) DeleteUserHabit(ctx context.Context, request *v1.DeleteUserHabitRequest) (*v1.DeleteUserHabitReply, error) {
	res := &v1.DeleteUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	uh := &UserHabit{}
	uh.ID = request.Id
	uh.UserID = userID
	if err := u.repo.Delete(ctx, uh); err != nil {
		return nil, err
	}

	// 需要将习惯关联的行为都删除
	go func() {
		// 删除推送提醒任务
		if u.pushUsecase != nil {
			if err := u.pushUsecase.DeleteTasksByUserHabit(ctx, userID, uh.ID); err != nil {
				u.log.Errorf("删除习惯推送提醒任务失败，error: %v，user_habit_id: %v", err, uh.ID)
			}
		}
	}()

	return res, nil
}

// generateHabitSnapshotByUserHabitID，根据用户打卡时间获取或生成当日习惯快照
func (u *UserHabitUsecase) generateHabitSnapshotByUserHabitID(ctx context.Context, userID, userHabitID int32, userTime time.Time) (*UserHabitSnapshot, error) {
	uhs := &UserHabitSnapshot{}
	now := time.Now()

	// 获取用户习惯详情
	uh := &UserHabit{}
	uh.ID = userHabitID
	uh.UserID = userID
	if err := u.repo.FirstByID(ctx, uh); err != nil {
		return nil, err
	}

	userTimestamp := tool.GetZeroTimestampByDate(userTime).Unix()
	// 获取用户习惯快照
	params := map[string]interface{}{
		"user_habit_id": uh.ID,
		"user_id":       uh.UserID,
		"today_time":    userTimestamp,
	}
	habitSnaps, err := u.repo.ListSnapshotByParams(ctx, params)
	if err != nil {
		return nil, err
	}
	if len(habitSnaps) > 0 {
		uhs = habitSnaps[0]
	} else {
		uhs = &UserHabitSnapshot{
			BasicModel: BasicModel{
				CreatedAt: now.Unix(),
				UpdatedAt: now.Unix(),
			},
			UserHabitID: uh.ID,
			UserID:      uh.UserID,
			Name:        uh.Name,
			Config:      uh.Config,
			HabitType:   uh.HabitType,
			IsCompleted: false,
			TodayTime:   userTimestamp,
			Status:      enums.UserHabitStatusNormal,
			TimeZone:    uh.TimeZone,
		}
		if err = u.repo.CreateSnapshot(ctx, uhs); err != nil {
			return nil, stderr.New(fmt.Sprintf("生成用户习惯快照失败，error: %v", err))
		}
	}

	return uhs, nil
}

func (u *UserHabitUsecase) CreateUserHabitMemo(ctx context.Context, request *v1.CreateUserHabitMemoRequest) (*v1.CreateUserHabitMemoReply, error) {
	res := &v1.CreateUserHabitMemoReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	// 获取用户所在时区的零点
	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return nil, err
	}
	uhs, err := u.generateHabitSnapshotByUserHabitID(ctx, userID, request.UserHabitId, userTime)
	if err != nil {
		return nil, err
	}

	// TODO: 随记会同步到星球中
	now := time.Now()
	imgUrls := make([]string, 0)
	if len(request.Images) > 0 {
		imgUrls = request.Images
	}
	planetPost := &PlanetPost{
		UserID:       userID,
		Content:      request.Content,
		ImgUrls:      imgUrls,
		HabitSnapID:  uhs.ID,
		IsPublic:     false,
		Status:       enums.PlanetPostStatusNormal,
		SmallStageID: request.SmallStageId,
	}
	planetPost.UpdatedAt = now.Unix()
	planetPost.CreatedAt = now.Unix()
	if err = u.userPlanetRepo.CreatePlanetPost(ctx, planetPost); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserHabitUsecase) PunchUserHabit(ctx context.Context, request *v1.PunchUserHabitRequest) (*v1.PunchUserHabitReply, error) {
	res := &v1.PunchUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return nil, err
	}
	uhs, err := u.generateHabitSnapshotByUserHabitID(ctx, userID, request.UserHabitId, userTime)
	if err != nil {
		return nil, err
	}

	// 获取用户所在时区的零点
	userTimestamp := tool.GetZeroTimestampByDate(userTime).Unix()
	up := &UserPunchLog{
		UserHabitID:  uhs.UserHabitID,
		HabitSnapID:  uhs.ID,
		UserID:       userID,
		RecordAt:     userTime.Unix(),
		RecordTime:   userTimestamp,
		SmallStageID: request.SmallStageId,
	}
	now := time.Now()
	up.UpdatedAt = now.Unix()
	up.CreatedAt = now.Unix()
	tx := u.db.Begin()
	if err = u.repo.CreatePunchLog(ctx, tx, up); err != nil {
		tx.Rollback()
		return nil, err
	}

	// 获取当日打卡次数（微习惯类型统计所有阶段打卡）
	punchCount, err := u.repo.CountUserPunch(ctx, map[string]interface{}{
		"habit_snap_id": uhs.ID,
		"record_time":   userTimestamp,
	})
	if err != nil {
		return nil, err
	}
	// 如果当日打卡次数大于等于最大打卡次数，则更新习惯状态为已完成
	if int32(punchCount)+1 >= uhs.Config.PunchMaxCount && uhs.IsCompleted == false {
		if err = u.repo.UpdateSnapshot(ctx, tx, uhs.ID, userID, map[string]interface{}{
			"is_completed": true,
			"updated_at":   now.Unix(),
		}); err != nil {
			tx.Rollback()
			return nil, err
		}

		// 异步重新计算统计数据（从打卡日期到昨天）
		u.AsyncRecalculateStatsFromDate(userID, userTime, uhs.TimeZone)
	}
	tx.Commit()

	return res, nil
}

func (u *UserHabitUsecase) UpdatePunchUserHabit(ctx context.Context, request *v1.UpdatePunchUserHabitRequest) (*v1.UpdatePunchUserHabitReply, error) {
	res := &v1.UpdatePunchUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	// 获取打卡记录
	punchLog, err := u.repo.FirstPunchLogByParams(ctx, map[string]interface{}{
		"user_id": userID,
		"id":      request.PunchId,
	})
	if err != nil {
		return nil, err
	}
	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return nil, err
	}
	punchLog.RecordAt = userTime.Unix()
	punchLog.UpdatedAt = time.Now().Unix()
	if err = u.repo.UpdatePunchLog(ctx, punchLog); err != nil {
		return nil, err
	}

	return res, nil

}

func (u *UserHabitUsecase) CancelPunchUserHabit(ctx context.Context, request *v1.CancelPunchUserHabitRequest) (*v1.CancelPunchUserHabitReply, error) {
	res := &v1.CancelPunchUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	uhs := &UserHabitSnapshot{}
	deletePunchLogParams := map[string]interface{}{}
	if request.PunchId > 0 {
		// 获取打卡记录
		punchLog, err := u.repo.FirstPunchLogByParams(ctx, map[string]interface{}{
			"user_id": userID,
			"id":      request.PunchId,
		})
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		if punchLog.ID == 0 {
			return res, nil
		}

		uhs.ID = punchLog.HabitSnapID
		uhs.UserID = userID
		if err = u.repo.FirstSnapshotByID(ctx, uhs); err != nil {
			return nil, err
		}
		deletePunchLogParams["id"] = request.PunchId
	} else {
		// 获取用户所在时区的零点
		userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
		if err != nil {
			u.log.Errorf("time.Parse error: %v, req: %v", err, request)
			return nil, err
		}
		uhs, err = u.generateHabitSnapshotByUserHabitID(ctx, userID, request.UserHabitId, userTime)
		if err != nil {
			return nil, err
		}
		deletePunchLogParams["habit_snap_id"] = uhs.ID
		deletePunchLogParams["small_stage_id"] = request.SmallStageId
	}

	// 获取当日打卡次数，统计总数
	punchCount, err := u.repo.CountUserPunch(ctx, map[string]interface{}{
		"habit_snap_id": uhs.ID,
	})
	if err != nil {
		return nil, err
	}

	now := time.Now()
	// 判断是否已打卡
	// 未打卡
	if punchCount == 0 {
		if err = u.repo.UpdateSnapshot(ctx, u.db, uhs.ID, userID, map[string]interface{}{
			"is_completed": false,
			"updated_at":   now.Unix(),
		}); err != nil {
			return nil, err
		}

		// 异步重新计算统计数据（从打卡日期到昨天）
		punchDate := time.Unix(uhs.TodayTime, 0)
		u.AsyncRecalculateStatsFromDate(userID, punchDate, uhs.TimeZone)

		return res, nil
	}
	// 判断是否允许取消打卡
	if !uhs.Config.IsAllowCancelPunch {
		return res, nil
	}

	tx := u.db.Begin()
	// 删除最新的一条打卡记录
	if err = u.repo.DeletePunchLog(ctx, tx, deletePunchLogParams); err != nil {
		tx.Rollback()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return res, nil
		}
		return nil, err
	}

	// 如果当日打卡次数小于最大打卡次数，则更新习惯状态为未完成
	if int32(punchCount)-1 < uhs.Config.PunchMaxCount && uhs.IsCompleted == true {
		if err = u.repo.UpdateSnapshot(ctx, tx, uhs.ID, userID, map[string]interface{}{
			"is_completed": false,
			"updated_at":   now.Unix(),
		}); err != nil {
			tx.Rollback()
			return nil, err
		}

		// 异步重新计算统计数据（从打卡日期到昨天）
		punchDate := time.Unix(uhs.TodayTime, 0)
		u.AsyncRecalculateStatsFromDate(userID, punchDate, uhs.TimeZone)
	}
	tx.Commit()

	return res, nil
}

func (u *UserHabitUsecase) ReckonUserHabit(ctx context.Context, request *v1.ReckonUserHabitRequest) (*v1.ReckonUserHabitReply, error) {
	res := &v1.ReckonUserHabitReply{
		Data: &v1.ReckonUserHabitReply_Data{},
	}

	userID := auth.GetUserIDFromCtx(ctx)
	// 获取用户所在时区的零点
	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return nil, err
	}
	uhs, err := u.generateHabitSnapshotByUserHabitID(ctx, userID, request.UserHabitId, userTime)
	if err != nil {
		return nil, err
	}

	// 查询是否存在其它习惯正在计时，只允许一个习惯计时
	params := map[string]interface{}{
		"user_id":  userID,
		"end_at":   0,
		"is_valid": true,
	}
	existedUrts, err := u.repo.ListReckonTimeByParams(ctx, params)
	if err != nil {
		return nil, err
	}
	urt := &UserReckonTime{}
	for _, item := range existedUrts {
		if item.HabitSnapID != uhs.ID || item.SmallStageID != request.SmallStageId {
			return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrReckonUserHabitMsg, nil))
		} else {
			urt = item
		}
	}

	// 如果存在计时，且入参 is_end=false，则返回已计时
	if urt.ID > 0 && !request.IsEnd {
		res.Data.IsReckoning = true
		res.Data.ReckonDuration = int32(userTime.Unix() - urt.StartAt)
		return res, nil
	}

	// 如果不存在计时，且入参 is_end=true，则返回未计时
	if urt.ID == 0 && request.IsEnd {
		return res, nil
	}

	now := time.Now()
	if urt.ID == 0 {
		// 不存在则创建
		urt.UserID = userID
		urt.UserHabitID = uhs.UserHabitID
		urt.HabitSnapID = uhs.ID
		urt.StartAt = userTime.Unix()
		urt.EndAt = 0
		urt.IsValid = true
		urt.CreatedAt = now.Unix()
		urt.UpdatedAt = now.Unix()
		urt.SmallStageID = request.SmallStageId
		if err = u.repo.CreateReckonTime(ctx, urt); err != nil {
			return nil, err
		}
		res.Data.IsReckoning = true

		// 创建定时任务，防止当天未手动结束计时
		go u.createReckonCronTask(ctx, uhs, urt)
	} else {
		// 存在则结束计时
		duration := userTime.Unix() - urt.StartAt
		// 如果入参传递的时长小于后端自行计算的时长，则以前端传递的为准（用户可能在点击暂停界面停留太久时间）
		if int64(request.ReckonDuration) < duration {
			duration = int64(request.ReckonDuration)
		}
		urt.Duration = int(duration)
		urt.EndAt = userTime.Unix()
		urt.UpdatedAt = now.Unix()
		if err = u.repo.UpdateReckonTime(ctx, urt); err != nil {
			return nil, err
		}

		// 删除【结束用户计时】任务
		go u.deleteReckonCronTask(ctx, uhs, urt)
	}

	return res, nil
}

func (u *UserHabitUsecase) createReckonCronTask(ctx context.Context, habitSnap *UserHabitSnapshot, userReckon *UserReckonTime) {
	now := time.Now()
	cronTasks := make([]*CronTask, 0, 1)
	triggerTime := time.Unix(habitSnap.TodayTime, 0).AddDate(0, 0, 1).UTC()
	cronTasks = append(cronTasks, &CronTask{
		BasicModel: BasicModel{
			CreatedAt: now.Unix(),
			UpdatedAt: now.Unix(),
		},
		UserID:      habitSnap.UserID,
		Payload:     CronTaskPayload{},
		TriggerTime: triggerTime.Format(time.TimeOnly),
		TodayTime:   habitSnap.TodayTime,
		Tag:         fmt.Sprintf("%v%v", enums.CronTaskReckonTimePrefix, userReckon.ID),
		Status:      enums.CronTaskStatusTypeProcessing,
		ThirdID:     fmt.Sprintf("%v", userReckon.ID),
		TaskType:    enums.CronTaskTypeUserReckon,
	})

	if err := u.cronTaskRepo.BatchCreate(ctx, cronTasks); err != nil {
		u.log.Errorf("生成【结束用户计时】定时任务失败，error: %v", err)
	}

	for _, item := range cronTasks {
		_, err := task.Scheduler.Every(1).Day().At(item.TriggerTime).Tag(item.Tag).Do(func(taskID int32) {
			runCronTaskBody := map[string]interface{}{
				"task_id": taskID,
			}
			if err := httpClient.RunCronTask(runCronTaskBody, u.confData.Task.HttpAddr); err != nil {
				u.log.Errorf("user_habit_usecase: 执行【结束用户计时】定时任务失败, error: %v, task_id: %v", err, taskID)
			}
		}, item.ID)
		if err != nil {
			u.log.Errorf("user_habit_usecase: 添加 scheduler 失败, error: %v", err)
		}
	}
}

func (u *UserHabitUsecase) deleteReckonCronTask(ctx context.Context, habitSnap *UserHabitSnapshot, userReckon *UserReckonTime) {
	// 修改定时任务状态
	where := Where{
		"third_id =":  fmt.Sprintf("%v", userReckon.ID),
		"task_type =": enums.CronTaskTypeUserReckon,
		"status in":   []enums.CronTaskStatusType{enums.CronTaskStatusTypePause, enums.CronTaskStatusTypeProcessing},
	}
	cronTasks, err := u.cronTaskRepo.FindCronTaskByParams(where)
	if err != nil {
		u.log.Errorf("获取 cron_task 列表失败，error: %v, habit_snap_id: %v", err, habitSnap.ID)
	}
	cronTaskIDs := make([]int32, 0, len(cronTasks))
	for _, item := range cronTasks {
		cronTaskIDs = append(cronTaskIDs, item.ID)
	}
	cronTask := &CronTask{}
	cronTask.Status = enums.CronTaskStatusTypeEnd
	cronTask.UpdatedAt = time.Now().Unix()
	if err = u.cronTaskRepo.BatchUpdateByID(ctx, cronTaskIDs, cronTask); err != nil {
		u.log.Errorf("修改【结束用户计时】任务状态失败，error: %v, task_id: %v", err, cronTask.ID)
	}

	// 删除用户习惯定时任务
	for _, item := range cronTasks {
		if err = task.Scheduler.RemoveByTag(item.Tag); err != nil {
			u.log.Errorf("user_habit_usecase: 删除 scheduler 失败, error: %v, task_id: %v", err, item.ID)
		}
	}
}

func (u *UserHabitUsecase) CancelReckonUserHabit(ctx context.Context, request *v1.CancelReckonUserHabitRequest) (*v1.CancelReckonUserHabitReply, error) {
	res := &v1.CancelReckonUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	// 获取用户所在时区的零点
	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return res, nil
	}
	uhs, err := u.generateHabitSnapshotByUserHabitID(ctx, userID, request.UserHabitId, userTime)
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"user_id":        userID,
		"habit_snap_id":  uhs.ID,
		"end_at":         0,
		"is_valid":       true,
		"small_stage_id": request.SmallStageId,
	}
	existedUrts, err := u.repo.ListReckonTimeByParams(ctx, params)
	if err != nil {
		return nil, err
	}
	// 是否存在正在计时的记录
	if len(existedUrts) > 0 {
		urt := existedUrts[0]
		// 存在则删除数据
		urt.IsValid = false
		urt.EndAt = userTime.Unix()
		urt.UpdatedAt = time.Now().Unix()
		if err = u.repo.UpdateReckonTime(ctx, urt); err != nil {
			return nil, err
		}

		// 删除【结束用户计时】任务
		go u.deleteReckonCronTask(ctx, uhs, urt)
	}

	return res, nil
}

// ListUserHabitSnapshot 根据日期获取用户习惯列表
// 1. 解析用户所在日期的零点
// 2. 根据用户日期获取用户习惯，判断是否当日展示。如果选择全部标签，则不用判断，直接展示
// 3. 根据用户习惯获取用户习惯快照信息
// 4. 对于不影响用户使用的流程报错，打印日志，跳过
func (u *UserHabitUsecase) ListUserHabitSnapshot(ctx context.Context, request *v1.ListUserHabitSnapshotRequest) (*v1.ListUserHabitSnapshotReply, error) {
	res := &v1.ListUserHabitSnapshotReply{
		Data: &v1.ListUserHabitSnapshotReply_Data{
			TopHabits:   make([]*v1.ListUserHabitSnapshotReply_HabitItem, 0),
			LowHabits:   make([]*v1.ListUserHabitSnapshotReply_HabitItem, 0),
			TrackHabits: make([]*v1.ListUserHabitSnapshotReply_HabitItem, 0),
			TodayStatisticData: &v1.TodayStatistic{
				SmallHabit:  &v1.TodayStatistic_HabitDetail{},
				NormalHabit: &v1.TodayStatistic_HabitDetail{},
			},
			PendingMotivationHabits: make([]int32, 0),
		},
	}

	userID := auth.GetUserIDFromCtx(ctx)
	// 获取用户所在时区的时间
	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return res, nil
	}

	// 获取今日用户习惯快照
	timestamp := tool.GetZeroTimestampByDate(userTime).Unix()
	snapshotParams := map[string]interface{}{
		"today_time": timestamp,
		"user_id":    userID,
	}
	uhs, err := u.repo.ListSnapshotByParams(ctx, snapshotParams)
	if err != nil {
		u.log.Errorf("获取用户习惯快照失败，error: %v, user_id: %v", err, userID)
	}
	mapHabitSnap := make(map[int32]*UserHabitSnapshot)
	habitSnapIds := make([]int32, 0)
	for _, item := range uhs {
		mapHabitSnap[item.UserHabitID] = item
		habitSnapIds = append(habitSnapIds, item.ID)
	}

	// 获取当日可展示的用户习惯
	// 如果选择全部标签，则不用判断，直接展示
	allDisplay := false
	if request.LabelType == int32(enums.UserHabitLabelAll) || request.LabelType == int32(enums.UserHabitLabelDone) {
		allDisplay = true
	}
	userHabits, err := u.listDisplayUserHabit(ctx, userID, userTime, mapHabitSnap, allDisplay)
	if err != nil {
		return nil, err
	}
	if len(userHabits) == 0 {
		return res, nil
	}

	// 根据习惯快照 id 获取当日打卡次数
	punchedCountMap := make(map[string]int32)
	if len(habitSnapIds) > 0 {
		punchedCountMap, err = u.repo.MapCountUserPunchByTime(ctx, habitSnapIds, timestamp)
		if err != nil {
			u.log.Errorf("获取【用户习惯快照打卡次数】失败，error: %v, user_id: %v", err, userID)
		}
	}

	// 获取当前正在计时的习惯
	reckonTimeParams := map[string]interface{}{
		"user_id":  userID,
		"end_at":   0,
		"is_valid": true,
	}
	urts, err := u.repo.ListReckonTimeByParams(ctx, reckonTimeParams)
	if err != nil {
		u.log.Errorf("获取【用户正在计时的习惯】失败，error: %v, user_id: %v", err, userID)
	}
	currentUrt := &UserReckonTime{}
	if len(urts) > 0 {
		currentUrt = urts[0]
	}

	// 获取缓存用户信息
	cacheUser, err := u.commonUsecase.GetUserInfoByUserID(ctx, userID)
	if err != nil {
		u.log.Errorf("获取用户信息失败，error: %v, user_id: %v", err, userID)
	}

	// 保存微习惯和普通习惯的完成情况
	var smallAllCount int32
	var smallDoneCount int32
	var normalAllCount int32
	var normalDoneCount int32
	calculateHabitIds := make([]int32, 0, len(userHabits))
	validUserHabits := make([]*UserHabit, 0, len(userHabits))
	for _, item := range userHabits {
		habitSnap, _ := mapHabitSnap[item.ID]
		// 如果设置需要弹出奖励，且当天必须打卡，则返回需要展示
		if item.Config.IsJoinAward && item.IsNecessary {
			res.Data.IsNeedShow = true
			if habitSnap == nil || habitSnap.IsCompleted == false {
				res.Data.PendingMotivationHabits = append(res.Data.PendingMotivationHabits, item.ID)
			}
		}
		// 对于所有标签下面的数据，仅统计当天必须完成的习惯，如果非必须完成的习惯打卡了，也需要统计
		if item.HabitType == enums.UserHabitSmall {
			if item.IsNecessary || (habitSnap != nil && habitSnap.IsCompleted == true) {
				smallAllCount += 1
			}
			if habitSnap != nil && habitSnap.IsCompleted == true {
				smallDoneCount += 1
			}
		} else {
			if item.IsNecessary || (habitSnap != nil && habitSnap.IsCompleted == true) {
				normalAllCount += 1
			}
			if habitSnap != nil && habitSnap.IsCompleted == true {
				normalDoneCount += 1
			}
		}
		if request.LabelType == int32(enums.UserHabitLabelDone) && (habitSnap == nil || habitSnap.IsCompleted == false) {
			continue
		} else if request.LabelType == int32(enums.UserHabitLabelUndo) && habitSnap != nil && habitSnap.IsCompleted == true {
			continue
		}
		if item.IsDisplay == false {
			continue
		}
		calculateHabitIds = append(calculateHabitIds, item.ID)
		validUserHabits = append(validUserHabits, item)
	}
	if len(calculateHabitIds) > 0 {
		// 获取用户习惯完成详情
		overCompleteIds, unCompleteIds := u.sortUserHabitByDone(ctx, calculateHabitIds, tool.GetZeroTimestampByDate(userTime))
		for _, item := range validUserHabits {
			habitSnap, _ := mapHabitSnap[item.ID]
			smallStages := make([]*v1.ListUserHabitSnapshotReply_SmallStageItem, 0)
			var punchedCount int32
			isReckoning := false
			var reckonDuration int32
			if item.HabitType == enums.UserHabitSmall {
				for stageID, stageName := range item.Config.SmallStages {
					isReckoning = false
					reckonDuration = 0
					var stagePunchedCount int32
					if habitSnap != nil {
						punchedKey := fmt.Sprintf("%v-%v", habitSnap.ID, stageID)
						stagePunchedCount, _ = punchedCountMap[punchedKey]
						if currentUrt.HabitSnapID == habitSnap.ID && int(currentUrt.SmallStageID) == stageID {
							isReckoning = true
							reckonDuration = int32(time.Now().Unix() - currentUrt.StartAt)
						}
					}
					if item.Config.IsSetPrivacy && cacheUser.IsSetPrivacy {
						if item.Config.PrivacyDisplayMode == enums.PrivacyDisplayModeTypeCustom {
							stageName = item.Config.PrivacyDisplayContent
						} else {
							stageName = tool.MaskString(stageName)
						}
					}
					smallStages = append(smallStages, &v1.ListUserHabitSnapshotReply_SmallStageItem{
						StageId:      int32(stageID),
						Name:         stageName,
						PunchedCount: stagePunchedCount,
						IsReckoning:  isReckoning,
					})
					// 累加微习惯阶段打卡总数
					punchedCount += stagePunchedCount
				}
			} else {
				if habitSnap != nil {
					punchedKey := fmt.Sprintf("%v-%v", habitSnap.ID, 0)
					punchedCount, _ = punchedCountMap[punchedKey]
					if currentUrt.HabitSnapID == habitSnap.ID && int(currentUrt.SmallStageID) == 0 {
						isReckoning = true
						reckonDuration = int32(time.Now().Unix() - currentUrt.StartAt)
					}
				}
			}
			var name = item.Name
			if item.Config.IsSetPrivacy && cacheUser.IsSetPrivacy {
				if item.Config.PrivacyDisplayMode == enums.PrivacyDisplayModeTypeCustom {
					name = item.Config.PrivacyDisplayContent
				} else {
					name = tool.MaskString(name)
				}
			}
			habitItem := &v1.ListUserHabitSnapshotReply_HabitItem{
				Id:                 item.ID,
				Name:               name,
				DoneCount:          punchedCount,
				AllCount:           item.Config.PunchMaxCount,
				HabitType:          int32(item.HabitType),
				SmallStages:        smallStages,
				IsReckoning:        isReckoning,
				Duration:           reckonDuration,
				IsAllowCancelPunch: item.Config.IsAllowCancelPunch,
				IsNecessary:        item.IsNecessary,
				IsSetPrivacy:       item.Config.IsSetPrivacy && cacheUser.IsSetPrivacy,
				IsJoinAward:        item.Config.IsJoinAward,
				PunchCycleType:     int32(item.Config.PunchCycleType),
				CreatedAt:          int32(item.CreatedAt),
				EndDate:            item.Config.EndDate,
			}
			category := u.categorizaHabit(item, overCompleteIds, unCompleteIds)
			switch category {
			case "top":
				res.Data.TopHabits = append(res.Data.TopHabits, habitItem)
			case "low":
				res.Data.LowHabits = append(res.Data.LowHabits, habitItem)
			case "track":
				res.Data.TrackHabits = append(res.Data.TrackHabits, habitItem)
			}
		}

		// 按必须完成正序排序
		sort.SliceStable(res.Data.TopHabits, func(i, j int) bool {
			return res.Data.TopHabits[i].IsNecessary && !res.Data.TopHabits[j].IsNecessary
		})
		sort.SliceStable(res.Data.LowHabits, func(i, j int) bool {
			return res.Data.LowHabits[i].IsNecessary && !res.Data.LowHabits[j].IsNecessary
		})
		sort.SliceStable(res.Data.TrackHabits, func(i, j int) bool {
			return res.Data.TrackHabits[i].IsNecessary && !res.Data.TrackHabits[j].IsNecessary
		})
	}
	res.Data.TodayStatisticData.SmallHabit.AllCount = smallAllCount
	res.Data.TodayStatisticData.SmallHabit.DoneCount = smallDoneCount
	res.Data.TodayStatisticData.SmallHabit.Per = tool.CalculatePercentage(smallDoneCount, smallAllCount)
	res.Data.TodayStatisticData.NormalHabit.AllCount = normalAllCount
	res.Data.TodayStatisticData.NormalHabit.DoneCount = normalDoneCount
	res.Data.TodayStatisticData.NormalHabit.Per = tool.CalculatePercentage(normalDoneCount, normalAllCount)
	// 统计最近一周完成情况
	weekData, err := u.getWeekData(ctx, userID, userTime)
	if err != nil {
		u.log.Errorf("获取最近七天习惯完成率数据失败: %v", err)
	}
	res.Data.TodayStatisticData.WeekData = weekData

	return res, nil
}

// getWeekData 获取最近七天的习惯完成率数据
// userTime 参数用于指定用户所在的时间，以此为基准获取最近七天的数据
func (u *UserHabitUsecase) getWeekData(ctx context.Context, userID int32, userTime time.Time) ([]*v1.TodayStatistic_WeekData, error) {
	// 获取最近七天的日期范围
	today := time.Date(userTime.Year(), userTime.Month(), userTime.Day(), 0, 0, 0, 0, userTime.Location())

	// 构建返回数据
	weekData := make([]*v1.TodayStatistic_WeekData, 0, 7)

	// 查询最近七天的统计数据
	startDate := today.AddDate(0, 0, -6)
	startTimestamp := startDate.Unix()
	endTimestamp := today.AddDate(0, 0, 1).Unix() // 包含今天

	stats, err := u.repo.GetUserHabitDailyStatsByDateRange(ctx, userID, startTimestamp, endTimestamp)
	if err != nil {
		u.log.Errorf("获取用户习惯统计数据失败: %v", err)
		// 继续处理，后面会计算缺失的数据
	}

	// 将统计数据转换为map，方便查找
	statsMap := make(map[int64]*UserHabitDailyStats)
	for _, stat := range stats {
		statsMap[stat.DateTime] = stat
	}

	// 处理最近7天的数据
	for i := 6; i >= 0; i-- {
		targetDate := today.AddDate(0, 0, -i)
		targetTimestamp := targetDate.Unix()

		// 获取星期几
		weekday := targetDate.Weekday()
		// 将星期几转换为中文格式
		weekdayStr := ""
		switch weekday {
		case time.Sunday:
			weekdayStr = "日"
		case time.Monday:
			weekdayStr = "一"
		case time.Tuesday:
			weekdayStr = "二"
		case time.Wednesday:
			weekdayStr = "三"
		case time.Thursday:
			weekdayStr = "四"
		case time.Friday:
			weekdayStr = "五"
		case time.Saturday:
			weekdayStr = "六"
		}

		// 创建日数据
		dayData := &v1.TodayStatistic_WeekData{
			Day:      weekdayStr,
			IsToday:  i == 0, // 是否为今天
			Progress: "--",
		}

		// 今日数据不进行统计
		if !dayData.IsToday {
			// 检查是否有统计数据
			if stat, ok := statsMap[targetTimestamp]; ok {
				// 使用已有统计数据
				if stat.TotalHabits > 0 {
					progress := int(float64(stat.CompletedHabits) / float64(stat.TotalHabits) * 100)
					dayData.Progress = fmt.Sprintf("%d%%", progress)
				}
			} else {
				// 计算该日期的统计数据
				totalHabits, completedHabits, err := u.calculateDailyStats(ctx, userID, targetTimestamp)
				if err != nil {
					u.log.Errorf("计算用户习惯统计数据失败: %v", err)
				} else {
					if totalHabits > 0 {
						progress := int(float64(completedHabits) / float64(totalHabits) * 100)
						dayData.Progress = fmt.Sprintf("%d%%", progress)
					}

					// 保存统计数据到数据库
					newStat := &UserHabitDailyStats{
						UserID:          userID,
						DateTime:        targetTimestamp,
						TotalHabits:     totalHabits,
						CompletedHabits: completedHabits,
						BasicModel: BasicModel{
							CreatedAt: time.Now().Unix(),
							UpdatedAt: time.Now().Unix(),
						},
					}

					err = u.repo.SaveUserHabitDailyStats(ctx, newStat)
					if err != nil {
						u.log.Errorf("保存用户习惯统计数据失败: %v", err)
					}
				}
			}
		}

		weekData = append(weekData, dayData)
	}

	return weekData, nil
}

// CalculateAndSaveUserHabitStats 计算并保存用户习惯统计数据
func (u *UserHabitUsecase) CalculateAndSaveUserHabitStats(ctx context.Context, userID, userHabitID int32) error {
	// 获取习惯详情
	uh := &UserHabit{}
	uh.ID = userHabitID
	uh.UserID = userID
	if err := u.repo.FirstByID(ctx, uh); err != nil {
		return err
	}

	// 计算 AllCount（总应打卡天数）
	allCount, err := u.calculateHabitAllCount(ctx, uh)
	if err != nil {
		u.log.Errorf("计算习惯总应打卡天数失败: %v", err)
		return err
	}

	// 计算 DoneCount（已完成打卡天数）
	doneCount, err := u.calculateHabitDoneCount(ctx, userHabitID)
	if err != nil {
		u.log.Errorf("计算习惯已完成打卡天数失败: %v", err)
		return err
	}

	// 计算 PersistLongDays（最长连续天数）和相关日期，以及当前坚持天数
	persistLongDays, persistStartDate, persistEndDate, currentPersistDays, err := u.calculateHabitPersistDays(ctx, uh)
	if err != nil {
		u.log.Errorf("计算习惯最长连续天数失败: %v", err)
		return err
	}

	// 保存统计数据
	stats := &UserHabitStats{
		BasicModel: BasicModel{
			CreatedAt: time.Now().Unix(),
			UpdatedAt: time.Now().Unix(),
		},
		UserID:             userID,
		UserHabitID:        userHabitID,
		AllCount:           int(allCount),
		DoneCount:          int(doneCount),
		PersistLongDays:    int(persistLongDays),
		PersistStartDate:   persistStartDate,
		PersistEndDate:     persistEndDate,
		CurrentPersistDays: int(currentPersistDays),
	}

	return u.repo.SaveUserHabitStats(ctx, stats)
}

// calculateHabitAllCount 计算习惯总应打卡天数
func (u *UserHabitUsecase) calculateHabitAllCount(ctx context.Context, habit *UserHabit) (int32, error) {
	// 使用现有的 getUserHabitStatisticByLabel 方法计算
	habitStatisticAll := &HabitStatisticAll{}
	currentTime := time.Now()
	if habit.Status == enums.UserHabitStatusEnd {
		currentTime = time.Unix(habit.UpdatedAt, 0)
	}

	err := u.getUserHabitStatisticByLabel(ctx, habit, enums.HabitDetailLabelTypeAll, currentTime, habitStatisticAll)
	if err != nil {
		return 0, err
	}

	return habitStatisticAll.PunchCount, nil
}

// calculateHabitDoneCount 计算习惯已完成打卡天数
func (u *UserHabitUsecase) calculateHabitDoneCount(ctx context.Context, userHabitID int32) (int32, error) {
	where := Where{
		"user_habit_id =": userHabitID,
		"is_completed =":  true,
	}
	mapPunchDayCount, err := u.repo.MapCountUserPunchByUserHabitIDs(ctx, where)
	if err != nil {
		return 0, err
	}

	return mapPunchDayCount[userHabitID], nil
}

// calculateHabitPersistDays 计算习惯最长连续天数、相关日期和当前坚持天数
func (u *UserHabitUsecase) calculateHabitPersistDays(ctx context.Context, habit *UserHabit) (int32, int32, int32, int32, error) {
	// 使用现有的逻辑计算连续天数
	habitStatisticAll := &HabitStatisticAll{}
	currentTime := time.Now()
	if habit.Status == enums.UserHabitStatusEnd {
		currentTime = time.Unix(habit.UpdatedAt, 0)
	}

	err := u.getUserHabitStatisticByLabel(ctx, habit, enums.HabitDetailLabelTypeAll, currentTime, habitStatisticAll)
	if err != nil {
		return 0, 0, 0, 0, err
	}

	var persistStartDate, persistEndDate int32
	if habitStatisticAll.PersistStartDate != "" {
		if startTime, err := time.Parse("2006-01-02", habitStatisticAll.PersistStartDate); err == nil {
			persistStartDate = int32(startTime.Unix())
		}
	}
	if habitStatisticAll.PersistEndDate != "" {
		if endTime, err := time.Parse("2006-01-02", habitStatisticAll.PersistEndDate); err == nil {
			persistEndDate = int32(endTime.Unix())
		}
	}

	// 计算当前坚持天数
	currentPersistDays, err := u.calculateCurrentPersistDays(ctx, habit)
	if err != nil {
		u.log.Errorf("计算当前坚持天数失败: %v", err)
		currentPersistDays = 0
	}

	return habitStatisticAll.MaxPersistDays, persistStartDate, persistEndDate, currentPersistDays, nil
}

// calculateCurrentPersistDays 计算当前坚持天数（从最近一次连续打卡开始到现在）
func (u *UserHabitUsecase) calculateCurrentPersistDays(ctx context.Context, habit *UserHabit) (int32, error) {
	// 获取用户时区
	timeZoneParts := strings.Split(habit.TimeZone, ";")
	var location *time.Location
	var err error
	if len(timeZoneParts) > 0 {
		location, err = time.LoadLocation(timeZoneParts[0])
		if err != nil {
			location = time.Local
		}
	} else {
		location = time.Local
	}

	// 获取当前时间
	now := time.Now().In(location)
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)

	// 获取所有已完成的快照，按日期倒序排列
	where := Where{
		"user_habit_id =": habit.ID,
		"is_completed =":  true,
	}
	snapshots, err := u.repo.ListSnapshotByParams(ctx, where)
	if err != nil {
		return 0, err
	}

	if len(snapshots) == 0 {
		return 0, nil
	}

	// 按日期倒序排序
	sort.Slice(snapshots, func(i, j int) bool {
		return snapshots[i].TodayTime > snapshots[j].TodayTime
	})

	// 从最近的打卡日期开始，向前计算连续天数
	currentPersistDays := int32(0)
	expectedDate := today

	for _, snapshot := range snapshots {
		snapshotDate := time.Unix(snapshot.TodayTime, 0).In(location)
		snapshotDay := time.Date(snapshotDate.Year(), snapshotDate.Month(), snapshotDate.Day(), 0, 0, 0, 0, location)

		// 检查这个日期是否应该打卡
		if u.shouldHabitPunchOnDate(habit, snapshotDay) {
			// 如果是连续的日期
			if snapshotDay.Equal(expectedDate) || snapshotDay.Equal(expectedDate.AddDate(0, 0, -1)) {
				currentPersistDays++
				expectedDate = snapshotDay.AddDate(0, 0, -1)
			} else {
				// 不连续，停止计算
				break
			}
		}
	}

	return currentPersistDays, nil
}

// shouldHabitPunchOnDate 检查习惯是否应该在指定日期打卡
func (u *UserHabitUsecase) shouldHabitPunchOnDate(habit *UserHabit, targetDate time.Time) bool {
	// 检查习惯创建时间
	habitCreateDate := time.Unix(habit.CreatedAt, 0)
	if targetDate.Before(habitCreateDate) {
		return false
	}

	// 检查结束日期
	if habit.Config.EndDate != "" {
		if endTime, err := time.Parse("2006-01-02", habit.Config.EndDate); err == nil {
			if targetDate.After(endTime) {
				return false
			}
		}
	}

	// 根据打卡周期类型检查
	switch habit.Config.PunchCycleType {
	case enums.PunchCycleFix: // 固定周期
		weekday := targetDate.Weekday()
		for _, day := range habit.Config.PunchCycle {
			if day == int32(weekday) {
				return true
			}
		}
		return false
	case enums.PunchCycleWeek: // 按周
		// 对于按周的习惯，需要检查该周是否需要打卡
		// 这里简化处理，认为每天都可能需要打卡
		return true
	case enums.PunchCycleMonth: // 按月
		// 对于按月的习惯，需要检查该月是否需要打卡
		// 这里简化处理，认为每天都可能需要打卡
		return true
	default:
		return true
	}
}

// AsyncRecalculateHabitStats 异步重新计算单个习惯的统计数据
func (u *UserHabitUsecase) AsyncRecalculateHabitStats(userID, userHabitID int32) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				u.log.Errorf("异步重新计算习惯统计数据发生panic: %v", r)
			}
		}()

		if err := u.CalculateAndSaveUserHabitStats(context.Background(), userID, userHabitID); err != nil {
			u.log.Errorf("异步重新计算习惯统计数据失败: %v, userID: %d, userHabitID: %d", err, userID, userHabitID)
		} else {
			u.log.Infof("异步重新计算习惯统计数据完成: userID: %d, userHabitID: %d", userID, userHabitID)
		}
	}()
}

// AsyncRecalculateStatsFromDate 异步重新计算从指定日期到昨天的统计数据
func (u *UserHabitUsecase) AsyncRecalculateStatsFromDate(userID int32, fromDate time.Time, timeZone string) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				u.log.Errorf("异步重新计算统计数据发生panic: %v", r)
			}
		}()

		// 获取用户时区
		timeZoneParts := strings.Split(timeZone, ";")
		var location *time.Location
		var err error
		if len(timeZoneParts) > 0 {
			location, err = time.LoadLocation(timeZoneParts[0])
			if err != nil {
				location = time.Local
			}
		} else {
			location = time.Local
		}

		// 获取当前时间（不包括今天）
		now := time.Now().In(location)
		today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)
		yesterday := today.AddDate(0, 0, -1)

		// 计算开始日期
		startDate := time.Date(fromDate.Year(), fromDate.Month(), fromDate.Day(), 0, 0, 0, 0, location)

		// 如果开始日期是今天或之后，则不需要重新计算
		if !startDate.Before(today) {
			return
		}

		// 计算结束日期（昨天）
		endDate := yesterday

		// 如果开始日期晚于结束日期，则不需要重新计算
		if startDate.After(endDate) {
			return
		}

		u.log.Infof("开始异步重新计算统计数据，UserID: %d, 日期范围: %s 到 %s",
			userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

		// 遍历日期范围，重新计算每一天的统计数据
		currentDate := startDate
		for !currentDate.After(endDate) {
			currentTimestamp := tool.GetZeroTimestampByDate(currentDate).Unix()

			// 计算该日期的统计数据
			totalHabits, completedHabits, calcErr := u.calculateDailyStats(context.Background(), userID, currentTimestamp)
			if calcErr != nil {
				u.log.Errorf("异步计算用户习惯统计数据失败: %v", calcErr)
			} else {
				// 保存统计数据到数据库
				newStat := &UserHabitDailyStats{
					UserID:          userID,
					DateTime:        currentTimestamp,
					TotalHabits:     totalHabits,
					CompletedHabits: completedHabits,
					BasicModel: BasicModel{
						CreatedAt: time.Now().Unix(),
						UpdatedAt: time.Now().Unix(),
					},
				}

				if saveErr := u.repo.SaveUserHabitDailyStats(context.Background(), newStat); saveErr != nil {
					u.log.Errorf("异步保存用户习惯统计数据失败: %v", saveErr)
				}
			}

			// 移动到下一天
			currentDate = currentDate.AddDate(0, 0, 1)
		}

		u.log.Infof("异步重新计算统计数据完成，UserID: %d, 日期范围: %s 到 %s",
			userID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	}()
}

// calculateDailyStats 计算指定日期的习惯统计数据
func (u *UserHabitUsecase) calculateDailyStats(ctx context.Context, userID int32, dateTime int64) (int, int, error) {
	// 获取用户所有习惯
	where := Where{
		"user_id =": userID,
		"status =":  enums.UserHabitStatusNormal,
	}
	habits, err := u.repo.List(ctx, where)
	if err != nil {
		return 0, 0, err
	}

	// 日期转换
	targetDate := time.Unix(dateTime, 0)

	// 获取今日用户习惯快照
	snapshotParams := map[string]interface{}{
		"today_time": dateTime,
		"user_id":    userID,
	}
	uhs, err := u.repo.ListSnapshotByParams(ctx, snapshotParams)
	if err != nil {
		u.log.Errorf("获取用户习惯快照失败，error: %v, user_id: %v", err, userID)
	}
	mapHabitSnap := make(map[int32]*UserHabitSnapshot)
	for _, item := range uhs {
		mapHabitSnap[item.UserHabitID] = item
	}

	totalHabits := 0
	completedHabits := 0

	// 遍历所有习惯，判断是否需要打卡
	for _, habit := range habits {
		// 检查习惯在目标日期是否存在（创建日期之后，结束日期之前）
		habitCreatedAt := time.Unix(habit.CreatedAt, 0)
		if habitCreatedAt.After(targetDate.AddDate(0, 0, 1)) {
			// 习惯在目标日期之后创建，跳过
			continue
		}

		// 如果当天已经打卡，则直接计入统计
		habitSnap, ok := mapHabitSnap[habit.ID]
		if ok && habitSnap.IsCompleted {
			totalHabits++
			completedHabits++
			continue
		}

		// 使用 listDisplayUserHabit 中的逻辑判断该日期是否需要打卡
		needPunch := false

		// 根据打卡周期判断
		switch habit.Config.PunchCycleType {
		case enums.PunchCycleFix:
			nowWeekDay := targetDate.Weekday()
			if tool.Contains(habit.Config.PunchCycle, int32(nowWeekDay)) {
				needPunch = true
			}
		case enums.PunchCycleWeek:
			// 获取本周开始时间和结束时间戳
			startTime, endTime := tool.GetWeekStartAndEndTime(targetDate)
			// 获取本周已打卡天数
			punchedDayCount, err := u.repo.CountUserPunchedDayByTime(ctx, habit.ID, startTime.Unix(), endTime.Unix())
			if err != nil {
				u.log.Errorf("获取用户习惯打卡天数失败，error: %v", err)
			}
			// 如果当周剩余天数小于待打卡天数，当天打卡状态返回为必须打卡
			remainDays := int64((endTime.Sub(targetDate).Hours() / 24) + 1)
			if int64(habit.Config.PunchCycle[0])-punchedDayCount > remainDays {
				needPunch = true
			}
		case enums.PunchCycleMonth:
			// 获取本月开始时间和结束时间戳
			startTime, endTime := tool.GetMonthStartAndEndTime(targetDate)
			// 获取本月已打卡天数
			punchedDayCount, err := u.repo.CountUserPunchedDayByTime(ctx, habit.ID, startTime.Unix(), endTime.Unix())
			if err != nil {
				u.log.Errorf("获取用户习惯打卡天数失败，error: %v", err)
			}
			// 如果当月剩余天数小于待打卡天数，当天打卡状态返回为必须打卡
			remainDays := int64((endTime.Sub(targetDate).Hours() / 24) + 1)
			if int64(habit.Config.PunchCycle[0])-punchedDayCount > remainDays {
				needPunch = true
			}
		}

		if !needPunch {
			// 该日期不需要打卡，跳过
			continue
		}

		// 该习惯在目标日期需要打卡
		totalHabits++
	}

	return totalHabits, completedHabits, nil
}

func (u *UserHabitUsecase) categorizaHabit(item *UserHabit, overCompleteIds, unCompleteIds []int32) (category string) {
	// 默认为跟踪习惯
	category = "track"

	// 检查是否为未配置周期的固定类型习惯
	isEmptyCycleFix := item.Config.PunchCycleType == enums.PunchCycleFix && len(item.Config.PunchCycle) == 0

	// 判断是否为超额完成习惯
	if len(overCompleteIds) > 0 && tool.Contains(overCompleteIds, item.ID) {
		if !isEmptyCycleFix {
			return "top"
		}
		return "track"
	}

	// 判断是否为未完成习惯
	if len(unCompleteIds) > 0 && tool.Contains(unCompleteIds, item.ID) {
		// 如果是空周期固定类型，保持为track
		if isEmptyCycleFix {
			return "track"
		}

		// 判断创建时间是否满足最小条件
		var minAge float64 = 24 * 7 * 2 // 两周（小时）
		if item.Config.PunchCycleType == enums.PunchCycleMonth {
			minAge = 24 * 30 // 一个月（小时）
		}

		// 计算习惯年龄（小时）
		habitAge := time.Now().Sub(time.Unix(item.CreatedAt, 0)).Hours()

		// 如果年龄足够长，归为低效习惯
		if habitAge >= minAge {
			return "low"
		}
	}

	return "track"
}

func (u *UserHabitUsecase) listDisplayUserHabit(ctx context.Context, userID int32, userTime time.Time, mapHabitSnap map[int32]*UserHabitSnapshot, allDisplay bool) ([]*UserHabit, error) {
	displayUserHabits := make([]*UserHabit, 0)

	where := Where{
		"user_id =": userID,
		"status =":  enums.UserHabitStatusNormal,
	}
	userHabits, err := u.repo.List(ctx, where)
	if err != nil {
		u.log.Errorf("获取用户习惯失败，error: %v", err)
		return displayUserHabits, nil
	}

	for _, item := range userHabits {
		// 判断是否到截止日期
		if item.Config.EndDate != "" && len(strings.Split(item.TimeZone, ";")) > 1 {
			timeName := strings.Split(item.TimeZone, ";")[0]
			timezone := strings.Split(item.TimeZone, ";")[1]
			if tool.IsAfterTargetNextDay(item.Config.EndDate, timeName, timezone) {
				// 修改习惯为完成
				if err = u.updateUserHabitStatusEnd(ctx, item); err != nil {
					u.log.Errorf("修改用户习惯状态失败，error: %v", err)
				}
				continue
			}
		}

		// 当日是否必须完成
		isNecessary := false
		isDisplay := false
		if item.Config.PunchCycleType == enums.PunchCycleFix { // 固定周期
			nowWeekDay := userTime.Weekday()
			if tool.Contains(item.Config.PunchCycle, int32(nowWeekDay)) {
				isDisplay = true
				isNecessary = true
			}
		} else if item.Config.PunchCycleType == enums.PunchCycleWeek { // 按周，一周打卡几天
			// 获取本周开始时间和结束时间戳
			startTime, endTime := tool.GetWeekStartAndEndTime(userTime)
			// 获取本周已打卡天数
			punchedDayCount, err := u.repo.CountUserPunchedDayByTime(ctx, item.ID, startTime.Unix(), endTime.Unix())
			if err != nil {
				u.log.Errorf("获取用户习惯打卡天数失败，error: %v", err)
			}
			if punchedDayCount < int64(item.Config.PunchCycle[0]) {
				isDisplay = true
			}
			// 如果当周剩余天数小于待打卡天数，当天打卡状态返回为必须打卡
			habitSnap, ok := mapHabitSnap[item.ID]
			remainDays := int64((endTime.Sub(userTime).Hours() / 24) + 1)
			if int64(item.Config.PunchCycle[0])-punchedDayCount > remainDays || (int64(item.Config.PunchCycle[0])-punchedDayCount == remainDays && ok && habitSnap.IsCompleted) {
				isNecessary = true
			}
		} else if item.Config.PunchCycleType == enums.PunchCycleMonth { // 按月，一月打卡几天
			// 获取本月开始时间和结束时间戳
			startTime, endTime := tool.GetMonthStartAndEndTime(userTime)
			// 获取本月已打卡天数
			punchedDayCount, err := u.repo.CountUserPunchedDayByTime(ctx, item.ID, startTime.Unix(), endTime.Unix())
			if err != nil {
				u.log.Errorf("获取用户习惯打卡天数失败，error: %v", err)
			}
			if punchedDayCount < int64(item.Config.PunchCycle[0]) {
				isDisplay = true
			}
			// 如果当月剩余天数小于待打卡天数，当天打卡状态返回为必须打卡
			habitSnap, ok := mapHabitSnap[item.ID]
			remainDays := int64((endTime.Sub(userTime).Hours() / 24) + 1)
			if int64(item.Config.PunchCycle[0])-punchedDayCount > remainDays || (int64(item.Config.PunchCycle[0])-punchedDayCount == remainDays && ok && habitSnap.IsCompleted) {
				isNecessary = true
			}
		}

		item.IsNecessary = isNecessary

		// 将 allDisplay 放到后面的目的是为了 isNecessary 校验
		if allDisplay || isDisplay {
			item.IsDisplay = true
		}
	}

	return userHabits, nil
}

// GetUserHabitCompleteDetail 获取用户习惯完成详情
// 固定周期，按照过去一周打卡数据进行统计
// 按周，按照过去一周打卡数据进行统计
// 按月，按照过去一月打卡数据进行统计
// 根据 打卡天数/总天数 计算完成度
func (u *UserHabitUsecase) GetUserHabitCompleteDetail(ctx context.Context, userHabitID int32, userID int32, currentDate string, labelType enums.HabitDetailLabelType, res *statisticv1.UserHabitStatisticFromDetailReply) error {
	// 获取习惯详情
	uh := &UserHabit{}
	uh.ID = userHabitID
	uh.UserID = userID
	if err := u.repo.FirstByID(ctx, uh); err != nil {
		return err
	}

	userTime, err := time.Parse(time.RFC3339, currentDate)
	if err != nil {
		return err
	}

	isShowCount := true

	// 习惯类型为记录时，获取平均数据
	if uh.HabitType == enums.UserHabitRecord {
		var averageData, maxData, minData string
		if uh.Config.RecordType == enums.RecordTypeDuration {
			minData, maxData, averageData, err = u.avgHabitRecordStatisticByDuration(ctx, userHabitID, labelType, userTime)
			if err != nil {
				return err
			}
		} else if uh.Config.RecordType == enums.RecordTypeCount {
			minData, maxData, averageData, err = u.avgHabitRecordStatisticByCount(ctx, userHabitID, labelType, userTime)
			if err != nil {
				return err
			}
		} else if uh.Config.RecordType == enums.RecordTypeTime {
			minData, maxData, averageData, err = u.avgHabitRecordStatisticByTime(ctx, userHabitID, userID, labelType, userTime)
			if err != nil {
				return err
			}
		}

		if labelType == enums.HabitDetailLabelTypeWeek {
			res.Data.WeekStatistic.AvgData = averageData
			isShowCount = false // 周数据不显示待打卡天数
		} else if labelType == enums.HabitDetailLabelTypeMonth {
			res.Data.MonthStatistic.AvgData = averageData
			isShowCount = false // 月数据不显示待打卡天数
		} else {
			res.Data.AllStatistic.AvgData = averageData
			res.Data.AllStatistic.MaxData = maxData
			res.Data.AllStatistic.MinData = minData
		}
	}

	// 获取总待打卡天数
	if isShowCount {
		habitStatisticAll := &HabitStatisticAll{}
		if err = u.getUserHabitStatisticByLabel(ctx, uh, labelType, userTime, habitStatisticAll); err != nil {
			return nil
		}
		if labelType == enums.HabitDetailLabelTypeWeek {
			res.Data.WeekStatistic.AllCount = habitStatisticAll.PunchCount
		} else if labelType == enums.HabitDetailLabelTypeMonth {
			res.Data.MonthStatistic.AllCount = habitStatisticAll.PunchCount
		} else {
			res.Data.AllStatistic.AllCount = habitStatisticAll.PunchCount
			res.Data.AllStatistic.PersistLongDays = habitStatisticAll.MaxPersistDays
			res.Data.AllStatistic.PersistStartDate = habitStatisticAll.PersistStartDate
			res.Data.AllStatistic.PersistEndDate = habitStatisticAll.PersistEndDate
		}
	}

	// 获取已打卡天数
	where := Where{
		"user_habit_id =": userHabitID,
		"is_completed =":  true,
	}
	if labelType == enums.HabitDetailLabelTypeWeek {
		startTime, endTime := tool.GetWeekStartAndEndTime(userTime)
		where["today_time >="] = startTime.Unix()
		where["today_time <="] = endTime.Unix()
	} else if labelType == enums.HabitDetailLabelTypeMonth {
		startTime, endTime := tool.GetMonthStartAndEndTime(userTime)
		where["today_time >="] = startTime.Unix()
		where["today_time <="] = endTime.Unix()
	}
	mapPunchDayCount, err := u.repo.MapCountUserPunchByUserHabitIDs(ctx, where)
	if err != nil {
		return err
	}
	if labelType == enums.HabitDetailLabelTypeWeek {
		res.Data.WeekStatistic.DoneCount = mapPunchDayCount[userHabitID]
	} else if labelType == enums.HabitDetailLabelTypeMonth {
		res.Data.MonthStatistic.DoneCount = mapPunchDayCount[userHabitID]
	} else {
		res.Data.AllStatistic.DoneCount = mapPunchDayCount[userHabitID]
	}
	// 计算完成度
	if res.Data.AllStatistic.AllCount == 0 {
		res.Data.AllStatistic.Per = res.Data.AllStatistic.DoneCount * 100
		// 打卡周期为固定，且打卡周期为空，跳过
		if uh.Config.PunchCycleType == enums.PunchCycleFix && len(uh.Config.PunchCycle) == 0 {
			res.Data.AllStatistic.Per = 100
		}
	} else {
		res.Data.AllStatistic.Per = int32(float64(res.Data.AllStatistic.DoneCount) / float64(res.Data.AllStatistic.AllCount) * 100)
	}

	// 获取周打卡次数
	if labelType == enums.HabitDetailLabelTypeWeek {
		weekPunchCountStatistic, err := u.weekPunchCountStatistic(ctx, uh, userID, userTime)
		if err != nil {
			return err
		}
		res.Data.WeekStatistic.Stages = weekPunchCountStatistic.Stages
		res.Data.WeekStatistic.Detail = weekPunchCountStatistic.Detail
		res.Data.WeekStatistic.ChartLeftCount = weekPunchCountStatistic.ChartLeftCount
	} else if labelType == enums.HabitDetailLabelTypeMonth {
		monthPunchCountStatistic, err := u.monthPunchCountStatistic(ctx, uh, userID, userTime)
		if err != nil {
			return err
		}
		res.Data.MonthStatistic.Stages = monthPunchCountStatistic.Stages
		res.Data.MonthStatistic.Detail = monthPunchCountStatistic.Detail
		res.Data.MonthStatistic.ChartLeftCount = monthPunchCountStatistic.ChartLeftCount
	}

	return nil
}

func (u *UserHabitUsecase) weekPunchCountStatistic(ctx context.Context, userHabit *UserHabit, userID int32, userTime time.Time) (*statisticv1.UserHabitStatisticFromDetailReply_WeekStatistic, error) {
	res := &statisticv1.UserHabitStatisticFromDetailReply_WeekStatistic{
		Detail: make([]*statisticv1.UserHabitStatisticFromDetailReply_Detail, 0),
	}

	startTime, endTime := tool.GetWeekStartAndEndTime(userTime)
	where := Where{
		"user_id =":       userID,
		"user_habit_id =": userHabit.ID,
		"record_time >=":  startTime.Unix(),
		"record_time <=":  endTime.Unix(),
	}
	punchLogs, err := u.repo.ListPunchLogByParams(ctx, where)
	if err != nil {
		return nil, err
	}
	mapPunchLog := make(map[int64][]*UserPunchLog)
	for _, item := range punchLogs {
		if _, ok := mapPunchLog[item.RecordTime]; !ok {
			mapPunchLog[item.RecordTime] = make([]*UserPunchLog, 0)
		}
		mapPunchLog[item.RecordTime] = append(mapPunchLog[item.RecordTime], item)
	}

	stages := []int32{0, 0}
	for i := 0; i < 7; i++ {
		tmpStages := []int32{0, 0}
		tmpTimestamp := startTime.AddDate(0, 0, i).Unix()
		if tmpPunchLogs, ok := mapPunchLog[tmpTimestamp]; ok {
			for _, tmpLog := range tmpPunchLogs {
				tmpStages[tmpLog.SmallStageID] += 1
				stages[tmpLog.SmallStageID] += 1
			}
		}
		res.Detail = append(res.Detail, &statisticv1.UserHabitStatisticFromDetailReply_Detail{
			Day:    int32(i),
			Stages: tmpStages,
		})
	}
	res.Stages = stages

	var maxStageValue int32
	for _, detail := range res.Detail {
		for _, stage := range detail.Stages {
			if stage > maxStageValue {
				maxStageValue = stage
			}
		}
	}
	if maxStageValue > 0 {
		res.ChartLeftCount = []int32{0, maxStageValue + 1}
		if maxStageValue > 1 {
			res.ChartLeftCount = []int32{0, maxStageValue / 2, maxStageValue + 1}

		}
	}

	return res, nil
}

func (u *UserHabitUsecase) monthPunchCountStatistic(ctx context.Context, userHabit *UserHabit, userID int32, userTime time.Time) (*statisticv1.UserHabitStatisticFromDetailReply_MonthStatistic, error) {
	res := &statisticv1.UserHabitStatisticFromDetailReply_MonthStatistic{
		Detail: make([]*statisticv1.UserHabitStatisticFromDetailReply_Detail, 0),
	}

	startTime, endTime := tool.GetMonthStartAndEndTime(userTime)
	where := Where{
		"user_id =":       userID,
		"user_habit_id =": userHabit.ID,
		"record_time >=":  startTime.Unix(),
		"record_time <=":  endTime.Unix(),
	}
	punchLogs, err := u.repo.ListPunchLogByParams(ctx, where)
	if err != nil {
		return nil, err
	}
	mapPunchLog := make(map[int64][]*UserPunchLog)
	for _, item := range punchLogs {
		if _, ok := mapPunchLog[item.RecordTime]; !ok {
			mapPunchLog[item.RecordTime] = make([]*UserPunchLog, 0)
		}
		mapPunchLog[item.RecordTime] = append(mapPunchLog[item.RecordTime], item)
	}

	stages := []int32{0, 0}
	tmpTimestamp := startTime.Unix()
	for i := 0; tmpTimestamp < endTime.Unix(); i++ {
		tmpStages := []int32{0, 0}
		tmpTimestamp = startTime.AddDate(0, 0, i).Unix()
		if tmpPunchLogs, ok := mapPunchLog[tmpTimestamp]; ok {
			for _, tmpLog := range tmpPunchLogs {
				tmpStages[tmpLog.SmallStageID] += 1
				stages[tmpLog.SmallStageID] += 1
			}
		}
		res.Detail = append(res.Detail, &statisticv1.UserHabitStatisticFromDetailReply_Detail{
			Day:    int32(i),
			Stages: tmpStages,
		})
	}
	res.Stages = stages

	var maxStageValue int32
	for _, detail := range res.Detail {
		for _, stage := range detail.Stages {
			if stage > maxStageValue {
				maxStageValue = stage
			}
		}
	}
	if maxStageValue > 0 {
		res.ChartLeftCount = []int32{0, maxStageValue + 1}
		if maxStageValue > 1 {
			res.ChartLeftCount = []int32{0, maxStageValue / 2, maxStageValue + 1}

		}
	}

	return res, nil
}

// avgHabitRecordStatisticByCount 获取记录习惯的平均打卡次数数据
func (u *UserHabitUsecase) avgHabitRecordStatisticByCount(ctx context.Context, userHabitID int32, labelType enums.HabitDetailLabelType, userTime time.Time) (string, string, string, error) {
	querySql := `
			WITH SnapCount AS (
				SELECT
					user_habit_id,
					habit_snap_id,
					COUNT(1) AS total_count
				FROM
					tb_user_punch_log
				WHERE
					user_habit_id = ?
	and created_at >= ? and created_at < ?
				GROUP BY
					user_habit_id, habit_snap_id
			)

			SELECT
				AVG(total_count) AS avg_data,
				MAX(total_count) AS max_data,
				MIN(total_count) AS min_data
			FROM
				SnapCount
		`
	queryVals := make([]interface{}, 0)
	if labelType == enums.HabitDetailLabelTypeWeek {
		startTime, endTime := tool.GetWeekStartAndEndTime(userTime)
		queryVals = []interface{}{userHabitID, startTime.Unix(), endTime.AddDate(0, 0, 1).Unix()}
	} else if labelType == enums.HabitDetailLabelTypeMonth {
		startTime, endTime := tool.GetMonthStartAndEndTime(userTime)
		queryVals = []interface{}{userHabitID, startTime.Unix(), endTime.AddDate(0, 0, 1).Unix()}
	}
	if labelType == enums.HabitDetailLabelTypeAll {
		querySql = `
			WITH SnapCount AS (
				SELECT
					user_habit_id,
					habit_snap_id,
					COUNT(1) AS total_count
				FROM
					tb_user_punch_log
				WHERE
					user_habit_id = ?
				GROUP BY
					user_habit_id, habit_snap_id
			)

			SELECT
				AVG(total_count) AS avg_data,
				MAX(total_count) AS max_data,
				MIN(total_count) AS min_data
			FROM
				SnapCount
		`
		queryVals = []interface{}{userHabitID}
	}
	recordData := &RecordResult{}
	err := u.db.Raw(querySql, queryVals...).Take(&recordData).Error
	if err != nil {
		return "", "", "", err
	}

	var avgData = fmt.Sprintf("%v", int(recordData.AvgData))
	var maxData = fmt.Sprintf("%v", recordData.MaxData)
	var minData = fmt.Sprintf("%v", recordData.MinData)

	return minData, maxData, avgData, nil
}

// avgHabitRecordStatisticByDuration 获取习惯记录平均时长
func (u *UserHabitUsecase) avgHabitRecordStatisticByDuration(ctx context.Context, userHabitID int32, labelType enums.HabitDetailLabelType, userTime time.Time) (string, string, string, error) {
	querySql := `
			WITH SnapDurations AS (
				SELECT
					user_habit_id,
					habit_snap_id,
					SUM(duration) AS total_duration
				FROM
					tb_user_reckon_time
				WHERE
					user_habit_id = ? and is_valid = 1
	and created_at >= ? and created_at < ?
				GROUP BY
					user_habit_id, habit_snap_id
			)

			SELECT
				AVG(total_duration) AS avg_data,
				MAX(total_duration) AS max_data,
				MIN(total_duration) AS min_data
			FROM
				SnapDurations
		`
	queryVals := make([]interface{}, 0)
	if labelType == enums.HabitDetailLabelTypeWeek {
		startTime, endTime := tool.GetWeekStartAndEndTime(userTime)
		queryVals = []interface{}{userHabitID, startTime.Unix(), endTime.AddDate(0, 0, 1).Unix()}
	} else if labelType == enums.HabitDetailLabelTypeMonth {
		startTime, endTime := tool.GetMonthStartAndEndTime(userTime)
		queryVals = []interface{}{userHabitID, startTime.Unix(), endTime.AddDate(0, 0, 1).Unix()}
	}
	if labelType == enums.HabitDetailLabelTypeAll {
		querySql = `
			WITH SnapDurations AS (
				SELECT
					user_habit_id,
					habit_snap_id,
					SUM(duration) AS total_duration
				FROM
					tb_user_reckon_time
				WHERE
					user_habit_id = ? and is_valid = 1
				GROUP BY
					user_habit_id, habit_snap_id
			)

			SELECT
				AVG(total_duration) AS avg_data,
				MAX(total_duration) AS max_data,
				MIN(total_duration) AS min_data
			FROM
				SnapDurations
		`
		queryVals = []interface{}{userHabitID}
	}
	recordData := &RecordResult{}
	err := u.db.Raw(querySql, queryVals...).Take(&recordData).Error
	if err != nil {
		return "", "", "", err
	}

	var avgData = fmt.Sprintf("%v", tool.FormatToMinutesSeconds(int(recordData.AvgData)))
	var maxData = fmt.Sprintf("%v", tool.FormatToMinutesSeconds(int(float64(recordData.MaxData))))
	var minData = fmt.Sprintf("%v", tool.FormatToMinutesSeconds(int(float64(recordData.MinData))))

	return minData, maxData, avgData, nil
}

// avgHabitRecordStatisticByTime 获取习惯记录时，获取平均打卡时间数据
func (u *UserHabitUsecase) avgHabitRecordStatisticByTime(ctx context.Context, userHabitID int32, userID int32, labelType enums.HabitDetailLabelType, userTime time.Time) (string, string, string, error) {
	where := Where{
		"user_id =":       userID,
		"user_habit_id =": userHabitID,
	}
	if labelType == enums.HabitDetailLabelTypeWeek {
		startTime, endTime := tool.GetWeekStartAndEndTime(userTime)
		where["record_time >="] = startTime.Unix()
		where["record_time <="] = endTime.Unix()
	} else if labelType == enums.HabitDetailLabelTypeMonth {
		startTime, endTime := tool.GetMonthStartAndEndTime(userTime)
		where["record_time >="] = startTime.Unix()
		where["record_time <="] = endTime.Unix()
	}
	punchLogs, err := u.repo.ListPunchLogByParams(ctx, where)
	if err != nil {
		return "", "", "", err
	}
	timestamps := make([]int64, 0)
	for _, item := range punchLogs {
		timestamps = append(timestamps, item.RecordAt)
	}

	earliestTime, latestTime, averageTime, err := tool.AnalyzeCheckInTimes(timestamps)
	if err != nil {
		u.log.Errorf("分析打卡时间失败，error: %v", err)
	}

	return earliestTime, latestTime, averageTime, nil
}

// getUserHabitStatisticByLabel 在习惯详情中，根据选择的标签，获取统计数据
// 所有，支持安定、按周、按月
// 当周，支持固定、按周
// 当月，支持固定、按周、按月打卡
func (u *UserHabitUsecase) getUserHabitStatisticByLabel(ctx context.Context, habit *UserHabit, labelType enums.HabitDetailLabelType, currentTime time.Time, habitStatisticAll *HabitStatisticAll) error {
	var punchAllCount int32

	if labelType == enums.HabitDetailLabelTypeWeek {
		// 如果是固定、按周，则获取当前周期内最新配置返回打卡总天数
		logStartTime, endTime := tool.GetWeekStartAndEndTime(currentTime)
		where := Where{
			"created_at <":    endTime.AddDate(0, 0, 1).Unix(),
			"user_habit_id =": habit.ID,
		}
		habitLogs, err := u.repo.ListHabitLogByParams(ctx, where)
		if err != nil {
			u.log.Errorf("获取习惯日志失败，error: %v", err)
			return err
		}
		if len(habitLogs) == 0 {
			return nil
		}
		habitConfig := habitLogs[len(habitLogs)-1].Config
		if habit.CreatedAt > logStartTime.Unix() {
			logStartTime = tool.GetZeroTimestampByDate(time.Unix(habit.CreatedAt, 0))
		}
		days := tool.DaysInWeekOfMonth(endTime, logStartTime, true)
		if habitConfig.PunchCycleType == enums.PunchCycleFix {
			if days == 7 {
				punchAllCount += int32(len(habitConfig.PunchCycle))
			} else {
				// 判断当周允许打卡的星期是否包含在配置中
				for j := 0; j < int(days); j++ {
					weekDay := endTime.AddDate(0, 0, -j).Weekday()
					if tool.Contains(habitConfig.PunchCycle, int32(weekDay)) {
						punchAllCount += 1
					}
				}
			}
		} else if habitConfig.PunchCycleType == enums.PunchCycleWeek {
			if days == 7 {
				punchAllCount += habitConfig.PunchCycle[0]
			} else {
				punchAllCount += int32(math.Round(float64(habitConfig.PunchCycle[0]*days) / float64(7)))
			}
		}
	} else if labelType == enums.HabitDetailLabelTypeMonth {
		// 如果按月，则获取当时周期内最新配置返回打卡总天数
		startTime, endTime := tool.GetMonthStartAndEndTime(currentTime)
		where := Where{
			"created_at <":    endTime.AddDate(0, 0, 1).Unix(),
			"user_habit_id =": habit.ID,
		}
		habitLogs, err := u.repo.ListHabitLogByParams(ctx, where)
		if err != nil {
			u.log.Errorf("获取习惯日志失败，error: %v", err)
			return err
		}
		if len(habitLogs) == 0 {
			return nil
		}
		habitConfig := habitLogs[len(habitLogs)-1].Config
		// 固定和按周
		if habitConfig.PunchCycleType == enums.PunchCycleFix || habitConfig.PunchCycleType == enums.PunchCycleWeek {
			if habit.CreatedAt > startTime.Unix() {
				startTime = tool.GetZeroTimestampByDate(time.Unix(habit.CreatedAt, 0))
			}
			punchAllCount, _ = monthCalculatePunchCountForWeek(habitLogs, startTime, endTime)
		} else if habitConfig.PunchCycleType == enums.PunchCycleMonth {
			// 对于最新打卡配置为按月，直接返回打卡天数
			punchAllCount = habitConfig.PunchCycle[0]
		}
	} else {
		// 截止到现在的打卡总天数
		// 开始时间：习惯创建时间
		// 结束时间：当前时间
		endTime := tool.GetZeroTimestampByDate(time.Now())
		if habit.Status == enums.UserHabitStatusEnd {
			endTime = tool.GetZeroTimestampByDate(time.Unix(habit.UpdatedAt, 0))
		}
		where := Where{
			"created_at <":    endTime.AddDate(0, 0, 1).Unix(),
			"user_habit_id =": habit.ID,
		}
		habitLogs, err := u.repo.ListHabitLogByParams(ctx, where)
		if err != nil {
			u.log.Errorf("获取习惯日志失败，error: %v", err)
			return err
		}
		if len(habitLogs) == 0 {
			return nil
		}
		validPunchConfigs := make([]*tool.PunchConfig, 0)
		startTime := tool.GetZeroTimestampByDate(time.Unix(habit.CreatedAt, 0))
		punchAllCount, validPunchConfigs = monthCalculatePunchCountForWeek(habitLogs, startTime, endTime)

		// 获取连续打卡天数和时间段
		if habit.HabitType == enums.UserHabitNormal || habit.HabitType == enums.UserHabitSmall {
			params := map[string]interface{}{
				"user_habit_id": habit.ID,
				"is_completed":  true,
			}
			habitSnaps, err := u.repo.ListSnapshotByParams(ctx, params)
			if err != nil {
				return err
			}
			punchTimestamps := make([]int64, 0, len(habitSnaps))
			for _, item := range habitSnaps {
				punchTimestamps = append(punchTimestamps, item.TodayTime)
			}
			tracker := tool.NewHabitTracker(punchTimestamps)
			maxPersistDays, startDate, endDate := tracker.CalculateMaxConsecutivePeriods(validPunchConfigs, time.Unix(habit.CreatedAt, 0))
			habitStatisticAll.MaxPersistDays = int32(maxPersistDays)
			if startDate > 0 {
				habitStatisticAll.PersistStartDate = time.Unix(startDate, 0).Format("2006-01-02")
			}
			if endDate > 0 {
				habitStatisticAll.PersistEndDate = time.Unix(endDate, 0).Format("2006-01-02")
			}
		}
	}

	habitStatisticAll.PunchCount = punchAllCount

	return nil
}

// monthCalculatePunchCountForWeek 计算打卡总天数
func monthCalculatePunchCountForWeek(habitLogs []*UserHabitUpdateLog, startTime time.Time, endTime time.Time) (int32, []*tool.PunchConfig) {
	var punchAllCount int32
	validPunchConfigs := make([]*tool.PunchConfig, 0)

	preStartTime := startTime
	preHabitLog := habitLogs[len(habitLogs)-1]
	currentEndTime := endTime

	for i := len(habitLogs) - 1; i >= 0; i-- {
		habitLog := habitLogs[i]

		if preHabitLog != habitLog && habitLog.CreatedAt >= preStartTime.Unix() {
			continue
		}

		currentLogStartTime, _ := tool.GetWeekStartAndEndTime(time.Unix(habitLog.CreatedAt, 0))
		if habitLog.Config.PunchCycleType == enums.PunchCycleMonth {
			currentLogStartTime, _ = tool.GetMonthStartAndEndTime(time.Unix(habitLog.CreatedAt, 0))
		}
		if currentLogStartTime.Before(startTime) {
			currentLogStartTime = startTime
		}

		for currentLogStartTime.Before(currentEndTime) || currentLogStartTime.Equal(currentEndTime) {
			isWeek := false
			if habitLog.Config.PunchCycleType == enums.PunchCycleWeek || habitLog.Config.PunchCycleType == enums.PunchCycleFix {
				isWeek = true
			}
			days := tool.DaysInWeekOfMonth(currentEndTime, startTime, isWeek)
			if days <= 0 {
				break
			}

			var curPunchAllCount int32
			curPunchConfig := &tool.PunchConfig{}
			switch habitLog.Config.PunchCycleType {
			case enums.PunchCycleFix:
				curPunchAllCount, curPunchConfig = calculateFixedCyclePunchCount(habitLog, days, currentEndTime)
			case enums.PunchCycleWeek:
				curPunchAllCount, curPunchConfig = calculateWeeklyCyclePunchCount(habitLog, days)
			case enums.PunchCycleMonth:
				curPunchAllCount, curPunchConfig = calculateMonthlyCyclePunchCount(habitLog, days, currentEndTime, startTime)
			}
			punchAllCount += curPunchAllCount
			if curPunchConfig.PunchCycle != nil {
				validPunchConfigs = append(validPunchConfigs, curPunchConfig)
			}

			currentEndTime = currentEndTime.AddDate(0, 0, -int(days))
		}

		preStartTime = currentLogStartTime
		preHabitLog = habitLog
	}

	validPunchConfigs = tool.ReverseSlice(validPunchConfigs)

	return punchAllCount, validPunchConfigs
}

// 辅助函数示例
func calculateFixedCyclePunchCount(habitLog *UserHabitUpdateLog, days int32, currentEndTime time.Time) (int32, *tool.PunchConfig) {
	var count int32
	punchCycle := make([]int32, 0)
	if days == 7 {
		count = int32(len(habitLog.Config.PunchCycle))
		punchCycle = habitLog.Config.PunchCycle
	} else {
		for j := 0; j < int(days); j++ {
			weekDay := currentEndTime.AddDate(0, 0, -j).Weekday()
			if tool.Contains(habitLog.Config.PunchCycle, int32(weekDay)) {
				punchCycle = append(punchCycle, int32(weekDay))
				count++
			}
		}
	}
	sort.Slice(punchCycle, func(i, j int) bool {
		return punchCycle[i] < punchCycle[j]
	})
	validPunchConfig := &tool.PunchConfig{
		PunchCycleType: enums.PunchCycleFix,
		PunchCycle:     punchCycle,
	}

	return count, validPunchConfig
}

func calculateWeeklyCyclePunchCount(habitLog *UserHabitUpdateLog, days int32) (int32, *tool.PunchConfig) {
	var count int32
	punchCycle := make([]int32, 0)
	if days == 7 {
		count = habitLog.Config.PunchCycle[0]
		punchCycle = habitLog.Config.PunchCycle
	} else {
		count = int32(math.Round(float64(habitLog.Config.PunchCycle[0]*days) / 7))
		punchCycle = []int32{count}
	}
	validPunchConfig := &tool.PunchConfig{
		PunchCycleType: enums.PunchCycleWeek,
		PunchCycle:     punchCycle,
	}

	return count, validPunchConfig
}

func calculateMonthlyCyclePunchCount(habitLog *UserHabitUpdateLog, days int32, currentEndTime, startTime time.Time) (int32, *tool.PunchConfig) {
	if currentEndTime.Month() != startTime.Month() {
		days = int32(currentEndTime.Day())
	} else {
		days = int32(currentEndTime.Day() - startTime.Day())
	}
	_, monthEndTime := tool.GetMonthStartAndEndTime(currentEndTime)
	monthDays := int32(monthEndTime.Day())
	count := int32(math.Round(float64(habitLog.Config.PunchCycle[0]*days) / float64(monthDays)))

	validPunchConfig := &tool.PunchConfig{
		PunchCycleType: enums.PunchCycleMonth,
		PunchCycle:     []int32{count},
	}

	return count, validPunchConfig
}

// sortUserHabitByDone 根据用户习惯完成情况进行排序，用于首页展示「频繁超额完成」和「频繁未完成」的习惯。
// 统计上个月到现在的数据
func (u *UserHabitUsecase) sortUserHabitByDone(ctx context.Context, userHabitIds []int32, userTime time.Time) (overCompleted, unCompleted []int32) {
	overCompleted = make([]int32, 0)
	unCompleted = make([]int32, 0)

	endTime := userTime
	where := Where{
		"created_at <":     endTime.AddDate(0, 0, 1).Unix(),
		"user_habit_id in": userHabitIds,
	}
	habitLogs, err := u.repo.ListHabitLogByParams(ctx, where)
	if err != nil {
		u.log.Errorf("获取习惯日志失败，error: %v", err)
		return
	}
	if len(habitLogs) == 0 {
		return
	}
	mapHabitLog := make(map[int32][]*UserHabitUpdateLog)
	for _, item := range habitLogs {
		if _, ok := mapHabitLog[item.UserHabitID]; !ok {
			mapHabitLog[item.UserHabitID] = make([]*UserHabitUpdateLog, 0)
		}
		mapHabitLog[item.UserHabitID] = append(mapHabitLog[item.UserHabitID], item)
	}

	mapPunchAllCount := make(map[int32]int32)
	// 统计上月月初到现在的数据
	lastMonth := userTime.AddDate(0, -1, 0)
	startTime, _ := tool.GetMonthStartAndEndTime(lastMonth)
	for _, item := range mapHabitLog {
		tmpStartTime := startTime
		if item[0].CreatedAt > startTime.Unix() {
			tmpStartTime = tool.GetZeroTimestampByDate(time.Unix(item[0].CreatedAt, 0))
		}
		mapPunchAllCount[item[0].UserHabitID], _ = monthCalculatePunchCountForWeek(item, tmpStartTime, endTime)
	}

	// 获取已打卡天数
	where = Where{
		"user_habit_id in": userHabitIds,
		"is_completed =":   true,
		"today_time >=":    startTime.Unix(),
		"today_time <=":    endTime.Unix(),
	}
	mapPunchDayCount, err := u.repo.MapCountUserPunchByUserHabitIDs(ctx, where)
	if err != nil {
		u.log.Errorf("获取已打卡天数失败，error: %v", err)
		return
	}

	for _, item := range userHabitIds {
		per := 0
		if mapPunchAllCount[item] == 0 {
			per = int(mapPunchDayCount[item] * 100)
		} else {
			per = int(mapPunchDayCount[item] * 100 / mapPunchAllCount[item])
		}
		if per >= 120 {
			overCompleted = append(overCompleted, item)
		} else if per < 70 && mapPunchAllCount[item] != 0 {
			unCompleted = append(unCompleted, item)
		}
	}

	return
}

// PauseUserHabit 暂停习惯，只有正常状态下的习惯才能暂停
func (u *UserHabitUsecase) PauseUserHabit(ctx context.Context, request *v1.PauseUserHabitRequest) (*v1.PauseUserHabitReply, error) {
	res := &v1.PauseUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	uh := &UserHabit{}
	uh.ID = request.Id
	uh.UserID = userID
	if err := u.repo.FirstByID(ctx, uh); err != nil {
		return nil, err
	}
	if uh.Status == enums.UserHabitStatusPause {
		return res, nil
	}
	if uh.Status != enums.UserHabitStatusNormal {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPauseUserHabitMsg, nil))
	}

	tx := u.db.Begin()
	now := time.Now()
	// 修改习惯本身状态为停止
	uh.Status = enums.UserHabitStatusPause
	uh.UpdatedAt = now.Unix()
	if err := u.repo.Update(ctx, tx, uh); err != nil {
		tx.Rollback()
		return nil, err
	}

	// 根据 user_habit_id 修改习惯快照状态为停止，并暂停提醒事件
	uhs := &UserHabitSnapshot{}
	uhs.UserHabitID = uh.ID
	uhs.Status = enums.UserHabitStatusPause
	uhs.UpdatedAt = now.Unix()
	if err := u.repo.UpdateSnapshotByUserHabitID(ctx, tx, uhs); err != nil {
		tx.Rollback()
		return nil, err
	}
	tx.Commit()

	return res, nil
}

// RecoverUserHabit 恢复习惯，只有暂停状态下的习惯才能恢复
func (u *UserHabitUsecase) RecoverUserHabit(ctx context.Context, request *v1.RecoverUserHabitRequest) (*v1.RecoverUserHabitReply, error) {
	res := &v1.RecoverUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	uh := &UserHabit{}
	uh.ID = request.Id
	uh.UserID = userID
	if err := u.repo.FirstByID(ctx, uh); err != nil {
		return nil, err
	}
	if uh.Status == enums.UserHabitStatusNormal {
		return res, nil
	}
	if uh.Status != enums.UserHabitStatusPause {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrRecoverUserHabitMsg, nil))
	}

	tx := u.db.Begin()
	now := time.Now()
	// 修改习惯本身状态为恢复
	uh.Status = enums.UserHabitStatusNormal
	uh.UpdatedAt = now.Unix()
	if err := u.repo.Update(ctx, tx, uh); err != nil {
		tx.Rollback()
		return nil, err
	}

	// 根据 user_habit_id 修改习惯快照状态为恢复，并恢复提醒事件
	uhs := &UserHabitSnapshot{}
	uhs.UserHabitID = uh.ID
	uhs.Status = enums.UserHabitStatusNormal
	uhs.UpdatedAt = now.Unix()
	if err := u.repo.UpdateSnapshotByUserHabitID(ctx, tx, uhs); err != nil {
		tx.Rollback()
		return nil, err
	}
	tx.Commit()

	// 需要将习惯关联的行为都恢复
	go func() {
		if err := u.recoverUserHabitCronTask(ctx, uh, enums.CronTaskStatusTypeProcessing); err != nil {
			u.log.Errorf("user_habit_usecase: 恢复习惯失败，err: %v, user_habit_id: %v", err, uh.ID)
		}
	}()

	return res, nil
}

// ArchiveUserHabit 归档习惯，所有状态下的习惯都能归档
func (u *UserHabitUsecase) ArchiveUserHabit(ctx context.Context, request *v1.ArchiveUserHabitRequest) (*v1.ArchiveUserHabitReply, error) {
	res := &v1.ArchiveUserHabitReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	uh := &UserHabit{}
	uh.ID = request.Id
	uh.UserID = userID
	if err := u.repo.FirstByID(ctx, uh); err != nil {
		return nil, err
	}

	err := u.updateUserHabitStatusEnd(ctx, uh)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserHabitUsecase) updateUserHabitStatusEnd(ctx context.Context, uh *UserHabit) error {
	if uh.Status == enums.UserHabitStatusEnd {
		return nil
	}

	tx := u.db.Begin()
	now := time.Now()
	// 修改习惯本身状态为停止
	uh.Status = enums.UserHabitStatusEnd
	uh.UpdatedAt = now.Unix()
	if err := u.repo.Update(ctx, tx, uh); err != nil {
		tx.Rollback()
		return err
	}

	// 根据 user_habit_id 修改习惯快照状态为停止，并删除提醒事件
	uhs := &UserHabitSnapshot{}
	uhs.UserHabitID = uh.ID
	uhs.Status = enums.UserHabitStatusEnd
	uhs.UpdatedAt = now.Unix()
	if err := u.repo.UpdateSnapshotByUserHabitID(ctx, tx, uhs); err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()

	return nil
}

// recoverUserHabitCronTask 恢复用户习惯相关的定时任务
func (u *UserHabitUsecase) recoverUserHabitCronTask(ctx context.Context, userHabit *UserHabit, status enums.CronTaskStatusType) error {
	// 修改定时任务状态
	where := Where{
		"status =":    enums.CronTaskStatusTypePause,
		"third_id =":  fmt.Sprintf("%v", userHabit.ID),
		"task_type =": enums.CronTaskTypeUserHabit,
	}
	cronTasks, err := u.cronTaskRepo.FindCronTaskByParams(where)
	if err != nil {
		return stderr.New(fmt.Sprintf("获取 cron_task 列表失败，error: %v, user_habit_id: %v", err, userHabit.ID))
	}
	if len(cronTasks) == 0 {
		return nil
	}
	cronTaskIDs := make([]int32, 0, len(cronTasks))
	for _, item := range cronTasks {
		cronTaskIDs = append(cronTaskIDs, item.ID)
	}
	cronTask := &CronTask{}
	cronTask.Status = status
	cronTask.UpdatedAt = time.Now().Unix()
	if err = u.cronTaskRepo.BatchUpdateByID(ctx, cronTaskIDs, cronTask); err != nil {
		return err
	}

	// 添加用户习惯定时任务
	for _, item := range cronTasks {
		_, err = task.Scheduler.Every(1).Day().At(item.TriggerTime).Tag(item.Tag).Do(func(taskID int32) {
			runCronTaskBody := map[string]interface{}{
				"task_id": taskID,
			}
			if err := httpClient.RunCronTask(runCronTaskBody, u.confData.Task.HttpAddr); err != nil {
				u.log.Errorf("user_habit_usecase: 执行用户习惯定时任务失败, error: %v, task_id: %v", err, taskID)
			}
		}, item.ID)
		if err != nil {
			u.log.Errorf("user_habit_usecase: 添加 scheduler 失败, error: %v", err)
		}
	}

	return nil
}

func (u *UserHabitUsecase) UpdateUserHabitMemo(ctx context.Context, request *v1.UpdateUserHabitMemoRequest) (*v1.UpdateUserHabitMemoReply, error) {
	res := &v1.UpdateUserHabitMemoReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	planetPost := &PlanetPost{}
	planetPost.ID = request.MemoId
	if err := u.userPlanetRepo.FirstPlanetPostByID(ctx, planetPost); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return res, err
	}
	if userID == 0 || planetPost.UserID != userID || len(request.Content) == 0 {
		return res, nil
	}
	planetPost.Content = request.Content
	planetPost.UpdatedAt = time.Now().Unix()
	if err := u.userPlanetRepo.UpdatePlanetPost(ctx, planetPost); err != nil {
		return res, err
	}

	return res, nil
}

func (u *UserHabitUsecase) DeleteUserHabitMemo(ctx context.Context, request *v1.DeleteUserHabitMemoRequest) (*v1.DeleteUserHabitMemoReply, error) {
	res := &v1.DeleteUserHabitMemoReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	if userID == 0 {
		return res, nil
	}

	planetPost := &PlanetPost{}
	planetPost.ID = request.MemoId
	planetPost.UserID = userID
	planetPost.UpdatedAt = time.Now().Unix()
	if err := u.userPlanetRepo.DeletePlantPost(ctx, planetPost); err != nil {
		return res, err
	}

	return res, nil
}

// CreateUserHabitReckon 创建习惯计时，和 ReckonUserHabit 区别在于，直接保存时长
func (u *UserHabitUsecase) CreateUserHabitReckon(ctx context.Context, request *v1.CreateUserHabitReckonRequest) (*v1.CreateUserHabitReckonReply, error) {
	res := &v1.CreateUserHabitReckonReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	// 获取用户所在时区的零点
	userTime, err := time.Parse(time.RFC3339, request.CurrentDate)
	if err != nil {
		u.log.Errorf("time.Parse error: %v, req: %v", err, request)
		return nil, err
	}
	uhs, err := u.generateHabitSnapshotByUserHabitID(ctx, userID, request.UserHabitId, userTime)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	urt := &UserReckonTime{
		UserID:       userID,
		UserHabitID:  uhs.UserHabitID,
		HabitSnapID:  uhs.ID,
		StartAt:      userTime.Unix(),
		EndAt:        userTime.Unix() + int64(request.ReckonDuration),
		IsValid:      true,
		SmallStageID: request.SmallStageId,
		Duration:     int(request.ReckonDuration),
	}
	urt.CreatedAt = now.Unix()
	urt.UpdatedAt = now.Unix()
	if err = u.repo.CreateReckonTime(ctx, urt); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserHabitUsecase) UpdateUserHabitReckon(ctx context.Context, request *v1.UpdateUserHabitReckonRequest) (*v1.UpdateUserHabitReckonReply, error) {
	res := &v1.UpdateUserHabitReckonReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	urt := &UserReckonTime{}
	urt.ID = request.ReckonId
	err := u.repo.FirstReckonTimeByID(ctx, urt)
	if err != nil {
		return nil, err
	}
	if urt.UserID != userID {
		return res, nil
	}
	urt.SmallStageID = request.SmallStageId
	urt.Duration = int(request.ReckonDuration)
	urt.UpdatedAt = time.Now().Unix()
	if err = u.repo.UpdateReckonTime(ctx, urt); err != nil {
		return nil, err
	}

	return res, nil
}

func (u *UserHabitUsecase) DeleteUserHabitReckon(ctx context.Context, request *v1.DeleteUserHabitReckonRequest) (*v1.DeleteUserHabitReckonReply, error) {
	res := &v1.DeleteUserHabitReckonReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	urt := &UserReckonTime{}
	urt.ID = request.ReckonId
	err := u.repo.FirstReckonTimeByID(ctx, urt)
	if err != nil {
		return nil, err
	}
	if urt.UserID != userID {
		return res, nil
	}
	urt.IsValid = false
	urt.UpdatedAt = time.Now().Unix()
	if err = u.repo.UpdateReckonTime(ctx, urt); err != nil {
		return nil, err
	}

	return res, nil
}

func NewUserHabitUsecase(repo UserHabitRepo, logger log.Logger, userRepo UserRepo, userPlanetRepo UserPlanetRepo, cronTaskRepo CronTaskRepo, messageRepo MessageRepo, confData *conf.Data, commonUsecase *CommonUsecase, pushUsecase *PushNotificationUsecase) *UserHabitUsecase {
	return &UserHabitUsecase{
		repo:           repo,
		userRepo:       userRepo,
		userPlanetRepo: userPlanetRepo,
		cronTaskRepo:   cronTaskRepo,
		messageRepo:    messageRepo,
		log:            log.NewHelper(logger),
		db:             db.GormClient,
		confData:       confData,
		commonUsecase:  commonUsecase,
		pushUsecase:    pushUsecase,
	}
}
