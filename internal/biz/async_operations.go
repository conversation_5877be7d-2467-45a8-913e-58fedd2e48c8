package biz

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	v1 "github.com/wlnil/life-log-be/api/user/v1"
	"github.com/wlnil/life-log-be/internal/pkg/cache"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
)

// performAsyncOperationsOptimized 优化的异步操作
// 目标：将非核心操作异步化，不阻塞登录响应
func (uc *UserUsecase) performAsyncOperationsOptimized(ctx context.Context, user *User, req *v1.LoginRequest) {
	operations := []AsyncOperation{
		{
			Name: "预加载用户设置",
			Func: func() error {
				return uc.preloadUserSettings(ctx, user.ID)
			},
		},
		{
			Name: "记录审计日志",
			Func: func() error {
				if uc.auditHelper != nil {
					uc.auditHelper.LogLogin(user.ID, req, true, "", "")
				}
				return nil
			},
		},
		{
			Name: "预加载常用数据",
			Func: func() error {
				uc.preloadUserDataOptimized(ctx, user.ID)
				return nil
			},
		},
	}

	// 并发执行异步操作
	for _, operation := range operations {
		go uc.executeAsyncOperation(operation)
	}
}

// AsyncOperation 异步操作定义
type AsyncOperation struct {
	Name string
	Func func() error
}

// executeAsyncOperation 执行异步操作
func (uc *UserUsecase) executeAsyncOperation(operation AsyncOperation) {
	defer func() {
		if r := recover(); r != nil {
			uc.log.Errorf("【异步操作】%s panic: %v", operation.Name, r)
		}
	}()

	startTime := time.Now()
	if err := operation.Func(); err != nil {
		uc.log.Errorf("【异步操作】%s 失败: %v", operation.Name, err)
	} else {
		uc.log.Infof("【异步操作】%s 完成，耗时: %v", operation.Name, time.Since(startTime))
	}
}

// cacheUserInfoOptimized 优化的用户信息缓存
func (uc *UserUsecase) cacheUserInfoOptimized(ctx context.Context, user *User) error {
	// 构建用户信息对象（只包含UserInfo结构体中存在的字段）
	userInfo := UserInfo{
		ID:             user.ID,
		Name:           user.Name,
		Email:          user.Email,
		Phone:          user.Phone,
		AvatarUrl:      user.AvatarUrl,
		Status:         user.Status,
		RoleType:       user.RoleType,
		TimezoneName:   user.TimezoneName,
		TimezoneOffset: user.TimezoneOffset,
		Gender:         user.Gender,
		IsSystemAdmin:  user.IsSystemAdmin,
		ExpiredAt:      user.ExpiredAt,
		Desc:           user.Desc,
		IsSetPwd:       len(user.Password) > 0,
		IsVip:          user.ExpiredAt > time.Now().Unix(),
	}

	// 缓存到Redis
	return uc.cacheUserInfoToRedis(ctx, user.ID, userInfo)
}

// cacheUserInfoToRedis 缓存用户信息到Redis
func (uc *UserUsecase) cacheUserInfoToRedis(ctx context.Context, userID int32, userInfo UserInfo) error {
	cacheKey := fmt.Sprintf("%v%v", enums.RedisUserInfoPrefix, userID)

	userInfoBytes, err := json.Marshal(userInfo)
	if err != nil {
		return err
	}

	// 设置1小时过期时间（使用Redis客户端直接设置）
	return cache.RedisClient.Set(ctx, cacheKey, string(userInfoBytes), time.Hour).Err()
}

// preloadUserSettings 预加载用户设置
func (uc *UserUsecase) preloadUserSettings(ctx context.Context, userID int32) error {
	// 查询用户设置
	userSetting := &UserSetting{UserID: userID}
	if err := uc.repo.GetUserSetting(ctx, userSetting); err != nil {
		// 如果用户设置不存在，创建默认设置
		if err.Error() == "record not found" {
			return uc.createDefaultUserSetting(ctx, userID)
		}
		return err
	}

	// 缓存用户设置
	return uc.cacheUserSetting(ctx, userID, userSetting)
}

// createDefaultUserSetting 创建默认用户设置
func (uc *UserUsecase) createDefaultUserSetting(ctx context.Context, userID int32) error {
	defaultSetting := &UserSetting{
		UserID:          userID,
		AwardImageUrl:   "award-imgs/default-1725106894969",
		PrivacyPassword: "1234",
	}

	if err := uc.repo.CreateUserSetting(ctx, defaultSetting); err != nil {
		return err
	}

	// 缓存默认设置
	return uc.cacheUserSetting(ctx, userID, defaultSetting)
}

// cacheUserSetting 缓存用户设置
func (uc *UserUsecase) cacheUserSetting(ctx context.Context, userID int32, setting *UserSetting) error {
	cacheKey := fmt.Sprintf("user_setting_%d", userID)

	settingBytes, err := json.Marshal(setting)
	if err != nil {
		return err
	}

	// 设置30分钟过期时间
	return cache.RedisClient.Set(ctx, cacheKey, string(settingBytes), 30*time.Minute).Err()
}

// preloadUserDataOptimized 预加载用户常用数据
func (uc *UserUsecase) preloadUserDataOptimized(ctx context.Context, userID int32) {
	// 并发预加载多种数据
	var wg sync.WaitGroup

	// 预加载用户习惯数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := uc.preloadUserHabits(ctx, userID); err != nil {
			uc.log.Errorf("【预加载】用户习惯数据失败: %v", err)
		}
	}()

	// 预加载用户统计数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := uc.preloadUserStats(ctx, userID); err != nil {
			uc.log.Errorf("【预加载】用户统计数据失败: %v", err)
		}
	}()

	// 预加载用户消息数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := uc.preloadUserMessages(ctx, userID); err != nil {
			uc.log.Errorf("【预加载】用户消息数据失败: %v", err)
		}
	}()

	// 等待所有预加载完成（设置超时）
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		uc.log.Infof("【预加载】用户数据预加载完成, userID: %d", userID)
	case <-time.After(10 * time.Second):
		uc.log.Warnf("【预加载】用户数据预加载超时, userID: %d", userID)
	}
}

// preloadUserHabits 预加载用户习惯数据
func (uc *UserUsecase) preloadUserHabits(ctx context.Context, userID int32) error {
	// 这里应该调用习惯相关的usecase
	// 示例实现
	cacheKey := fmt.Sprintf("user_habits_%d", userID)

	// 模拟查询用户习惯数据
	habits := []map[string]interface{}{
		{"id": 1, "name": "早起", "status": "active"},
		{"id": 2, "name": "运动", "status": "active"},
	}

	habitsBytes, err := json.Marshal(habits)
	if err != nil {
		return err
	}

	// 缓存15分钟
	return cache.RedisClient.Set(ctx, cacheKey, string(habitsBytes), 15*time.Minute).Err()
}

// preloadUserStats 预加载用户统计数据
func (uc *UserUsecase) preloadUserStats(ctx context.Context, userID int32) error {
	cacheKey := fmt.Sprintf("user_stats_%d", userID)

	// 模拟查询用户统计数据
	stats := map[string]interface{}{
		"total_habits":    10,
		"completed_today": 5,
		"streak_days":     7,
		"total_points":    150,
	}

	statsBytes, err := json.Marshal(stats)
	if err != nil {
		return err
	}

	// 缓存10分钟
	return cache.RedisClient.Set(ctx, cacheKey, string(statsBytes), 10*time.Minute).Err()
}

// preloadUserMessages 预加载用户消息数据
func (uc *UserUsecase) preloadUserMessages(ctx context.Context, userID int32) error {
	cacheKey := fmt.Sprintf("user_messages_%d", userID)

	// 模拟查询用户消息数据
	messages := map[string]interface{}{
		"unread_count": 3,
		"latest_message": map[string]interface{}{
			"id":      1,
			"title":   "欢迎使用",
			"content": "欢迎使用life-log应用",
			"time":    time.Now().Unix(),
		},
	}

	messagesBytes, err := json.Marshal(messages)
	if err != nil {
		return err
	}

	// 缓存5分钟
	return cache.RedisClient.Set(ctx, cacheKey, string(messagesBytes), 5*time.Minute).Err()
}

// determinePushService 确定推送服务类型（备用方法）
func (uc *UserUsecase) determinePushService(requestedService, platform string) string {
	// 如果有配置管理器，优先使用配置管理器
	if uc.pushConfigManager != nil {
		if requestedService != "" && uc.pushConfigManager.IsServiceEnabled(requestedService) {
			return requestedService
		}
		return uc.pushConfigManager.GetDefaultService()
	}

	// 备用逻辑：用户明确指定的服务
	if requestedService != "" {
		switch requestedService {
		case "jpush", "fcm", "apns":
			return requestedService
		}
	}

	// 根据平台自动选择
	switch platform {
	case "ios":
		return "apns"
	case "android":
		return "fcm"
	default:
		return "jpush"
	}
}
