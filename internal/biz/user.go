package biz

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/wlnil/life-log-be/api/user/v1"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/auth"
	"github.com/wlnil/life-log-be/internal/pkg/cache"
	"github.com/wlnil/life-log-be/internal/pkg/code"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"github.com/wlnil/life-log-be/internal/pkg/firebase"
	httpClient "github.com/wlnil/life-log-be/internal/pkg/http_client"
	"github.com/wlnil/life-log-be/internal/pkg/middleware/localize"
	"github.com/wlnil/life-log-be/internal/pkg/store/qiniu"
	"github.com/wlnil/life-log-be/internal/pkg/task"
	"github.com/wlnil/life-log-be/internal/pkg/tool"
	"gorm.io/gorm"
	"regexp"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

const DefaultAvatarUrl = ""

// User 用户信息表
type User struct {
	BasicModel
	Name           string               `gorm:"column:name" json:"name"`                         // 用户昵称
	Password       string               `gorm:"column:password" json:"password"`                 // 密码
	AvatarUrl      string               `gorm:"column:avatar_url" json:"avatar_url"`             // 头像地址
	Phone          string               `gorm:"column:phone" json:"phone"`                       // 手机号
	Email          string               `gorm:"column:email" json:"email"`                       // 邮箱
	Gender         enums.UserGenderType `gorm:"column:gender" json:"gender"`                     // 性别
	Status         enums.UserStatus     `gorm:"column:status" json:"status"`                     // 状态
	Desc           string               `gorm:"column:desc" json:"desc"`                         // 个人简介
	RoleType       enums.UserRoleType   `gorm:"column:role_type" json:"role_type"`               // 用户角色
	ExpiredAt      int64                `gorm:"column:expired_at" json:"expired_at"`             // 会员过期时间
	IsSystemAdmin  bool                 `gorm:"column:is_system_admin" json:"is_system_admin"`   // 是否是系统管理员
	ThirdPartyID   string               `gorm:"column:third_party_id" json:"third_party_id"`     // 第三方平台ID
	ThirdPartyType enums.ThirdPartyType `gorm:"column:third_party_type" json:"third_party_type"` // 第三方平台类型
	TimezoneName   string               `gorm:"column:timezone_name" json:"timezone_name"`       // 时区名称
	TimezoneOffset string               `gorm:"column:timezone_offset" json:"timezone_offset"`   // 时区偏移量
	FirebaseUID    string               `gorm:"column:firebase_uid" json:"firebase_uid"`         // Firebase用户唯一标识
	Locale         string               `gorm:"column:locale" json:"locale"`                     // 用户语言偏好
	LastLoginAt    int64                `gorm:"column:last_login_at" json:"last_login_at"`       // 最后登录时间
	LoginCount     int32                `gorm:"column:login_count" json:"login_count"`           // 登录次数

	IsInit bool `gorm:"-" json:"-"` // 是否初始化
}

func (u *User) TableName() string {
	return "tb_user"
}

func (u *User) GetAvatarUrl() string {
	if u.AvatarUrl == "" {
		return ""
	}
	return fmt.Sprintf("%v/%v", qiniu.Client.PublicAddr, u.AvatarUrl)
}

// IsChineseUser 判断是否为中国用户
// 判断逻辑按优先级顺序：
// 1. 手机号检查：如果Phone字段不为空且以"+86"开头或符合中国手机号格式（11位数字），返回true
// 2. 时区检查：如果TimezoneName包含"Asia/Shanghai"、"Asia/Beijing"或"Asia/Chongqing"，返回true
// 3. 国家代码检查：如果CountryCode等于"CN"或"CHN"，返回true
// 4. 默认值：以上条件都不满足时返回false（海外用户）
func (u *User) IsChineseUser() bool {
	// 1. 手机号检查
	if u.Phone != "" {
		// 检查是否以+86开头
		if strings.HasPrefix(u.Phone, "+86") {
			return true
		}
		// 检查是否为11位数字（中国手机号格式）
		if len(u.Phone) == 11 {
			// 验证是否全为数字
			for _, char := range u.Phone {
				if char < '0' || char > '9' {
					break
				}
			}
			// 如果是11位数字，认为是中国手机号
			if matched, _ := regexp.MatchString(`^1[3-9]\d{9}$`, u.Phone); matched {
				return true
			}
		}
	}

	// 2. 时区检查
	if u.TimezoneName != "" {
		chineseTimezones := []string{"Asia/Shanghai", "Asia/Beijing", "Asia/Chongqing"}
		for _, timezone := range chineseTimezones {
			if strings.Contains(u.TimezoneName, timezone) {
				return true
			}
		}
	}

	// 3. 国家代码检查
	if u.Locale != "" {
		if strings.Contains(strings.ToUpper(u.Locale), "ZH") {
			return true
		}
	}

	// 4. 默认值：海外用户
	return false
}

// UserRelation 用户关系表
type UserRelation struct {
	BasicModel
	UserID       int32                        `gorm:"column:user_id" json:"user_id"`               // 用户 ID
	FollowUserID int32                        `gorm:"column:follow_user_id" json:"follow_user_id"` // 粉丝用户 ID
	Status       enums.UserRelationStatusType `gorm:"column:status" json:"status"`                 // 状态
}

func (ur *UserRelation) TableName() string {
	return "tb_user_relation"
}

type UserStatistic struct {
	BasicModel
	UserID    int32  `json:"user_id" gorm:"column:user_id"`       // 用户 id
	TodayTime int64  `json:"today_time" gorm:"column:today_time"` // 当日时间戳，精确到天
	Content   string `json:"content" gorm:"column:content"`
}

func (m *UserStatistic) TableName() string {
	return "tb_user_statistic"
}

type UserSetting struct {
	BasicModel
	UserID                int32  `json:"user_id" gorm:"column:user_id"`                                 // 用户ID
	AwardImageUrl         string `json:"award_image_url" gorm:"column:award_image_url"`                 // 激励图片地址
	PrivacyPassword       string `json:"privacy_password" gorm:"column:privacy_password"`               // 隐私密码
	PushPermissionGranted bool   `json:"push_permission_granted" gorm:"column:push_permission_granted"` // 推送权限是否授权
}

func (m *UserSetting) TableName() string {
	return "tb_user_setting"
}

type UserRepo interface {
	Create(context.Context, *User) error
	Update(context.Context, *User) error
	FindByID(context.Context, *User) error
	FindBySearch(context.Context, *User, Where) error

	FirstUserRelation(ctx context.Context, ur *UserRelation) error
	UpdateUserRelationByID(ctx context.Context, ur *UserRelation) error
	CreateUserRelation(ctx context.Context, ur *UserRelation) error
	ListUserFavoritePostIDByPostID(ctx context.Context, userID int32, postIDs []int32) ([]int32, error)
	ListUserLikePostIDByPostID(ctx context.Context, userID int32, postIDs []int32) ([]string, error)

	GetUserSetting(ctx context.Context, us *UserSetting) error
	UpdateUserSetting(ctx context.Context, us *UserSetting, params map[string]interface{}) error
	CreateUserSetting(ctx context.Context, us *UserSetting) error
}

type UserUsecase struct {
	repo              UserRepo
	authUc            *UserAuthUsecase
	commonUseCase     *CommonUsecase
	cronTaskUsecase   *CronTaskUsecase
	auditHelper       *AuditHelper
	multiCache        *cache.MultiLevelCache
	pushConfigManager *PushConfigManager
	enhancedAuthUc    *EnhancedAuthUsecase

	log      *log.Helper
	confData *conf.Data
}

func NewUserUsecase(repo UserRepo, logger log.Logger, authUc *UserAuthUsecase, commonUseCase *CommonUsecase, cronTaskUsecase *CronTaskUsecase, auditHelper *AuditHelper, multiCache *cache.MultiLevelCache, enhancedAuthUc *EnhancedAuthUsecase, confData *conf.Data) *UserUsecase {
	uc := &UserUsecase{
		repo:            repo,
		log:             log.NewHelper(logger),
		confData:        confData,
		authUc:          authUc,
		commonUseCase:   commonUseCase,
		cronTaskUsecase: cronTaskUsecase,
		auditHelper:     auditHelper,
		multiCache:      multiCache,
		enhancedAuthUc:  enhancedAuthUc,
	}

	// 初始化推送配置管理器
	uc.pushConfigManager = NewPushConfigManager(confData, logger)

	// 记录配置信息
	uc.pushConfigManager.LogConfigInfo()

	return uc
}

func (uc *UserUsecase) Register(ctx context.Context, req *v1.RegisterRequest) (*v1.RegisterReply, error) {
	res := &v1.RegisterReply{}

	// 检查验证码是否正确
	redisRes, err := cache.RedisClient.Get(ctx, enums.RedisPhoneRegisterPrefix+req.Phone).Result()
	if err != nil {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}
	if redisRes != req.VerifyCode {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}

	// 检查手机号是否重复
	searchUser, err := uc.IsRepeatedByPhone(ctx, req.Phone)
	if err != nil {
		return nil, err
	}
	if searchUser.ID > 0 {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPhoneRegisteredMsg, nil))
	}

	// 检查密码是否一致
	if req.Password != req.ConfirmPassword {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordNotSameMsg, nil))
	}

	// 检查密码是否符合规则
	if !auth.CheckPassword(req.Password) {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordNotAllowMsg, nil))
	}
	encryptPwd, err := auth.Encrypt(req.Password)
	if err != nil {
		return res, err
	}

	now := time.Now()
	nowUnix := now.Unix()
	u := &User{
		Name:           tool.GenerateUserName(req.Phone, ""),
		Phone:          req.Phone,
		Password:       encryptPwd,
		AvatarUrl:      DefaultAvatarUrl,
		Status:         enums.UserStatusNormal,
		LastLoginAt:    nowUnix,
		RoleType:       enums.UserRoleTypeNormal,
		ExpiredAt:      0,
		IsSystemAdmin:  false,
		TimezoneName:   "Asia/Shanghai",
		TimezoneOffset: "+08:00",
	}
	u.CreatedAt = now.Unix()
	u.UpdatedAt = now.Unix()
	if err := uc.repo.Create(ctx, u); err != nil {
		return nil, err
	}

	// TODO: 创建用户认证信息

	// 异步记录注册审计日志
	if uc.auditHelper != nil {
		go uc.auditHelper.LogRegister(u.ID, req.Phone, "", "android", "", "", true, "", "")
	}

	return res, nil
}

// Login 如果不存在默认注册
func (uc *UserUsecase) Login(ctx context.Context, req *v1.LoginRequest) (*v1.LoginReply, error) {
	startTime := time.Now()
	res := &v1.LoginReply{}

	// 记录性能指标
	defer func() {
		duration := time.Since(startTime)
		uc.log.Infof("【登录优化】登录耗时: %v, 类型: %d", duration, req.LoginType)
	}()

	// 1. 参数校验（复用现有逻辑）
	if err := uc.validateLoginRequest(ctx, req); err != nil {
		return nil, err
	}

	// 2. 执行核心登录逻辑（批量处理，减少SQL查询）
	user, userToken, err := uc.performCoreLogin(ctx, req)
	if err != nil {
		// 记录失败的审计日志
		go uc.logLoginFailure(ctx, req, err)
		return res, err
	}

	isSetPrivacy := uc.handleUserPushSettings(ctx, user.ID, false)

	// 保存用户信息到 redis
	go func() {
		_, err = uc.commonUseCase.CacheUserInfo(ctx, user.ID, user, &isSetPrivacy)
		if err != nil {
			uc.log.Errorf("【用户登录】缓存用户信息失败, userID: %v, err: %v", user.ID, err)
		}
	}()

	// 异步初始化新用户数据（不阻塞登录响应）
	if user.IsInit {
		go uc.initializeNewUserAsync(ctx, user)
	}

	res.Data = &v1.LoginReply_Data{
		Token:         userToken.Token,
		ExpireAt:      int32(userToken.ExpireAt),
		UserId:        user.ID,
		Name:          user.Name,
		AvatarUrl:     user.GetAvatarUrl(),
		IsSystemAdmin: user.IsSystemAdmin,
		RoleType:      int32(user.RoleType),
		Phone:         user.Phone,
		Email:         user.Email,
		Status:        int32(user.Status),
		Gender:        int32(user.Gender),
		Desc:          user.Desc,
		IsSetPwd:      len(user.Password) > 0,
		IsSetPrivacy:  isSetPrivacy,
	}
	if user.IsSystemAdmin || user.ExpiredAt > time.Now().Unix() {
		res.Data.IsVip = true
	}

	// 4. 异步执行非核心操作（不阻塞响应）
	go uc.performAsyncOperationsOptimized(context.Background(), user, req)

	return res, nil
}

// performCoreLogin 核心登录逻辑优化
// 目标：在单个事务中完成所有必要的数据库操作
func (uc *UserUsecase) performCoreLogin(ctx context.Context, req *v1.LoginRequest) (*User, *UserToken, error) {
	var user *User
	var err error

	// 根据登录类型进行认证
	switch {
	case req.LoginType == int32(enums.UserLoginTypeGoogle) || req.LoginType == int32(enums.UserLoginTypeApple):
		user, err = uc.authenticateFirebase(ctx, req)
	case req.PassType == int32(enums.UserPassTypeCode):
		user, err = uc.authenticateByCode(ctx, req)
	default:
		user, err = uc.authenticateByPassword(ctx, req)
	}

	if err != nil {
		return nil, nil, err
	}

	// 在事务中批量处理用户更新和Token创建
	var userToken *UserToken
	err = uc.performTransaction(ctx, user, req, &userToken)

	return user, userToken, err
}

// authenticateFirebase Firebase认证优化版本
func (uc *UserUsecase) authenticateFirebase(ctx context.Context, req *v1.LoginRequest) (*User, error) {
	// 1. 验证Firebase ID Token（真实Firebase验证）
	firebaseUser, err := firebase.FirebaseAuthInstance.VerifyIDToken(ctx, req.ThirdPartyToken)
	if err != nil {
		uc.log.Errorf("【Firebase认证】验证Firebase token失败, err: %v", err)
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrThirdPartyTokenWrongMsg, nil))
	}

	uc.log.Infof("【Firebase认证】验证成功: uid=%s, email=%s, provider=%s",
		firebaseUser.UID, firebaseUser.Email, firebaseUser.Provider)

	// 2. 首先通过Firebase UID查找用户
	user := &User{}
	where := Where{"firebase_uid =": firebaseUser.UID, "status !=": enums.UserStatusDelete}

	err = uc.repo.FindBySearch(ctx, user, where)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 3. 如果通过Firebase UID找到用户，直接返回
	if err == nil {
		uc.log.Infof("【Firebase认证】通过Firebase UID找到用户: user_id=%d", user.ID)
		// 同步最新的Firebase用户信息
		if firebaseUser.Email != "" && user.Email != firebaseUser.Email {
			user.Email = firebaseUser.Email
		}
		return user, nil
	}

	// 4. 如果没有找到，尝试通过email查找现有用户并关联Firebase UID
	if firebaseUser.Email != "" {
		existingUser, err := uc.findUserByEmailAndAssociateFirebase(ctx, firebaseUser)
		if err != nil {
			return nil, err
		}
		if existingUser != nil {
			uc.log.Infof("【Firebase认证】通过email找到现有用户并关联Firebase UID: user_id=%d", existingUser.ID)
			return existingUser, nil
		}
	}

	// 5. 用户不存在，创建新用户
	uc.log.Infof("【Firebase认证】创建新用户: email=%s", firebaseUser.Email)
	return uc.createUser(ctx, firebaseUser, req)
}

// findUserByEmailAndAssociateFirebase 通过email查找现有用户并关联Firebase UID
func (uc *UserUsecase) findUserByEmailAndAssociateFirebase(ctx context.Context, firebaseUser *firebase.FirebaseUser) (*User, error) {
	if firebaseUser.Email == "" {
		return nil, nil
	}

	// 通过email查找现有用户
	user := &User{}
	where := Where{"email =": firebaseUser.Email, "status !=": enums.UserStatusDelete}

	err := uc.repo.FindBySearch(ctx, user, where)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	// 如果用户已经有Firebase UID，检查是否匹配
	if user.FirebaseUID != "" {
		if user.FirebaseUID != firebaseUser.UID {
			uc.log.Warnf("【Firebase认证】用户email=%s已关联不同的Firebase UID: existing=%s, new=%s",
				firebaseUser.Email, user.FirebaseUID, firebaseUser.UID)
			return nil, errors.BadRequest("", "该邮箱已关联其他账号")
		}
		return user, nil
	}

	// 关联Firebase UID到现有用户
	user.FirebaseUID = firebaseUser.UID

	uc.log.Infof("【Firebase认证】成功关联Firebase UID到现有用户: user_id=%d, firebase_uid=%s",
		user.ID, firebaseUser.UID)

	return user, nil
}

func (uc *UserUsecase) createUser(ctx context.Context, firebaseUser *firebase.FirebaseUser, req *v1.LoginRequest) (*User, error) {
	now := time.Now()
	nowUnix := now.Unix()

	user := &User{
		BasicModel: BasicModel{
			CreatedAt: nowUnix,
			UpdatedAt: nowUnix,
		},
		Status:         enums.UserStatusNormal,
		LastLoginAt:    nowUnix,
		RoleType:       enums.UserRoleTypeNormal,
		TimezoneName:   uc.getTimezoneOrDefault(req.TimezoneName),
		TimezoneOffset: uc.getTimezoneOffsetOrDefault(req.TimezoneOffset),
		Locale:         uc.getLocaleOrDefault("", req.Locale),
	}

	// 优先使用Firebase用户信息，如果没有则使用请求中的信息
	if firebaseUser != nil && firebaseUser.UID != "" {
		user.FirebaseUID = firebaseUser.UID
		user.Email = firebaseUser.Email
		user.Name = uc.generateUserName(firebaseUser)
		user.AvatarUrl = firebaseUser.Picture

		uc.log.Infof("【Firebase认证】创建Firebase用户: uid=%s, email=%s, name=%s",
			firebaseUser.UID, firebaseUser.Email, user.Name)
	} else {
		// 非Firebase用户（验证码登录等）
		user.Email = req.Email
		user.Phone = req.Phone
		user.Name = tool.GenerateUserName(req.Phone, req.Email)
		user.AvatarUrl = ""

		uc.log.Infof("【用户创建】创建普通用户: email=%s, phone=%s", req.Email, req.Phone)
	}

	if err := uc.repo.Create(ctx, user); err != nil {
		uc.log.Errorf("【用户创建】创建用户失败: %v", err)
		return nil, err
	}

	user.IsInit = true
	uc.log.Infof("【用户创建】用户创建成功: user_id=%d", user.ID)

	return user, nil
}

// authenticateByCode 验证码认证优化版本
func (uc *UserUsecase) authenticateByCode(ctx context.Context, req *v1.LoginRequest) (*User, error) {
	// 验证验证码
	redisKey := enums.RedisPhoneLoginPrefix + req.Phone
	if req.LoginType == int32(enums.UserLoginTypeEmail) {
		redisKey = enums.RedisEmailLoginPrefix + req.Email
	}
	redisRes, err := cache.RedisClient.Get(ctx, redisKey).Result()
	if err != nil {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}
	if redisRes != req.VerifyCode {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}

	user := &User{}
	var where Where

	// 使用优化索引查询
	if req.Phone != "" {
		where = Where{"phone =": req.Phone, "status !=": enums.UserStatusDelete}
	} else if req.Email != "" {
		where = Where{"email =": req.Email, "status !=": enums.UserStatusDelete}
	} else {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUserOrPasswordWrongMsg, nil))
	}

	err = uc.repo.FindBySearch(ctx, user, where)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 3. 用户不存在则创建，存在则直接返回（更新操作在事务中进行）
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return uc.createUser(ctx, nil, req)
	}

	// 4. 删除验证码
	if err = cache.RedisClient.Del(ctx, redisKey).Err(); err != nil {
		uc.log.Errorf("【用户登录】删除验证码失败, key: %v, err: %v", redisKey, err)
	}

	return user, nil
}

// authenticateByPassword 密码认证
func (uc *UserUsecase) authenticateByPassword(ctx context.Context, req *v1.LoginRequest) (*User, error) {
	user := &User{}
	var where Where

	// 使用优化索引查询
	if req.Phone != "" {
		where = Where{"phone =": req.Phone, "status !=": enums.UserStatusDelete}
	} else if req.Email != "" {
		where = Where{"email =": req.Email, "status !=": enums.UserStatusDelete}
	} else {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUserOrPasswordWrongMsg, nil))
	}

	if err := uc.repo.FindBySearch(ctx, user, where); err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			uc.log.Errorf("【用户登录】用户不存在, phone: %v, email: %v", req.Phone, req.Email)
			return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUserOrPasswordWrongMsg, nil))
		}
		return nil, err
	}

	// 验证密码
	if err := auth.Compare(user.Password, req.Password); err != nil {
		uc.log.Warnf("【用户登录】密码错误, phone: %v, email: %v, err: %v", req.Phone, req.Email, err)
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUserOrPasswordWrongMsg, nil))
	}

	return user, nil
}

// initializeNewUserAsync 异步初始化新用户数据
func (uc *UserUsecase) initializeNewUserAsync(ctx context.Context, user *User) {
	defer func() {
		if r := recover(); r != nil {
			uc.log.Errorf("【异步初始化】新用户初始化panic: %v", r)
		}
	}()

	// 初始化用户数据
	useEnglish := user.Locale != "" && !strings.HasPrefix(user.Locale, "zh")
	timeZoneStr := user.TimezoneName + ";" + user.TimezoneOffset

	if err := uc.commonUseCase.InitUserData(ctx, user.ID, useEnglish, timeZoneStr); err != nil {
		uc.log.Errorf("【异步初始化】初始化用户数据失败, userID: %v, err: %v", user.ID, err)
	}
}

// performTransaction 执行优化的事务操作
func (uc *UserUsecase) performTransaction(ctx context.Context, user *User, req *v1.LoginRequest, userToken **UserToken) error {
	// 简化的事务处理，直接更新用户信息和创建token
	if !user.IsInit {
		now := time.Now()
		nowUnix := now.Unix()

		// 更新用户登录信息
		user.LastLoginAt = nowUnix
		user.LoginCount++
		user.UpdatedAt = nowUnix

		if req.TimezoneName != "" {
			user.TimezoneName = req.TimezoneName
		}
		if req.TimezoneOffset != "" {
			user.TimezoneOffset = req.TimezoneOffset
		}
		if req.Locale != "" {
			user.Locale = req.Locale
		}

		// 更新用户信息
		if err := uc.repo.Update(ctx, user); err != nil {
			return err
		}
	}

	// 创建新Token
	token, err := uc.authUc.CreateUserToken(ctx, user.ID, req.Platform, "", "", "", "", "", "")
	if err != nil {
		return err
	}
	*userToken = token

	return nil
}

// handleUserPushSettingsAsync 异步处理用户推送设置
func (uc *UserUsecase) handleUserPushSettings(ctx context.Context, userID int32, pushPermissionGranted bool) (isSetPrivacy bool) {
	// 获取或创建用户设置
	userSetting := &UserSetting{UserID: userID}
	err := uc.repo.GetUserSetting(ctx, userSetting)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		uc.log.Errorf("获取用户设置失败: %v", err)
		return false
	}

	go func() {
		// 更新推送权限设置
		updates := map[string]interface{}{
			"push_permission_granted": pushPermissionGranted,
			"updated_at":              time.Now().Unix(),
		}

		if userSetting.ID == 0 {
			// 创建新的用户设置
			userSetting.PushPermissionGranted = pushPermissionGranted
			userSetting.AwardImageUrl = "award-imgs/default-1725106894969"
			userSetting.PrivacyPassword = "1234"
			userSetting.CreatedAt = time.Now().Unix()
			userSetting.UpdatedAt = time.Now().Unix()

			if err = uc.repo.CreateUserSetting(ctx, userSetting); err != nil {
				uc.log.Errorf("创建用户设置失败: %v", err)
			}
		} else {
			// 更新现有用户设置
			if err = uc.repo.UpdateUserSetting(ctx, userSetting, updates); err != nil {
				uc.log.Errorf("更新用户推送设置失败: %v", err)
			}
		}
	}()

	return len(userSetting.PrivacyPassword) > 0
}

func (uc *UserUsecase) validateLoginRequest(ctx context.Context, req *v1.LoginRequest) error {
	// 参数校验
	if req.LoginType == int32(enums.UserLoginTypeGoogle) || req.LoginType == int32(enums.UserLoginTypeApple) {
		// 第三方登录需要提供 token
		if req.ThirdPartyToken == "" {
			return errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrParamsMsg, nil))
		}
	} else if (req.Phone == "" && req.Email == "") || (req.PassType == int32(enums.UserPassTypeCode) && req.VerifyCode == "") || (req.PassType == int32(enums.UserPassTypePassword) && req.Password == "") {
		return errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrParamsMsg, nil))
	}

	return nil
}

func (uc *UserUsecase) logLoginFailure(ctx context.Context, req *v1.LoginRequest, err error) {
	if uc.auditHelper != nil {
		uc.auditHelper.LogLogin(0, req, false, "LOGIN_FAILED", err.Error())
	}
}

func (uc *UserUsecase) IsRepeatedByPhone(ctx context.Context, phone string) (*User, error) {
	u := &User{}
	if err := uc.repo.FindBySearch(ctx, u, Where{"phone =": phone}); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	return u, nil
}

func (uc *UserUsecase) Logout(ctx context.Context, req *v1.LogoutRequest) (*v1.LogoutReply, error) {
	res := &v1.LogoutReply{}
	userID := auth.GetUserIDFromCtx(ctx)

	// 删除用户认证信息
	if err := uc.authUc.repo.InvalidateUserTokens(ctx, userID, enums.UserTokenPlatformType(req.Platform)); err != nil {
		return nil, err
	}

	// 异步记录登出审计日志
	if uc.auditHelper != nil {
		go uc.auditHelper.LogLogout(userID, req.Platform, req.DeviceId, "", 0)
	}

	return res, nil
}

func (uc *UserUsecase) ForgetPassword(ctx context.Context, request *v1.ForgetPasswordRequest) (*v1.ForgetPasswordReply, error) {
	res := &v1.ForgetPasswordReply{}
	userID := auth.GetUserIDFromCtx(ctx)
	if userID == 0 {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidUserMsg, nil))
	}
	u := &User{BasicModel: BasicModel{ID: userID}}
	if err := uc.repo.FindByID(ctx, u); err != nil {
		return nil, err
	}
	if u.Phone != request.Phone && u.Email != request.Email {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidUserMsg, nil))
	}

	// 检查验证码是否正确
	redisKey := enums.RedisPhoneForgetPasswordPrefix + request.Phone
	if request.Email != "" {
		redisKey = enums.RedisEmailForgetPasswordPrefix + request.Email
	}
	redisRes, err := cache.RedisClient.Get(ctx, redisKey).Result()
	if err != nil {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}
	if redisRes != request.VerifyCode {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}

	// 检查密码是否一致
	if request.Password != request.ConfirmPassword {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordNotSameMsg, nil))
	}

	// 检查密码是否符合规则
	if !auth.CheckPassword(request.Password) {
		return res, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordNotAllowMsg, nil))
	}
	encryptPwd, err := auth.Encrypt(request.Password)
	if err != nil {
		return res, err
	}

	// 更新密码
	u.Password = encryptPwd
	u.UpdatedAt = time.Now().Unix()
	if err = uc.repo.Update(ctx, u); err != nil {
		return nil, err
	}

	// 重新缓存用户信息
	go func() {
		_, _ = uc.commonUseCase.CacheUserInfo(ctx, u.ID, u, nil)

		// 删除验证码
		if err = cache.RedisClient.Del(ctx, redisKey).Err(); err != nil {
			uc.log.Errorf("【忘记密码】删除验证码失败, key: %v, err: %v", redisKey, err)
		}
	}()

	return res, nil
}

func (uc *UserUsecase) GetUserProfile(ctx context.Context, request *v1.GetUserProfileRequest) (*v1.GetUserProfileReply, error) {
	res := &v1.GetUserProfileReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	u := &User{BasicModel: BasicModel{ID: userID}}
	if err := uc.repo.FindByID(ctx, u); err != nil {
		return nil, err
	}

	// 获取统计数据
	allHabits, onTacks, doneHabits, err := uc.commonUseCase.GetUserHabitStats(ctx, userID)
	if err != nil {
		uc.log.Errorf("用户信息中获取统计数据失败，err: %v", err)
	}
	if err != nil {
	}
	goals := make([]string, 0)
	goals = append(goals, "starter")

	res.Data = &v1.GetUserProfileReply_Data{
		Stats: &v1.GetUserProfileReply_Stats{
			AllHabits:     allHabits,
			OnTrackHabits: onTacks,
			DoneHabits:    doneHabits,
		},
		Goals:     goals,
		Username:  u.Name,
		Phone:     u.Phone,
		Email:     u.Email,
		AvatarUrl: u.GetAvatarUrl(),
		Desc:      u.Desc,
	}

	return res, nil
}

func (uc *UserUsecase) UpdateUserProfile(ctx context.Context, request *v1.UpdateUserProfileRequest) (*v1.UpdateUserProfileReply, error) {
	res := &v1.UpdateUserProfileReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	u := &User{BasicModel: BasicModel{ID: userID}}
	if err := uc.repo.FindByID(ctx, u); err != nil {
		return nil, err
	}

	if request.Username != "" {
		u.Name = request.Username
	}
	if request.AvatarUrl != "" {
		u.AvatarUrl = request.AvatarUrl
	}
	u.Desc = request.Desc
	u.UpdatedAt = time.Now().Unix()
	if err := uc.repo.Update(ctx, u); err != nil {
		return nil, err
	}
	res.Data = &v1.UpdateUserProfileReply_Data{
		Username:  u.Name,
		AvatarUrl: u.GetAvatarUrl(),
		Desc:      u.Desc,
	}

	// 重新缓存用户信息
	go func() {
		_, _ = uc.commonUseCase.CacheUserInfo(ctx, u.ID, u, nil)
	}()

	return res, nil
}

func (uc *UserUsecase) FollowUser(ctx context.Context, request *v1.FollowUserRequest) (*v1.FollowUserReply, error) {
	res := &v1.FollowUserReply{}
	now := time.Now()

	userID := auth.GetUserIDFromCtx(ctx)
	if userID == request.UserId {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUserCanNotUnfollowSelfMsg, nil))
	}
	followedUserID := &User{BasicModel: BasicModel{ID: request.UserId}}
	if err := uc.repo.FindByID(ctx, followedUserID); err != nil {
		return nil, err
	}

	// 检查是否已经关注
	ur := &UserRelation{
		UserID:       followedUserID.ID,
		FollowUserID: userID,
	}
	if err := uc.repo.FirstUserRelation(ctx, ur); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	// 已存在关系，且未关注状态修改为已关注
	if ur.ID > 0 && ur.Status != enums.UserRelationStatusNormal {
		ur.Status = enums.UserRelationStatusNormal
		ur.UpdatedAt = now.Unix()
		if err := uc.repo.UpdateUserRelationByID(ctx, ur); err != nil {
			return nil, err
		}
	} else if ur.ID == 0 {
		// 未关注，则新建
		ur.Status = enums.UserRelationStatusNormal
		ur.UpdatedAt = now.Unix()
		ur.CreatedAt = now.Unix()
		if err := uc.repo.CreateUserRelation(ctx, ur); err != nil {
			return nil, err
		}
	}

	return res, nil
}

func (uc *UserUsecase) UnfollowUser(ctx context.Context, request *v1.UnfollowUserRequest) (*v1.UnfollowUserReply, error) {
	res := &v1.UnfollowUserReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	followedUserID := &User{BasicModel: BasicModel{ID: request.UserId}}
	if err := uc.repo.FindByID(ctx, followedUserID); err != nil {
		return nil, err
	}

	// 检查是否已经关注
	ur := &UserRelation{
		UserID:       followedUserID.ID,
		FollowUserID: userID,
	}
	if err := uc.repo.FirstUserRelation(ctx, ur); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	// 已存在关系，且状态为已关注
	if ur.ID > 0 && ur.Status == enums.UserRelationStatusNormal {
		ur.Status = enums.UserRelationStatusDelete
		ur.UpdatedAt = time.Now().Unix()
		if err := uc.repo.UpdateUserRelationByID(ctx, ur); err != nil {
			return nil, err
		}
	}

	return res, nil
}

func (uc *UserUsecase) RefreshToken(ctx context.Context, req *v1.RefreshTokenRequest) (*v1.LoginReply, error) {
	res := &v1.LoginReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	cacheUser, err := uc.commonUseCase.GetUserInfoByUserID(ctx, userID)
	if err != nil {
		uc.log.Errorf("获取用户信息失败，error: %v, user_id: %v", err, userID)
	}

	res.Data = &v1.LoginReply_Data{
		UserId:        cacheUser.ID,
		Name:          cacheUser.Name,
		AvatarUrl:     cacheUser.GetAvatarUrl(),
		IsSystemAdmin: cacheUser.IsSystemAdmin,
		RoleType:      int32(cacheUser.RoleType),
		Phone:         cacheUser.Phone,
		Email:         cacheUser.Email,
		Status:        int32(cacheUser.Status),
		Gender:        int32(cacheUser.Gender),
		Desc:          cacheUser.Desc,
		IsSetPwd:      cacheUser.IsSetPwd,
		IsSetPrivacy:  cacheUser.IsSetPrivacy,
		IsVip:         cacheUser.IsVip,
	}

	// 如果 token 还有 7 天过期时间，则刷新 token
	if auth.IsTokenExpiringSoon(ctx) {
		ut, err := uc.authUc.CreateUserToken(ctx, userID, req.Platform, req.DeviceId, req.FcmToken, req.ApnsToken, req.JpushRegistrationId, req.RecommendedPushChannel, req.DeviceBrand)
		if err != nil {
			return nil, err
		}
		res.Data.Token = ut.Token
		res.Data.ExpireAt = int32(ut.ExpireAt)
	}

	return res, nil
}

func (uc *UserUsecase) UpdateUserAward(ctx context.Context, request *v1.UpdateUserAwardRequest) (*v1.UpdateUserAwardReply, error) {
	res := &v1.UpdateUserAwardReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	us := &UserSetting{
		UserID: userID,
	}
	if err := uc.repo.GetUserSetting(ctx, us); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	us.AwardImageUrl = request.AwardImageUrl
	if err := uc.updateUserSetting(ctx, us, map[string]interface{}{
		"award_image_url": us.AwardImageUrl,
	}); err != nil {
		return nil, err
	}

	return res, nil
}

func (uc *UserUsecase) updateUserSetting(ctx context.Context, us *UserSetting, params map[string]interface{}) error {
	if us.ID == 0 {
		if err := uc.repo.CreateUserSetting(ctx, us); err != nil {
			return err
		}
	} else {
		if err := uc.repo.UpdateUserSetting(ctx, us, params); err != nil {
			return err
		}
	}

	return nil
}

func (uc *UserUsecase) GetUserSetting(ctx context.Context, request *v1.GetUserSettingRequest) (*v1.GetUserSettingReply, error) {
	res := &v1.GetUserSettingReply{
		Data: &v1.GetUserSettingReply_Data{},
	}

	userID := auth.GetUserIDFromCtx(ctx)
	us := &UserSetting{
		UserID: userID,
	}
	if err := uc.repo.GetUserSetting(ctx, us); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	awardImageUrlWithDomain := ""
	if us.AwardImageUrl != "" {
		awardImageUrlWithDomain = qiniu.Client.CreateDnURL(us.AwardImageUrl, "", 3600*24*3+3600)
	}
	resData := &v1.GetUserSettingReply_Data{
		AwardImageUrl:           us.AwardImageUrl,
		AwardImageUrlWithDomain: awardImageUrlWithDomain,
	}
	res.Data = resData

	if request.Scene == string(enums.UserSettingSceneHomePage) {
		go func() {
			// 当前时间
			now := time.Now()
			// 计算到午夜的持续时间
			midnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
			durationUntilMidnight := time.Until(midnight)
			redisKey := fmt.Sprintf("%v%v", enums.RedisShowUserAwardPrefix, userID)
			if err := cache.RedisClient.Set(ctx, redisKey, "1", durationUntilMidnight).Err(); err != nil {
				uc.log.Errorf("写入 redis 失败，error: %v", err)
			}
		}()
	}

	return res, nil
}

func (uc *UserUsecase) ChangePhone(ctx context.Context, request *v1.ChangePhoneRequest) (*v1.ChangePhoneReply, error) {
	res := &v1.ChangePhoneReply{}
	userID := auth.GetUserIDFromCtx(ctx)
	if userID == 0 {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidUserMsg, nil))
	}

	// 检查验证码是否正确
	redisKey := enums.RedisPhoneChangePrefix + request.Phone
	redisRes, err := cache.RedisClient.Get(ctx, redisKey).Result()
	if err != nil {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}
	if redisRes != request.VerifyCode {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}

	// 判断手机号是否被使用，注销用户跳过
	phone := strings.TrimSpace(request.Phone)
	existedUser := &User{}
	if err = uc.repo.FindBySearch(ctx, existedUser, Where{"phone =": phone, "status !=": enums.UserStatusDelete}); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if existedUser.ID > 0 {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUsedPhoneMsg, nil))
	}

	u := &User{BasicModel: BasicModel{ID: userID}}
	if err = uc.repo.FindByID(ctx, u); err != nil {
		return nil, err
	}

	u.Phone = phone
	u.UpdatedAt = time.Now().Unix()
	if err = uc.repo.Update(ctx, u); err != nil {
		return nil, err
	}

	// 重新缓存用户信息
	go func() {
		_, _ = uc.commonUseCase.CacheUserInfo(ctx, u.ID, u, nil)

		// 删除验证码
		if err = cache.RedisClient.Del(ctx, redisKey).Err(); err != nil {
			uc.log.Errorf("【修改手机号】删除验证码失败, key: %v, err: %v", redisKey, err)
		}
	}()

	return res, nil
}

func (uc *UserUsecase) ChangeEmail(ctx context.Context, request *v1.ChangeEmailRequest) (*v1.ChangeEmailReply, error) {
	res := &v1.ChangeEmailReply{}
	userID := auth.GetUserIDFromCtx(ctx)
	if userID == 0 {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidUserMsg, nil))
	}

	// 检查验证码是否正确
	redisKey := enums.RedisEmailChangePrefix + request.Email
	redisRes, err := cache.RedisClient.Get(ctx, redisKey).Result()
	if err != nil {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}
	if redisRes != request.VerifyCode {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrVerifyCodeWrongMsg, nil))
	}

	// 判断邮箱号是否被使用，注销用户跳过
	email := strings.TrimSpace(request.Email)
	existedUser := &User{}
	if err = uc.repo.FindBySearch(ctx, existedUser, Where{"email =": email, "status !=": enums.UserStatusDelete}); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if existedUser.ID > 0 {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUsedEmailMsg, nil))
	}

	u := &User{BasicModel: BasicModel{ID: userID}}
	if err = uc.repo.FindByID(ctx, u); err != nil {
		return nil, err
	}

	u.Email = email
	u.UpdatedAt = time.Now().Unix()
	if err = uc.repo.Update(ctx, u); err != nil {
		return nil, err
	}

	// 重新缓存用户信息
	go func() {
		_, _ = uc.commonUseCase.CacheUserInfo(ctx, u.ID, u, nil)

		// 删除验证码
		if err = cache.RedisClient.Del(ctx, redisKey).Err(); err != nil {
			uc.log.Errorf("【修改邮箱】删除验证码失败, key: %v, err: %v", redisKey, err)
		}
	}()

	return res, nil
}

// ChangePassword 修改密码
func (uc *UserUsecase) ChangePassword(ctx context.Context, request *v1.ChangePasswordRequest) (*v1.ChangePasswordReply, error) {
	res := &v1.ChangePasswordReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	u := &User{BasicModel: BasicModel{ID: userID}}
	if err := uc.repo.FindByID(ctx, u); err != nil {
		return nil, err
	}

	// 如果用户当前密码不为空，那么需要校验旧密码是否正确
	if len(u.Password) > 0 {
		// 校验旧密码是否正确
		err := auth.Compare(u.Password, request.OldPassword)
		if err != nil {
			uc.log.Warnf("【用户修改密码】密码错误, phone: %v, err: %v", u.Phone, err)
			return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordWrongMsg, nil))
		}
	}

	// 检查密码是否一致
	if request.NewPassword != request.ConfirmPassword {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordNotSameMsg, nil))
	}

	// 如果新密码与旧密码相同，不需要修改
	if request.OldPassword == request.NewPassword {
		return res, nil
	}

	// 检查密码是否符合规则
	if !auth.CheckPassword(request.NewPassword) {
		return res, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordNotAllowMsg, nil))
	}

	// 加密新密码
	encryptPwd, err := auth.Encrypt(request.NewPassword)
	if err != nil {
		return res, err
	}

	// 更新密码
	u.Password = encryptPwd
	u.UpdatedAt = time.Now().Unix()
	if err = uc.repo.Update(ctx, u); err != nil {
		return nil, err
	}

	// 重新缓存用户信息
	go func() {
		_, _ = uc.commonUseCase.CacheUserInfo(ctx, u.ID, u, nil)
	}()

	return res, nil
}

// DeleteUser 删除用户
// 将用户状态置为 删除
// 删除定时任务
func (uc *UserUsecase) DeleteUser(ctx context.Context, request *v1.DeleteUserRequest) (*v1.DeleteUserReply, error) {
	res := &v1.DeleteUserReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	u := &User{BasicModel: BasicModel{ID: userID}}
	if err := uc.repo.FindByID(ctx, u); err != nil {
		return nil, err
	}
	if u.Phone != request.Phone && u.Email != request.Email {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidUserMsg, nil))
	}

	// 校验旧密码是否正确
	if u.Password == "" || request.Password == "" {
		uc.log.Warnf("【注销账号】密码错误, phone: %v, password: %v", u.Phone, request.Password)
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUserOrPasswordWrongMsg, nil))
	}
	err := auth.Compare(u.Password, request.Password)
	if err != nil {
		uc.log.Warnf("【注销账号】密码错误, phone: %v, err: %v", u.Phone, err)
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUserOrPasswordWrongMsg, nil))
	}

	u.Status = enums.UserStatusDelete
	u.UpdatedAt = time.Now().Unix()
	if err = uc.repo.Update(ctx, u); err != nil {
		return nil, err
	}

	if err = uc.cronTaskUsecase.DeleteCronTaskByUserID(ctx, userID); err != nil {
		return nil, err
	}

	return res, nil
}

func (uc *UserUsecase) ChangeUserPrivacyPassword(ctx context.Context, request *v1.ChangeUserPrivacyPasswordRequest) (*v1.ChangeUserPrivacyPasswordReply, error) {
	res := &v1.ChangeUserPrivacyPasswordReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	us := &UserSetting{
		UserID: userID,
	}
	if err := uc.repo.GetUserSetting(ctx, us); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 检查用户权限
	//cacheUser, err := uc.commonUseCase.GetUserInfoByUserID(ctx, userID)
	//if err != nil {
	//	uc.log.Errorf("获取用户信息失败，error: %v, user_id: %v", err, userID)
	//}
	//if !cacheUser.IsVip {
	//	return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrUserNotVipMsg, nil))
	//}
	// 如果用户当前密码不为空，且设置的新隐私密码为空，则关闭隐私密码
	if us.PrivacyPassword != "" && request.NewPassword == "" {
		if err := uc.closeUserPrivacyPassword(ctx, us, request.OldPassword); err != nil {
			return nil, err
		}
	} else {
		if err := uc.setUserPrivacyPassword(ctx, us, request.OldPassword, request.NewPassword, request.ConfirmPassword); err != nil {
			return nil, err
		}
	}

	return res, nil
}

// setUserPrivacyPassword 设置隐私密码
func (uc *UserUsecase) setUserPrivacyPassword(ctx context.Context, us *UserSetting, oldPassword, newPassword, confirmPassword string) error {
	// 如果用户当前密码不为空，那么需要校验旧密码是否正确
	if us.ID > 0 && us.PrivacyPassword != "" {
		// 校验旧密码是否正确
		if us.PrivacyPassword != oldPassword {
			uc.log.Warnf("【修改隐私密码】密码错误, user_id: %v, 旧密码错误", us.UserID)
			return errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordWrongMsg, nil))
		}
	}

	// 检查密码是否一致
	if newPassword != confirmPassword {
		return errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordNotSameMsg, nil))
	}

	// 检查密码是否符合规则
	if len(newPassword) != 4 {
		return errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordNotAllowMsg, nil))
	}

	us.PrivacyPassword = newPassword
	if err := uc.updateUserSetting(ctx, us, map[string]interface{}{
		"privacy_password": newPassword,
	}); err != nil {
		return err
	}

	// 重新缓存用户信息
	go func() {
		isSetPrivacy := true
		_, _ = uc.commonUseCase.CacheUserInfo(ctx, us.UserID, nil, &isSetPrivacy)
	}()

	return nil
}

// closeUserPrivacyPassword 关闭隐私密码
func (uc *UserUsecase) closeUserPrivacyPassword(ctx context.Context, us *UserSetting, oldPassword string) error {
	// 校验旧密码是否正确
	if us.PrivacyPassword != oldPassword {
		uc.log.Warnf("【修改隐私密码】密码错误, user_id: %v, 旧密码错误", us.UserID)
		return errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrPasswordWrongMsg, nil))
	}

	if err := uc.updateUserSetting(ctx, us, map[string]interface{}{
		"privacy_password": "",
	}); err != nil {
		return err
	}

	// 重新缓存用户信息
	go func() {
		isSetPrivacy := false
		_, _ = uc.commonUseCase.CacheUserInfo(ctx, us.UserID, nil, &isSetPrivacy)
	}()

	return nil
}

func (uc *UserUsecase) CheckUserPrivacyPassword(ctx context.Context, request *v1.CheckUserPrivacyPasswordRequest) (*v1.CheckUserPrivacyPasswordReply, error) {
	res := &v1.CheckUserPrivacyPasswordReply{
		Data: &v1.CheckUserPrivacyPasswordReply_Data{
			IsPass: false,
		},
	}

	userID := auth.GetUserIDFromCtx(ctx)
	us := &UserSetting{
		UserID: userID,
	}
	if err := uc.repo.GetUserSetting(ctx, us); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	// 查完数据库后默认为校验通过，防止数据不存在导致用户校验失败
	res.Data.IsPass = true
	if us.ID > 0 && us.PrivacyPassword != "" {
		// 校验旧密码是否正确
		if us.PrivacyPassword != request.Password {
			res.Data.IsPass = false
		}
	}

	return res, nil
}

// createUserHabitCronTask 生成用户 vip 定时任务
func (uc *UserUsecase) createUserVipCronTask(ctx context.Context, user *User) {
	now := time.Now()
	if user.ExpiredAt <= 0 {
		return
	}

	cronTasks := make([]*CronTask, 0)
	warnTime := time.Unix(user.ExpiredAt, 0)
	todayTime := tool.GetZeroTimestampByDate(warnTime)
	cronTasks = append(cronTasks, &CronTask{
		BasicModel: BasicModel{
			CreatedAt: now.Unix(),
			UpdatedAt: now.Unix(),
		},
		UserID:      user.ID,
		Payload:     CronTaskPayload{},
		TriggerTime: warnTime.Format(time.DateTime),
		TodayTime:   todayTime.Unix(),
		Tag:         fmt.Sprintf("%v%v", enums.CronTaskUserVipTrialPrefix, user.ID),
		Status:      enums.CronTaskStatusTypeProcessing,
		ThirdID:     fmt.Sprintf("%v", user.ID),
		TaskType:    enums.CronTaskTypeUserVipTrial,
	})
	// 2. 写入定时任务表
	if len(cronTasks) == 0 {
		return
	}
	if err := uc.cronTaskUsecase.repo.BatchCreate(ctx, cronTasks); err != nil {
		uc.log.Errorf("生成用户 vip 定时任务失败，error: %v", err)
		return
	}
	// 3. 添加到 scheduler
	for _, item := range cronTasks {
		// 创建目标时间
		triggerTime, err := tool.ConvertLocalToUTC(item.TriggerTime)
		if err != nil {
			uc.log.Errorf("user_usecase: 执行用户 vip 定时任务失败, error: %v, task_id: %v", err, item.ID)
			continue
		}

		_, err = task.Scheduler.Every(1).Day().At(triggerTime.UTC()).Tag(item.Tag).Do(func(taskID int32) {
			now = time.Now().UTC()

			// 检查当前日期和时间是否匹配
			if now.Month() == triggerTime.Month() && now.Day() == triggerTime.Day() && now.Format("15:04") == triggerTime.Format("15:04") {
				runCronTaskBody := map[string]interface{}{
					"task_id": taskID,
				}
				if err = httpClient.RunCronTask(runCronTaskBody, uc.confData.Task.HttpAddr); err != nil {
					uc.log.Errorf("user_usecase: 执行用户 vip 定时任务失败, error: %v, task_id: %v", err, item.ID)
				}
			}

		}, item.ID)
		if err != nil {
			uc.log.Errorf("user_usecase: 添加用户 vip 定时任务到 scheduler 失败, error: %v", err)
		}
	}

	return
}

func (uc *UserUsecase) GetUserVipInfo(ctx context.Context, request *v1.GetUserVipInfoRequest) (*v1.GetUserVipInfoReply, error) {
	res := &v1.GetUserVipInfoReply{}

	userID := auth.GetUserIDFromCtxByNoAuth(ctx)
	cacheUser, err := uc.commonUseCase.GetUserInfoByUserID(ctx, userID)
	if err != nil {
		return nil, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidUserMsg, nil))
	}

	var remainDays int32
	differenceInSeconds := cacheUser.ExpiredAt - time.Now().Unix()
	if differenceInSeconds > 0 {
		// 计算剩余天数，不足一天算作一天
		remainDays = int32((differenceInSeconds + 24*3600 - 1) / (24 * 3600))
	}

	data := &v1.GetUserVipInfoReply_Data{
		IsVip:      cacheUser.IsVip,
		RemainDays: remainDays,
		MonthPrice: &v1.GetUserVipInfoReply_VipDetail{
			Price:         uc.confData.UserVipPrice.MonthPrice,
			DiscountPrice: uc.confData.UserVipPrice.DiscountMonthPrice,
		},
		QuarterPrice: &v1.GetUserVipInfoReply_VipDetail{
			Price:         uc.confData.UserVipPrice.QuarterPrice,
			DiscountPrice: uc.confData.UserVipPrice.DiscountQuarterPrice,
		},
		YearPrice: &v1.GetUserVipInfoReply_VipDetail{
			Price:         uc.confData.UserVipPrice.YearPrice,
			DiscountPrice: uc.confData.UserVipPrice.DiscountYearPrice,
		},
	}
	res.Data = data

	return res, nil
}

// generateUserName 生成用户名
func (uc *UserUsecase) generateUserName(firebaseUser *firebase.FirebaseUser) string {
	if firebaseUser.Name != "" {
		return firebaseUser.Name
	}
	if firebaseUser.Email != "" {
		return tool.GenerateUserName("", firebaseUser.Email)
	}
	return tool.GenerateUserName("", "")
}

// getTimezoneOrDefault 获取时区或默认值
func (uc *UserUsecase) getTimezoneOrDefault(timezone string) string {
	if timezone != "" {
		return timezone
	}
	return "Asia/Shanghai"
}

// getTimezoneOffsetOrDefault 获取时区偏移或默认值
func (uc *UserUsecase) getTimezoneOffsetOrDefault(offset string) string {
	if offset != "" {
		return offset
	}
	return "+08:00"
}

// getLocaleOrDefault 获取语言偏好或默认值
func (uc *UserUsecase) getLocaleOrDefault(firebaseLocale, fallback string) string {
	if firebaseLocale != "" {
		return firebaseLocale
	}
	if fallback != "" {
		return fallback
	}
	return "zh-CN"
}

func (uc *UserUsecase) PushToken(ctx context.Context, req *v1.PushTokenRequest) (*v1.PushTokenReply, error) {
	res := &v1.PushTokenReply{}

	userID := auth.GetUserIDFromCtx(ctx)
	if userID == 0 {
		return res, errors.BadRequest("", localize.TranslateMsg(ctx, code.ErrInvalidUserMsg, nil))
	}

	// 更新 user_token 数据
	if err := uc.authUc.UpdateUserToken(ctx, userID, req.Platform, req.DeviceId, req.FcmToken, req.ApnsToken, req.JpushRegistrationId, req.RecommendedPushChannel, req.DeviceBrand); err != nil {
		return res, err
	}
	// 调用推送token管理器更新
	if err := uc.enhancedAuthUc.ProcessEnhancedLogin(ctx, req, userID); err != nil {
		// 记录失败审计日志
		go uc.logPushTokenUpdateFailure(ctx, userID, req.DeviceId, req.Platform, err)
		return res, err
	}

	// 4. 异步记录成功审计日志
	go uc.logPushTokenUpdateSuccess(ctx, userID, req.DeviceId, req.Platform)

	// 5. 构建响应
	return res, nil
}

// logPushTokenUpdateSuccess 记录推送Token更新成功审计日志
func (uc *UserUsecase) logPushTokenUpdateSuccess(ctx context.Context, userID int32, deviceId, platform string) {
	if uc.auditHelper != nil {
		auditLog := &AuthAuditLog{
			UserID:    userID,
			Action:    "update_push_token",
			DeviceID:  deviceId,
			Platform:  platform,
			Success:   true,
			ErrorCode: "",
			ErrorMsg:  "",
		}
		uc.auditHelper.auditService.LogAuthEvent(auditLog)
	}
}

// logPushTokenUpdateFailure 记录推送Token更新失败审计日志
func (uc *UserUsecase) logPushTokenUpdateFailure(ctx context.Context, userID int32, deviceId, platform string, err error) {
	if uc.auditHelper != nil {
		auditLog := &AuthAuditLog{
			UserID:    userID,
			Action:    "update_push_token",
			DeviceID:  deviceId,
			Platform:  platform,
			Success:   false,
			ErrorCode: "UPDATE_FAILED",
			ErrorMsg:  err.Error(),
		}
		uc.auditHelper.auditService.LogAuthEvent(auditLog)
	}
}
