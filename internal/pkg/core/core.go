package core

import (
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/wlnil/life-log-be/internal/pkg/code"
	"github.com/wlnil/life-log-be/internal/pkg/middleware/localize"
	stdhttp "net/http"
)

// httpResponse 响应结构体
type httpResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data"`
}

// EncoderError 错误响应封装
func EncoderError() http.EncodeErrorFunc {
	return func(w stdhttp.ResponseWriter, r *stdhttp.Request, err error) {
		if err == nil {
			return
		}
		se := &httpResponse{}
		gs := errors.FromError(err)
		se = &httpResponse{
			Code:    int(gs.Code),
			Message: gs.Message,
			Data:    nil,
		}
		if len(gs.Reason) > 0 {
			log.Errorf("err: %v", gs.Reason)
		}
		codec, _ := http.CodecForRequest(r, "Accept")
		if se.Code == stdhttp.StatusInternalServerError {
			msg, _ := localize.Localizer.Localize(&i18n.LocalizeConfig{
				DefaultMessage: code.ErrInternalMsg,
			})
			se.Message = msg
		}
		body, err := codec.Marshal(se)
		if err != nil {
			w.WriteHeader(stdhttp.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/"+codec.Name())
		w.WriteHeader(se.Code)
		_, _ = w.Write(body)
	}
}
