package infrastructure

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/firebase"
	httpClient "github.com/wlnil/life-log-be/internal/pkg/http_client"
	"github.com/wlnil/life-log-be/internal/pkg/pay"
	"github.com/wlnil/life-log-be/internal/pkg/sms"
	"github.com/wlnil/life-log-be/internal/pkg/store/qiniu"
	"github.com/wlnil/life-log-be/internal/pkg/task"
)

// ProviderSet is infrastructure providers.
var ProviderSet = wire.NewSet(NewInfrastructure)

// Infrastructure 基础设施管理器
type Infrastructure struct {
	goCornX  *task.GoCornX
	httpX    *httpClient.HttpX
	logger   *log.Helper
}

// NewInfrastructure 创建基础设施管理器
func NewInfrastructure(c *conf.Data, logger log.Logger) *Infrastructure {
	helper := log.NewHelper(logger)
	
	// 初始化定时任务调度器
	goCornX := &task.GoCornX{}
	goCornX.Init()
	helper.Info("定时任务调度器初始化完成")

	// 初始化HTTP客户端
	httpX := &httpClient.HttpX{
		Timeout:    int(c.ThirdHttp.Timeout),
		RetryCount: int(c.ThirdHttp.RetryCount),
	}
	httpX.Init()
	helper.Info("HTTP客户端初始化完成")

	// 初始化SMS服务
	sms.New(c)
	helper.Info("SMS服务初始化完成")

	// 初始化七牛云存储客户端
	qiniu.Client = &qiniu.QiniuX{
		AccessKey:    c.Qiniu.AccessKey,
		SecretKey:    c.Qiniu.SecretKey,
		Bucket:       c.Qiniu.Bucket,
		TokenExpires: c.Qiniu.TokenExpires,
		Addr:         c.Qiniu.Addr,
		PublicAddr:   c.Qiniu.PublicAddr,
		PublicBucket: c.Qiniu.PublicBucket,
	}
	helper.Info("七牛云存储客户端初始化完成")

	// 初始化支付宝客户端
	pay.AliPayInstance = &pay.AliPay{
		AppId:         c.AliPay.AppId,
		AppPrivateKey: c.AliPay.AppPrivateKey,
		AliPublicKey:  c.AliPay.AliPublicKey,
		EncryptKey:    c.AliPay.EncryptKey,
		IsProduction:  c.AliPay.IsProduction,
		NotifyURL:     c.AliPay.NotifyUrl,
		ReturnURL:     c.AliPay.ReturnUrl,
	}
	helper.Info("支付宝客户端初始化完成")

	// 初始化Firebase认证（如果启用）
	if c.Firebase != nil && c.Firebase.Enabled {
		firebase.NewFirebaseAuth(c, logger)
		helper.Info("Firebase认证初始化完成")
	}

	infra := &Infrastructure{
		goCornX: goCornX,
		httpX:   httpX,
		logger:  helper,
	}

	return infra
}

// GetGoCornX 获取定时任务调度器
func (i *Infrastructure) GetGoCornX() *task.GoCornX {
	return i.goCornX
}

// GetHttpX 获取HTTP客户端
func (i *Infrastructure) GetHttpX() *httpClient.HttpX {
	return i.httpX
}

// Cleanup 清理基础设施资源
func (i *Infrastructure) Cleanup() {
	if i.goCornX != nil {
		i.goCornX.Close()
	}
	i.logger.Info("基础设施资源清理完成")
}
