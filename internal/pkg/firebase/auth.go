package firebase

import (
	"context"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	firebase "firebase.google.com/go/v4"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"firebase.google.com/go/v4/auth"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-jwt/jwt/v4"
	"github.com/wlnil/life-log-be/internal/conf"
	"google.golang.org/api/option"
)

// FirebaseUser Firebase用户信息
type FirebaseUser struct {
	UID           string `json:"uid"`
	Email         string `json:"email"`
	EmailVerified bool   `json:"email_verified"`
	Name          string `json:"name"`
	Picture       string `json:"picture"`
	Provider      string `json:"provider"`
	Locale        string `json:"locale"`
}

// FirebaseAuth Firebase认证客户端
type FirebaseAuth struct {
	projectID    string
	authClient   *auth.Client     // 可能为nil，如果没有凭据
	customClient *CustomFCMClient // 自定义FCM客户端（支持代理）
	httpClient   *http.Client
	log          *log.Helper
}

var FirebaseAuthInstance *FirebaseAuth

// NewFirebaseAuth 创建Firebase认证客户端
func NewFirebaseAuth(confData *conf.Data, logger log.Logger) *FirebaseAuth {
	logHelper := log.NewHelper(logger)

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	projectID := confData.Firebase.ProjectId
	credentialsFile := confData.Firebase.CredentialsFile

	// 尝试初始化Firebase Admin SDK
	ctx := context.Background()
	config := &firebase.Config{
		ProjectID: projectID,
	}

	var authClient *auth.Client
	var customClient *CustomFCMClient

	// 尝试初始化Firebase应用
	var firebaseApp *firebase.App
	var err error

	// 检查是否需要使用代理客户端
	if confData.Env == conf.EnvType_Test {
		logHelper.Infof("🌐 检测到需要代理环境，使用自定义FCM客户端")

		// 初始化自定义FCM客户端
		if credentialsFile != "" && projectID != "" {
			proxyURL := GetProxyURL()

			if client, err := NewCustomFCMClient(credentialsFile, proxyURL, logger); err != nil {
				logHelper.Errorf("❌ 自定义FCM客户端初始化失败: %v", err)
			} else {
				customClient = client
				logHelper.Infof("✅ 自定义FCM客户端初始化成功，使用代理: %s", proxyURL)
			}
		}
	} else {
		// 如果指定了凭据文件路径，使用该文件
		if credentialsFile != "" {
			// 检查文件是否存在
			if _, err := os.Stat(credentialsFile); err == nil {
				logHelper.Infof("🔑 使用指定的凭据文件: %s", credentialsFile)
				opt := option.WithCredentialsFile(credentialsFile)
				firebaseApp, err = firebase.NewApp(ctx, config, opt)

				// 获取Auth客户端
				authClient, err = firebaseApp.Auth(ctx)
				if err != nil {
					logHelper.Warnf("⚠️  无法获取Firebase Auth客户端: %v", err)
					logHelper.Infof("🔄 将使用Google API验证模式，项目ID: %s", projectID)
					authClient = nil
				} else {
					logHelper.Infof("✅ Firebase Admin SDK初始化成功，项目ID: %s", projectID)
				}
			} else {
				logHelper.Warnf("⚠️  指定的凭据文件不存在: %s", credentialsFile)
				err = fmt.Errorf("凭据文件不存在: %s", credentialsFile)
			}
		} else {
			logHelper.Warnf("⚠️  无法初始化Firebase Admin SDK: %v", err)
			logHelper.Infof("🔄 将使用Google API验证模式，项目ID: %s", projectID)
		}
	}

	FirebaseAuthInstance = &FirebaseAuth{
		projectID:    projectID,
		authClient:   authClient,
		httpClient:   httpClient,
		customClient: customClient,
		log:          logHelper,
	}

	return FirebaseAuthInstance
}

// VerifyIDToken 验证Firebase ID Token
func (fa *FirebaseAuth) VerifyIDToken(ctx context.Context, idToken string) (*FirebaseUser, error) {
	if idToken == "" {
		return nil, fmt.Errorf("Firebase ID Token不能为空")
	}

	// 验证token格式（基本检查）
	if len(idToken) < 100 {
		return nil, fmt.Errorf("firebase ID Token格式无效")
	}

	// 创建带超时的上下文（增加超时时间）
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 如果有Firebase Admin SDK客户端，优先使用
	if fa.authClient != nil {
		return fa.verifyWithAdminSDK(ctx, idToken)
	}

	if fa.customClient != nil {
		return fa.customClient.VerifyIDTokenWithProxy(ctx, idToken)
	}

	// 否则使用Google API验证
	return fa.verifyWithGoogleAPI(ctx, idToken)
}

// verifyWithAdminSDK 使用Firebase Admin SDK验证ID Token
func (fa *FirebaseAuth) verifyWithAdminSDK(ctx context.Context, idToken string) (*FirebaseUser, error) {
	// 使用Firebase Admin SDK验证ID Token
	token, err := fa.authClient.VerifyIDToken(ctx, idToken)
	if err != nil {
		fa.log.Errorf("Firebase Admin SDK验证失败: %v", err)
		return nil, fmt.Errorf("firebase token验证失败: %v", err)
	}

	// 解析用户信息
	firebaseUser := fa.parseFirebaseToken(token)

	// 验证必要字段
	if firebaseUser.UID == "" {
		return nil, fmt.Errorf("Firebase用户UID为空")
	}

	fa.log.Infof("Firebase用户验证成功(Admin SDK): uid=%s, email=%s, provider=%s",
		firebaseUser.UID, firebaseUser.Email, firebaseUser.Provider)
	return firebaseUser, nil
}

// verifyWithGoogleAPI 使用Firebase公钥验证Firebase ID Token（直连，不使用代理）
func (fa *FirebaseAuth) verifyWithGoogleAPI(ctx context.Context, idToken string) (*FirebaseUser, error) {
	if idToken == "" {
		return nil, fmt.Errorf("Firebase ID Token不能为空")
	}

	// 验证token格式（基本检查）
	if len(idToken) < 100 {
		return nil, fmt.Errorf("firebase ID Token格式无效")
	}

	// 本地解码JWT以进行基本验证和调试
	if claims, err := fa.decodeJWTLocally(idToken); err != nil {
		fa.log.Warnf("⚠️  本地JWT解码失败: %v", err)
	} else {
		fa.log.Infof("📋 Token信息: iss=%s, aud=%s, sub=%s, exp=%v",
			claims["iss"], claims["aud"], claims["sub"], claims["exp"])

		// 检查基本的过期时间
		if exp, ok := claims["exp"].(float64); ok {
			if time.Now().Unix() > int64(exp) {
				return nil, fmt.Errorf("token已过期（本地检查）")
			}
		}
	}

	// 使用与VerifyIDTokenWithProxy相同的验证逻辑
	firebaseUser, err := fa.verifyFirebaseIDToken(ctx, idToken)
	if err != nil {
		return nil, err
	}

	fa.log.Infof("✅ Firebase用户验证成功（直连模式）: uid=%s, email=%s, provider=%s",
		firebaseUser.UID, firebaseUser.Email, firebaseUser.Provider)
	return firebaseUser, nil
}

// parseFirebaseToken 解析Firebase Admin SDK返回的Token为FirebaseUser
func (fa *FirebaseAuth) parseFirebaseToken(token *auth.Token) *FirebaseUser {
	user := &FirebaseUser{
		UID: token.UID,
	}

	// 从Claims中提取用户信息
	if email, ok := token.Claims["email"].(string); ok {
		user.Email = email
	}

	if emailVerified, ok := token.Claims["email_verified"].(bool); ok {
		user.EmailVerified = emailVerified
	}

	if name, ok := token.Claims["name"].(string); ok {
		user.Name = name
	}

	if picture, ok := token.Claims["picture"].(string); ok {
		user.Picture = picture
	}

	// 从Firebase信息中提取认证提供商
	if firebaseInfo, ok := token.Claims["firebase"].(map[string]interface{}); ok {
		if signInProvider, ok := firebaseInfo["sign_in_provider"].(string); ok {
			switch signInProvider {
			case "google.com":
				user.Provider = "google"
			case "apple.com":
				user.Provider = "apple"
			case "password":
				user.Provider = "email"
			default:
				user.Provider = signInProvider
			}
		}
	}

	// 如果没有找到provider，默认为firebase
	if user.Provider == "" {
		user.Provider = "firebase"
	}

	// 提取语言偏好
	if locale, ok := token.Claims["locale"].(string); ok {
		user.Locale = locale
	}

	return user
}

// decodeJWTLocally 本地解码JWT token（不验证签名，仅用于调试）
func (fa *FirebaseAuth) decodeJWTLocally(token string) (map[string]interface{}, error) {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("JWT格式无效，应该有3个部分")
	}

	// 解码payload部分
	payload := parts[1]

	// 添加padding
	for len(payload)%4 != 0 {
		payload += "="
	}

	// Base64解码
	decoded, err := base64.URLEncoding.DecodeString(payload)
	if err != nil {
		return nil, fmt.Errorf("Base64解码失败: %v", err)
	}

	// JSON解析
	var claims map[string]interface{}
	if err := json.Unmarshal(decoded, &claims); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	return claims, nil
}

// verifyFirebaseIDToken 使用与VerifyIDTokenWithProxy相同的验证逻辑（但不使用代理）
func (fa *FirebaseAuth) verifyFirebaseIDToken(ctx context.Context, idToken string) (*FirebaseUser, error) {
	// 1. 解析JWT header获取kid
	parts := strings.Split(idToken, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("JWT格式无效")
	}

	// 解码header
	headerData := parts[0]
	for len(headerData)%4 != 0 {
		headerData += "="
	}

	headerBytes, err := base64.URLEncoding.DecodeString(headerData)
	if err != nil {
		return nil, fmt.Errorf("解码JWT header失败: %v", err)
	}

	var header map[string]interface{}
	if err := json.Unmarshal(headerBytes, &header); err != nil {
		return nil, fmt.Errorf("解析JWT header失败: %v", err)
	}

	kid, ok := header["kid"].(string)
	if !ok {
		return nil, fmt.Errorf("JWT header中缺少kid字段")
	}

	// 2. 获取Firebase公钥证书（直连）
	certificates, err := fa.getFirebaseCertificatesDirect(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取Firebase证书失败: %v", err)
	}

	certificate, ok := certificates[kid]
	if !ok {
		return nil, fmt.Errorf("找不到对应的证书，kid: %s", kid)
	}

	// 3. 验证JWT签名
	token, err := jwt.Parse(idToken, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("意外的签名算法: %v", token.Header["alg"])
		}

		// 解析证书
		return fa.parseCertificate(certificate)
	})

	if err != nil {
		return nil, fmt.Errorf("JWT签名验证失败: %v", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("JWT token无效")
	}

	// 4. 验证claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("无法解析JWT claims")
	}

	if err := fa.validateFirebaseClaims(claims); err != nil {
		return nil, err
	}

	// 5. 解析用户信息
	firebaseUser := fa.parseFirebaseClaims(claims)

	fa.log.Infof("✅ Firebase ID Token验证成功: uid=%s, email=%s, provider=%s",
		firebaseUser.UID, firebaseUser.Email, firebaseUser.Provider)

	return firebaseUser, nil
}

// getFirebaseCertificatesDirect 直连获取Firebase公钥证书（不使用代理）
func (fa *FirebaseAuth) getFirebaseCertificatesDirect(ctx context.Context) (map[string]string, error) {
	// Firebase公钥证书URL
	certURL := "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"

	fa.log.Infof("🌐 获取Firebase证书（直连）: %s", certURL)

	req, err := http.NewRequestWithContext(ctx, "GET", certURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建证书请求失败: %v", err)
	}

	req.Header.Set("User-Agent", "life-log-be/1.0")
	req.Header.Set("Accept", "application/json")

	resp, err := fa.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("获取证书请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("获取证书失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	var certificates map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&certificates); err != nil {
		return nil, fmt.Errorf("解析证书响应失败: %v", err)
	}

	fa.log.Infof("✅ 成功获取 %d 个证书（直连）", len(certificates))
	return certificates, nil
}

// parseCertificate 解析X.509证书
func (fa *FirebaseAuth) parseCertificate(certPEM string) (interface{}, error) {
	block, _ := pem.Decode([]byte(certPEM))
	if block == nil {
		return nil, fmt.Errorf("无法解析PEM证书")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("解析X.509证书失败: %v", err)
	}

	return cert.PublicKey, nil
}

// validateFirebaseClaims 验证Firebase JWT claims
func (fa *FirebaseAuth) validateFirebaseClaims(claims jwt.MapClaims) error {
	// 验证issuer
	iss, ok := claims["iss"].(string)
	if !ok {
		return fmt.Errorf("缺少issuer字段")
	}
	expectedIssuer := fmt.Sprintf("https://securetoken.google.com/%s", fa.projectID)
	if iss != expectedIssuer {
		return fmt.Errorf("issuer不匹配，期望: %s, 实际: %s", expectedIssuer, iss)
	}

	// 验证audience
	aud, ok := claims["aud"].(string)
	if !ok {
		return fmt.Errorf("缺少audience字段")
	}
	if aud != fa.projectID {
		return fmt.Errorf("audience不匹配，期望: %s, 实际: %s", fa.projectID, aud)
	}

	// 验证过期时间
	exp, ok := claims["exp"].(float64)
	if !ok {
		return fmt.Errorf("缺少exp字段")
	}
	if time.Now().Unix() > int64(exp) {
		return fmt.Errorf("token已过期")
	}

	// 验证签发时间
	iat, ok := claims["iat"].(float64)
	if !ok {
		return fmt.Errorf("缺少iat字段")
	}
	if time.Now().Unix() < int64(iat) {
		return fmt.Errorf("token签发时间无效")
	}

	// 验证认证时间
	authTime, ok := claims["auth_time"].(float64)
	if !ok {
		return fmt.Errorf("缺少auth_time字段")
	}
	if int64(authTime) > time.Now().Unix() {
		return fmt.Errorf("认证时间无效")
	}

	// 验证subject
	sub, ok := claims["sub"].(string)
	if !ok || sub == "" {
		return fmt.Errorf("缺少或无效的sub字段")
	}

	return nil
}

// parseFirebaseClaims 解析Firebase JWT claims为FirebaseUser
func (fa *FirebaseAuth) parseFirebaseClaims(claims jwt.MapClaims) *FirebaseUser {
	user := &FirebaseUser{}

	// 提取UID
	if sub, ok := claims["sub"].(string); ok {
		user.UID = sub
	}

	// 提取邮箱
	if email, ok := claims["email"].(string); ok {
		user.Email = email
	}

	// 提取邮箱验证状态
	if emailVerified, ok := claims["email_verified"].(bool); ok {
		user.EmailVerified = emailVerified
	}

	// 提取用户名
	if name, ok := claims["name"].(string); ok {
		user.Name = name
	}

	// 提取头像
	if picture, ok := claims["picture"].(string); ok {
		user.Picture = picture
	}

	// 提取认证提供商
	if firebaseInfo, ok := claims["firebase"].(map[string]interface{}); ok {
		if signInProvider, ok := firebaseInfo["sign_in_provider"].(string); ok {
			switch signInProvider {
			case "google.com":
				user.Provider = "google"
			case "apple.com":
				user.Provider = "apple"
			case "password":
				user.Provider = "email"
			default:
				user.Provider = signInProvider
			}
		}
	}

	// 如果没有找到provider，默认为firebase
	if user.Provider == "" {
		user.Provider = "firebase"
	}

	// 提取语言偏好
	if locale, ok := claims["locale"].(string); ok {
		user.Locale = locale
	}

	return user
}

// FirebaseAuthInterface Firebase认证接口
type FirebaseAuthInterface interface {
	VerifyIDToken(ctx context.Context, idToken string) (*FirebaseUser, error)
}

// 确保实现了接口
var _ FirebaseAuthInterface = (*FirebaseAuth)(nil)
