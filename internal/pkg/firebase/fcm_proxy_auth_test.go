package firebase

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/wlnil/life-log-be/internal/conf"
)

func TestCustomFCMClient_VerifyIDTokenWithProxy(t *testing.T) {
	logger := log.NewStdLogger(os.Stdout)

	// 测试用例
	tests := []struct {
		name        string
		idToken     string
		expectError bool
		description string
	}{
		{
			name:        "有效的Apple Sign-In Firebase ID Token",
			idToken:     "eyJhbGciOiJSUzI1NiIsImtpZCI6IjNiZjA1MzkxMzk2OTEzYTc4ZWM4MGY0MjcwMzM4NjM2NDA2MTBhZGMiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tadQeJ_K5KHP5g3X6nAL64UUI96oSFlEa6JGHUiJ_UlJDECn17HLYCbm-_B4TAoqtSH6IxjPmgYFusqJP0_nWAhc_yIVghi2m7lFbPnpN8n6KObvRwAx_XQb5iv_7vMIId4LufKQAJbCnAXsz16y5Z8oeIPnNtG2ayM9EsnF08ZfrMyqdvbm_qhj61sULN-1-Hgh6Pz6aw1vXYY5SrVA5SZV3Dx3WHjgzdi5wWxnGFhPF4WVWzHpOIRsHqnvqvCWTsJ8Krr88WbiSHtIQHnQzPKZyDR6r0pVVCEYIfYSTYykpB_Gc_n9B94cBLinsSAyng6B6jAQ4Ed1wVg98eofRw",
			expectError: false, // 验证应该成功
			description: "真实有效的Apple Sign-In Firebase ID Token，验证应该成功",
		},
	}

	// 只有在有代理的情况下才运行实际的网络测试
	if !isProxyAvailable() {
		t.Skip("跳过代理测试：代理不可用")
		return
	}

	// 创建自定义FCM客户端
	credentialsFile := "/Users/<USER>/my_pro/life-log-be/configs/firebase-service-account.json"
	proxyURL := GetProxyURL()

	client, err := NewCustomFCMClient(credentialsFile, proxyURL, logger)
	if err != nil {
		t.Fatalf("创建自定义FCM客户端失败: %v", err)
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			user, err := client.VerifyIDTokenWithProxy(ctx, tt.idToken)

			if tt.expectError {
				if err == nil {
					t.Errorf("期望错误但没有收到错误")
				}
				t.Logf("✅ 预期错误: %v", err)
			} else {
				if err != nil {
					// 对于模拟token，我们期望网络验证失败，这是正常的
					t.Logf("⚠️  网络验证失败（预期）: %v", err)
				} else {
					t.Logf("✅ 验证成功: uid=%s, email=%s, provider=%s",
						user.UID, user.Email, user.Provider)
				}
			}
		})
	}
}

// isProxyAvailable 检查代理是否可用
func isProxyAvailable() bool {
	// 简单检查：尝试解析代理URL
	proxyURL := GetProxyURL()
	return proxyURL != ""
}

// 清理函数
func TestMain(m *testing.M) {
	// 运行测试
	code := m.Run()

	// 清理环境变量
	os.Unsetenv("FCM_USE_PROXY_CLIENT")
	os.Unsetenv("FCM_PROXY_URL")
	os.Unsetenv("NETWORK_REGION")
	os.Unsetenv("TZ")
	os.Unsetenv("LANG")

	os.Exit(code)
}

func TestCustomFCMClient_ProxyDiagnosis(t *testing.T) {
	// 设置测试环境变量
	os.Setenv("FCM_USE_PROXY_CLIENT", "true")
	os.Setenv("FCM_PROXY_URL", "socks5://127.0.0.1:1086")

	logger := log.NewStdLogger(os.Stdout)

	// 只有在有代理的情况下才运行测试
	if !isProxyAvailable() {
		t.Skip("跳过代理诊断测试：代理不可用")
		return
	}

	// 创建自定义FCM客户端
	credentialsFile := "/Users/<USER>/my_pro/life-log-be/configs/firebase-service-account.json"
	proxyURL := GetProxyURL()

	client, err := NewCustomFCMClient(credentialsFile, proxyURL, logger)
	if err != nil {
		t.Fatalf("创建自定义FCM客户端失败: %v", err)
	}

	// 运行代理诊断
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	err = client.DiagnoseProxyConnection(ctx)
	if err != nil {
		t.Logf("⚠️  代理诊断完成，可能存在连接问题: %v", err)
	} else {
		t.Logf("✅ 代理诊断完成")
	}
}

func TestFirebaseAuth_AutoProxyDetection(t *testing.T) {
	// 设置测试环境变量
	os.Setenv("FCM_USE_PROXY_CLIENT", "true")
	os.Setenv("FCM_PROXY_URL", "socks5://127.0.0.1:1086")

	logger := log.NewStdLogger(os.Stdout)

	// 只有在有代理的情况下才运行测试
	if !isProxyAvailable() {
		t.Skip("跳过Firebase认证代理测试：代理不可用")
		return
	}

	// 模拟配置
	confData := &conf.Data{
		Firebase: &conf.Data_Firebase{
			ProjectId:       "lifehabit-2dd0d",
			CredentialsFile: "/Users/<USER>/my_pro/life-log-be/configs/firebase-service-account.json",
			Enabled:         true,
		},
	}

	// 创建Firebase认证客户端
	firebaseAuth := NewFirebaseAuth(confData, logger)

	// 测试token验证
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用有效的token进行测试
	validToken := "eyJhbGciOiJSUzI1NiIsImtpZCI6IjNiZjA1MzkxMzk2OTEzYTc4ZWM4MGY0MjcwMzM4NjM2NDA2MTBhZGMiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tadQeJ_K5KHP5g3X6nAL64UUI96oSFlEa6JGHUiJ_UlJDECn17HLYCbm-_B4TAoqtSH6IxjPmgYFusqJP0_nWAhc_yIVghi2m7lFbPnpN8n6KObvRwAx_XQb5iv_7vMIId4LufKQAJbCnAXsz16y5Z8oeIPnNtG2ayM9EsnF08ZfrMyqdvbm_qhj61sULN-1-Hgh6Pz6aw1vXYY5SrVA5SZV3Dx3WHjgzdi5wWxnGFhPF4WVWzHpOIRsHqnvqvCWTsJ8Krr88WbiSHtIQHnQzPKZyDR6r0pVVCEYIfYSTYykpB_Gc_n9B94cBLinsSAyng6B6jAQ4Ed1wVg98eofRw"

	user, err := firebaseAuth.VerifyIDToken(ctx, validToken)
	if err != nil {
		t.Logf("⚠️  Firebase认证失败（可能是token过期）: %v", err)
	} else {
		t.Logf("✅ Firebase认证成功: uid=%s, email=%s, provider=%s",
			user.UID, user.Email, user.Provider)
	}
}

func TestFirebaseAuth_DirectConnection(t *testing.T) {
	// 测试不使用代理的直连模式
	os.Unsetenv("FCM_USE_PROXY_CLIENT")
	os.Unsetenv("FCM_PROXY_URL")
	os.Unsetenv("NETWORK_REGION")

	logger := log.NewStdLogger(os.Stdout)

	// 模拟配置（不使用代理）
	confData := &conf.Data{
		Firebase: &conf.Data_Firebase{
			ProjectId:       "lifehabit-2dd0d",
			CredentialsFile: "/Users/<USER>/my_pro/life-log-be/configs/firebase-service-account.json",
			Enabled:         true,
		},
	}

	// 创建Firebase认证客户端
	firebaseAuth := NewFirebaseAuth(confData, logger)

	// 测试token验证
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 使用有效的token进行测试
	validToken := "eyJhbGciOiJSUzI1NiIsImtpZCI6IjNiZjA1MzkxMzk2OTEzYTc4ZWM4MGY0MjcwMzM4NjM2NDA2MTBhZGMiLCJ0eXAiOiJKV1QifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tadQeJ_K5KHP5g3X6nAL64UUI96oSFlEa6JGHUiJ_UlJDECn17HLYCbm-_B4TAoqtSH6IxjPmgYFusqJP0_nWAhc_yIVghi2m7lFbPnpN8n6KObvRwAx_XQb5iv_7vMIId4LufKQAJbCnAXsz16y5Z8oeIPnNtG2ayM9EsnF08ZfrMyqdvbm_qhj61sULN-1-Hgh6Pz6aw1vXYY5SrVA5SZV3Dx3WHjgzdi5wWxnGFhPF4WVWzHpOIRsHqnvqvCWTsJ8Krr88WbiSHtIQHnQzPKZyDR6r0pVVCEYIfYSTYykpB_Gc_n9B94cBLinsSAyng6B6jAQ4Ed1wVg98eofRw"

	user, err := firebaseAuth.VerifyIDToken(ctx, validToken)
	if err != nil {
		t.Logf("⚠️  Firebase认证失败（直连模式，可能是网络问题）: %v", err)
	} else {
		t.Logf("✅ Firebase认证成功（直连模式）: uid=%s, email=%s, provider=%s",
			user.UID, user.Email, user.Provider)
	}
}

func TestFirebaseAuth_ModeSelection(t *testing.T) {
	logger := log.NewStdLogger(os.Stdout)

	// 模拟配置
	confData := &conf.Data{
		Firebase: &conf.Data_Firebase{
			ProjectId:       "lifehabit-2dd0d",
			CredentialsFile: "/Users/<USER>/my_pro/life-log-be/configs/firebase-service-account.json",
			Enabled:         true,
		},
	}

	tests := []struct {
		name        string
		envVars     map[string]string
		expectProxy bool
		description string
	}{
		{
			name: "强制启用代理模式",
			envVars: map[string]string{
				"FCM_USE_PROXY_CLIENT": "true",
				"FCM_PROXY_URL":        "socks5://127.0.0.1:1086",
			},
			expectProxy: true,
			description: "通过环境变量强制启用代理",
		},
		{
			name: "中国网络环境自动启用代理",
			envVars: map[string]string{
				"NETWORK_REGION": "china",
				"FCM_PROXY_URL":  "socks5://127.0.0.1:1086",
			},
			expectProxy: true,
			description: "检测到中国网络环境，自动启用代理",
		},
		{
			name:        "海外环境直连模式",
			envVars:     map[string]string{},
			expectProxy: false,
			description: "海外环境，使用直连模式",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 清理环境变量
			os.Unsetenv("FCM_USE_PROXY_CLIENT")
			os.Unsetenv("FCM_PROXY_URL")
			os.Unsetenv("NETWORK_REGION")
			os.Unsetenv("TZ")
			os.Unsetenv("LANG")

			// 设置测试环境变量
			for key, value := range tt.envVars {
				os.Setenv(key, value)
			}

			// 创建Firebase认证客户端
			firebaseAuth := NewFirebaseAuth(confData, logger)

			// 检查是否按预期选择了正确的模式
			if tt.expectProxy {
				if firebaseAuth.customClient == nil {
					t.Errorf("期望使用代理模式，但customClient为nil")
				} else {
					t.Logf("✅ 正确选择代理模式: %s", tt.description)
				}
			} else {
				if firebaseAuth.customClient != nil {
					t.Errorf("期望使用直连模式，但customClient不为nil")
				} else {
					t.Logf("✅ 正确选择直连模式: %s", tt.description)
				}
			}
		})
	}
}
