package firebase

import (
	"bytes"
	"context"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-jwt/jwt/v4"
)

// ServiceAccountCredentials Firebase服务账户凭据结构
type ServiceAccountCredentials struct {
	Type                    string `json:"type"`
	ProjectID               string `json:"project_id"`
	PrivateKeyID            string `json:"private_key_id"`
	PrivateKey              string `json:"private_key"`
	ClientEmail             string `json:"client_email"`
	ClientID                string `json:"client_id"`
	AuthURI                 string `json:"auth_uri"`
	TokenURI                string `json:"token_uri"`
	AuthProviderX509CertURL string `json:"auth_provider_x509_cert_url"`
	ClientX509CertURL       string `json:"client_x509_cert_url"`
}

// FCMAccessToken FCM访问令牌
type FCMAccessToken struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	ExpiresIn   int    `json:"expires_in"`
}

// CustomFCMClient 自定义FCM客户端（支持代理）
type CustomFCMClient struct {
	projectID          string
	credentials        *ServiceAccountCredentials
	httpClient         *http.Client
	accessToken        string
	tokenExpiry        time.Time
	certificates       map[string]string // 证书缓存
	certificatesExpiry time.Time         // 证书过期时间
	log                *log.Helper
}

// FCMMessage FCM消息结构
type FCMMessage struct {
	Token        string            `json:"token"`
	Notification *FCMNotification  `json:"notification,omitempty"`
	Data         map[string]string `json:"data,omitempty"`
	Android      *FCMAndroidConfig `json:"android,omitempty"`
	APNS         *FCMAPNSConfig    `json:"apns,omitempty"`
}

// FCMNotification FCM通知结构
type FCMNotification struct {
	Title string `json:"title"`
	Body  string `json:"body"`
}

// FCMAndroidConfig Android特定配置
type FCMAndroidConfig struct {
	Priority string `json:"priority,omitempty"`
}

// FCMAPNSConfig APNS特定配置
type FCMAPNSConfig struct {
	Headers map[string]string `json:"headers,omitempty"`
}

// FCMResponse FCM响应结构
type FCMResponse struct {
	Success   bool   `json:"success"`
	MessageID string `json:"message_id"`
	Error     string `json:"error,omitempty"`
}

// NewCustomFCMClient 创建自定义FCM客户端
func NewCustomFCMClient(credentialsFile, proxyURL string, logger log.Logger) (*CustomFCMClient, error) {
	logHelper := log.NewHelper(logger)

	// 读取服务账户凭据
	credData, err := os.ReadFile(credentialsFile)
	if err != nil {
		return nil, fmt.Errorf("读取凭据文件失败: %v", err)
	}

	var credentials ServiceAccountCredentials
	if err := json.Unmarshal(credData, &credentials); err != nil {
		return nil, fmt.Errorf("解析凭据文件失败: %v", err)
	}

	// 创建带代理的HTTP客户端
	httpClient := createFCMProxyHTTPClient(proxyURL, logHelper)

	client := &CustomFCMClient{
		projectID:   credentials.ProjectID,
		credentials: &credentials,
		httpClient:  httpClient,
		log:         logHelper,
	}

	logHelper.Infof("✅ 自定义FCM客户端初始化成功，项目ID: %s", credentials.ProjectID)
	return client, nil
}

// createFCMProxyHTTPClient 创建FCM专用代理HTTP客户端
func createFCMProxyHTTPClient(proxyURL string, logger *log.Helper) *http.Client {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
		},
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
		DisableKeepAlives:   false,
	}

	// 配置代理
	if proxyURL != "" {
		if proxy, err := url.Parse(proxyURL); err == nil {
			transport.Proxy = http.ProxyURL(proxy)
			logger.Infof("🌐 FCM客户端使用代理: %s", proxyURL)
		} else {
			logger.Warnf("⚠️  代理URL解析失败: %v", err)
		}
	}

	return &http.Client{
		Timeout:   60 * time.Second, // 增加超时时间到60秒
		Transport: transport,
	}
}

// getAccessToken 获取FCM访问令牌
func (c *CustomFCMClient) getAccessToken(ctx context.Context) (string, error) {
	// 检查现有token是否有效
	if c.accessToken != "" && time.Now().Before(c.tokenExpiry) {
		return c.accessToken, nil
	}

	c.log.Infof("🔑 开始获取FCM访问令牌...")

	// 生成JWT断言
	jwtToken, err := c.createJWTAssertion()
	if err != nil {
		return "", fmt.Errorf("创建JWT断言失败: %v", err)
	}

	// 请求访问令牌
	tokenResp, err := c.requestAccessToken(ctx, jwtToken)
	if err != nil {
		return "", fmt.Errorf("请求访问令牌失败: %v", err)
	}

	// 更新token和过期时间
	c.accessToken = tokenResp.AccessToken
	c.tokenExpiry = time.Now().Add(time.Duration(tokenResp.ExpiresIn-300) * time.Second) // 提前5分钟过期

	c.log.Infof("✅ FCM访问令牌获取成功，有效期: %d秒", tokenResp.ExpiresIn)
	return c.accessToken, nil
}

// createJWTAssertion 创建JWT断言
func (c *CustomFCMClient) createJWTAssertion() (string, error) {
	// 解析私钥
	block, _ := pem.Decode([]byte(c.credentials.PrivateKey))
	if block == nil {
		return "", fmt.Errorf("无法解析PEM格式的私钥")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("解析私钥失败: %v", err)
	}

	rsaKey, ok := privateKey.(*rsa.PrivateKey)
	if !ok {
		return "", fmt.Errorf("私钥不是RSA格式")
	}

	// 创建JWT Claims
	now := time.Now()
	claims := jwt.MapClaims{
		"iss":   c.credentials.ClientEmail,
		"scope": "https://www.googleapis.com/auth/firebase.messaging",
		"aud":   "https://oauth2.googleapis.com/token",
		"iat":   now.Unix(),
		"exp":   now.Add(time.Hour).Unix(),
	}

	// 创建并签名JWT
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	signedToken, err := token.SignedString(rsaKey)
	if err != nil {
		return "", fmt.Errorf("JWT签名失败: %v", err)
	}

	return signedToken, nil
}

// requestAccessToken 请求访问令牌
func (c *CustomFCMClient) requestAccessToken(ctx context.Context, jwtAssertion string) (*FCMAccessToken, error) {
	// 准备请求数据
	data := fmt.Sprintf(
		"grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=%s",
		jwtAssertion,
	)

	// 创建请求
	req, err := http.NewRequestWithContext(
		ctx,
		"POST",
		"https://oauth2.googleapis.com/token",
		strings.NewReader(data),
	)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "life-log-be/1.0")

	c.log.Infof("🌐 发送OAuth令牌请求到: %s", req.URL.String())

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送令牌请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		c.log.Errorf("❌ OAuth令牌请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
		return nil, fmt.Errorf("令牌请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var tokenResp FCMAccessToken
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("解析令牌响应失败: %v", err)
	}

	return &tokenResp, nil
}

// SendMessage 发送FCM消息
func (c *CustomFCMClient) SendMessage(ctx context.Context, message *FCMMessage) (*FCMResponse, error) {
	// 获取访问令牌
	accessToken, err := c.getAccessToken(ctx)
	if err != nil {
		return nil, err
	}

	// 准备消息数据
	messageData, err := json.Marshal(map[string]interface{}{
		"message": message,
	})
	if err != nil {
		return nil, fmt.Errorf("序列化消息失败: %v", err)
	}

	// 创建请求
	url := fmt.Sprintf("https://fcm.googleapis.com/v1/projects/%s/messages:send", c.projectID)
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewReader(messageData))
	if err != nil {
		return nil, err
	}

	// 设置请求头
	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "life-log-be/1.0")

	c.log.Infof("🚀 发送FCM消息到: %s", url)

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送FCM消息失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		c.log.Errorf("❌ FCM API错误，状态码: %d, 响应: %s", resp.StatusCode, string(respBody))
		return &FCMResponse{
			Success: false,
			Error:   fmt.Sprintf("FCM API错误: %s", string(respBody)),
		}, nil
	}

	// 解析成功响应
	var fcmResp map[string]interface{}
	if err := json.Unmarshal(respBody, &fcmResp); err != nil {
		return nil, fmt.Errorf("解析FCM响应失败: %v", err)
	}

	messageID := ""
	if name, ok := fcmResp["name"].(string); ok {
		// 从响应中提取消息ID
		parts := strings.Split(name, "/")
		if len(parts) > 0 {
			messageID = parts[len(parts)-1]
		}
	}

	c.log.Infof("✅ FCM消息发送成功，消息ID: %s", messageID)
	return &FCMResponse{
		Success:   true,
		MessageID: messageID,
	}, nil
}

// 注意：FirebaseUser 类型已在 auth.go 中定义，这里不需要重复声明

// VerifyIDTokenWithProxy 使用代理验证Firebase ID Token
func (c *CustomFCMClient) VerifyIDTokenWithProxy(ctx context.Context, idToken string) (*FirebaseUser, error) {
	if idToken == "" {
		return nil, fmt.Errorf("Firebase ID Token不能为空")
	}

	// 验证token格式（基本检查）
	if len(idToken) < 100 {
		return nil, fmt.Errorf("firebase ID Token格式无效")
	}

	c.log.Infof("🔐 开始验证Firebase ID Token（使用代理）...")

	// 本地解码JWT以进行基本验证和调试
	if claims, err := c.decodeJWTLocally(idToken); err != nil {
		c.log.Warnf("⚠️  本地JWT解码失败: %v", err)
	} else {
		c.log.Infof("📋 Token信息: iss=%s, aud=%s, sub=%s, exp=%v",
			claims["iss"], claims["aud"], claims["sub"], claims["exp"])

		// 检查基本的过期时间
		if exp, ok := claims["exp"].(float64); ok {
			if time.Now().Unix() > int64(exp) {
				return nil, fmt.Errorf("token已过期（本地检查）")
			}
		}
	}

	// 使用正确的Firebase ID Token验证方法
	firebaseUser, err := c.verifyFirebaseIDToken(ctx, idToken)
	if err != nil {
		return nil, err
	}

	c.log.Infof("✅ Firebase用户验证成功（代理模式）: uid=%s, email=%s, provider=%s",
		firebaseUser.UID, firebaseUser.Email, firebaseUser.Provider)
	return firebaseUser, nil
}

// validateTokenInfo 验证token信息
func (c *CustomFCMClient) validateTokenInfo(tokenInfo map[string]interface{}) error {
	// 验证audience（项目ID）
	if aud, ok := tokenInfo["aud"].(string); ok {
		if aud != c.projectID {
			return fmt.Errorf("token的audience不匹配，期望: %s, 实际: %s", c.projectID, aud)
		}
	} else {
		return fmt.Errorf("token中缺少audience信息")
	}

	// 验证issuer
	if iss, ok := tokenInfo["iss"].(string); ok {
		expectedIssuer := fmt.Sprintf("https://securetoken.google.com/%s", c.projectID)
		if iss != expectedIssuer {
			return fmt.Errorf("token的issuer不匹配，期望: %s, 实际: %s", expectedIssuer, iss)
		}
	} else {
		return fmt.Errorf("token中缺少issuer信息")
	}

	// 验证过期时间
	if exp, ok := tokenInfo["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			return fmt.Errorf("token已过期")
		}
	} else {
		return fmt.Errorf("token中缺少过期时间信息")
	}

	return nil
}

// parseTokenInfo 解析Google API返回的token信息为FirebaseUser
func (c *CustomFCMClient) parseTokenInfo(tokenInfo map[string]interface{}) *FirebaseUser {
	user := &FirebaseUser{}

	// 提取UID（必需）
	if uid, ok := tokenInfo["sub"].(string); ok {
		user.UID = uid
	}

	// 提取邮箱
	if email, ok := tokenInfo["email"].(string); ok {
		user.Email = email
	}

	// 提取邮箱验证状态
	if emailVerified, ok := tokenInfo["email_verified"].(bool); ok {
		user.EmailVerified = emailVerified
	}

	// 提取用户名
	if name, ok := tokenInfo["name"].(string); ok {
		user.Name = name
	}

	// 提取头像
	if picture, ok := tokenInfo["picture"].(string); ok {
		user.Picture = picture
	}

	// 提取认证提供商
	if firebaseInfo, ok := tokenInfo["firebase"].(map[string]interface{}); ok {
		if identities, ok := firebaseInfo["identities"].(map[string]interface{}); ok {
			// 确定主要的认证提供商
			if _, hasGoogle := identities["google.com"]; hasGoogle {
				user.Provider = "google"
			} else if _, hasApple := identities["apple.com"]; hasApple {
				user.Provider = "apple"
			} else {
				user.Provider = "firebase"
			}
		}
	}

	// 如果没有找到provider，默认为firebase
	if user.Provider == "" {
		user.Provider = "firebase"
	}

	// 提取语言偏好
	if locale, ok := tokenInfo["locale"].(string); ok {
		user.Locale = locale
	}

	return user
}

// decodeJWTLocally 本地解码JWT token（不验证签名，仅用于调试）
func (c *CustomFCMClient) decodeJWTLocally(token string) (map[string]interface{}, error) {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("JWT格式无效，应该有3个部分")
	}

	// 解码payload部分
	payload := parts[1]

	// 添加padding
	for len(payload)%4 != 0 {
		payload += "="
	}

	// Base64解码
	decoded, err := base64.URLEncoding.DecodeString(payload)
	if err != nil {
		return nil, fmt.Errorf("Base64解码失败: %v", err)
	}

	// JSON解析
	var claims map[string]interface{}
	if err := json.Unmarshal(decoded, &claims); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %v", err)
	}

	return claims, nil
}

// verifyFirebaseIDToken 使用正确的Firebase ID Token验证方法
func (c *CustomFCMClient) verifyFirebaseIDToken(ctx context.Context, idToken string) (*FirebaseUser, error) {
	c.log.Infof("🔐 开始Firebase ID Token验证（使用公钥验证）...")

	// 1. 解析JWT header获取kid
	parts := strings.Split(idToken, ".")
	if len(parts) != 3 {
		return nil, fmt.Errorf("JWT格式无效")
	}

	// 解码header
	headerData := parts[0]
	for len(headerData)%4 != 0 {
		headerData += "="
	}

	headerBytes, err := base64.URLEncoding.DecodeString(headerData)
	if err != nil {
		return nil, fmt.Errorf("解码JWT header失败: %v", err)
	}

	var header map[string]interface{}
	if err := json.Unmarshal(headerBytes, &header); err != nil {
		return nil, fmt.Errorf("解析JWT header失败: %v", err)
	}

	kid, ok := header["kid"].(string)
	if !ok {
		return nil, fmt.Errorf("JWT header中缺少kid字段")
	}

	c.log.Infof("📋 JWT kid: %s", kid)

	// 2. 获取Firebase公钥证书
	certificates, err := c.getFirebaseCertificates(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取Firebase证书失败: %v", err)
	}

	certificate, ok := certificates[kid]
	if !ok {
		return nil, fmt.Errorf("找不到对应的证书，kid: %s", kid)
	}

	// 3. 验证JWT签名
	token, err := jwt.Parse(idToken, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("意外的签名算法: %v", token.Header["alg"])
		}

		// 解析证书
		return c.parseCertificate(certificate)
	})

	if err != nil {
		return nil, fmt.Errorf("JWT签名验证失败: %v", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("JWT token无效")
	}

	// 4. 验证claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, fmt.Errorf("无法解析JWT claims")
	}

	if err := c.validateFirebaseClaims(claims); err != nil {
		return nil, err
	}

	// 5. 解析用户信息
	firebaseUser := c.parseFirebaseClaims(claims)

	c.log.Infof("✅ Firebase ID Token验证成功: uid=%s, email=%s, provider=%s, user=%v",
		firebaseUser.UID, firebaseUser.Email, firebaseUser.Provider, firebaseUser)

	return firebaseUser, nil
}

// getFirebaseCertificates 获取Firebase公钥证书（带缓存和重试）
func (c *CustomFCMClient) getFirebaseCertificates(ctx context.Context) (map[string]string, error) {
	// 检查缓存是否有效
	if c.certificates != nil && time.Now().Before(c.certificatesExpiry) {
		c.log.Infof("📋 使用缓存的Firebase证书，剩余有效期: %v", c.certificatesExpiry.Sub(time.Now()))
		return c.certificates, nil
	}

	// Firebase公钥证书URL
	certURL := "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>"

	c.log.Infof("🌐 获取Firebase证书: %s", certURL)

	// 重试机制
	maxRetries := 3
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		if attempt > 1 {
			c.log.Warnf("⚠️  第 %d 次重试获取Firebase证书...", attempt)
			// 指数退避
			backoff := time.Duration(attempt*attempt) * time.Second
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(backoff):
			}
		}

		certificates, err := c.fetchCertificatesOnce(ctx, certURL)
		if err != nil {
			lastErr = err
			c.log.Warnf("⚠️  获取证书失败 (尝试 %d/%d): %v", attempt, maxRetries, err)
			continue
		}

		// 成功获取，更新缓存
		c.certificates = certificates
		c.certificatesExpiry = time.Now().Add(1 * time.Hour) // 缓存1小时

		c.log.Infof("✅ 成功获取 %d 个证书，缓存1小时", len(certificates))
		return certificates, nil
	}

	return nil, fmt.Errorf("获取Firebase证书失败，已重试 %d 次: %v", maxRetries, lastErr)
}

// fetchCertificatesOnce 单次获取证书
func (c *CustomFCMClient) fetchCertificatesOnce(ctx context.Context, certURL string) (map[string]string, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", certURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建证书请求失败: %v", err)
	}

	req.Header.Set("User-Agent", "life-log-be/1.0")
	req.Header.Set("Accept", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("获取证书请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("获取证书失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	var certificates map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&certificates); err != nil {
		return nil, fmt.Errorf("解析证书响应失败: %v", err)
	}

	return certificates, nil
}

// parseCertificate 解析X.509证书
func (c *CustomFCMClient) parseCertificate(certPEM string) (interface{}, error) {
	block, _ := pem.Decode([]byte(certPEM))
	if block == nil {
		return nil, fmt.Errorf("无法解析PEM证书")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("解析X.509证书失败: %v", err)
	}

	return cert.PublicKey, nil
}

// validateFirebaseClaims 验证Firebase JWT claims
func (c *CustomFCMClient) validateFirebaseClaims(claims jwt.MapClaims) error {
	// 验证issuer
	iss, ok := claims["iss"].(string)
	if !ok {
		return fmt.Errorf("缺少issuer字段")
	}
	expectedIssuer := fmt.Sprintf("https://securetoken.google.com/%s", c.projectID)
	if iss != expectedIssuer {
		return fmt.Errorf("issuer不匹配，期望: %s, 实际: %s", expectedIssuer, iss)
	}

	// 验证audience
	aud, ok := claims["aud"].(string)
	if !ok {
		return fmt.Errorf("缺少audience字段")
	}
	if aud != c.projectID {
		return fmt.Errorf("audience不匹配，期望: %s, 实际: %s", c.projectID, aud)
	}

	// 验证过期时间
	exp, ok := claims["exp"].(float64)
	if !ok {
		return fmt.Errorf("缺少exp字段")
	}
	if time.Now().Unix() > int64(exp) {
		return fmt.Errorf("token已过期")
	}

	// 验证签发时间
	iat, ok := claims["iat"].(float64)
	if !ok {
		return fmt.Errorf("缺少iat字段")
	}
	if time.Now().Unix() < int64(iat) {
		return fmt.Errorf("token签发时间无效")
	}

	// 验证认证时间
	authTime, ok := claims["auth_time"].(float64)
	if !ok {
		return fmt.Errorf("缺少auth_time字段")
	}
	if int64(authTime) > time.Now().Unix() {
		return fmt.Errorf("认证时间无效")
	}

	// 验证subject
	sub, ok := claims["sub"].(string)
	if !ok || sub == "" {
		return fmt.Errorf("缺少或无效的sub字段")
	}

	return nil
}

// parseFirebaseClaims 解析Firebase JWT claims为FirebaseUser
func (c *CustomFCMClient) parseFirebaseClaims(claims jwt.MapClaims) *FirebaseUser {
	user := &FirebaseUser{}

	// 提取UID
	if sub, ok := claims["sub"].(string); ok {
		user.UID = sub
	}

	// 提取邮箱
	if email, ok := claims["email"].(string); ok {
		user.Email = email
	}

	// 提取邮箱验证状态
	if emailVerified, ok := claims["email_verified"].(bool); ok {
		user.EmailVerified = emailVerified
	}

	// 提取用户名
	if name, ok := claims["name"].(string); ok {
		user.Name = name
	}

	// 提取头像
	if picture, ok := claims["picture"].(string); ok {
		user.Picture = picture
	}

	// 提取认证提供商
	if firebaseInfo, ok := claims["firebase"].(map[string]interface{}); ok {
		if signInProvider, ok := firebaseInfo["sign_in_provider"].(string); ok {
			switch signInProvider {
			case "google.com":
				user.Provider = "google"
			case "apple.com":
				user.Provider = "apple"
			case "password":
				user.Provider = "email"
			default:
				user.Provider = signInProvider
			}
		}
	}

	// 如果没有找到provider，默认为firebase
	if user.Provider == "" {
		user.Provider = "firebase"
	}

	// 提取语言偏好
	if locale, ok := claims["locale"].(string); ok {
		user.Locale = locale
	}

	return user
}

// DiagnoseProxyConnection 诊断代理连接状态
func (c *CustomFCMClient) DiagnoseProxyConnection(ctx context.Context) error {
	c.log.Infof("🔍 开始诊断代理连接状态...")

	// 测试URL列表
	testURLs := []string{
		"https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>",
		"https://oauth2.googleapis.com/token",
		"https://www.google.com",
	}

	for i, testURL := range testURLs {
		c.log.Infof("🌐 测试连接 %d/%d: %s", i+1, len(testURLs), testURL)

		req, err := http.NewRequestWithContext(ctx, "HEAD", testURL, nil)
		if err != nil {
			c.log.Errorf("❌ 创建测试请求失败: %v", err)
			continue
		}

		req.Header.Set("User-Agent", "life-log-be/1.0")

		start := time.Now()
		resp, err := c.httpClient.Do(req)
		duration := time.Since(start)

		if err != nil {
			c.log.Errorf("❌ 连接失败 (%v): %v", duration, err)
			continue
		}
		resp.Body.Close()

		c.log.Infof("✅ 连接成功 (%v): 状态码 %d", duration, resp.StatusCode)
	}

	return nil
}

// GetProxyURL 获取代理URL
func GetProxyURL() string {
	// 优先从环境变量读取
	if proxy := os.Getenv("FCM_PROXY_URL"); proxy != "" {
		return proxy
	}

	if proxy := os.Getenv("HTTPS_PROXY"); proxy != "" {
		return proxy
	}

	if proxy := os.Getenv("HTTP_PROXY"); proxy != "" {
		return proxy
	}

	// 默认Shadowsocks代理配置
	return "socks5://127.0.0.1:1086"
}
