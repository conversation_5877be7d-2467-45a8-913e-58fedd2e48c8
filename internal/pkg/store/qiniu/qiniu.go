package qiniu

import (
	"github.com/qiniu/go-sdk/v7/auth"
	"github.com/qiniu/go-sdk/v7/storage"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"time"
)

var Client *QiniuX

type QiniuX struct {
	AccessKey    string
	SecretKey    string
	Bucket       string
	TokenExpires int32
	Addr         string

	PublicAddr   string
	PublicBucket string
}

var SceneDirMap = map[enums.FileSceneType]string{
	enums.FileSceneTypeAvatar: "avatar-imgs",
	enums.FileSceneTypeAward:  "award-imgs",
}

func (q *QiniuX) CreateUptoken(scene enums.StorageVisibleType) string {
	putPolicy := storage.PutPolicy{
		Scope:      q.Bucket,
		FsizeLimit: 1024 * 1024 * 20, // 20 MB
		MimeLimit:  "image/*",        // 图片类型
	}
	if scene == enums.StorageVisibleTypePublic {
		putPolicy = storage.PutPolicy{
			Scope:      q.PublicBucket,
			FsizeLimit: 1024 * 1024 * 10, // 10 MB
			MimeLimit:  "image/*",        // 图片类型
		}
	} else {
		putPolicy.Expires = uint64(q.TokenExpires)
	}

	mac := auth.New(q.AccessKey, q.SecretKey)
	return putPolicy.UploadToken(mac)
}

func (q *QiniuX) CreateDnURL(key string, scene enums.FileSceneType, expireTime int) string {
	mac := auth.New(q.AccessKey, q.SecretKey)
	url := q.Addr
	if fileDir, ok := SceneDirMap[scene]; ok {
		url += "/" + fileDir
	}
	deadline := time.Now().Add(time.Duration(expireTime) * time.Second).Unix()
	return storage.MakePrivateURL(mac, url, key, deadline)
}
