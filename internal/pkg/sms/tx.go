package sms

import (
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/wlnil/life-log-be/internal/conf"

	sms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sms/v20210111" // 引入sms
)

type Tx struct {
	Domain     string
	AppId      string
	SecretId   string
	SecretKey  string
	TemplateId string
	Sign       string

	client  *sms.Client
	request *sms.SendSmsRequest
}

func NewTx(conf *conf.Data) BaseInfoRepo {
	tx := &Tx{
		Domain:     conf.TxSms.Domain,
		AppId:      conf.TxSms.AppId,
		SecretId:   conf.TxSms.SecretId,
		SecretKey:  conf.TxSms.SecretKey,
		TemplateId: conf.TxSms.TemplateId,
		Sign:       conf.TxSms.Sign,
	}
	if err := tx.init(); err != nil {
		log.Errorf("sms tx init err: %v", err)
	}

	return tx
}

func (t *Tx) init() error {
	credential := common.NewCredential(
		t.SecretId,
		t.SecretKey,
	)
	cpf := profile.NewClientProfile()
	t.client, _ = sms.NewClient(credential, "ap-guangzhou", cpf)
	t.request = sms.NewSendSmsRequest()
	t.request.SmsSdkAppId = common.StringPtr(t.AppId)
	t.request.SignName = common.StringPtr(t.Sign)
	t.request.TemplateId = common.StringPtr(t.TemplateId)

	return nil
}

func (t *Tx) Send(phone string, code string, expires int) error {
	/* 模板参数: 模板参数的个数需要与 TemplateId 对应模板的变量个数保持一致，若无模板参数，则设置为空*/
	t.request.TemplateParamSet = common.StringPtrs([]string{code, fmt.Sprintf("%v", expires/60)})
	/* 下发手机号码，采用 E.164 标准，+[国家或地区码][手机号]
	 * 示例如：+8613711112222， 其中前面有一个+号 ，86为国家码，13711112222为手机号，最多不要超过200个手机号*/
	t.request.PhoneNumberSet = common.StringPtrs([]string{fmt.Sprintf("+86%v", phone)})
	response, err := t.client.SendSms(t.request)
	if err != nil {
		log.Errorf("sms tx send err: %v", err)
		return err
	}
	b, _ := json.Marshal(response.Response)
	fmt.Printf("%s", b)

	return nil
}
