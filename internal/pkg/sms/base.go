package sms

import (
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jordan-wright/email"
	"github.com/wlnil/life-log-be/internal/conf"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"net/smtp"
	"net/textproto"
)

var Client *ClientUseCase

type BaseInfoRepo interface {
	Send(phone string, code string, expires int) error
}

type ClientUseCase struct {
	BaseInfoRepo

	ChannelType enums.SmsChannelType
	Email       struct {
		From     string
		Host     string
		Port     int
		Username string
		Password string
	}
}

func New(conf *conf.Data) {
	Client = &ClientUseCase{}

	// 短信配置
	switch conf.UseSmsChannel {
	case string(enums.SmsChannelTypeTx):
		Client.BaseInfoRepo = NewTx(conf)
		Client.ChannelType = enums.SmsChannelTypeTx
	default:
		log.Errorf("sms channel not support")
	}

	// 邮件配置
	Client.Email.Host = conf.Email.Host
	Client.Email.Port = int(conf.Email.Port)
	Client.Email.From = conf.Email.From
	Client.Email.Username = conf.Email.Username
	Client.Email.Password = conf.Email.Password
}

func (c *ClientUseCase) SendEmail(to string, code string, expires int) error {
	e := &email.Email{
		To:      []string{to},
		From:    c.Email.From,
		Subject: fmt.Sprintf("Your verification code is %v", code),
		HTML: []byte(fmt.Sprintf(`
		<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification Code</title>
    <style>
        body, html {
            width: 100%%;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f7f7f7;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px 0;
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            width: 90%%;
            max-width: 420px;
            text-align: center;
            padding: 30px 20px;
            margin: 0 auto;
        }
        .logo {
            margin-bottom: 15px;
        }
        .header {
            color: #333;
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 25px;
        }
        .code-box {
            background: #f2f2f2;
            border-radius: 8px;
            color: #333;
            font-size: 28px;
            font-weight: bold;
            letter-spacing: 2px;
            margin: 25px auto;
            padding: 15px 0;
            width: 80%%;
            border: 1px solid #e0e0e0;
        }
        .info {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            margin-top: 20px;
        }
        .footer {
            margin-top: 30px;
            font-size: 12px;
            color: #999;
        }
    </style>
</head>
<body>
<div class="card">
    <div class="header">Life Habit Verification Code</div>
    <div class="code-box">%v</div>
    <p class="info">This code is valid for <b>%v minutes</b>.<br>If you didn't request this code, please ignore this email.</p>
    <div class="footer">© Life Habit App. All rights reserved.</div>
</div>
</body>
</html>`, code, expires/60)),
		Headers: textproto.MIMEHeader{},
	}
	return e.Send(fmt.Sprintf("%v:%v", c.Email.Host, c.Email.Port), smtp.PlainAuth("", c.Email.Username, c.Email.Password, c.Email.Host))

}
