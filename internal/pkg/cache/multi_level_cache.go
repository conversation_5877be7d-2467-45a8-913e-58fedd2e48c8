package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

// MultiLevelCache 多级缓存
type MultiLevelCache struct {
	localCache  *LocalCache
	redisClient *redis.Client
	log         *log.Helper
}

// LocalCache 本地缓存
type LocalCache struct {
	data   sync.Map
	expire sync.Map
}

// CacheItem 缓存项
type CacheItem struct {
	Value     interface{}
	ExpireAt  time.Time
}

// NewLocalCache 创建本地缓存
func NewLocalCache() *LocalCache {
	lc := &LocalCache{}
	// 启动清理协程
	go lc.cleanup()
	return lc
}

// NewMultiLevelCache 创建多级缓存
func NewMultiLevelCache(redisClient *redis.Client, logger log.Logger) *MultiLevelCache {
	return &MultiLevelCache{
		localCache:  NewLocalCache(),
		redisClient: redisClient,
		log:         log.<PERSON><PERSON>elper(logger),
	}
}

// Get 从本地缓存获取数据
func (lc *LocalCache) Get(key string) (interface{}, bool) {
	if value, ok := lc.data.Load(key); ok {
		item := value.(*CacheItem)
		if time.Now().Before(item.ExpireAt) {
			return item.Value, true
		}
		// 过期删除
		lc.data.Delete(key)
		lc.expire.Delete(key)
	}
	return nil, false
}

// Set 设置本地缓存
func (lc *LocalCache) Set(key string, value interface{}, duration time.Duration) {
	expireAt := time.Now().Add(duration)
	item := &CacheItem{
		Value:    value,
		ExpireAt: expireAt,
	}
	lc.data.Store(key, item)
	lc.expire.Store(key, expireAt)
}

// Delete 删除本地缓存
func (lc *LocalCache) Delete(key string) {
	lc.data.Delete(key)
	lc.expire.Delete(key)
}

// cleanup 清理过期缓存
func (lc *LocalCache) cleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		now := time.Now()
		lc.expire.Range(func(key, value interface{}) bool {
			expireAt := value.(time.Time)
			if now.After(expireAt) {
				keyStr := key.(string)
				lc.data.Delete(keyStr)
				lc.expire.Delete(keyStr)
			}
			return true
		})
	}
}

// GetUserInfo 多级缓存获取用户信息
func (mlc *MultiLevelCache) GetUserInfo(ctx context.Context, userID int32) (map[string]interface{}, error) {
	cacheKey := fmt.Sprintf("user_info_%d", userID)
	
	// 1. 本地缓存查询（0ms延迟）
	if value, ok := mlc.localCache.Get(cacheKey); ok {
		mlc.log.Debugf("【多级缓存】本地缓存命中: %s", cacheKey)
		return value.(map[string]interface{}), nil
	}
	
	// 2. Redis缓存查询（150ms延迟）
	redisKey := fmt.Sprintf("user_info_%d", userID)
	if cacheUser, err := mlc.redisClient.Get(ctx, redisKey).Result(); err == nil {
		var userInfo map[string]interface{}
		if err := json.Unmarshal([]byte(cacheUser), &userInfo); err == nil {
			// 写入本地缓存
			mlc.localCache.Set(cacheKey, userInfo, 5*time.Minute)
			mlc.log.Debugf("【多级缓存】Redis缓存命中: %s", cacheKey)
			return userInfo, nil
		}
	}
	
	// 3. 缓存未命中
	mlc.log.Debugf("【多级缓存】缓存未命中: %s", cacheKey)
	return nil, fmt.Errorf("cache miss")
}

// SetUserInfo 设置多级缓存
func (mlc *MultiLevelCache) SetUserInfo(ctx context.Context, userID int32, userInfo map[string]interface{}) error {
	cacheKey := fmt.Sprintf("user_info_%d", userID)
	
	// 1. 设置本地缓存
	mlc.localCache.Set(cacheKey, userInfo, 5*time.Minute)
	
	// 2. 设置Redis缓存
	redisKey := fmt.Sprintf("user_info_%d", userID)
	userInfoBytes, err := json.Marshal(userInfo)
	if err != nil {
		return err
	}
	
	return mlc.redisClient.Set(ctx, redisKey, userInfoBytes, time.Hour).Err()
}

// GetUserSetting 获取用户设置
func (mlc *MultiLevelCache) GetUserSetting(ctx context.Context, userID int32) (map[string]interface{}, error) {
	cacheKey := fmt.Sprintf("user_setting_%d", userID)
	
	// 1. 本地缓存查询
	if value, ok := mlc.localCache.Get(cacheKey); ok {
		return value.(map[string]interface{}), nil
	}
	
	// 2. Redis缓存查询
	redisKey := fmt.Sprintf("user_setting_%d", userID)
	if cacheSetting, err := mlc.redisClient.Get(ctx, redisKey).Result(); err == nil {
		var userSetting map[string]interface{}
		if err := json.Unmarshal([]byte(cacheSetting), &userSetting); err == nil {
			// 写入本地缓存
			mlc.localCache.Set(cacheKey, userSetting, 10*time.Minute)
			return userSetting, nil
		}
	}
	
	return nil, fmt.Errorf("cache miss")
}

// SetUserSetting 设置用户设置缓存
func (mlc *MultiLevelCache) SetUserSetting(ctx context.Context, userID int32, userSetting map[string]interface{}) error {
	cacheKey := fmt.Sprintf("user_setting_%d", userID)
	
	// 1. 设置本地缓存
	mlc.localCache.Set(cacheKey, userSetting, 10*time.Minute)
	
	// 2. 设置Redis缓存
	redisKey := fmt.Sprintf("user_setting_%d", userID)
	settingBytes, err := json.Marshal(userSetting)
	if err != nil {
		return err
	}
	
	return mlc.redisClient.Set(ctx, redisKey, settingBytes, 30*time.Minute).Err()
}

// GetUserHabits 获取用户习惯数据
func (mlc *MultiLevelCache) GetUserHabits(ctx context.Context, userID int32) ([]map[string]interface{}, error) {
	cacheKey := fmt.Sprintf("user_habits_%d", userID)
	
	// 1. 本地缓存查询
	if value, ok := mlc.localCache.Get(cacheKey); ok {
		return value.([]map[string]interface{}), nil
	}
	
	// 2. Redis缓存查询
	redisKey := fmt.Sprintf("user_habits_%d", userID)
	if cacheHabits, err := mlc.redisClient.Get(ctx, redisKey).Result(); err == nil {
		var userHabits []map[string]interface{}
		if err := json.Unmarshal([]byte(cacheHabits), &userHabits); err == nil {
			// 写入本地缓存
			mlc.localCache.Set(cacheKey, userHabits, 3*time.Minute)
			return userHabits, nil
		}
	}
	
	return nil, fmt.Errorf("cache miss")
}

// SetUserHabits 设置用户习惯缓存
func (mlc *MultiLevelCache) SetUserHabits(ctx context.Context, userID int32, userHabits []map[string]interface{}) error {
	cacheKey := fmt.Sprintf("user_habits_%d", userID)
	
	// 1. 设置本地缓存
	mlc.localCache.Set(cacheKey, userHabits, 3*time.Minute)
	
	// 2. 设置Redis缓存
	redisKey := fmt.Sprintf("user_habits_%d", userID)
	habitsBytes, err := json.Marshal(userHabits)
	if err != nil {
		return err
	}
	
	return mlc.redisClient.Set(ctx, redisKey, habitsBytes, 15*time.Minute).Err()
}

// InvalidateUserCache 清除用户相关缓存
func (mlc *MultiLevelCache) InvalidateUserCache(ctx context.Context, userID int32) error {
	// 清除本地缓存
	mlc.localCache.Delete(fmt.Sprintf("user_info_%d", userID))
	mlc.localCache.Delete(fmt.Sprintf("user_setting_%d", userID))
	mlc.localCache.Delete(fmt.Sprintf("user_habits_%d", userID))
	
	// 清除Redis缓存
	keys := []string{
		fmt.Sprintf("user_info_%d", userID),
		fmt.Sprintf("user_setting_%d", userID),
		fmt.Sprintf("user_habits_%d", userID),
	}
	
	return mlc.redisClient.Del(ctx, keys...).Err()
}

// GetCacheStats 获取缓存统计信息
func (mlc *MultiLevelCache) GetCacheStats() map[string]interface{} {
	localCount := 0
	mlc.localCache.data.Range(func(key, value interface{}) bool {
		localCount++
		return true
	})

	return map[string]interface{}{
		"local_cache_count": localCount,
		"timestamp":         time.Now().Unix(),
	}
}

// WarmupCache 缓存预热
func (mlc *MultiLevelCache) WarmupCache(ctx context.Context, userID int32) error {
	mlc.log.Infof("【缓存预热】开始预热用户缓存: %d", userID)
	
	// 这里可以预加载一些常用数据到缓存
	// 例如：用户信息、用户设置、最近的习惯数据等
	
	// 示例：预热用户基础信息
	userInfo := map[string]interface{}{
		"id":         userID,
		"preloaded":  true,
		"warmup_at":  time.Now().Unix(),
	}
	
	if err := mlc.SetUserInfo(ctx, userID, userInfo); err != nil {
		return err
	}
	
	mlc.log.Infof("【缓存预热】用户缓存预热完成: %d", userID)
	return nil
}
