package cache

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"time"
)

var RedisClient *redis.Client

type RedisX struct {
	Addr     string
	Username string
	Password string
	DB       int
	init     bool
}

func (r *RedisX) Init() {
	if !r.init {
		r.new()
		r.init = true
	}
}

func (r *RedisX) new() {
	if r.init {
		return
	}

	// 优化Redis连接池配置
	client := redis.NewClient(&redis.Options{
		Addr:     r.Addr,
		Username: r.Username,
		Password: r.Password,
		DB:       r.DB,

		// 连接池优化配置
		PoolSize:     20, // 连接池大小：默认10 → 20
		MinIdleConns: 5,  // 最小空闲连接数：新增
		MaxRetries:   3,  // 最大重试次数：新增

		// 超时配置优化
		DialTimeout:  5 * time.Second, // 连接超时
		ReadTimeout:  3 * time.Second, // 读取超时
		WriteTimeout: 3 * time.Second, // 写入超时

		// 连接生命周期
		PoolTimeout:     4 * time.Second, // 连接池超时
		ConnMaxIdleTime: 5 * time.Minute, // 空闲连接超时
	})

	ctx := context.Background()
	if _, err := client.Ping(ctx).Result(); err != nil {
		panic(fmt.Sprintf("cache Ping err: %v", err))
	}

	RedisClient = client
}

func (r *RedisX) Close() {
	ctx := context.Background()
	if _, err := RedisClient.Ping(ctx).Result(); err != nil {
		log.Error(err)
		return
	}
	if err := RedisClient.Close(); err != nil {
		log.Error(err)
	}

	return
}
