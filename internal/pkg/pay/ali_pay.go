package pay

import (
	"context"
	"fmt"
	"github.com/smartwalle/alipay/v3"
	"sync"
)

type AliPay struct {
	client *alipay.Client

	AppId         string
	AppPrivateKey string
	AliPublicKey  string
	EncryptKey    string
	IsProduction  bool
	NotifyURL     string
	ReturnURL     string
}

var (
	AliPayInstance *AliPay
	once           sync.Once
)

func (a *AliPay) Init(ctx context.Context) error {
	var err error
	once.Do(func() {
		client, err := alipay.New(AliPayInstance.AppId, AliPayInstance.AppPrivateKey, AliPayInstance.IsProduction)
		if err != nil {
			return
		}
		err = client.LoadAliPayPublicKey(AliPayInstance.AliPublicKey)
		if err != nil {
			return
		}
		err = client.SetEncryptKey(AliPayInstance.EncryptKey)
		AliPayInstance.client = client
	})
	return err
}

func (a *AliPay) Pay(ctx context.Context, amount float64, serialNumber string) (string, error) {
	var p = alipay.TradeAppPay{}
	p.NotifyURL = AliPayInstance.NotifyURL
	p.ReturnURL = AliPayInstance.ReturnURL
	p.Subject = "支付测试:" + serialNumber
	p.OutTradeNo = serialNumber
	p.TotalAmount = fmt.Sprintf("%.2f", amount)
	p.ProductCode = "FAST_INSTANT_TRADE_PAY"

	url, err := a.client.TradeAppPay(p)
	if err != nil {
		return "", err
	}
	return url, nil
}

func (a *AliPay) Refund(orderID string, amount float64) (string, error) {
	// 实现退款逻辑
	return "AliPay refund ID", nil
}

func (a *AliPay) Query(orderID string) (string, error) {
	// 实现查询逻辑
	return "AliPay status", nil
}
