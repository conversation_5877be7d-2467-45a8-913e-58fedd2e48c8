package httpClient

import (
	"fmt"
	"github.com/imroc/req/v3"
	"time"
)

var Client *req.Client

type HttpX struct {
	Timeout    int
	RetryCount int
	init       bool
}

type ErrorMessage struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
}

func (msg *ErrorMessage) Error() string {
	return fmt.Sprintf("API Error: %s", msg.Message)
}

func (h *HttpX) Init() {
	if h.init {
		return
	}
	h.new()
	h.init = true
}

func (h *HttpX) new() {
	if h.init {
		return
	}

	Client = req.C().
		SetUserAgent("life-log").
		SetTimeout(time.Duration(h.Timeout) * time.Second).
		EnableDumpEachRequest().
		SetCommonErrorResult(&ErrorMessage{}).
		SetCommonRetryCount(h.RetryCount).
		OnAfterResponse(func(client *req.Client, resp *req.Response) error {
			if resp.Err != nil {
				return nil
			}
			if errMsg, ok := resp.ErrorResult().(*ErrorMessage); ok {
				resp.Err = errMsg
				return nil
			}
			if !resp.IsSuccessState() {
				// Neither a success response nor a error response, record details to help troubleshooting
				resp.Err = fmt.Errorf("bad status: %s\nraw content:\n%s", resp.Status, resp.Dump())
			}
			return nil
		})
}
