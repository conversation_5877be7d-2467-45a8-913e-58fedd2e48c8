package db

import (
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"time"
)

var GormClient *gorm.DB

type GormX struct {
	Conn         string
	MaxIdleCount int
	MaxOpenCount int
	init         bool
}

func (g *GormX) Init() {
	if !g.init {
		g.new()
		g.init = true
	}
}

func (g *GormX) new() {
	if g.init {
		return
	}

	client, err := gorm.Open(mysql.Open(g.Conn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		panic(fmt.Sprintf("gorm open mysql err: %v", err))
	}

	db, err := client.DB()
	if err != nil {
		panic(fmt.Sprintf("gorm return db err: %v", err))
	}
	if err = db.Ping(); err != nil {
		panic(fmt.Sprintf("mysql Ping err: %v", err))
	}

	// 优化数据库连接池配置
	maxOpenCount, maxIdleCount := 50, 25
	if g.MaxOpenCount > 0 {
		maxOpenCount = g.MaxOpenCount
	}
	if g.MaxIdleCount > 0 {
		maxIdleCount = g.MaxIdleCount
	}

	// 设置连接池参数
	db.SetMaxOpenConns(maxOpenCount)        // 最大打开连接数
	db.SetMaxIdleConns(maxIdleCount)        // 最大空闲连接数
	db.SetConnMaxLifetime(60 * time.Minute) // 连接最大生命周期
	db.SetConnMaxIdleTime(10 * time.Minute) // 空闲连接超时时间

	GormClient = client
}

func (g *GormX) Close() {
	db, err := GormClient.DB()
	if err != nil {
		log.Error(err)
		return
	}
	if err = db.Ping(); err != nil {
		log.Error(err)
		return
	}
	if err = db.Close(); err != nil {
		log.Error(err)
	}

	return
}
