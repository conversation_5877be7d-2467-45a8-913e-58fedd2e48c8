package task

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/tx7do/kratos-transport/transport/asynq"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	httpClient "github.com/wlnil/life-log-be/internal/pkg/http_client"
)

const (
	NormalTask   = "normal_ask"
	DelayTask    = "delay_task"
	PeriodicTask = "periodic_task"
)

var Client *asynq.Server

type Task struct {
	RedisAddr     string
	RedisPassword string
	DB            int
	init          bool
}

type Payload struct {
	ApiUrl string           `json:"api_url"` // 接口地址
	Method enums.HttpMethod `json:"method"`  // 请求方法
	Body   string           `json:"body"`    // 请求体
}

func (t *Task) Init() {
	if !t.init {
		t.new()
		t.init = true
	}
}

func (t *Task) new() {
	if t.init {
		return
	}

	client := asynq.NewServer(
		asynq.WithAddress(t.RedisAddr),
		asynq.WithRedisPassword(t.RedisPassword),
		asynq.WithRedisDatabase(t.DB),
		asynq.WithQueues(map[string]int{
			NormalTask:   4,
			DelayTask:    3,
			PeriodicTask: 3,
		}),
	)
	Client = client

	_ = asynq.RegisterSubscriber(Client, NormalTask, HandleNormalTask)
	_ = asynq.RegisterSubscriber(Client, DelayTask, HandleDelayTask)
	_ = asynq.RegisterSubscriber(Client, PeriodicTask, HandlePeriodTask)

	go t.start()
}

func (t *Task) start() {
	ctx := context.Background()
	if err := Client.Start(ctx); err != nil {
		panic(fmt.Sprintf("task start err: %v", err))
	}
}

func (t *Task) Close() {
	ctx := context.Background()
	if err := Client.Stop(ctx); err != nil {
		log.Errorf("task close, expected nil got %v", err)
	}

	return
}

func HandleNormalTask(taskType string, taskData *Payload) error {
	return nil
}

func HandleDelayTask(taskType string, taskData *Payload) error {
	bodyJsonStr := taskData.Body
	if bodyJsonStr == "" {
		bodyJsonByte, _ := json.Marshal(map[string]interface{}{})
		bodyJsonStr = string(bodyJsonByte)
	}
	_, err := httpClient.Client.R().
		SetBodyJsonString(taskData.Body).
		Post(taskData.ApiUrl)
	if err != nil {
		log.Fatal(err)
	}

	return nil
}

func HandlePeriodTask(taskType string, taskData *Payload) error {
	return nil
}
