package task

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/go-co-op/gocron"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

// DistributedScheduler 分布式调度器
type DistributedScheduler struct {
	scheduler   *gocron.Scheduler
	redisClient *redis.Client
	instanceID  string
	log         *log.Helper
	ctx         context.Context
	cancel      context.CancelFunc
}

// TaskFunc 任务执行函数类型
type TaskFunc func() error

// ScheduleConfig 调度配置
type ScheduleConfig struct {
	Name        string        // 任务名称
	Interval    time.Duration // 执行间隔
	LockTimeout time.Duration // 分布式锁超时时间
	TaskFunc    TaskFunc      // 任务执行函数
	Tags        []string      // 任务标签，用于任务管理
}

// NewDistributedScheduler 创建分布式调度器
func NewDistributedScheduler(redisClient *redis.Client, logger log.Logger) *DistributedScheduler {
	ctx, cancel := context.WithCancel(context.Background())
	instanceID := generateInstanceID()

	return &DistributedScheduler{
		scheduler:   gocron.NewScheduler(time.UTC),
		redisClient: redisClient,
		instanceID:  instanceID,
		log:         log.NewHelper(logger),
		ctx:         ctx,
		cancel:      cancel,
	}
}

// generateInstanceID 生成实例ID
func generateInstanceID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// Start 启动分布式调度器
func (ds *DistributedScheduler) Start() {
	ds.log.Infof("启动分布式调度器，实例ID: %s", ds.instanceID)
	ds.scheduler.StartAsync()
}

// Stop 停止分布式调度器
func (ds *DistributedScheduler) Stop() {
	ds.log.Info("停止分布式调度器")
	ds.cancel()
	ds.scheduler.Stop()
}

// AddTask 添加分布式任务
func (ds *DistributedScheduler) AddTask(config ScheduleConfig) error {
	// 检查任务是否已存在
	if ds.TaskExists(config.Name) {
		ds.log.Infof("任务 %s 已存在，跳过添加", config.Name)
		return nil
	}

	// 创建任务并添加标签
	job := ds.scheduler.Every(config.Interval)

	// 添加任务名称作为默认标签
	job = job.Tag(config.Name)

	// 添加额外标签
	for _, tag := range config.Tags {
		job = job.Tag(tag)
	}

	_, err := job.Do(func() {
		ds.executeWithLock(config)
	})

	if err != nil {
		return fmt.Errorf("添加任务失败: %w", err)
	}

	// 在Redis中标记任务已添加
	taskStateKey := fmt.Sprintf("task_state:%s", config.Name)
	if err := ds.redisClient.SetEx(ds.ctx, taskStateKey, "active", 24*time.Hour).Err(); err != nil {
		ds.log.Warnf("设置任务状态失败: %v", err)
	}

	ds.log.Infof("添加分布式任务: %s, 间隔: %v, 标签: %v", config.Name, config.Interval, append([]string{config.Name}, config.Tags...))
	return nil
}

// TaskExists 检查任务是否已存在
func (ds *DistributedScheduler) TaskExists(taskName string) bool {
	// 检查Redis中是否有该任务的锁记录或执行记录
	lockKey := fmt.Sprintf("task_lock:%s", taskName)
	exists, err := ds.redisClient.Exists(ds.ctx, lockKey).Result()
	if err != nil {
		ds.log.Warnf("检查任务存在性失败: %v", err)
		return false
	}

	// 如果Redis中有锁记录，说明任务可能正在执行或已存在
	if exists > 0 {
		return true
	}

	// 检查调度器中是否有该任务
	// 注意：gocron库没有直接的方法检查任务是否存在，这里使用Redis作为状态存储
	taskStateKey := fmt.Sprintf("task_state:%s", taskName)
	stateExists, err := ds.redisClient.Exists(ds.ctx, taskStateKey).Result()
	if err != nil {
		ds.log.Warnf("检查任务状态失败: %v", err)
		return false
	}

	return stateExists > 0
}

// executeWithLock 使用分布式锁执行任务
func (ds *DistributedScheduler) executeWithLock(config ScheduleConfig) {
	lockKey := fmt.Sprintf("task_lock:%s", config.Name)
	lockValue := fmt.Sprintf("%s:%d", ds.instanceID, time.Now().Unix())

	// 尝试获取分布式锁
	acquired, err := ds.acquireLock(lockKey, lockValue, config.LockTimeout)
	if err != nil {
		ds.log.Errorf("获取分布式锁失败: %v", err)
		return
	}

	if !acquired {
		ds.log.Debugf("任务 %s 正在其他实例执行，跳过", config.Name)
		return
	}

	// 确保释放锁
	defer func() {
		if err := ds.releaseLock(lockKey, lockValue); err != nil {
			ds.log.Errorf("释放分布式锁失败: %v", err)
		}
	}()

	// 执行任务
	ds.log.Infof("实例 %s 开始执行任务: %s", ds.instanceID, config.Name)
	startTime := time.Now()

	if err := config.TaskFunc(); err != nil {
		ds.log.Errorf("任务执行失败: %s, 错误: %v", config.Name, err)
	} else {
		ds.log.Infof("任务执行成功: %s, 耗时: %v", config.Name, time.Since(startTime))
	}
}

// acquireLock 获取分布式锁
func (ds *DistributedScheduler) acquireLock(lockKey, lockValue string, timeout time.Duration) (bool, error) {
	// 使用 SET key value EX timeout NX 命令实现分布式锁
	result, err := ds.redisClient.SetNX(ds.ctx, lockKey, lockValue, timeout).Result()
	if err != nil {
		return false, fmt.Errorf("Redis SetNX 失败: %w", err)
	}

	return result, nil
}

// releaseLock 释放分布式锁（使用 Lua 脚本确保原子性）
func (ds *DistributedScheduler) releaseLock(lockKey, lockValue string) error {
	// Lua 脚本确保只有锁的持有者才能释放锁
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`

	result, err := ds.redisClient.Eval(ds.ctx, luaScript, []string{lockKey}, lockValue).Result()
	if err != nil {
		return fmt.Errorf("释放锁失败: %w", err)
	}

	if result.(int64) == 0 {
		ds.log.Warnf("锁已被其他实例释放或过期: %s", lockKey)
	}

	return nil
}

// IsRunning 检查调度器是否运行中
func (ds *DistributedScheduler) IsRunning() bool {
	return ds.scheduler.IsRunning()
}

// GetJobs 获取所有任务
func (ds *DistributedScheduler) GetJobs() []*gocron.Job {
	return ds.scheduler.Jobs()
}

// GetInstanceID 获取实例ID
func (ds *DistributedScheduler) GetInstanceID() string {
	return ds.instanceID
}

// RemoveTask 移除分布式任务
func (ds *DistributedScheduler) RemoveTask(taskName string) error {
	// 检查任务是否存在
	if !ds.TaskExists(taskName) {
		ds.log.Infof("任务 %s 不存在，跳过移除", taskName)
		return nil
	}

	// 从调度器中移除任务
	if err := ds.scheduler.RemoveByTag(taskName); err != nil {
		ds.log.Errorf("从调度器移除任务失败: %v", err)
		return fmt.Errorf("从调度器移除任务失败: %w", err)
	}

	// 清理Redis中的任务状态
	taskStateKey := fmt.Sprintf("task_state:%s", taskName)
	lockKey := fmt.Sprintf("task_lock:%s", taskName)

	pipe := ds.redisClient.Pipeline()
	pipe.Del(ds.ctx, taskStateKey)
	pipe.Del(ds.ctx, lockKey)
	_, err := pipe.Exec(ds.ctx)
	if err != nil {
		ds.log.Warnf("清理Redis任务状态失败: %v", err)
		return fmt.Errorf("清理Redis任务状态失败: %w", err)
	}

	ds.log.Infof("成功移除分布式任务: %s", taskName)
	return nil
}

// RemoveTaskByTags 通过标签移除分布式任务
func (ds *DistributedScheduler) RemoveTaskByTags(tags ...string) error {
	if len(tags) == 0 {
		return fmt.Errorf("标签不能为空")
	}

	// 从调度器中移除任务
	if err := ds.scheduler.RemoveByTags(tags...); err != nil {
		ds.log.Errorf("通过标签移除任务失败: %v", err)
		return fmt.Errorf("通过标签移除任务失败: %w", err)
	}

	// 注意：这里不清理Redis状态，因为我们不知道具体的任务名称
	// 如果需要清理，可以先获取任务列表，然后逐个清理

	ds.log.Infof("成功通过标签移除任务: %v", tags)
	return nil
}

// =============================================================================
// 推送任务辅助方法
// =============================================================================

// GeneratePushTaskTag 生成推送任务标签
func GeneratePushTaskTag(taskType string, userID, relatedID int32) string {
	return fmt.Sprintf("%s_%d_%d", taskType, userID, relatedID)
}

// CreatePushTask 创建推送任务
func (ds *DistributedScheduler) CreatePushTask(taskType string, userID, relatedID int32, interval time.Duration, taskFunc TaskFunc) error {
	taskTag := GeneratePushTaskTag(taskType, userID, relatedID)

	config := ScheduleConfig{
		Name:        taskTag,
		Interval:    interval,
		LockTimeout: 30 * time.Second,
		TaskFunc:    taskFunc,
		Tags:        []string{fmt.Sprintf("user_%d", userID), fmt.Sprintf("type_%s", taskType)},
	}

	return ds.AddTask(config)
}

// RemovePushTask 移除推送任务
func (ds *DistributedScheduler) RemovePushTask(taskType string, userID, relatedID int32) error {
	taskTag := GeneratePushTaskTag(taskType, userID, relatedID)
	return ds.RemoveTask(taskTag)
}

// RemoveUserPushTasks 移除用户的所有推送任务
func (ds *DistributedScheduler) RemoveUserPushTasks(userID int32) error {
	userTag := fmt.Sprintf("user_%d", userID)
	return ds.RemoveTaskByTags(userTag)
}

// =============================================================================
// 兼容性：保留旧的全局调度器用于其他任务
// =============================================================================

var (
	Scheduler *gocron.Scheduler
	ctx       = context.Background()
)

type GoCornX struct {
	init bool
}

func (g *GoCornX) Init() {
	if !g.init {
		g.new()
		g.init = true
	}
}

func (g *GoCornX) new() {
	if g.init {
		return
	}
	Scheduler = gocron.NewScheduler(time.UTC)

	go Scheduler.StartBlocking()
}

func (g *GoCornX) Close() {
	Scheduler.StopBlockingChan()
	return
}
