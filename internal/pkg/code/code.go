package code

import "github.com/nicksnyder/go-i18n/v2/i18n"

var (
	ErrInternalMsg = &i18n.Message{
		Description: "Internal Server Error",
		ID:          "Internal Server Error",
		One:         "Internal Server Error",
		Other:       "Internal Server Error",
	}
	ErrParamsMsg = &i18n.Message{
		Description: "Params Error",
		ID:          "Params Error",
		One:         "Params Error",
		Other:       "Params Error",
	}
)

// user errors
var (
	// ErrUserExistMsg is user is exited.
	ErrUserExistMsg = &i18n.Message{
		Description: "user_name is exited",
		ID:          "user_name is exited",
		One:         "{{.Name}} is exited",
		Other:       "{{.Name}} is exited",
	}
	ErrThirdPartyTokenWrongMsg = &i18n.Message{
		Description: "third party token is wrong",
		ID:          "third party token is wrong",
		One:         "third party token is wrong",
		Other:       "third party token is wrong",
	}
	ErrVerifyCodeExpiredMsg = &i18n.Message{
		Description: "verify_code is expired",
		ID:          "verify_code is expired",
		One:         "verify_code is expired",
		Other:       "verify_code is expired",
	}
	ErrVerifyCodeWrongMsg = &i18n.Message{
		Description: "verify_code is wrong",
		ID:          "verify_code is wrong",
		One:         "verify_code is wrong",
		Other:       "verify_code is wrong",
	}
	ErrVerifyCodeLimitedMsg = &i18n.Message{
		Description: "verify_code is limited",
		ID:          "verify_code is limited",
		One:         "verify_code is limited",
		Other:       "verify_code is limited",
	}
	ErrPhoneRegisteredMsg = &i18n.Message{
		Description: "phone is registered",
		ID:          "phone is registered",
		One:         "phone is registered",
		Other:       "phone is registered",
	}
	ErrPasswordNotSameMsg = &i18n.Message{
		Description: "password is not same",
		ID:          "password is not same",
		One:         "password is not same",
		Other:       "password is not same",
	}
	ErrPasswordNotAllowMsg = &i18n.Message{
		Description: "password is not allow",
		ID:          "password is not allow",
		One:         "password is not allow",
		Other:       "password is not allow",
	}
	ErrUserOrPasswordWrongMsg = &i18n.Message{
		Description: "user or password is wrong",
		ID:          "user or password is wrong",
		One:         "user or password is wrong",
		Other:       "user or password is wrong",
	}
	ErrPasswordWrongMsg = &i18n.Message{
		Description: "password is wrong",
		ID:          "password is wrong",
		One:         "password is wrong",
		Other:       "password is wrong",
	}
	ErrUserCanNotUnfollowSelfMsg = &i18n.Message{
		Description: "user can not unfollow self",
		ID:          "user can not unfollow self",
		One:         "user can not unfollow self",
		Other:       "user can not unfollow self",
	}
	ErrUsedPhoneMsg = &i18n.Message{
		Description: "phone is used",
		ID:          "phone is used",
		One:         "phone is used",
		Other:       "phone is used",
	}
	ErrUsedEmailMsg = &i18n.Message{
		Description: "email is used",
		ID:          "email is used",
		One:         "email is used",
		Other:       "email is used",
	}
	ErrUserNotVipMsg = &i18n.Message{
		Description: "user is not vip",
		ID:          "user is not vip",
		One:         "user is not vip",
		Other:       "user is not vip",
	}
	ErrInvalidUserMsg = &i18n.Message{
		Description: "invalid user",
		ID:          "invalid user",
		One:         "invalid user",
		Other:       "invalid user",
	}
)

// user_habit errors
var (
	ErrPauseUserHabitMsg = &i18n.Message{
		Description: "pause user habit error",
		ID:          "pause user habit error",
		One:         "pause user habit error",
		Other:       "pause user habit error",
	}
	ErrRecoverUserHabitMsg = &i18n.Message{
		Description: "recover user habit error",
		ID:          "recover user habit error",
		One:         "recover user habit error",
		Other:       "recover user habit error",
	}
	ErrReckonUserHabitMsg = &i18n.Message{
		Description: "reckon user habit error",
		ID:          "reckon user habit error",
		One:         "reckon user habit error",
		Other:       "reckon user habit error",
	}
)

// user_order errors
var (
	ErrInvalidPaymentMsg = &i18n.Message{
		Description: "invalid payment error",
		ID:          "invalid payment error",
		One:         "invalid payment error",
		Other:       "invalid payment error",
	}
	ErrCreateOrderMsg = &i18n.Message{
		Description: "create order error",
		ID:          "create order error",
		One:         "create order error",
		Other:       "create order error",
	}
)
