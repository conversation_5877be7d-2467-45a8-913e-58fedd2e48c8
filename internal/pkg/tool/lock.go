package tool

import (
	"context"
	"errors"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"time"
)

type RedisLock struct {
	client *redis.Client
	token  string
}

func NewRedisLock(client *redis.Client) *RedisLock {
	return &RedisLock{
		client: client,
	}
}

func (rl *RedisLock) Lock(ctx context.Context, key string, ttl, timeout, spinTime time.Duration, isDirectReturn bool) error {
	now := time.Now()
	rl.token = uuid.New().String()
	for {
		flag, err := rl.client.SetNX(ctx, key, rl.token, ttl).Result()
		if err != nil {
			return err
		}
		if flag {
			return nil
		}
		if isDirectReturn {
			return errors.New("locking")
		}
		if timeout > 0 && time.Since(now) > timeout {
			return errors.New("lock Timeout")
		}
		time.Sleep(spinTime)
	}
}

func (rl *RedisLock) Unlock(ctx context.Context, key string) error {
	token, err := rl.client.Get(ctx, key).Result()
	if err != nil {
		return err
	}
	if rl.token != token {
		return nil
	}
	_, err = rl.client.Del(ctx, key).Result()
	return err
}
