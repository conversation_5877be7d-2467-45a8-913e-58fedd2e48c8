package tool

import (
	"fmt"
	"sync"
	"time"
)

// OrderIDGenerator 结构体
type OrderIDGenerator struct {
	machineID    int64
	sequence     int64
	lastTime     int64
	sequenceMask int64
	lock         sync.Mutex
}

// NewOrderIDGenerator 创建新的订单号生成器
func NewOrderIDGenerator(machineID int64) *OrderIDGenerator {
	return &OrderIDGenerator{
		machineID:    machineID,
		sequence:     0,
		lastTime:     -1,
		sequenceMask: 999, // 支持每毫秒最多生成 1000 个订单号
	}
}

// GenerateOrderID 生成订单号
func (g *OrderIDGenerator) GenerateOrderID() string {
	g.lock.Lock()
	defer g.lock.Unlock()

	now := time.Now().UnixNano() / int64(time.Millisecond)
	if now == g.lastTime {
		g.sequence = (g.sequence + 1) & g.sequenceMask
		if g.sequence == 0 {
			// 如果当前毫秒内的序列号用完，则等待下一毫秒
			for now <= g.lastTime {
				now = time.Now().UnixNano() / int64(time.Millisecond)
			}
		}
	} else {
		g.sequence = 0
	}

	g.lastTime = now
	orderID := fmt.Sprintf("%d%03d%03d", now, g.machineID, g.sequence)
	return orderID
}
