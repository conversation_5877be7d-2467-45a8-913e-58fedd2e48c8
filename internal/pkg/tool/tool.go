package tool

import (
	"crypto/rand"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/mssola/useragent"
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"golang.org/x/exp/constraints"
	mathRand "math/rand"
	"os"
	"strconv"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"
)

// GenerateUserName 生成用户名，规则：用户+手机号后4位（邮箱后4位）+时间戳
func GenerateUserName(phone string, email string) string {
	if phone != "" {
		return fmt.Sprintf("用户%v_%d", phone[len(phone)-4:], time.Now().Unix())
	}
	emailPrefix := strings.Split(email, "@")[0]
	if len(emailPrefix) <= 4 {
		return fmt.Sprintf("用户%v_%d", emailPrefix, time.Now().Unix())
	}
	return fmt.Sprintf("用户%v_%d", emailPrefix[len(emailPrefix)-4:], time.Now().Unix())
}

func ParseUserAgent(userAgent string) enums.UserTokenPlatformType {
	ua := useragent.New(userAgent)
	deviceOS := ua.OS()
	platform := enums.UserTokenPlatformUnknown
	if strings.Contains(strings.ToLower(deviceOS), "android") {
		platform = enums.UserTokenPlatformAndroid
	} else if strings.Contains(strings.ToLower(deviceOS), "iphone") {
		platform = enums.UserTokenPlatformIOS
	}

	return platform
}

// Contains checks if a slice contains a value.
func Contains[T comparable](s []T, v T) bool {
	for _, item := range s {
		if item == v {
			return true
		}
	}

	return false
}

// RemoveElement 从切片中移除指定元素（优化版本）
func RemoveElement[T comparable](slice []T, element T) []T {
	// 记录移除元素的索引
	removeIndex := -1

	// 查找要移除的元素索引
	for i, v := range slice {
		if v == element {
			removeIndex = i
			break
		}
	}

	// 如果找到要移除的元素
	if removeIndex != -1 {
		// 直接在原始切片上进行修改，避免创建新的切片
		copy(slice[removeIndex:], slice[removeIndex+1:])
		slice = slice[:len(slice)-1]
	}

	return slice
}

// Unique returns a slice with duplicate values removed.
func Unique[T comparable](s []T) []T {
	unique := make(map[T]struct{})
	var result []T
	for _, item := range s {
		if _, ok := unique[item]; !ok {
			result = append(result, item)
			unique[item] = struct{}{}
		}
	}

	return result
}

// GetZeroTimestampByDate 获取某天的零点时间
func GetZeroTimestampByDate(date time.Time) time.Time {
	return time.Date(date.Year(), date.Month(), date.Day(), 0, 0, 0, 0, date.Location())
}

// GetWeekStartAndEndTime 获取每周的开始时间和结束时间，精确到天
func GetWeekStartAndEndTime(now time.Time) (time.Time, time.Time) {
	// 获取本周开始时间
	weekStart := now.AddDate(0, 0, -int(now.Weekday()+6)%7)
	weekStart = time.Date(weekStart.Year(), weekStart.Month(), weekStart.Day(), 0, 0, 0, 0, now.Location())

	// 获取本周结束时间
	weekEnd := weekStart.AddDate(0, 0, 6)
	weekEnd = time.Date(weekEnd.Year(), weekEnd.Month(), weekEnd.Day(), 0, 0, 0, 0, now.Location())

	return weekStart, weekEnd
}

// GetMonthStartAndEndTime 获取每月的开始时间和结束时间，精确到天
func GetMonthStartAndEndTime(now time.Time) (time.Time, time.Time) {
	// 获取本月开始时间
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// 获取本月结束时间
	monthEnd := monthStart.AddDate(0, 1, -1)

	return monthStart, monthEnd
}

// GetUTCByHourTime 获取用户时区的某个小时的 UTC 时间
func GetUTCByHourTime(hourStr string, timezone string) (time.Time, error) {
	hourUserTime, err := time.Parse(time.TimeOnly, hourStr)
	if err != nil {
		return hourUserTime, err
	}
	hour, _ := strconv.Atoi(timezone[1:3])
	minute, _ := strconv.Atoi(timezone[4:])
	offset := hour*60*60 + minute*60*60/100
	if timezone[0:1] == "-" {
		offset = -offset
	}
	loc := time.FixedZone("CST", offset)
	userTime := time.Date(0, 0, 0, hourUserTime.Hour(), hourUserTime.Minute(), 0, 0, loc)

	return userTime.UTC(), nil
}

// ConvertLocalToUTC 将本地时间字符串转换为 UTC 时间
func ConvertLocalToUTC(localTimeStr string) (time.Time, error) {
	// 解析本地时间字符串
	localTime, err := time.ParseInLocation("2006-01-02 15:04:05", localTimeStr, time.Local)
	if err != nil {
		return time.Time{}, fmt.Errorf("时间解析错误: %v", err)
	}

	// 转换为 UTC 时间
	utcTime := localTime.UTC()

	// 返回 UTC 时间
	return utcTime, nil
}

func GenerateVerifyCode() string {
	// 初始化随机数种子
	mathRand.New(mathRand.NewSource(time.Now().UnixNano()))

	return fmt.Sprintf("%v", mathRand.Intn(900000)+100000)
}

// GenerateUID 生成用户唯一标识符
// 格式：8-12位字母数字组合，类似常见APP的UID格式
func GenerateUID() string {
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 10 // 生成10位UID

	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		// 如果加密随机数生成失败，使用时间戳作为种子的伪随机数
		mathRand.New(mathRand.NewSource(time.Now().UnixNano()))
		for i := range b {
			b[i] = charset[mathRand.Intn(len(charset))]
		}
		return string(b)
	}

	for i := range b {
		b[i] = charset[b[i]%byte(len(charset))]
	}

	return string(b)
}

func MaskString(input string) string {
	// 如果字符串为空或只有一个字符，直接返回
	runes := []rune(input)
	if len(runes) <= 1 {
		return input
	}

	// 检测是否包含中文字符
	containsChinese := false
	for _, r := range runes {
		if unicode.Is(unicode.Han, r) {
			containsChinese = true
			break
		}
	}

	// 包含中文，使用中文处理逻辑
	if containsChinese {
		return string(runes[0]) + strings.Repeat("*", len(runes)-1)
	}

	// 英文处理逻辑：拆分单词，保留每个单词首字母
	words := strings.Fields(input)
	result := ""

	for i, word := range words {
		if len(word) > 0 {
			// 保留首字母，其余用星号替换
			result += string([]rune(word)[0]) + strings.Repeat("*", len([]rune(word))-1)

			// 除了最后一个单词外，添加空格
			if i < len(words)-1 {
				result += " "
			}
		}
	}

	return result
}

func CalculatePercentage(smallDoneCount, smallAllCount int32) int32 {
	if smallAllCount == 0 {
		// 处理分母为零的情况，可以返回0或其他适当的值
		return 0
	}

	// 使用 float64 进行安全的除法计算，并转换为 int32
	percentage := float64(smallDoneCount) / float64(smallAllCount) * 100
	return int32(percentage)
}

// CompareSlices checks if two slices are equal using generics
func CompareSlices[T constraints.Ordered](slice1, slice2 []T) bool {
	// Check if the lengths are different
	if len(slice1) != len(slice2) {
		return false
	}

	// Compare each element
	for i := range slice1 {
		if slice1[i] != slice2[i] {
			return false
		}
	}

	return true
}

// FormatToMinutesSeconds 将秒转换为分钟:秒的格式
func FormatToMinutesSeconds(seconds int) string {
	minutes := seconds / 60
	secs := seconds % 60
	return fmt.Sprintf("%d:%02d", minutes, secs)
}

// ConvertTimestampToHourMinute 将时间戳转换为 "小时:分钟" 格式
func ConvertTimestampToHourMinute(timestamp int64) string {
	t := time.Unix(timestamp, 0)
	return t.Format("15:04")
}

func DaysInWeekOfMonth(endDate time.Time, startDate time.Time, isWeek bool) int32 {
	count := 0

	if !isWeek {
		count += endDate.Day()
		if endDate.Month() == startDate.Month() {
			count -= startDate.Day() + 1
		}
	} else {
		// 找到该日期所在周的周一
		weekday := int(endDate.Weekday())
		if weekday == 0 {
			weekday = 7 // 将星期天视为一周的第七天
		}
		// 计算周一的日期
		monday := endDate.AddDate(0, 0, -weekday+1)

		// 遍历一周的天数
		for i := 0; i < 7; i++ {
			day := monday.AddDate(0, 0, i)
			if day.Before(startDate) || day.After(endDate) {
				continue
			}
			count++
			// 检查该天是否在同一个月中
			//if day.Month() == endDate.Month() {
			//	count++
			//}
		}
	}

	return int32(count)
}

func TruncateString(name string, length int) string {
	if utf8.RuneCountInString(name) > length {
		runes := []rune(name)
		return string(runes[:length])
	}

	return name
}

// ReverseSlice 使用泛型反转任意类型的切片
func ReverseSlice[T comparable](slice []T) []T {
	for i, j := 0, len(slice)-1; i < j; i, j = i+1, j-1 {
		slice[i], slice[j] = slice[j], slice[i]
	}
	return slice
}

// ParseTimezone 解析时区，优先使用时区名称，然后再使用偏移量
func ParseTimezone(timezoneName, timezoneOffset string) (*time.Location, error) {
	var loc *time.Location
	var err error

	// 优先使用时区名称
	if timezoneName != "" {
		loc, err = time.LoadLocation(timezoneName)
		if err == nil {
			return loc, nil
		}
		// 如果时区名称无效且没有偏移量，返回错误
		if timezoneOffset == "" {
			return nil, fmt.Errorf("无效的时区名称: %v", err)
		}
	}

	// 如果没有时区名称或时区名称无效，使用偏移量
	if timezoneOffset != "" {
		// 解析偏移量，格式如 "+08:00"
		hours, _ := strconv.Atoi(timezoneOffset[1:3])
		minutes, _ := strconv.Atoi(timezoneOffset[4:6])

		// 根据符号调整正负
		if timezoneOffset[0] == '-' {
			hours = -hours
			minutes = -minutes
		}

		// 创建固定偏移的时区，使用偏移量字符串作为名称
		return time.FixedZone(timezoneOffset, hours*3600+minutes*60), nil
	}

	// 如果没有有效的时区信息，使用 UTC
	return time.UTC, nil
}

// IsAfterTargetNextDay 判断当前时间是否过了指定日期的下一天
// 支持时区处理，优先使用时区名称，然后再使用偏移量
func IsAfterTargetNextDay(targetStr, timezoneName, timezoneOffset string) bool {
	// 解析时区
	loc, err := ParseTimezone(timezoneName, timezoneOffset)
	if err != nil {
		return false
	}

	// 获取当前时间（在指定时区中）
	now := time.Now().In(loc)

	// 解析日期字符串，设置为目标日期的00:00:00
	targetTimeInLoc, err := time.ParseInLocation("2006-01-02", targetStr, loc)
	if err != nil {
		log.Errorf("time.ParseInLocation error: %v", err)
		return false
	}

	// 计算目标时间的下一天
	targetNextDay := time.Date(
		targetTimeInLoc.Year(),
		targetTimeInLoc.Month(),
		targetTimeInLoc.Day()+1,
		0, 0, 0, 0,
		loc,
	)

	// 判断当前时间是否过了目标时间的下一天
	return now.After(targetNextDay)
}

// GetSecondsUntilMidnight 计算到用户所在时区零点的剩余秒数
func GetSecondsUntilMidnight(timezoneName, timezoneOffset string) (int64, error) {
	// 解析时区
	loc, err := ParseTimezone(timezoneName, timezoneOffset)
	if err != nil {
		return 0, fmt.Errorf("无效的时区信息: %v", err)
	}

	// 获取用户时区的当前时间
	now := time.Now().In(loc)

	// 计算下一个零点
	nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, loc)

	// 计算剩余秒数
	return nextMidnight.Unix() - now.Unix(), nil
}

// GetUserLocalTime 获取用户所在时区的当前时间
func GetUserLocalTime(timezoneName, timezoneOffset string) (time.Time, error) {
	var loc *time.Location
	var err error

	// 优先使用时区名称
	if timezoneName != "" {
		loc, err = time.LoadLocation(timezoneName)
		if err != nil {
			// 时区名称无效，尝试使用偏移量
			if timezoneOffset == "" {
				return time.Time{}, fmt.Errorf("无效的时区信息: %v", err)
			}
		}
	}

	// 如果没有时区名称或时区名称无效，使用偏移量
	if loc == nil && timezoneOffset != "" {
		// 解析偏移量，格式如 "+08:00"
		hours, _ := strconv.Atoi(timezoneOffset[1:3])
		minutes, _ := strconv.Atoi(timezoneOffset[4:6])

		// 根据符号调整正负
		if timezoneOffset[0] == '-' {
			hours = -hours
			minutes = -minutes
		}

		// 创建固定偏移的时区
		loc = time.FixedZone(timezoneOffset, hours*3600+minutes*60)
	}

	// 如果没有有效的时区信息，使用 UTC
	if loc == nil {
		loc = time.UTC
	}

	// 返回用户时区的当前时间
	return time.Now().In(loc), nil
}

// IsUserLocalMidnight 判断用户所在时区是否是零点（允许一定误差）
func IsUserLocalMidnight(timezoneName, timezoneOffset string, allowedErrorSeconds int) (bool, error) {
	userTime, err := GetUserLocalTime(timezoneName, timezoneOffset)
	if err != nil {
		return false, err
	}

	// 获取用户时区的时间
	hour := userTime.Hour()
	minute := userTime.Minute()
	second := userTime.Second()

	// 计算距离零点的秒数
	secondsFromMidnight := hour*3600 + minute*60 + second

	// 如果是零点附近（考虑允许的误差）
	if secondsFromMidnight <= allowedErrorSeconds ||
		(24*3600-secondsFromMidnight) <= allowedErrorSeconds {
		return true, nil
	}

	return false, nil
}

// SplitString 分割字符串
func SplitString(s, sep string) []string {
	return strings.Split(s, sep)
}

// StringToInt32 将字符串转换为int32
func StringToInt32(s string) int32 {
	if s == "" {
		return 0
	}
	num, err := strconv.Atoi(s)
	if err != nil {
		return 0
	}
	return int32(num)
}

// GetProxyURL 获取代理URL
func GetProxyURL() string {
	// 优先从环境变量读取
	if proxy := os.Getenv("FCM_PROXY_URL"); proxy != "" {
		return proxy
	}

	if proxy := os.Getenv("HTTPS_PROXY"); proxy != "" {
		return proxy
	}

	if proxy := os.Getenv("HTTP_PROXY"); proxy != "" {
		return proxy
	}

	// 默认Shadowsocks代理配置
	return "socks5://127.0.0.1:1086"
}
