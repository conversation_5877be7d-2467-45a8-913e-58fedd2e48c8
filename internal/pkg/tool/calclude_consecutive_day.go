package tool

import (
	"github.com/wlnil/life-log-be/internal/pkg/enums"
	"time"
)

type PunchConfig struct {
	PunchCycleType enums.PunchCycleType
	PunchCycle     []int32
}

type HabitTracker struct {
	timestamps []int64 // 打卡的时间戳
}

func NewHabitTracker(timestamps []int64) *HabitTracker {
	return &HabitTracker{
		timestamps: timestamps,
	}
}

func (ht *HabitTracker) CalculateMaxConsecutivePeriods(data []*PunchConfig, startDate time.Time) (maxConsecutive int, punchStartDate, punchEndDate int64) {
	if len(ht.timestamps) == 0 {
		return
	}
	currentConsecutive := 0

	periodStart := startDate
	var periodEnd time.Time
	var requiredDays int32
	var tsIndex int

	currentStartDate := ht.timestamps[tsIndex]
	currentEndDate := ht.timestamps[tsIndex]
	for _, item := range data {
		switch item.PunchCycleType {
		case enums.PunchCycleWeek:
			_, periodEnd = GetWeekStartAndEndTime(periodStart)
			periodEnd = periodEnd.AddDate(0, 0, 1)
			requiredDays = item.PunchCycle[0]
		case enums.PunchCycleMonth:
			_, periodEnd = GetMonthStartAndEndTime(periodStart)
			periodEnd = periodEnd.AddDate(0, 0, 1)
			requiredDays = item.PunchCycle[0]
		case enums.PunchCycleFix:
			_, periodEnd = GetWeekStartAndEndTime(periodStart)
			periodEnd = periodEnd.AddDate(0, 0, 1)
			// 处理未设置打卡的情况
			if len(item.PunchCycle) == 0 {
				item.PunchCycle = []int32{0}
			} else if item.PunchCycle[0] == 0 {
				item.PunchCycle = append(item.PunchCycle[1:], 7)
			}
		}

		var actualDays int32
		weekdayIndex := 0
		dealFixWeek := false
		for tsIndex < len(ht.timestamps) {
			// 将获取到的日期设置为当前结束日期
			currentEndDate = ht.timestamps[tsIndex]

			punchDate := time.Unix(ht.timestamps[tsIndex], 0)

			if punchDate.After(periodEnd) || punchDate.Equal(periodEnd) {
				// 如果跳出循环时，还没有遍历到最后一个星期几，那么需要重置当前连续天数
				if item.PunchCycleType == enums.PunchCycleFix && (weekdayIndex != len(item.PunchCycle)-1 || !dealFixWeek) {
					currentConsecutive = 0
					currentStartDate = currentEndDate
				}
				periodStart = periodEnd
				break
			}

			if item.PunchCycleType == enums.PunchCycleFix {
				for weekdayIndex < len(item.PunchCycle) {
					weekDay := int32(punchDate.Weekday())
					if weekDay == 0 {
						weekDay = 7
					}
					if weekDay > item.PunchCycle[weekdayIndex] {
						if currentConsecutive > maxConsecutive {
							maxConsecutive = currentConsecutive
							punchStartDate = currentStartDate
							punchEndDate = currentEndDate
						}
						if item.PunchCycle[weekdayIndex] == 0 {
							currentConsecutive += 1
							break
						}
						if weekdayIndex == len(item.PunchCycle) {
							weekdayIndex = len(item.PunchCycle) - 1
							currentConsecutive += 1
							dealFixWeek = true
						} else {
							// 重置当前连续天数
							currentConsecutive = 0
							weekdayIndex++
							currentStartDate = currentEndDate
							continue
						}
					} else if weekDay == item.PunchCycle[weekdayIndex] {
						currentConsecutive += 1
						weekdayIndex++
						if weekdayIndex == len(item.PunchCycle) {
							weekdayIndex = len(item.PunchCycle) - 1
							dealFixWeek = true
						}
					} else {
						currentConsecutive += 1
					}
					break
				}

				if currentConsecutive > maxConsecutive {
					maxConsecutive = currentConsecutive
					punchStartDate = currentStartDate
					punchEndDate = currentEndDate
				}
			} else if item.PunchCycleType == enums.PunchCycleWeek || item.PunchCycleType == enums.PunchCycleMonth {
				actualDays++
				currentConsecutive++
				if currentConsecutive > maxConsecutive {
					maxConsecutive = currentConsecutive
					punchStartDate = currentStartDate
					punchEndDate = currentEndDate
				}
			}
			tsIndex += 1
		}

		if item.PunchCycleType == enums.PunchCycleWeek || item.PunchCycleType == enums.PunchCycleMonth {
			if actualDays < requiredDays {
				// 重置当前连续天数
				currentConsecutive = 0
				currentStartDate = currentEndDate
			}
		}

		if tsIndex == len(ht.timestamps) {
			break
		}
	}

	return
}
