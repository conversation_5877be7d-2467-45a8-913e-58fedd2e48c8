package tool

import (
	"fmt"
	"math/rand"
	"sync"
	"time"
)

const KMEANS_K = 3
const KMEANS_MAX_ITERATIONS = 100
const WORKER_POOL_SIZE = 100 // 工作池大小

// AnalyzeCheckInTimes 分析打卡时间，返回最早时间、最晚时间和加权平均时间
func AnalyzeCheckInTimes(timestamps []int64) (string, string, string, error) {
	if len(timestamps) == 0 {
		return "", "", "", fmt.Errorf("empty timestamp list")
	}

	// 转换时间戳为秒数
	seconds := ConvertTimestampsToSeconds(timestamps)

	// 创建一个新的随机数生成器
	r := rand.New(rand.NewSource(42))

	// 执行 K-means 聚类
	centroids, clusters, err := KMeans(seconds, KMEANS_K, KMEANS_MAX_ITERATIONS, r)
	if err != nil {
		return "", "", "", fmt.Errorf("error in clustering: %v", err)
	}

	// 计算最早、最晚和加权平均时间
	earliest, latest, weightedAverage := CalculateTimeStatsWithWeightedAverage(seconds, centroids, clusters)

	return earliest, latest, weightedAverage, nil
}

// ConvertTimestampsToSeconds 将时间戳转换为一天中的秒数
func ConvertTimestampsToSeconds(timestamps []int64) []float64 {
	seconds := make([]float64, len(timestamps))
	for i, ts := range timestamps {
		t := time.Unix(ts, 0)
		hour, minute, sec := t.Clock()
		seconds[i] = float64(hour*3600 + minute*60 + sec)
	}
	return seconds
}

// KMeans 聚类算法的并行实现
func KMeans(data []float64, k int, maxIterations int, r *rand.Rand) ([]float64, [][]float64, error) {
	centroids := make([]float64, k)
	for i := 0; i < k; i++ {
		centroids[i] = data[r.Intn(len(data))]
	}

	var clusters [][]float64
	for iter := 0; iter < maxIterations; iter++ {
		clusters = make([][]float64, k)
		var mu sync.Mutex
		var wg sync.WaitGroup
		tasks := make(chan float64, len(data))

		// 启动工作池
		for i := 0; i < WORKER_POOL_SIZE; i++ {
			go func() {
				for point := range tasks {
					closest := 0
					minDist := abs(point - centroids[0])
					for i := 1; i < k; i++ {
						dist := abs(point - centroids[i])
						if dist < minDist {
							closest = i
							minDist = dist
						}
					}

					mu.Lock()
					clusters[closest] = append(clusters[closest], point)
					mu.Unlock()
					wg.Done()
				}
			}()
		}

		// 分配任务
		for _, point := range data {
			wg.Add(1)
			tasks <- point
		}
		close(tasks)
		wg.Wait()

		for i := 0; i < k; i++ {
			if len(clusters[i]) == 0 {
				continue
			}
			sum := 0.0
			for _, point := range clusters[i] {
				sum += point
			}
			centroids[i] = sum / float64(len(clusters[i]))
		}
	}

	return centroids, clusters, nil
}

// CalculateTimeStatsWithWeightedAverage 计算最早、最晚和加权平均时间
func CalculateTimeStatsWithWeightedAverage(allData []float64, clusterCentroids []float64, clusters [][]float64) (string, string, string) {
	earliest := allData[0]
	latest := allData[0]

	for _, second := range allData {
		if second < earliest {
			earliest = second
		}
		if second > latest {
			latest = second
		}
	}

	// 使用加权平均计算主要簇的平均时间
	var weightedTotal float64
	var totalWeight int
	for i, centroid := range clusterCentroids {
		weight := len(clusters[i])
		weightedTotal += centroid * float64(weight)
		totalWeight += weight
	}
	weightedAverage := weightedTotal / float64(totalWeight)

	earliestTime := formatSecondsToTime(earliest)
	latestTime := formatSecondsToTime(latest)
	weightedAverageTime := formatSecondsToTime(weightedAverage)

	return earliestTime, latestTime, weightedAverageTime
}

// formatSecondsToTime 将秒数格式化为 "小时:分钟"
func formatSecondsToTime(second float64) string {
	hour := int(second) / 3600
	minute := (int(second) % 3600) / 60
	return fmt.Sprintf("%02d:%02d", hour, minute)
}

func abs(a float64) float64 {
	if a < 0 {
		return -a
	}
	return a
}
