// Package auth encrypt and compare password string.
package auth

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	jwtv4 "github.com/golang-jwt/jwt/v4"
	"github.com/spf13/cast"
	"golang.org/x/crypto/bcrypt"
	"strconv"
	"time"
)

var Secret *SecretInfo

type SecretInfo struct {
	Key      string `json:"key"`
	Issuer   string `json:"issuer"`
	ExpireAt int32  `json:"expire_at"`
}

// Encrypt encrypts the plain text with bcrypt.
func Encrypt(source string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(source), bcrypt.DefaultCost)
	return string(hashedBytes), err
}

// Compare compares the encrypted text with the plain text if it's the same.
func Compare(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// CheckPassword 检查密码强度
func CheckPassword(password string) bool {
	if len(password) < 8 || len(password) > 16 {
		return false
	}

	return true
}

type CustomerClaims struct {
	jwtv4.RegisteredClaims

	UserID string `json:"user_id"`
}

// GenerateToken generates the jwt token with userID.
func GenerateToken(userID int32, expireAt time.Time) (string, error) {
	jwtClaims := CustomerClaims{
		RegisteredClaims: jwtv4.RegisteredClaims{
			ExpiresAt: jwtv4.NewNumericDate(expireAt),
			Issuer:    Secret.Issuer,
		},
		UserID: fmt.Sprintf("%v", userID),
	}
	claims := jwtv4.NewWithClaims(jwtv4.SigningMethodHS256, jwtClaims)
	token, err := claims.SignedString([]byte(Secret.Key))
	if err != nil {
		return "", err
	}

	return token, nil
}

func GetPayloadFromCtx(ctx context.Context, partName string) (string, error) {
	if claims, ok := jwt.FromContext(ctx); ok {
		if m, ok := claims.(*jwtv4.MapClaims); ok {
			if v, ok := (*m)[partName].(string); ok {
				return v, nil
			}
		}
	}
	return "", errors.New("invalid token")
}

// GetUserIDFromCtx 从认证路由中获取 user_id
func GetUserIDFromCtx(ctx context.Context) int32 {
	userID, err := GetPayloadFromCtx(ctx, "user_id")
	if err != nil {
		return 0
	}
	res, err := strconv.Atoi(userID)
	if err != nil {
		return 0
	}

	return int32(res)
}

// IsTokenExpiringSoon 检查 JWT token 是否将在 7 天内过期
func IsTokenExpiringSoon(ctx context.Context) bool {
	if claims, ok := jwt.FromContext(ctx); ok {
		if m, ok := claims.(*jwtv4.MapClaims); ok {
			// 获取过期时间
			if exp, ok := (*m)["exp"].(float64); ok {
				expirationTime := time.Unix(int64(exp), 0)
				// 检查是否小于 7 天
				if time.Until(expirationTime) < 7*24*time.Hour {
					return true
				}
			}
		}
	}

	return false
}

// GetUserIDFromCtxByNoAuth 从未认证路由中获取 user_id
func GetUserIDFromCtxByNoAuth(ctx context.Context) int32 {
	var userID string
	var err error

	next := func(ctx context.Context, req interface{}) (interface{}, error) {
		userID, err = GetPayloadFromCtx(ctx, "user_id")
		return "", err
	}
	server := jwt.Server(func(token *jwtv4.Token) (interface{}, error) {
		return []byte(Secret.Key), nil
	}, jwt.WithSigningMethod(jwtv4.SigningMethodHS256), jwt.WithClaims(func() jwtv4.Claims {
		return &jwtv4.MapClaims{}
	}))(next)
	_, _ = server(ctx, "")

	return int32(cast.ToInt(userID))
}
