package localize

import (
	"context"
	"github.com/wlnil/life-log-be/internal/conf"

	"github.com/BurntSushi/toml"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
)

type localizerKey struct{}

var Localizer *i18n.Localizer

func I18N(c *conf.Data_LocalizePath) middleware.Middleware {
	bundle := i18n.NewBundle(language.Chinese)
	bundle.RegisterUnmarshalFunc("toml", toml.Unmarshal)
	bundle.MustLoadMessageFile(c.Zh)
	if c.En != "" {
		bundle.MustLoadMessageFile(c.En)
	}

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (reply interface{}, err error) {
			if tr, ok := transport.FromServerContext(ctx); ok {
				accept := tr.RequestHeader().Get("accept-language")
				Localizer = i18n.NewLocalizer(bundle, accept)
				ctx = context.WithValue(ctx, localizerKey{}, Localizer)
			}
			return handler(ctx, req)
		}
	}
}

func TranslateMsg(ctx context.Context, message *i18n.Message, templateData interface{}) string {
	localizer := ctx.Value(localizerKey{}).(*i18n.Localizer)
	msg, err := localizer.Localize(&i18n.LocalizeConfig{
		DefaultMessage: message,
		TemplateData:   templateData,
	})
	if err != nil {
		return message.Description
	}

	return msg
}
