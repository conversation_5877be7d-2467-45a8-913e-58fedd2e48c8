package enums

type UserHabitType int

const (
	UserHabitNormal UserHabitType = 1 // 一般习惯类型
	UserHabitSmall  UserHabitType = 2 // 微习惯类型
	UserHabitRecord UserHabitType = 3 // 记录类型
)

type UserHabitStatus int

const (
	UserHabitStatusNormal UserHabitStatus = 1 // 正常
	UserHabitStatusPause  UserHabitStatus = 2 // 暂停
	UserHabitStatusEnd    UserHabitStatus = 3 // 结束
)

type PunchCycleType int8

const (
	PunchCycleFix   PunchCycleType = 1 // 固定打卡
	PunchCycleWeek  PunchCycleType = 2 // 按周打卡
	PunchCycleMonth PunchCycleType = 3 // 按月打卡
)

type UserRelationStatusType int8

const (
	UserRelationStatusNormal UserRelationStatusType = 1 // 正常
	UserRelationStatusBlock  UserRelationStatusType = 2 // 屏蔽
	UserRelationStatusDelete UserRelationStatusType = 3 // 删除
)

type CronTaskStatusType int8

const (
	CronTaskStatusTypeProcessing CronTaskStatusType = 1 // 进行中
	CronTaskStatusTypePause      CronTaskStatusType = 2 // 暂停
	CronTaskStatusTypeEnd        CronTaskStatusType = 3 // 结束
	CronTaskStatusTypeSkip       CronTaskStatusType = 4 // 跳过
	CronTaskStatusTypeDelete     CronTaskStatusType = 5 // 删除
)

type HttpMethod string

const (
	HttpMethodGET  HttpMethod = "get"
	HttpMethodPOST HttpMethod = "post"
)

type MessageType string

const (
	MessageTypeSystem    MessageType = "system"     // 系统消息
	MessageTypeUserHabit MessageType = "user_habit" // 用户习惯
)

type UserFavoriteType string

const (
	UserFavoriteTypePlanetPost UserFavoriteType = "planet_post" // 星球帖子
)

type UserFavoriteStatus int8

const (
	UserFavoriteStatusNormal UserFavoriteStatus = 1 // 正常
	UserFavoriteStatusDelete UserFavoriteStatus = 2 // 删除
)

type PlanetPostStatus int8

const (
	PlanetPostStatusNormal    PlanetPostStatus = 1 // 正常
	PlanetPostStatusDelete    PlanetPostStatus = 2 // 删除
	PlanetPostStatusForbidden PlanetPostStatus = 3 // 禁用
)

type PlanetPostLabelType int8

const (
	PlanetPostLabelTypeLatest   PlanetPostLabelType = 0 // 最新
	PlanetPostLabelTypeHot      PlanetPostLabelType = 1 // 最热
	PlanetPostLabelTypeMy       PlanetPostLabelType = 2 // 我的
	PlanetPostLabelTypeFavorite PlanetPostLabelType = 3 // 收藏
	PlanetPostLabelTypeTop      PlanetPostLabelType = 4 // 置顶
)

type PostCommentStatus int8

const (
	PostCommentStatusNormal    PostCommentStatus = 1 // 正常
	PostCommentStatusDelete    PostCommentStatus = 2 // 删除
	PostCommentStatusForbidden PostCommentStatus = 3 // 封禁
)

type UserGenderType int8

const (
	UserGenderTypeMale   UserGenderType = 1 // 男
	UserGenderTypeFemale UserGenderType = 2 // 女
)

type UserStatus int8

const (
	UserStatusNormal    UserStatus = 1 // 正常
	UserStatusDelete    UserStatus = 2 // 删除
	UserStatusForbidden UserStatus = 3 // 封禁
)

type UserRoleType int8

const (
	UserRoleTypeNormal     UserRoleType = 1 // 普通用户
	UserRoleTypeMonthVip   UserRoleType = 2 // 月度会员
	UserRoleTypeQuarterVip UserRoleType = 3 // 季度会员
	UserRoleTypeYearVip    UserRoleType = 4 // 年度会员
)

type UserPassType int8

const (
	UserPassTypeCode     UserPassType = 1 // 验证码登录
	UserPassTypePassword UserPassType = 2 // 密码登录
)

type UserLoginType int8

const (
	UserLoginTypePhone  UserLoginType = 1 // 手机号登录
	UserLoginTypeEmail  UserLoginType = 2 // 邮箱登录
	UserLoginTypeGoogle UserLoginType = 3 // 谷歌登录
	UserLoginTypeApple  UserLoginType = 4 // 苹果登录
)

type UserTokenPlatformType string

const (
	UserTokenPlatformUnknown UserTokenPlatformType = "unknown" // 未知
	UserTokenPlatformWebsite UserTokenPlatformType = "web"     // 网站
	UserTokenPlatformIOS     UserTokenPlatformType = "ios"     // 苹果系统
	UserTokenPlatformAndroid UserTokenPlatformType = "android" // 安卓系统
)

type UserHabitLabelType int8

const (
	UserHabitLabelAll  UserHabitLabelType = 0 // 全部
	UserHabitLabelDone UserHabitLabelType = 1 // 已完成
	UserHabitLabelUndo UserHabitLabelType = 2 // 未完成
)

type PlanetStatus int8

const (
	PlanetStatusNormal    PlanetStatus = 1 // 正常
	PlanetStatusDelete    PlanetStatus = 2 // 删除
	PlanetStatusForbidden PlanetStatus = 3 // 封禁
)

type PlanetTargetStatus int8

const (
	PlanetTargetStatusNormal    PlanetTargetStatus = 1 // 正常
	PlanetTargetStatusEnd       PlanetTargetStatus = 2 // 结束
	PlanetTargetStatusDelete    PlanetTargetStatus = 3 // 删除
	PlanetTargetStatusForbidden PlanetTargetStatus = 4 // 封禁
)

type PlanetMemberStatus int8

const (
	PlanetMemberStatusNormal    PlanetMemberStatus = 1 // 正常
	PlanetMemberStatusQuite     PlanetMemberStatus = 2 // 退出
	PlanetMemberStatusForbidden PlanetMemberStatus = 3 // 封禁
)

type TargetMemberStatus int8

const (
	TargetMemberStatusNormal    TargetMemberStatus = 1 // 正常
	TargetMemberStatusQuite     TargetMemberStatus = 2 // 退出
	TargetMemberStatusForbidden TargetMemberStatus = 3 // 封禁
)

type CronTaskType int8

const (
	CronTaskTypeUserHabit    CronTaskType = 1 // 用户习惯
	CronTaskTypeUserReckon   CronTaskType = 2 // 用户计时
	CronTaskTypeUserVipTrial CronTaskType = 3 // 用户会员试用期
)

type UserLikeType string

const (
	UserLikeTypePlanetPost UserLikeType = "planet_post" // 星球帖子
)

type FileSceneType string

const (
	FileSceneTypeDefault FileSceneType = ""       // 默认
	FileSceneTypeAward   FileSceneType = "award"  // 激励
	FileSceneTypeAvatar  FileSceneType = "avatar" // 头像
)

type StorageVisibleType string

const (
	StorageVisibleTypePublic  StorageVisibleType = "public"  // 公开
	StorageVisibleTypePrivate StorageVisibleType = "private" // 私有
)

type VerifyCodeSceneType string

const (
	VerifyCodeSceneTypeLogin          VerifyCodeSceneType = "login"           // 登录
	VerifyCodeSceneTypeChangePhone    VerifyCodeSceneType = "change-phone"    // 修改手机号
	VerifyCodeSceneTypeForgetPassword VerifyCodeSceneType = "forget-password" // 忘记密码
)

type SmsChannelType string

const (
	SmsChannelTypeTx  SmsChannelType = "tx"
	SmsChannelTypeAli SmsChannelType = "ali"
)

type TimelineOperateType string

const (
	TimelineOperatePunch  TimelineOperateType = "punch"  // 打卡
	TimelineOperateReckon TimelineOperateType = "reckon" // 计时
	TimelineOperateMemo   TimelineOperateType = "memo"   // 随记
)

type MotiveMemoStatus int8

const (
	MotiveMemoStatusNormal    PlanetStatus = 1 // 正常
	MotiveMemoStatusDelete    PlanetStatus = 2 // 删除
	MotiveMemoStatusForbidden PlanetStatus = 3 // 封禁
)

type MotiveMemoType int8

const (
	MotiveMemoTypePublic  MotiveMemoType = 1 // 公共
	MotiveMemoTypePrivate MotiveMemoType = 2 // 私人
)

type UserSettingSceneType string

const (
	UserSettingSceneHomePage UserSettingSceneType = "home_page" // 首页
)

type FeedBackStatusType int8

const (
	FeedBackStatusTypeUndo FeedBackStatusType = 1 // 未处理
	FeedBackStatusTypeDone FeedBackStatusType = 2 // 已处理
)

type RecordType int8

const (
	RecordTypeTime     RecordType = 1 // 时间
	RecordTypeDuration RecordType = 2 // 时长
	RecordTypeCount    RecordType = 3 // 次数
)

type HabitDetailLabelType string

const (
	HabitDetailLabelTypeAll   HabitDetailLabelType = "all"   // 全部
	HabitDetailLabelTypeWeek  HabitDetailLabelType = "week"  // 当周
	HabitDetailLabelTypeMonth HabitDetailLabelType = "month" // 当月
)

type UserOrderStatusType string

const (
	UserOrderStatusTypeExpired UserOrderStatusType = "expired" // 过期
	UserOrderStatusTypePaid    UserOrderStatusType = "paid"    // 已支付
	UserOrderStatusTypePending UserOrderStatusType = "pending" // 待支付
	UserOrderStatusTypeFailed  UserOrderStatusType = "failed"  // 失败
)

type UserOrderThirdType string

const (
	UserOrderThirdTypeUserVip UserOrderThirdType = "user_vip" // 会员
)

type OrderPaymentType string

const (
	OrderPaymentTypeAlipay OrderPaymentType = "ali_pay"    // 支付宝
	OrderPaymentTypeWechat OrderPaymentType = "wx_pay"     // 微信
	OrderPaymentTypeApple  OrderPaymentType = "apple_pay"  // 苹果
	OrderPaymentTypeGoogle OrderPaymentType = "google_pay" // 谷歌
)

type OrderPaymentStatus string

const (
	OrderPaymentStatusPaid    OrderPaymentStatus = "paid"    // 已支付
	OrderPaymentStatusPending OrderPaymentStatus = "pending" // 待支付
	OrderPaymentStatusFailed  OrderPaymentStatus = "failed"  // 失败
)

type ThirdPartyType int8

const (
	ThirdPartyTypeNone   ThirdPartyType = 0 // 无
	ThirdPartyTypeGoogle ThirdPartyType = 1 // 谷歌
	ThirdPartyTypeApple  ThirdPartyType = 2 // 苹果
)

type PrivacyDisplayModeType string

const (
	PrivacyDisplayModeTypeCustom  PrivacyDisplayModeType = "custom"
	PrivacyDisplayModeTypeDefault PrivacyDisplayModeType = "default"
	PrivacyDisplayModeTypeMask    PrivacyDisplayModeType = "mask"
)

type PushTaskType string

const (
	PushTaskTypeHabitReminder PushTaskType = "habit_reminder" // 习惯提醒
	PushTaskTypeSystemNotice  PushTaskType = "system_notice"  // 系统通知
)

type PushTaskStatus int8

const (
	PushTaskStatusPending PushTaskStatus = 0 // 待执行
	PushTaskStatusSuccess PushTaskStatus = 1 // 成功
	PushTaskStatusFailed  PushTaskStatus = 2 // 失败
	PushTaskStatusCancel  PushTaskStatus = 3 // 取消
)
