syntax = "proto3";
package kratos.api;

option go_package = "github.com/wlnil/life-log-be/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Auth auth = 3;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string source = 1;
    int32 max_idle_count = 2;
    int32 max_open_count = 3;
  }
  message Redis {
    string addr = 1;
    string username = 3;
    string password = 2;
    int32 db = 5;
  }
  message LocalizePath {
    string zh = 1;
    string en = 2;
  }
  message Qiniu {
    string access_key = 1;
    string secret_key = 2;
    string bucket = 3;
    int32 token_expires = 4;
    string addr = 5;
    string public_addr = 6;
    string public_bucket = 7;
  }
  message Task {
    string normal_task = 1;
    string delay_task = 2;
    string period_task = 3;
    string redis_addr = 4;
    string redis_password = 5;
    int32 redis_db = 6;
    string http_addr = 7;
  }
  message ThirdHttp {
    int32 timeout = 1;
    int32 retry_count = 2;
  }
  message TxSms {
    string app_id = 1;
    string secret_id = 2;
    string secret_key = 3;
    string sign = 4;
    string template_id = 5;
    string domain = 6;
  }
  message Email {
    string host = 1;
    int32 port = 2;
    string username = 3;
    string password = 4;
    string from = 5;
  }
  message AliPay {
    string app_id = 1;
    string app_private_key = 2;
    string ali_public_key = 3;
    string notify_url = 4;
    string return_url = 5;
    bool is_production = 6;
    string encrypt_key = 7;
  }
  message UserVipPrice {
    int32 month_price = 1;
    int32 discount_month_price = 2;
    int32 quarter_price = 3;
    int32 discount_quarter_price = 4;
    int32 year_price = 5;
    int32 discount_year_price = 6;
  }
  message JPush {
    string app_key = 1;
    string app_secret = 2;
    string api_endpoint = 3;
    int32 timeout = 4;
    int32 max_retry = 5;
    bool enabled = 6;
  }
  message PushConfig {
    repeated string enabled_services = 1;
    string default_service = 2;
    bool enable_fallback = 3;
    int32 health_check_interval = 4;
    bool enable_domestic_routing = 5;
    string domestic_service = 6;
    string overseas_service = 7;
  }

  message Firebase {
    string project_id = 1;
    int32 timeout = 2;
    bool enabled = 3;
    string credentials_file = 4;
    bool mock_mode = 5;
  }
  Database database = 1;
  Redis redis = 2;
  EnvType env = 3;
  LocalizePath localize_path = 4;
  string jaeger_url = 5;
  Qiniu qiniu = 6;
  Task task = 7;
  ThirdHttp third_http = 8;
  bool is_display_planet = 9;
  string sentry_dsn = 10;
  TxSms tx_sms = 11;
  string use_sms_channel = 12;
  int32 sms_code_expires = 13;
  Email email = 14;
  AliPay ali_pay = 15;
  UserVipPrice user_vip_price = 16;
  JPush jpush = 17;
  PushConfig push_config = 20;
  Firebase firebase = 21;
}

message Auth {
  string secret_key = 1;
  string issuer = 2;
  int32 expire_at = 3;
  string redirect_url = 6;

  message GoogleOAuth {
    string client_id = 1;
    string client_secret = 2;
    string redirect_uri = 3;
  }

  message AppleOAuth {
    string client_id = 1;
    string team_id = 2;
    string key_id = 3;
    string private_key = 4;
    string redirect_uri = 5;
  }

  GoogleOAuth google_oauth = 4;
  AppleOAuth apple_oauth = 5;
}

enum EnvType {
  Test = 0; // 测试环境
  Pre = 1; // 预发布
  Release = 2; // 生产环境
}
