// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: conf/conf.proto

package conf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnvType int32

const (
	EnvType_Test    EnvType = 0 // 测试环境
	EnvType_Pre     EnvType = 1 // 预发布
	EnvType_Release EnvType = 2 // 生产环境
)

// Enum value maps for EnvType.
var (
	EnvType_name = map[int32]string{
		0: "Test",
		1: "Pre",
		2: "Release",
	}
	EnvType_value = map[string]int32{
		"Test":    0,
		"Pre":     1,
		"Release": 2,
	}
)

func (x EnvType) Enum() *EnvType {
	p := new(EnvType)
	*p = x
	return p
}

func (x EnvType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnvType) Descriptor() protoreflect.EnumDescriptor {
	return file_conf_conf_proto_enumTypes[0].Descriptor()
}

func (EnvType) Type() protoreflect.EnumType {
	return &file_conf_conf_proto_enumTypes[0]
}

func (x EnvType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnvType.Descriptor instead.
func (EnvType) EnumDescriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{0}
}

type Bootstrap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Server        *Server                `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Data          *Data                  `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Auth          *Auth                  `protobuf:"bytes,3,opt,name=auth,proto3" json:"auth,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bootstrap) Reset() {
	*x = Bootstrap{}
	mi := &file_conf_conf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bootstrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bootstrap) ProtoMessage() {}

func (x *Bootstrap) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bootstrap.ProtoReflect.Descriptor instead.
func (*Bootstrap) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{0}
}

func (x *Bootstrap) GetServer() *Server {
	if x != nil {
		return x.Server
	}
	return nil
}

func (x *Bootstrap) GetData() *Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Bootstrap) GetAuth() *Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

type Server struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Http          *Server_HTTP           `protobuf:"bytes,1,opt,name=http,proto3" json:"http,omitempty"`
	Grpc          *Server_GRPC           `protobuf:"bytes,2,opt,name=grpc,proto3" json:"grpc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_conf_conf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1}
}

func (x *Server) GetHttp() *Server_HTTP {
	if x != nil {
		return x.Http
	}
	return nil
}

func (x *Server) GetGrpc() *Server_GRPC {
	if x != nil {
		return x.Grpc
	}
	return nil
}

type Data struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Database        *Data_Database         `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	Redis           *Data_Redis            `protobuf:"bytes,2,opt,name=redis,proto3" json:"redis,omitempty"`
	Env             EnvType                `protobuf:"varint,3,opt,name=env,proto3,enum=kratos.api.EnvType" json:"env,omitempty"`
	LocalizePath    *Data_LocalizePath     `protobuf:"bytes,4,opt,name=localize_path,json=localizePath,proto3" json:"localize_path,omitempty"`
	JaegerUrl       string                 `protobuf:"bytes,5,opt,name=jaeger_url,json=jaegerUrl,proto3" json:"jaeger_url,omitempty"`
	Qiniu           *Data_Qiniu            `protobuf:"bytes,6,opt,name=qiniu,proto3" json:"qiniu,omitempty"`
	Task            *Data_Task             `protobuf:"bytes,7,opt,name=task,proto3" json:"task,omitempty"`
	ThirdHttp       *Data_ThirdHttp        `protobuf:"bytes,8,opt,name=third_http,json=thirdHttp,proto3" json:"third_http,omitempty"`
	IsDisplayPlanet bool                   `protobuf:"varint,9,opt,name=is_display_planet,json=isDisplayPlanet,proto3" json:"is_display_planet,omitempty"`
	SentryDsn       string                 `protobuf:"bytes,10,opt,name=sentry_dsn,json=sentryDsn,proto3" json:"sentry_dsn,omitempty"`
	TxSms           *Data_TxSms            `protobuf:"bytes,11,opt,name=tx_sms,json=txSms,proto3" json:"tx_sms,omitempty"`
	UseSmsChannel   string                 `protobuf:"bytes,12,opt,name=use_sms_channel,json=useSmsChannel,proto3" json:"use_sms_channel,omitempty"`
	SmsCodeExpires  int32                  `protobuf:"varint,13,opt,name=sms_code_expires,json=smsCodeExpires,proto3" json:"sms_code_expires,omitempty"`
	Email           *Data_Email            `protobuf:"bytes,14,opt,name=email,proto3" json:"email,omitempty"`
	AliPay          *Data_AliPay           `protobuf:"bytes,15,opt,name=ali_pay,json=aliPay,proto3" json:"ali_pay,omitempty"`
	UserVipPrice    *Data_UserVipPrice     `protobuf:"bytes,16,opt,name=user_vip_price,json=userVipPrice,proto3" json:"user_vip_price,omitempty"`
	Jpush           *Data_JPush            `protobuf:"bytes,17,opt,name=jpush,proto3" json:"jpush,omitempty"`
	PushConfig      *Data_PushConfig       `protobuf:"bytes,20,opt,name=push_config,json=pushConfig,proto3" json:"push_config,omitempty"`
	Firebase        *Data_Firebase         `protobuf:"bytes,21,opt,name=firebase,proto3" json:"firebase,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Data) Reset() {
	*x = Data{}
	mi := &file_conf_conf_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data) ProtoMessage() {}

func (x *Data) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data.ProtoReflect.Descriptor instead.
func (*Data) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2}
}

func (x *Data) GetDatabase() *Data_Database {
	if x != nil {
		return x.Database
	}
	return nil
}

func (x *Data) GetRedis() *Data_Redis {
	if x != nil {
		return x.Redis
	}
	return nil
}

func (x *Data) GetEnv() EnvType {
	if x != nil {
		return x.Env
	}
	return EnvType_Test
}

func (x *Data) GetLocalizePath() *Data_LocalizePath {
	if x != nil {
		return x.LocalizePath
	}
	return nil
}

func (x *Data) GetJaegerUrl() string {
	if x != nil {
		return x.JaegerUrl
	}
	return ""
}

func (x *Data) GetQiniu() *Data_Qiniu {
	if x != nil {
		return x.Qiniu
	}
	return nil
}

func (x *Data) GetTask() *Data_Task {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *Data) GetThirdHttp() *Data_ThirdHttp {
	if x != nil {
		return x.ThirdHttp
	}
	return nil
}

func (x *Data) GetIsDisplayPlanet() bool {
	if x != nil {
		return x.IsDisplayPlanet
	}
	return false
}

func (x *Data) GetSentryDsn() string {
	if x != nil {
		return x.SentryDsn
	}
	return ""
}

func (x *Data) GetTxSms() *Data_TxSms {
	if x != nil {
		return x.TxSms
	}
	return nil
}

func (x *Data) GetUseSmsChannel() string {
	if x != nil {
		return x.UseSmsChannel
	}
	return ""
}

func (x *Data) GetSmsCodeExpires() int32 {
	if x != nil {
		return x.SmsCodeExpires
	}
	return 0
}

func (x *Data) GetEmail() *Data_Email {
	if x != nil {
		return x.Email
	}
	return nil
}

func (x *Data) GetAliPay() *Data_AliPay {
	if x != nil {
		return x.AliPay
	}
	return nil
}

func (x *Data) GetUserVipPrice() *Data_UserVipPrice {
	if x != nil {
		return x.UserVipPrice
	}
	return nil
}

func (x *Data) GetJpush() *Data_JPush {
	if x != nil {
		return x.Jpush
	}
	return nil
}

func (x *Data) GetPushConfig() *Data_PushConfig {
	if x != nil {
		return x.PushConfig
	}
	return nil
}

func (x *Data) GetFirebase() *Data_Firebase {
	if x != nil {
		return x.Firebase
	}
	return nil
}

type Auth struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SecretKey     string                 `protobuf:"bytes,1,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	Issuer        string                 `protobuf:"bytes,2,opt,name=issuer,proto3" json:"issuer,omitempty"`
	ExpireAt      int32                  `protobuf:"varint,3,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	RedirectUrl   string                 `protobuf:"bytes,6,opt,name=redirect_url,json=redirectUrl,proto3" json:"redirect_url,omitempty"`
	GoogleOauth   *Auth_GoogleOAuth      `protobuf:"bytes,4,opt,name=google_oauth,json=googleOauth,proto3" json:"google_oauth,omitempty"`
	AppleOauth    *Auth_AppleOAuth       `protobuf:"bytes,5,opt,name=apple_oauth,json=appleOauth,proto3" json:"apple_oauth,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Auth) Reset() {
	*x = Auth{}
	mi := &file_conf_conf_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Auth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Auth) ProtoMessage() {}

func (x *Auth) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Auth.ProtoReflect.Descriptor instead.
func (*Auth) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{3}
}

func (x *Auth) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *Auth) GetIssuer() string {
	if x != nil {
		return x.Issuer
	}
	return ""
}

func (x *Auth) GetExpireAt() int32 {
	if x != nil {
		return x.ExpireAt
	}
	return 0
}

func (x *Auth) GetRedirectUrl() string {
	if x != nil {
		return x.RedirectUrl
	}
	return ""
}

func (x *Auth) GetGoogleOauth() *Auth_GoogleOAuth {
	if x != nil {
		return x.GoogleOauth
	}
	return nil
}

func (x *Auth) GetAppleOauth() *Auth_AppleOAuth {
	if x != nil {
		return x.AppleOauth
	}
	return nil
}

type Server_HTTP struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_HTTP) Reset() {
	*x = Server_HTTP{}
	mi := &file_conf_conf_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_HTTP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_HTTP) ProtoMessage() {}

func (x *Server_HTTP) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_HTTP.ProtoReflect.Descriptor instead.
func (*Server_HTTP) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Server_HTTP) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_HTTP) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_HTTP) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Server_GRPC struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Network       string                 `protobuf:"bytes,1,opt,name=network,proto3" json:"network,omitempty"`
	Addr          string                 `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
	Timeout       *durationpb.Duration   `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server_GRPC) Reset() {
	*x = Server_GRPC{}
	mi := &file_conf_conf_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server_GRPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server_GRPC) ProtoMessage() {}

func (x *Server_GRPC) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server_GRPC.ProtoReflect.Descriptor instead.
func (*Server_GRPC) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Server_GRPC) GetNetwork() string {
	if x != nil {
		return x.Network
	}
	return ""
}

func (x *Server_GRPC) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Server_GRPC) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type Data_Database struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Source        string                 `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	MaxIdleCount  int32                  `protobuf:"varint,2,opt,name=max_idle_count,json=maxIdleCount,proto3" json:"max_idle_count,omitempty"`
	MaxOpenCount  int32                  `protobuf:"varint,3,opt,name=max_open_count,json=maxOpenCount,proto3" json:"max_open_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Database) Reset() {
	*x = Data_Database{}
	mi := &file_conf_conf_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Database) ProtoMessage() {}

func (x *Data_Database) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Database.ProtoReflect.Descriptor instead.
func (*Data_Database) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Data_Database) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *Data_Database) GetMaxIdleCount() int32 {
	if x != nil {
		return x.MaxIdleCount
	}
	return 0
}

func (x *Data_Database) GetMaxOpenCount() int32 {
	if x != nil {
		return x.MaxOpenCount
	}
	return 0
}

type Data_Redis struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Addr          string                 `protobuf:"bytes,1,opt,name=addr,proto3" json:"addr,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Db            int32                  `protobuf:"varint,5,opt,name=db,proto3" json:"db,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Redis) Reset() {
	*x = Data_Redis{}
	mi := &file_conf_conf_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Redis) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Redis) ProtoMessage() {}

func (x *Data_Redis) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Redis.ProtoReflect.Descriptor instead.
func (*Data_Redis) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Data_Redis) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Data_Redis) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Data_Redis) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Data_Redis) GetDb() int32 {
	if x != nil {
		return x.Db
	}
	return 0
}

type Data_LocalizePath struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Zh            string                 `protobuf:"bytes,1,opt,name=zh,proto3" json:"zh,omitempty"`
	En            string                 `protobuf:"bytes,2,opt,name=en,proto3" json:"en,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_LocalizePath) Reset() {
	*x = Data_LocalizePath{}
	mi := &file_conf_conf_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_LocalizePath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_LocalizePath) ProtoMessage() {}

func (x *Data_LocalizePath) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_LocalizePath.ProtoReflect.Descriptor instead.
func (*Data_LocalizePath) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 2}
}

func (x *Data_LocalizePath) GetZh() string {
	if x != nil {
		return x.Zh
	}
	return ""
}

func (x *Data_LocalizePath) GetEn() string {
	if x != nil {
		return x.En
	}
	return ""
}

type Data_Qiniu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessKey     string                 `protobuf:"bytes,1,opt,name=access_key,json=accessKey,proto3" json:"access_key,omitempty"`
	SecretKey     string                 `protobuf:"bytes,2,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	Bucket        string                 `protobuf:"bytes,3,opt,name=bucket,proto3" json:"bucket,omitempty"`
	TokenExpires  int32                  `protobuf:"varint,4,opt,name=token_expires,json=tokenExpires,proto3" json:"token_expires,omitempty"`
	Addr          string                 `protobuf:"bytes,5,opt,name=addr,proto3" json:"addr,omitempty"`
	PublicAddr    string                 `protobuf:"bytes,6,opt,name=public_addr,json=publicAddr,proto3" json:"public_addr,omitempty"`
	PublicBucket  string                 `protobuf:"bytes,7,opt,name=public_bucket,json=publicBucket,proto3" json:"public_bucket,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Qiniu) Reset() {
	*x = Data_Qiniu{}
	mi := &file_conf_conf_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Qiniu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Qiniu) ProtoMessage() {}

func (x *Data_Qiniu) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Qiniu.ProtoReflect.Descriptor instead.
func (*Data_Qiniu) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 3}
}

func (x *Data_Qiniu) GetAccessKey() string {
	if x != nil {
		return x.AccessKey
	}
	return ""
}

func (x *Data_Qiniu) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *Data_Qiniu) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *Data_Qiniu) GetTokenExpires() int32 {
	if x != nil {
		return x.TokenExpires
	}
	return 0
}

func (x *Data_Qiniu) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *Data_Qiniu) GetPublicAddr() string {
	if x != nil {
		return x.PublicAddr
	}
	return ""
}

func (x *Data_Qiniu) GetPublicBucket() string {
	if x != nil {
		return x.PublicBucket
	}
	return ""
}

type Data_Task struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NormalTask    string                 `protobuf:"bytes,1,opt,name=normal_task,json=normalTask,proto3" json:"normal_task,omitempty"`
	DelayTask     string                 `protobuf:"bytes,2,opt,name=delay_task,json=delayTask,proto3" json:"delay_task,omitempty"`
	PeriodTask    string                 `protobuf:"bytes,3,opt,name=period_task,json=periodTask,proto3" json:"period_task,omitempty"`
	RedisAddr     string                 `protobuf:"bytes,4,opt,name=redis_addr,json=redisAddr,proto3" json:"redis_addr,omitempty"`
	RedisPassword string                 `protobuf:"bytes,5,opt,name=redis_password,json=redisPassword,proto3" json:"redis_password,omitempty"`
	RedisDb       int32                  `protobuf:"varint,6,opt,name=redis_db,json=redisDb,proto3" json:"redis_db,omitempty"`
	HttpAddr      string                 `protobuf:"bytes,7,opt,name=http_addr,json=httpAddr,proto3" json:"http_addr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Task) Reset() {
	*x = Data_Task{}
	mi := &file_conf_conf_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Task) ProtoMessage() {}

func (x *Data_Task) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Task.ProtoReflect.Descriptor instead.
func (*Data_Task) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 4}
}

func (x *Data_Task) GetNormalTask() string {
	if x != nil {
		return x.NormalTask
	}
	return ""
}

func (x *Data_Task) GetDelayTask() string {
	if x != nil {
		return x.DelayTask
	}
	return ""
}

func (x *Data_Task) GetPeriodTask() string {
	if x != nil {
		return x.PeriodTask
	}
	return ""
}

func (x *Data_Task) GetRedisAddr() string {
	if x != nil {
		return x.RedisAddr
	}
	return ""
}

func (x *Data_Task) GetRedisPassword() string {
	if x != nil {
		return x.RedisPassword
	}
	return ""
}

func (x *Data_Task) GetRedisDb() int32 {
	if x != nil {
		return x.RedisDb
	}
	return 0
}

func (x *Data_Task) GetHttpAddr() string {
	if x != nil {
		return x.HttpAddr
	}
	return ""
}

type Data_ThirdHttp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Timeout       int32                  `protobuf:"varint,1,opt,name=timeout,proto3" json:"timeout,omitempty"`
	RetryCount    int32                  `protobuf:"varint,2,opt,name=retry_count,json=retryCount,proto3" json:"retry_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_ThirdHttp) Reset() {
	*x = Data_ThirdHttp{}
	mi := &file_conf_conf_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_ThirdHttp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_ThirdHttp) ProtoMessage() {}

func (x *Data_ThirdHttp) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_ThirdHttp.ProtoReflect.Descriptor instead.
func (*Data_ThirdHttp) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 5}
}

func (x *Data_ThirdHttp) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *Data_ThirdHttp) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

type Data_TxSms struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppId         string                 `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	SecretId      string                 `protobuf:"bytes,2,opt,name=secret_id,json=secretId,proto3" json:"secret_id,omitempty"`
	SecretKey     string                 `protobuf:"bytes,3,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	Sign          string                 `protobuf:"bytes,4,opt,name=sign,proto3" json:"sign,omitempty"`
	TemplateId    string                 `protobuf:"bytes,5,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	Domain        string                 `protobuf:"bytes,6,opt,name=domain,proto3" json:"domain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_TxSms) Reset() {
	*x = Data_TxSms{}
	mi := &file_conf_conf_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_TxSms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_TxSms) ProtoMessage() {}

func (x *Data_TxSms) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_TxSms.ProtoReflect.Descriptor instead.
func (*Data_TxSms) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 6}
}

func (x *Data_TxSms) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Data_TxSms) GetSecretId() string {
	if x != nil {
		return x.SecretId
	}
	return ""
}

func (x *Data_TxSms) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *Data_TxSms) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *Data_TxSms) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *Data_TxSms) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type Data_Email struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Host          string                 `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	From          string                 `protobuf:"bytes,5,opt,name=from,proto3" json:"from,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_Email) Reset() {
	*x = Data_Email{}
	mi := &file_conf_conf_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Email) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Email) ProtoMessage() {}

func (x *Data_Email) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Email.ProtoReflect.Descriptor instead.
func (*Data_Email) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 7}
}

func (x *Data_Email) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Data_Email) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Data_Email) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Data_Email) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Data_Email) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

type Data_AliPay struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppId         string                 `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	AppPrivateKey string                 `protobuf:"bytes,2,opt,name=app_private_key,json=appPrivateKey,proto3" json:"app_private_key,omitempty"`
	AliPublicKey  string                 `protobuf:"bytes,3,opt,name=ali_public_key,json=aliPublicKey,proto3" json:"ali_public_key,omitempty"`
	NotifyUrl     string                 `protobuf:"bytes,4,opt,name=notify_url,json=notifyUrl,proto3" json:"notify_url,omitempty"`
	ReturnUrl     string                 `protobuf:"bytes,5,opt,name=return_url,json=returnUrl,proto3" json:"return_url,omitempty"`
	IsProduction  bool                   `protobuf:"varint,6,opt,name=is_production,json=isProduction,proto3" json:"is_production,omitempty"`
	EncryptKey    string                 `protobuf:"bytes,7,opt,name=encrypt_key,json=encryptKey,proto3" json:"encrypt_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_AliPay) Reset() {
	*x = Data_AliPay{}
	mi := &file_conf_conf_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_AliPay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_AliPay) ProtoMessage() {}

func (x *Data_AliPay) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_AliPay.ProtoReflect.Descriptor instead.
func (*Data_AliPay) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 8}
}

func (x *Data_AliPay) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *Data_AliPay) GetAppPrivateKey() string {
	if x != nil {
		return x.AppPrivateKey
	}
	return ""
}

func (x *Data_AliPay) GetAliPublicKey() string {
	if x != nil {
		return x.AliPublicKey
	}
	return ""
}

func (x *Data_AliPay) GetNotifyUrl() string {
	if x != nil {
		return x.NotifyUrl
	}
	return ""
}

func (x *Data_AliPay) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *Data_AliPay) GetIsProduction() bool {
	if x != nil {
		return x.IsProduction
	}
	return false
}

func (x *Data_AliPay) GetEncryptKey() string {
	if x != nil {
		return x.EncryptKey
	}
	return ""
}

type Data_UserVipPrice struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	MonthPrice           int32                  `protobuf:"varint,1,opt,name=month_price,json=monthPrice,proto3" json:"month_price,omitempty"`
	DiscountMonthPrice   int32                  `protobuf:"varint,2,opt,name=discount_month_price,json=discountMonthPrice,proto3" json:"discount_month_price,omitempty"`
	QuarterPrice         int32                  `protobuf:"varint,3,opt,name=quarter_price,json=quarterPrice,proto3" json:"quarter_price,omitempty"`
	DiscountQuarterPrice int32                  `protobuf:"varint,4,opt,name=discount_quarter_price,json=discountQuarterPrice,proto3" json:"discount_quarter_price,omitempty"`
	YearPrice            int32                  `protobuf:"varint,5,opt,name=year_price,json=yearPrice,proto3" json:"year_price,omitempty"`
	DiscountYearPrice    int32                  `protobuf:"varint,6,opt,name=discount_year_price,json=discountYearPrice,proto3" json:"discount_year_price,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *Data_UserVipPrice) Reset() {
	*x = Data_UserVipPrice{}
	mi := &file_conf_conf_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_UserVipPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_UserVipPrice) ProtoMessage() {}

func (x *Data_UserVipPrice) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_UserVipPrice.ProtoReflect.Descriptor instead.
func (*Data_UserVipPrice) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 9}
}

func (x *Data_UserVipPrice) GetMonthPrice() int32 {
	if x != nil {
		return x.MonthPrice
	}
	return 0
}

func (x *Data_UserVipPrice) GetDiscountMonthPrice() int32 {
	if x != nil {
		return x.DiscountMonthPrice
	}
	return 0
}

func (x *Data_UserVipPrice) GetQuarterPrice() int32 {
	if x != nil {
		return x.QuarterPrice
	}
	return 0
}

func (x *Data_UserVipPrice) GetDiscountQuarterPrice() int32 {
	if x != nil {
		return x.DiscountQuarterPrice
	}
	return 0
}

func (x *Data_UserVipPrice) GetYearPrice() int32 {
	if x != nil {
		return x.YearPrice
	}
	return 0
}

func (x *Data_UserVipPrice) GetDiscountYearPrice() int32 {
	if x != nil {
		return x.DiscountYearPrice
	}
	return 0
}

type Data_JPush struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppKey        string                 `protobuf:"bytes,1,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`
	AppSecret     string                 `protobuf:"bytes,2,opt,name=app_secret,json=appSecret,proto3" json:"app_secret,omitempty"`
	ApiEndpoint   string                 `protobuf:"bytes,3,opt,name=api_endpoint,json=apiEndpoint,proto3" json:"api_endpoint,omitempty"`
	Timeout       int32                  `protobuf:"varint,4,opt,name=timeout,proto3" json:"timeout,omitempty"`
	MaxRetry      int32                  `protobuf:"varint,5,opt,name=max_retry,json=maxRetry,proto3" json:"max_retry,omitempty"`
	Enabled       bool                   `protobuf:"varint,6,opt,name=enabled,proto3" json:"enabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Data_JPush) Reset() {
	*x = Data_JPush{}
	mi := &file_conf_conf_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_JPush) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_JPush) ProtoMessage() {}

func (x *Data_JPush) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_JPush.ProtoReflect.Descriptor instead.
func (*Data_JPush) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 10}
}

func (x *Data_JPush) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *Data_JPush) GetAppSecret() string {
	if x != nil {
		return x.AppSecret
	}
	return ""
}

func (x *Data_JPush) GetApiEndpoint() string {
	if x != nil {
		return x.ApiEndpoint
	}
	return ""
}

func (x *Data_JPush) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *Data_JPush) GetMaxRetry() int32 {
	if x != nil {
		return x.MaxRetry
	}
	return 0
}

func (x *Data_JPush) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type Data_PushConfig struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	EnabledServices       []string               `protobuf:"bytes,1,rep,name=enabled_services,json=enabledServices,proto3" json:"enabled_services,omitempty"`
	DefaultService        string                 `protobuf:"bytes,2,opt,name=default_service,json=defaultService,proto3" json:"default_service,omitempty"`
	EnableFallback        bool                   `protobuf:"varint,3,opt,name=enable_fallback,json=enableFallback,proto3" json:"enable_fallback,omitempty"`
	HealthCheckInterval   int32                  `protobuf:"varint,4,opt,name=health_check_interval,json=healthCheckInterval,proto3" json:"health_check_interval,omitempty"`
	EnableDomesticRouting bool                   `protobuf:"varint,5,opt,name=enable_domestic_routing,json=enableDomesticRouting,proto3" json:"enable_domestic_routing,omitempty"`
	DomesticService       string                 `protobuf:"bytes,6,opt,name=domestic_service,json=domesticService,proto3" json:"domestic_service,omitempty"`
	OverseasService       string                 `protobuf:"bytes,7,opt,name=overseas_service,json=overseasService,proto3" json:"overseas_service,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *Data_PushConfig) Reset() {
	*x = Data_PushConfig{}
	mi := &file_conf_conf_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_PushConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_PushConfig) ProtoMessage() {}

func (x *Data_PushConfig) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_PushConfig.ProtoReflect.Descriptor instead.
func (*Data_PushConfig) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 11}
}

func (x *Data_PushConfig) GetEnabledServices() []string {
	if x != nil {
		return x.EnabledServices
	}
	return nil
}

func (x *Data_PushConfig) GetDefaultService() string {
	if x != nil {
		return x.DefaultService
	}
	return ""
}

func (x *Data_PushConfig) GetEnableFallback() bool {
	if x != nil {
		return x.EnableFallback
	}
	return false
}

func (x *Data_PushConfig) GetHealthCheckInterval() int32 {
	if x != nil {
		return x.HealthCheckInterval
	}
	return 0
}

func (x *Data_PushConfig) GetEnableDomesticRouting() bool {
	if x != nil {
		return x.EnableDomesticRouting
	}
	return false
}

func (x *Data_PushConfig) GetDomesticService() string {
	if x != nil {
		return x.DomesticService
	}
	return ""
}

func (x *Data_PushConfig) GetOverseasService() string {
	if x != nil {
		return x.OverseasService
	}
	return ""
}

type Data_Firebase struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	Timeout         int32                  `protobuf:"varint,2,opt,name=timeout,proto3" json:"timeout,omitempty"`
	Enabled         bool                   `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled,omitempty"`
	CredentialsFile string                 `protobuf:"bytes,4,opt,name=credentials_file,json=credentialsFile,proto3" json:"credentials_file,omitempty"`
	MockMode        bool                   `protobuf:"varint,5,opt,name=mock_mode,json=mockMode,proto3" json:"mock_mode,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Data_Firebase) Reset() {
	*x = Data_Firebase{}
	mi := &file_conf_conf_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Data_Firebase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Data_Firebase) ProtoMessage() {}

func (x *Data_Firebase) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Data_Firebase.ProtoReflect.Descriptor instead.
func (*Data_Firebase) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{2, 12}
}

func (x *Data_Firebase) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Data_Firebase) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *Data_Firebase) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *Data_Firebase) GetCredentialsFile() string {
	if x != nil {
		return x.CredentialsFile
	}
	return ""
}

func (x *Data_Firebase) GetMockMode() bool {
	if x != nil {
		return x.MockMode
	}
	return false
}

type Auth_GoogleOAuth struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      string                 `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret  string                 `protobuf:"bytes,2,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	RedirectUri   string                 `protobuf:"bytes,3,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Auth_GoogleOAuth) Reset() {
	*x = Auth_GoogleOAuth{}
	mi := &file_conf_conf_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Auth_GoogleOAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Auth_GoogleOAuth) ProtoMessage() {}

func (x *Auth_GoogleOAuth) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Auth_GoogleOAuth.ProtoReflect.Descriptor instead.
func (*Auth_GoogleOAuth) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{3, 0}
}

func (x *Auth_GoogleOAuth) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Auth_GoogleOAuth) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *Auth_GoogleOAuth) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

type Auth_AppleOAuth struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientId      string                 `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	TeamId        string                 `protobuf:"bytes,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	KeyId         string                 `protobuf:"bytes,3,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	PrivateKey    string                 `protobuf:"bytes,4,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	RedirectUri   string                 `protobuf:"bytes,5,opt,name=redirect_uri,json=redirectUri,proto3" json:"redirect_uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Auth_AppleOAuth) Reset() {
	*x = Auth_AppleOAuth{}
	mi := &file_conf_conf_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Auth_AppleOAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Auth_AppleOAuth) ProtoMessage() {}

func (x *Auth_AppleOAuth) ProtoReflect() protoreflect.Message {
	mi := &file_conf_conf_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Auth_AppleOAuth.ProtoReflect.Descriptor instead.
func (*Auth_AppleOAuth) Descriptor() ([]byte, []int) {
	return file_conf_conf_proto_rawDescGZIP(), []int{3, 1}
}

func (x *Auth_AppleOAuth) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *Auth_AppleOAuth) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *Auth_AppleOAuth) GetKeyId() string {
	if x != nil {
		return x.KeyId
	}
	return ""
}

func (x *Auth_AppleOAuth) GetPrivateKey() string {
	if x != nil {
		return x.PrivateKey
	}
	return ""
}

func (x *Auth_AppleOAuth) GetRedirectUri() string {
	if x != nil {
		return x.RedirectUri
	}
	return ""
}

var File_conf_conf_proto protoreflect.FileDescriptor

const file_conf_conf_proto_rawDesc = "" +
	"\n" +
	"\x0fconf/conf.proto\x12\n" +
	"kratos.api\x1a\x1egoogle/protobuf/duration.proto\"\x83\x01\n" +
	"\tBootstrap\x12*\n" +
	"\x06server\x18\x01 \x01(\v2\x12.kratos.api.ServerR\x06server\x12$\n" +
	"\x04data\x18\x02 \x01(\v2\x10.kratos.api.DataR\x04data\x12$\n" +
	"\x04auth\x18\x03 \x01(\v2\x10.kratos.api.AuthR\x04auth\"\xb8\x02\n" +
	"\x06Server\x12+\n" +
	"\x04http\x18\x01 \x01(\v2\x17.kratos.api.Server.HTTPR\x04http\x12+\n" +
	"\x04grpc\x18\x02 \x01(\v2\x17.kratos.api.Server.GRPCR\x04grpc\x1ai\n" +
	"\x04HTTP\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\x1ai\n" +
	"\x04GRPC\x12\x18\n" +
	"\anetwork\x18\x01 \x01(\tR\anetwork\x12\x12\n" +
	"\x04addr\x18\x02 \x01(\tR\x04addr\x123\n" +
	"\atimeout\x18\x03 \x01(\v2\x19.google.protobuf.DurationR\atimeout\"\x86\x19\n" +
	"\x04Data\x125\n" +
	"\bdatabase\x18\x01 \x01(\v2\x19.kratos.api.Data.DatabaseR\bdatabase\x12,\n" +
	"\x05redis\x18\x02 \x01(\v2\x16.kratos.api.Data.RedisR\x05redis\x12%\n" +
	"\x03env\x18\x03 \x01(\x0e2\x13.kratos.api.EnvTypeR\x03env\x12B\n" +
	"\rlocalize_path\x18\x04 \x01(\v2\x1d.kratos.api.Data.LocalizePathR\flocalizePath\x12\x1d\n" +
	"\n" +
	"jaeger_url\x18\x05 \x01(\tR\tjaegerUrl\x12,\n" +
	"\x05qiniu\x18\x06 \x01(\v2\x16.kratos.api.Data.QiniuR\x05qiniu\x12)\n" +
	"\x04task\x18\a \x01(\v2\x15.kratos.api.Data.TaskR\x04task\x129\n" +
	"\n" +
	"third_http\x18\b \x01(\v2\x1a.kratos.api.Data.ThirdHttpR\tthirdHttp\x12*\n" +
	"\x11is_display_planet\x18\t \x01(\bR\x0fisDisplayPlanet\x12\x1d\n" +
	"\n" +
	"sentry_dsn\x18\n" +
	" \x01(\tR\tsentryDsn\x12-\n" +
	"\x06tx_sms\x18\v \x01(\v2\x16.kratos.api.Data.TxSmsR\x05txSms\x12&\n" +
	"\x0fuse_sms_channel\x18\f \x01(\tR\ruseSmsChannel\x12(\n" +
	"\x10sms_code_expires\x18\r \x01(\x05R\x0esmsCodeExpires\x12,\n" +
	"\x05email\x18\x0e \x01(\v2\x16.kratos.api.Data.EmailR\x05email\x120\n" +
	"\aali_pay\x18\x0f \x01(\v2\x17.kratos.api.Data.AliPayR\x06aliPay\x12C\n" +
	"\x0euser_vip_price\x18\x10 \x01(\v2\x1d.kratos.api.Data.UserVipPriceR\fuserVipPrice\x12,\n" +
	"\x05jpush\x18\x11 \x01(\v2\x16.kratos.api.Data.JPushR\x05jpush\x12<\n" +
	"\vpush_config\x18\x14 \x01(\v2\x1b.kratos.api.Data.PushConfigR\n" +
	"pushConfig\x125\n" +
	"\bfirebase\x18\x15 \x01(\v2\x19.kratos.api.Data.FirebaseR\bfirebase\x1an\n" +
	"\bDatabase\x12\x16\n" +
	"\x06source\x18\x01 \x01(\tR\x06source\x12$\n" +
	"\x0emax_idle_count\x18\x02 \x01(\x05R\fmaxIdleCount\x12$\n" +
	"\x0emax_open_count\x18\x03 \x01(\x05R\fmaxOpenCount\x1ac\n" +
	"\x05Redis\x12\x12\n" +
	"\x04addr\x18\x01 \x01(\tR\x04addr\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x0e\n" +
	"\x02db\x18\x05 \x01(\x05R\x02db\x1a.\n" +
	"\fLocalizePath\x12\x0e\n" +
	"\x02zh\x18\x01 \x01(\tR\x02zh\x12\x0e\n" +
	"\x02en\x18\x02 \x01(\tR\x02en\x1a\xdc\x01\n" +
	"\x05Qiniu\x12\x1d\n" +
	"\n" +
	"access_key\x18\x01 \x01(\tR\taccessKey\x12\x1d\n" +
	"\n" +
	"secret_key\x18\x02 \x01(\tR\tsecretKey\x12\x16\n" +
	"\x06bucket\x18\x03 \x01(\tR\x06bucket\x12#\n" +
	"\rtoken_expires\x18\x04 \x01(\x05R\ftokenExpires\x12\x12\n" +
	"\x04addr\x18\x05 \x01(\tR\x04addr\x12\x1f\n" +
	"\vpublic_addr\x18\x06 \x01(\tR\n" +
	"publicAddr\x12#\n" +
	"\rpublic_bucket\x18\a \x01(\tR\fpublicBucket\x1a\xe5\x01\n" +
	"\x04Task\x12\x1f\n" +
	"\vnormal_task\x18\x01 \x01(\tR\n" +
	"normalTask\x12\x1d\n" +
	"\n" +
	"delay_task\x18\x02 \x01(\tR\tdelayTask\x12\x1f\n" +
	"\vperiod_task\x18\x03 \x01(\tR\n" +
	"periodTask\x12\x1d\n" +
	"\n" +
	"redis_addr\x18\x04 \x01(\tR\tredisAddr\x12%\n" +
	"\x0eredis_password\x18\x05 \x01(\tR\rredisPassword\x12\x19\n" +
	"\bredis_db\x18\x06 \x01(\x05R\aredisDb\x12\x1b\n" +
	"\thttp_addr\x18\a \x01(\tR\bhttpAddr\x1aF\n" +
	"\tThirdHttp\x12\x18\n" +
	"\atimeout\x18\x01 \x01(\x05R\atimeout\x12\x1f\n" +
	"\vretry_count\x18\x02 \x01(\x05R\n" +
	"retryCount\x1a\xa7\x01\n" +
	"\x05TxSms\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\tR\x05appId\x12\x1b\n" +
	"\tsecret_id\x18\x02 \x01(\tR\bsecretId\x12\x1d\n" +
	"\n" +
	"secret_key\x18\x03 \x01(\tR\tsecretKey\x12\x12\n" +
	"\x04sign\x18\x04 \x01(\tR\x04sign\x12\x1f\n" +
	"\vtemplate_id\x18\x05 \x01(\tR\n" +
	"templateId\x12\x16\n" +
	"\x06domain\x18\x06 \x01(\tR\x06domain\x1a{\n" +
	"\x05Email\x12\x12\n" +
	"\x04host\x18\x01 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x12\n" +
	"\x04from\x18\x05 \x01(\tR\x04from\x1a\xf1\x01\n" +
	"\x06AliPay\x12\x15\n" +
	"\x06app_id\x18\x01 \x01(\tR\x05appId\x12&\n" +
	"\x0fapp_private_key\x18\x02 \x01(\tR\rappPrivateKey\x12$\n" +
	"\x0eali_public_key\x18\x03 \x01(\tR\faliPublicKey\x12\x1d\n" +
	"\n" +
	"notify_url\x18\x04 \x01(\tR\tnotifyUrl\x12\x1d\n" +
	"\n" +
	"return_url\x18\x05 \x01(\tR\treturnUrl\x12#\n" +
	"\ris_production\x18\x06 \x01(\bR\fisProduction\x12\x1f\n" +
	"\vencrypt_key\x18\a \x01(\tR\n" +
	"encryptKey\x1a\x8b\x02\n" +
	"\fUserVipPrice\x12\x1f\n" +
	"\vmonth_price\x18\x01 \x01(\x05R\n" +
	"monthPrice\x120\n" +
	"\x14discount_month_price\x18\x02 \x01(\x05R\x12discountMonthPrice\x12#\n" +
	"\rquarter_price\x18\x03 \x01(\x05R\fquarterPrice\x124\n" +
	"\x16discount_quarter_price\x18\x04 \x01(\x05R\x14discountQuarterPrice\x12\x1d\n" +
	"\n" +
	"year_price\x18\x05 \x01(\x05R\tyearPrice\x12.\n" +
	"\x13discount_year_price\x18\x06 \x01(\x05R\x11discountYearPrice\x1a\xb3\x01\n" +
	"\x05JPush\x12\x17\n" +
	"\aapp_key\x18\x01 \x01(\tR\x06appKey\x12\x1d\n" +
	"\n" +
	"app_secret\x18\x02 \x01(\tR\tappSecret\x12!\n" +
	"\fapi_endpoint\x18\x03 \x01(\tR\vapiEndpoint\x12\x18\n" +
	"\atimeout\x18\x04 \x01(\x05R\atimeout\x12\x1b\n" +
	"\tmax_retry\x18\x05 \x01(\x05R\bmaxRetry\x12\x18\n" +
	"\aenabled\x18\x06 \x01(\bR\aenabled\x1a\xcb\x02\n" +
	"\n" +
	"PushConfig\x12)\n" +
	"\x10enabled_services\x18\x01 \x03(\tR\x0fenabledServices\x12'\n" +
	"\x0fdefault_service\x18\x02 \x01(\tR\x0edefaultService\x12'\n" +
	"\x0fenable_fallback\x18\x03 \x01(\bR\x0eenableFallback\x122\n" +
	"\x15health_check_interval\x18\x04 \x01(\x05R\x13healthCheckInterval\x126\n" +
	"\x17enable_domestic_routing\x18\x05 \x01(\bR\x15enableDomesticRouting\x12)\n" +
	"\x10domestic_service\x18\x06 \x01(\tR\x0fdomesticService\x12)\n" +
	"\x10overseas_service\x18\a \x01(\tR\x0foverseasService\x1a\xa5\x01\n" +
	"\bFirebase\x12\x1d\n" +
	"\n" +
	"project_id\x18\x01 \x01(\tR\tprojectId\x12\x18\n" +
	"\atimeout\x18\x02 \x01(\x05R\atimeout\x12\x18\n" +
	"\aenabled\x18\x03 \x01(\bR\aenabled\x12)\n" +
	"\x10credentials_file\x18\x04 \x01(\tR\x0fcredentialsFile\x12\x1b\n" +
	"\tmock_mode\x18\x05 \x01(\bR\bmockMode\"\x90\x04\n" +
	"\x04Auth\x12\x1d\n" +
	"\n" +
	"secret_key\x18\x01 \x01(\tR\tsecretKey\x12\x16\n" +
	"\x06issuer\x18\x02 \x01(\tR\x06issuer\x12\x1b\n" +
	"\texpire_at\x18\x03 \x01(\x05R\bexpireAt\x12!\n" +
	"\fredirect_url\x18\x06 \x01(\tR\vredirectUrl\x12?\n" +
	"\fgoogle_oauth\x18\x04 \x01(\v2\x1c.kratos.api.Auth.GoogleOAuthR\vgoogleOauth\x12<\n" +
	"\vapple_oauth\x18\x05 \x01(\v2\x1b.kratos.api.Auth.AppleOAuthR\n" +
	"appleOauth\x1ar\n" +
	"\vGoogleOAuth\x12\x1b\n" +
	"\tclient_id\x18\x01 \x01(\tR\bclientId\x12#\n" +
	"\rclient_secret\x18\x02 \x01(\tR\fclientSecret\x12!\n" +
	"\fredirect_uri\x18\x03 \x01(\tR\vredirectUri\x1a\x9d\x01\n" +
	"\n" +
	"AppleOAuth\x12\x1b\n" +
	"\tclient_id\x18\x01 \x01(\tR\bclientId\x12\x17\n" +
	"\ateam_id\x18\x02 \x01(\tR\x06teamId\x12\x15\n" +
	"\x06key_id\x18\x03 \x01(\tR\x05keyId\x12\x1f\n" +
	"\vprivate_key\x18\x04 \x01(\tR\n" +
	"privateKey\x12!\n" +
	"\fredirect_uri\x18\x05 \x01(\tR\vredirectUri*)\n" +
	"\aEnvType\x12\b\n" +
	"\x04Test\x10\x00\x12\a\n" +
	"\x03Pre\x10\x01\x12\v\n" +
	"\aRelease\x10\x02B1Z/github.com/wlnil/life-log-be/internal/conf;confb\x06proto3"

var (
	file_conf_conf_proto_rawDescOnce sync.Once
	file_conf_conf_proto_rawDescData []byte
)

func file_conf_conf_proto_rawDescGZIP() []byte {
	file_conf_conf_proto_rawDescOnce.Do(func() {
		file_conf_conf_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_conf_conf_proto_rawDesc), len(file_conf_conf_proto_rawDesc)))
	})
	return file_conf_conf_proto_rawDescData
}

var file_conf_conf_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_conf_conf_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_conf_conf_proto_goTypes = []any{
	(EnvType)(0),                // 0: kratos.api.EnvType
	(*Bootstrap)(nil),           // 1: kratos.api.Bootstrap
	(*Server)(nil),              // 2: kratos.api.Server
	(*Data)(nil),                // 3: kratos.api.Data
	(*Auth)(nil),                // 4: kratos.api.Auth
	(*Server_HTTP)(nil),         // 5: kratos.api.Server.HTTP
	(*Server_GRPC)(nil),         // 6: kratos.api.Server.GRPC
	(*Data_Database)(nil),       // 7: kratos.api.Data.Database
	(*Data_Redis)(nil),          // 8: kratos.api.Data.Redis
	(*Data_LocalizePath)(nil),   // 9: kratos.api.Data.LocalizePath
	(*Data_Qiniu)(nil),          // 10: kratos.api.Data.Qiniu
	(*Data_Task)(nil),           // 11: kratos.api.Data.Task
	(*Data_ThirdHttp)(nil),      // 12: kratos.api.Data.ThirdHttp
	(*Data_TxSms)(nil),          // 13: kratos.api.Data.TxSms
	(*Data_Email)(nil),          // 14: kratos.api.Data.Email
	(*Data_AliPay)(nil),         // 15: kratos.api.Data.AliPay
	(*Data_UserVipPrice)(nil),   // 16: kratos.api.Data.UserVipPrice
	(*Data_JPush)(nil),          // 17: kratos.api.Data.JPush
	(*Data_PushConfig)(nil),     // 18: kratos.api.Data.PushConfig
	(*Data_Firebase)(nil),       // 19: kratos.api.Data.Firebase
	(*Auth_GoogleOAuth)(nil),    // 20: kratos.api.Auth.GoogleOAuth
	(*Auth_AppleOAuth)(nil),     // 21: kratos.api.Auth.AppleOAuth
	(*durationpb.Duration)(nil), // 22: google.protobuf.Duration
}
var file_conf_conf_proto_depIdxs = []int32{
	2,  // 0: kratos.api.Bootstrap.server:type_name -> kratos.api.Server
	3,  // 1: kratos.api.Bootstrap.data:type_name -> kratos.api.Data
	4,  // 2: kratos.api.Bootstrap.auth:type_name -> kratos.api.Auth
	5,  // 3: kratos.api.Server.http:type_name -> kratos.api.Server.HTTP
	6,  // 4: kratos.api.Server.grpc:type_name -> kratos.api.Server.GRPC
	7,  // 5: kratos.api.Data.database:type_name -> kratos.api.Data.Database
	8,  // 6: kratos.api.Data.redis:type_name -> kratos.api.Data.Redis
	0,  // 7: kratos.api.Data.env:type_name -> kratos.api.EnvType
	9,  // 8: kratos.api.Data.localize_path:type_name -> kratos.api.Data.LocalizePath
	10, // 9: kratos.api.Data.qiniu:type_name -> kratos.api.Data.Qiniu
	11, // 10: kratos.api.Data.task:type_name -> kratos.api.Data.Task
	12, // 11: kratos.api.Data.third_http:type_name -> kratos.api.Data.ThirdHttp
	13, // 12: kratos.api.Data.tx_sms:type_name -> kratos.api.Data.TxSms
	14, // 13: kratos.api.Data.email:type_name -> kratos.api.Data.Email
	15, // 14: kratos.api.Data.ali_pay:type_name -> kratos.api.Data.AliPay
	16, // 15: kratos.api.Data.user_vip_price:type_name -> kratos.api.Data.UserVipPrice
	17, // 16: kratos.api.Data.jpush:type_name -> kratos.api.Data.JPush
	18, // 17: kratos.api.Data.push_config:type_name -> kratos.api.Data.PushConfig
	19, // 18: kratos.api.Data.firebase:type_name -> kratos.api.Data.Firebase
	20, // 19: kratos.api.Auth.google_oauth:type_name -> kratos.api.Auth.GoogleOAuth
	21, // 20: kratos.api.Auth.apple_oauth:type_name -> kratos.api.Auth.AppleOAuth
	22, // 21: kratos.api.Server.HTTP.timeout:type_name -> google.protobuf.Duration
	22, // 22: kratos.api.Server.GRPC.timeout:type_name -> google.protobuf.Duration
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_conf_conf_proto_init() }
func file_conf_conf_proto_init() {
	if File_conf_conf_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_conf_conf_proto_rawDesc), len(file_conf_conf_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conf_conf_proto_goTypes,
		DependencyIndexes: file_conf_conf_proto_depIdxs,
		EnumInfos:         file_conf_conf_proto_enumTypes,
		MessageInfos:      file_conf_conf_proto_msgTypes,
	}.Build()
	File_conf_conf_proto = out.File
	file_conf_conf_proto_goTypes = nil
	file_conf_conf_proto_depIdxs = nil
}
