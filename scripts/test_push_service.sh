#!/bin/bash

# Push Service 测试运行脚本
# 用于运行推送服务的各种测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go_env() {
    if ! command -v go &> /dev/null; then
        print_error "Go 未安装或不在PATH中"
        exit 1
    fi
    
    print_info "Go 版本: $(go version)"
}

# 检查依赖
check_dependencies() {
    print_info "检查测试依赖..."
    
    # 检查testify
    if ! go list -m github.com/stretchr/testify &> /dev/null; then
        print_warning "testify 未安装，正在安装..."
        go get github.com/stretchr/testify
    fi
    
    print_success "依赖检查完成"
}

# 运行单元测试
run_unit_tests() {
    print_info "运行单元测试（跳过集成测试）..."
    
    cd internal/service
    
    if go test -v -short -timeout 30s; then
        print_success "单元测试通过"
    else
        print_error "单元测试失败"
        return 1
    fi
    
    cd - > /dev/null
}

# 运行性能测试
run_benchmark_tests() {
    print_info "运行性能基准测试..."
    
    cd internal/service
    
    if go test -bench=. -benchmem -timeout 60s; then
        print_success "性能测试完成"
    else
        print_error "性能测试失败"
        return 1
    fi
    
    cd - > /dev/null
}

# 运行并发测试
run_concurrency_tests() {
    print_info "运行并发安全测试..."
    
    cd internal/service
    
    if go test -v -run TestConcurrency -timeout 60s; then
        print_success "并发测试通过"
    else
        print_error "并发测试失败"
        return 1
    fi
    
    cd - > /dev/null
}

# 运行覆盖率测试
run_coverage_tests() {
    print_info "运行测试覆盖率分析..."
    
    cd internal/service
    
    # 生成覆盖率报告
    if go test -coverprofile=coverage.out -timeout 30s -short; then
        # 显示覆盖率
        coverage=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
        print_success "测试覆盖率: $coverage"
        
        # 生成HTML报告
        go tool cover -html=coverage.out -o coverage.html
        print_info "HTML覆盖率报告已生成: internal/service/coverage.html"
    else
        print_error "覆盖率测试失败"
        return 1
    fi
    
    cd - > /dev/null
}

# 运行集成测试（需要真实配置）
run_integration_tests() {
    print_warning "运行集成测试（需要真实的推送服务配置）..."
    print_warning "请确保已在测试文件中配置真实的推送服务参数"
    
    read -p "是否继续运行集成测试？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cd internal/service
        
        if go test -v -run TestIntegration -timeout 120s; then
            print_success "集成测试通过"
        else
            print_error "集成测试失败（可能是配置问题）"
            return 1
        fi
        
        cd - > /dev/null
    else
        print_info "跳过集成测试"
    fi
}

# 运行特定测试
run_specific_test() {
    local test_pattern=$1
    print_info "运行特定测试: $test_pattern"
    
    cd internal/service
    
    if go test -v -run "$test_pattern" -timeout 60s; then
        print_success "测试 $test_pattern 通过"
    else
        print_error "测试 $test_pattern 失败"
        return 1
    fi
    
    cd - > /dev/null
}

# 清理测试文件
cleanup() {
    print_info "清理测试生成的文件..."
    
    rm -f internal/service/coverage.out
    rm -f internal/service/coverage.html
    
    print_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "Push Service 测试运行脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  unit        运行单元测试"
    echo "  bench       运行性能基准测试"
    echo "  concurrency 运行并发测试"
    echo "  coverage    运行覆盖率测试"
    echo "  integration 运行集成测试（需要真实配置）"
    echo "  all         运行所有测试（除集成测试）"
    echo "  specific    运行特定测试（需要提供测试名称模式）"
    echo "  cleanup     清理测试生成的文件"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 unit                    # 运行单元测试"
    echo "  $0 specific JPush          # 运行所有JPush相关测试"
    echo "  $0 specific TestSend       # 运行所有Send相关测试"
    echo ""
    echo "注意:"
    echo "  - 集成测试需要真实的推送服务配置"
    echo "  - 请在运行前确保已安装必要的依赖"
}

# 主函数
main() {
    case "${1:-help}" in
        "unit")
            check_go_env
            check_dependencies
            run_unit_tests
            ;;
        "bench")
            check_go_env
            check_dependencies
            run_benchmark_tests
            ;;
        "concurrency")
            check_go_env
            check_dependencies
            run_concurrency_tests
            ;;
        "coverage")
            check_go_env
            check_dependencies
            run_coverage_tests
            ;;
        "integration")
            check_go_env
            check_dependencies
            run_integration_tests
            ;;
        "all")
            check_go_env
            check_dependencies
            run_unit_tests
            run_benchmark_tests
            run_concurrency_tests
            run_coverage_tests
            print_success "所有测试完成"
            ;;
        "specific")
            if [ -z "$2" ]; then
                print_error "请提供测试名称模式"
                echo "示例: $0 specific JPush"
                exit 1
            fi
            check_go_env
            check_dependencies
            run_specific_test "$2"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
