# 使用特定版本的 Go 镜像
FROM golang:1.21 AS builder

WORKDIR /app
COPY . .

# 设置 GOPROXY 并构建项目
ENV GOPROXY=https://goproxy.cn

# 使用特定版本的 Alpine 镜像
FROM alpine:latest AS runner

# 更换镜像源并安装时区数据库
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
    && apk update && apk add --no-cache tzdata \
    && ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

WORKDIR /app

# 复制构建产物和配置文件
COPY --from=builder /app/bin/life-log-be .
COPY --from=builder /app/active.en.toml /app/
COPY --from=builder /app/active.zh.toml /app/

EXPOSE 80

CMD ["./life-log-be", "-conf", "/data/conf/config.yaml"]