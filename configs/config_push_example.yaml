# 推送通知系统配置示例
# 将以下配置添加到你的 configs/config.yaml 文件中

data:
  # ... 现有配置保持不变
  
  # 极光推送配置（中国大陆用户）
  jpush:
    app_key: "your_jpush_app_key"           # 极光推送应用Key
    app_secret: "your_jpush_app_secret"     # 极光推送应用Secret
    
  # Apple推送通知服务配置（海外iOS用户）
  apns:
    key_id: "your_apns_key_id"              # APNs认证密钥ID
    team_id: "your_apple_team_id"           # Apple开发者团队ID
    bundle_id: "com.yourcompany.yourapp"    # 应用Bundle ID
    key_path: "/path/to/AuthKey_XXXXXXXXXX.p8"  # APNs认证密钥文件路径
    is_product: false                       # 是否为生产环境（false=开发环境，true=生产环境）
    
  # Firebase云消息配置（海外Android用户）
  fcm:
    server_key: "your_fcm_server_key"       # FCM服务器密钥
    


# 推送服务获取方式说明：

# 1. 极光推送（JPush）
#    - 注册地址：https://www.jiguang.cn/
#    - 创建应用后在"应用设置"中获取AppKey和AppSecret
#    - 适用于中国大陆用户

# 2. Apple推送通知服务（APNs）
#    - 登录Apple Developer：https://developer.apple.com/
#    - 创建APNs认证密钥：Certificates, Identifiers & Profiles > Keys
#    - 下载.p8密钥文件并记录Key ID和Team ID
#    - 适用于海外iOS用户

# 3. Firebase云消息（FCM）
#    - 访问Firebase控制台：https://console.firebase.google.com/
#    - 创建项目并启用Cloud Messaging
#    - 在项目设置 > 云消息传递中获取服务器密钥
#    - 适用于海外Android用户

# 4. OneSignal
#    - 注册地址：https://onesignal.com/
#    - 创建应用后在Settings > Keys & IDs中获取App ID和API Key
#    - 作为所有平台的备选推送服务

# 环境配置说明：
# - 开发环境：is_product: false，使用APNs沙盒环境
# - 生产环境：is_product: true，使用APNs生产环境
# - 确保密钥文件路径正确且应用有读取权限
# - 建议使用环境变量或密钥管理服务存储敏感信息

# 安全建议：
# 1. 不要将密钥直接写在配置文件中提交到版本控制
# 2. 使用环境变量：
#    jpush:
#      app_key: ${JPUSH_APP_KEY}
#      app_secret: ${JPUSH_APP_SECRET}
# 3. 或使用密钥管理服务（如AWS Secrets Manager、Azure Key Vault等）
