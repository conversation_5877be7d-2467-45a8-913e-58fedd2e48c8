server:
  http:
    addr: 0.0.0.0:8001
    timeout: 1s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
data:
  database:
    driver: mysql
    source: root:123456asdf!A@tcp(192.168.0.195:3306)/db_life_habit
  redis:
    addr: 192.168.0.195:6379
    username: "default"
    password: "123456"
    db: 0
  env: 0
  localize_path:
    zh: ./active.zh.toml
  qiniu:
    access_key: "Uy0QYsCVNCoqSJFt_OLbRmXzWc1T77P7KMbY_xb1"
    secret_key: "CAg9siyEYOGTlc9-amP2tYASFMcw2mySTA2460Us"
    bucket: "life-log-privacy"
    token_expires: 120
    addr: "https://storage-privacy.dxwvv.com"
    public_addr: "https://storage.dxwvv.com"
    public_bucket: "life-log-public"
  task:
    normal_task: "normal_task"
    delay_task: "delay_task"
    period_task: "period_task"
    redis_addr: "192.168.0.192:6379"
    redis_password: "123456"
    redis_db: 2
    http_addr: "http://127.0.0.1:8001"
  third_http:
    timeout: 10
    retry_count: 3
  is_display_planet: true
  sentry_dsn: "https://<EMAIL>/4506362684375040"
  tx_sms:
    app_id: "1400879638"
    secret_id: "AKIDiGajkX6RZFhz2rSvCHArNgkcR4ILH3cx"
    secret_key: "DBwZzCD5qR0TnwuoaDj5FDzYu3zsUu78"
    template_id: "2037099"
    sign: "成都但行无为科技"
    domain: "https://sms.tencentcloudapi.com"
  use_sms_channel: tx
  sms_code_expires: 300
  email:
    host: "smtpdm.aliyun.com"
    port: 25
    from: "LifeHabit<<EMAIL>>"
    username: "<EMAIL>"
    password: "47h9Ux3yTCuF8bn"
  ali_pay:
    app_id: "9021000143679091"
    app_private_key: "MIIEpAIBAAKCAQEAtaYlpjU+76A0HhF1nRNOUu/9ZnMXpvoh2B1qwNOwOP9SxezAqOK0MVwk/za/NG5fXNOD39Ew5gwErpEWWLlZGu929VVg8f5/VGixBaoNmBcMW+ktdzRV5CG1NMEUqDHvGQr0jG+yOuF2q+q2/aqrBCO/Fg8yZY1xssWQ/nCg2mxC3N1O6si/vDeB1zgANdTkI2NMTk2Mkoq63vBdUdOYz35tlryNiI8GvTV7DnEKSssAnEdUAEZIIm88EoA4We0ra3FwkO65iS79nuCT1KWvDBb3xy3FLVlk70L1i06sw035KtaR9X71M7xShGF9FlDeo1NojQCLqZc9Ss8X3Qk3LwIDAQABAoIBAC5mq7ViYppJnSaVJSsjtF1BSsSx37faq2yOTyfEq35T0mwWxzwc9RSqyhSbgoo9K4UFzukMgeYx6bxB+aheaGGWDBgtYB9Q+Guydo3Q27ofIRwVW3TY3Jvy9Fvuen1gjAgkoUWCfs/Fqk/OHEJ+J0NeckCLTz9bu1CEYT5tvNkuYkPULhFRLX51U8kBrbZRiU7ILVKEm9lNJRGkA+MyLwiAHE6iPAqyy9AaahpVGSvnmG1NwXS7lhA0FwPZoz1JnrQxnRi+pSAW2Tt0/tQSDULnyp6PwHKFmJYz1cCmNZTq+jOp1E9VDiqJf741QtTAZMD/Hv4PschttdscV5NuOwECgYEA35Sl/JSe4P/1P0R6C201xj+6UktTNoLzzRAU9sa3duKiDiA4rXUoveGckPOEB++E9IqM9wjryWYM+4/dIWYMjWaUEmNuU09YTclDKNQM4pKV8aoi0x/aAENvbh/7Hs31GuKzEvKYaWaEgA1rSODrUi/pMMJGCQXPprG3aK07m68CgYEAz/z9KyLXLMZmZ/jK2fopxfD4yGzMVTS32LVpAoQ6PRKwx2e8J3GRpPJRRO1Slb0lCtjyXYozMsEPjPR1kIn0CbjUO/bp7hUT2WCo5dM2b9KAX6Q7W/WkSmsTCVij8KJpQl9CZrm5RlZUVkjTiW35D5ZbzEXBgESE/PbMMYmZfIECgYEAlcd/azYlOZY/NaMqH32KCMWojHjpxhr0OOwNH0UAoyQwbEYpsgpKwOnjeTepu6c56pjm1pejXch/6gb2nOlAf3m4uJjjz+vXvESYOUHVX/PTf5H6wnUUFhkXDS9fi8Nt8BTu4kKi1/lVynMy7B/Bhc2S6pc7U9AQzBtCQ13KEQECgYBlIuQIobx4LVGGpVgefqLpea+/5gfGNwhe/He/nYSkRf5oOG0p0PO9fYc/ORe7cGoeyDeyfI1A1Ng29nhkfHCKebHaJsrB1sX8IiB/ASGrZnS+DSJAKUZmWYK8PLobPR5qA+BKC22oYjpIaslSPoOprLeesRzSvn7cczX/sKVOAQKBgQDLZ7kjvJ6dBr4qADVuB4tBJ075ffYvxYN+2Ian9VooepBdifj9Ad+b2n6kwDIf3WTmlU4x+2JzMevx+0xrjEog00bBYTwcWVdGEqm+5nOqSeadZjQCZ3uBoR2k9V4BpcDVxeL+Cofq/XS0DNj14SP+GLExEiNhldOaY/W1SlcB+Q=="
    ali_public_key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo9Ec+U4c1rtQ5EDxR1wZURE9ikBqxgq6oYD4ooL/u6xqHJfYZMaLAaZTzcZ/6rVPob4nWEx2jUuq85DtWThXoERyBw6GF50ZrSOsTxJLDxYmILSBfO9SWolzXVFTRfNWC8y5LuT2XZaTqxFn+oppCIQm3FGlZQs+V7f0lsT/vZwfryJBlTSr4cb3rd7ckRbEXyW4mgg0XV7s8IcX8zTKzd5nOL5pAsHvCNgFHn3BWreLQJR42+O+/GroNQ1Vy82vVrHTPb2U0BpcZ03wnwu1wc2bEY88WA17zP4wBpxfmtKDepn9/oTxqwTBWZ9Ta1/1p0EvaFnCgixkaS2FmKZxLwIDAQAB"
    notify_url: "http://127.0.0.1:8001/api/v1/order/callback"
    return_url: "http://127.0.0.1:8001/api/v1/order/return"
    is_production: false
    encrypt_key: "iotxR/d234tfAwom/UaSqiQ=="
  user_vip_price:
    month_price: 1900
    discount_month_price: 900
    quarter_price: 3900
    discount_quarter_price: 1800
    year_price: 9900
    discount_year_price: 5900
  # 推送服务配置
  jpush:
    app_key: "24b07b1a7ad6ae1b1016ad92"
    app_secret: "c03a896dd61026e35b076665"
    api_endpoint: "https://api.jpush.cn/v3/push"
    timeout: 30
    max_retry: 3
    enabled: true
  push_config:
    enabled_services: ["jpush", "fcm", "apns"]
    default_service: "jpush"
    enable_fallback: true
    health_check_interval: 300
    enable_domestic_routing: true
    domestic_service: "jpush"
    overseas_service: "fcm"
  firebase:
    project_id: "lifehabit-2dd0d"
    timeout: 10
    enabled: true
    credentials_file: "./configs/firebase-service-account.json"
    mock_mode: false  # 根据环境自动判断，Test环境为true，Release环境为false
auth:
  secret_key: "8eae9afa51314_123ACHa1.."
  expire_at: 2592000
  issuer: project.life_log
  redirect_url: "https://your-frontend-app.com/auth/callback"
  google_oauth:
    client_id: "************"
    client_secret: "AIzaSyB5S46RJHO8nP8pqcaOXmSNoLqQkWQuBeU"
    redirect_uri: "http://localhost:8001/api/auth/google/callback"
  apple_oauth:
    client_id: "com.dxwvv.LifeHabit.service"
    team_id: "3HYS7DKY3Z"
    key_id: "YK2PK93XPB"
    private_key: |
*****************************************************************************************************************************************************************************************************************************************************************************************************
    redirect_uri: "http://localhost:8001/api/auth/apple/callback"
