["Internal Server Error"]
description = "Internal Server Error"
hash = "sha1-476ab18c43c93ac8083bd0eea1a5f70d13cc9e60"
other = "服务繁忙，请稍后再试"

["Params Error"]
description = "Params Error"
hash = "sha1-ba6f3a242504d089e1d9ed076377a8442dc00a09"
other = "参数错误"

["password is not allow"]
description = "password is not allow"
hash = "sha1-251e7887c5f18937d822412d75c17ea3adc32977"
other = "密码不符合规范"

["password is not same"]
description = "password is not same"
hash = "sha1-cf898498764c26a5f4a06bb5d433e25fc6acca28"
other = "两次密码不一致"

["password is wrong"]
description = "password is wrong"
hash = "sha1-d1483280e3c81bc1b57ce3c4f8f336a03e630413"
other = "密码错误"

["pause user habit error"]
description = "pause user habit error"
hash = "sha1-71cc854e172c23780c0d83ff3cc1c3103522ae6a"
other = "习惯「暂停」失败"

["phone is registered"]
description = "phone is registered"
hash = "sha1-a381314fe9c805d9d91ce4f4ecc21d97fd0d2860"
other = "手机号已注册"

["phone is used"]
description = "phone is used"
hash = "sha1-007da4f93ff8200a72ba21681fe9299532412e61"
other = "手机号已被使用"

["reckon user habit error"]
description = "reckon user habit error"
hash = "sha1-27617b10feec15f6f6284babb1f437ceb9dadb29"
other = "习惯「计时」失败"

["recover user habit error"]
description = "recover user habit error"
hash = "sha1-7a061d553e1d36dad1228fa5e15eaf3d675537e9"
other = "习惯「恢复」失败"

["user can not unfollow self"]
description = "user can not unfollow self"
hash = "sha1-aab1ea4155eb2659320ae3bd36421e08fb457e4c"
other = "不能取消关注自己"

["user or password is wrong"]
description = "user or password is wrong"
hash = "sha1-1508807d7a50dfc55873d88147b99d9cd085dd51"
other = "账户或密码错误"

["user_name is exited"]
description = "user_name is exited"
hash = "sha1-0a04dacff232d0fcce502936db6ccf52d56f85ff"
other = "{{.Name}} 昵称已存在"

["verify_code is expired"]
description = "verify_code is expired"
hash = "sha1-dca4d9e64bafcf701914d590b57cdea9cc509183"
other = "验证码已过期"

["verify_code is limited"]
description = "verify_code is limited"
hash = "sha1-485fb5b67e03557a1e278f83db83ac911f65fa8e"
other = "发送验证码频率过高"

["verify_code is wrong"]
description = "verify_code is wrong"
hash = "sha1-9641d848a33e0aceeecf0621182b7a4261672b29"
other = "验证码错误"

["user is not vip"]
description = "user is not vip"
hash = "sha1-ac95afa1848eefafa4937107107abda610b8df21"
other = "当前用户不是 VIP"

["third party token is wrong"]
description = "third party token is wrong"
hash = "sha1-b2c4c1c7e3c9e3c9e3c9e3c9e3c9e3c9e3c9e3c9"
other = "第三方登录验证失败"
