-- 为现有用户生成UID的数据迁移脚本
-- 执行时间：2025-08-21
-- 说明：为所有现有用户生成唯一的UID

-- 创建临时存储过程来生成UID
DELIMITER $$

CREATE PROCEDURE GenerateUIDForExistingUsers()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE user_id INT;
    DECLARE new_uid VARCHAR(12);
    DECLARE uid_exists INT;
    DECLARE attempt_count INT;
    
    -- 声明游标
    DECLARE user_cursor CURSOR FOR 
        SELECT id FROM tb_user WHERE uid = '' OR uid IS NULL;
    
    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 打开游标
    OPEN user_cursor;
    
    -- 循环处理每个用户
    user_loop: LOOP
        FETCH user_cursor INTO user_id;
        IF done THEN
            LEAVE user_loop;
        END IF;
        
        -- 为当前用户生成唯一UID
        SET attempt_count = 0;
        generate_uid: LOOP
            SET attempt_count = attempt_count + 1;
            
            -- 生成10位随机UID（字母数字组合）
            SET new_uid = CONCAT(
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1),
                SUBSTRING('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', FLOOR(1 + RAND() * 36), 1)
            );
            
            -- 检查UID是否已存在
            SELECT COUNT(*) INTO uid_exists FROM tb_user WHERE uid = new_uid;
            
            -- 如果UID不存在或尝试次数过多，跳出循环
            IF uid_exists = 0 OR attempt_count > 10 THEN
                LEAVE generate_uid;
            END IF;
        END LOOP generate_uid;
        
        -- 如果尝试次数过多，使用时间戳后缀
        IF attempt_count > 10 THEN
            SET new_uid = CONCAT(
                SUBSTRING(new_uid, 1, 6),
                LPAD(UNIX_TIMESTAMP() % 10000, 4, '0')
            );
        END IF;
        
        -- 更新用户UID
        UPDATE tb_user SET uid = new_uid, updated_at = UNIX_TIMESTAMP() WHERE id = user_id;
        
    END LOOP user_loop;
    
    -- 关闭游标
    CLOSE user_cursor;
    
END$$

DELIMITER ;

-- 执行存储过程
CALL GenerateUIDForExistingUsers();

-- 删除临时存储过程
DROP PROCEDURE GenerateUIDForExistingUsers;

-- 验证结果
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN uid != '' AND uid IS NOT NULL THEN 1 END) as users_with_uid,
    COUNT(CASE WHEN uid = '' OR uid IS NULL THEN 1 END) as users_without_uid
FROM tb_user 
WHERE status != 3; -- 排除已删除用户

-- 检查UID唯一性
SELECT uid, COUNT(*) as count 
FROM tb_user 
WHERE uid != '' AND uid IS NOT NULL 
GROUP BY uid 
HAVING COUNT(*) > 1;
