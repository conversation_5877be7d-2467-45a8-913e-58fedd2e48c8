-- 为推送任务表添加失败处理相关字段
-- 执行时间：2025-07-11
-- 说明：支持渐进式失败处理和防重复执行机制

-- 添加失败处理相关字段
ALTER TABLE tb_push_task 
ADD COLUMN IF NOT EXISTS failure_count INT DEFAULT 0 COMMENT '连续失败次数',
ADD COLUMN IF NOT EXISTS max_failures INT DEFAULT 5 COMMENT '最大失败次数',
ADD COLUMN IF NOT EXISTS last_failure_time BIGINT DEFAULT 0 COMMENT '最后失败时间',
ADD COLUMN IF NOT EXISTS failure_reason VARCHAR(50) DEFAULT '' COMMENT '失败原因分类',
ADD COLUMN IF NOT EXISTS version INT DEFAULT 1 COMMENT '乐观锁版本号';

-- 添加性能优化索引
CREATE INDEX IF NOT EXISTS idx_task_status_version ON tb_push_task (status, version);
CREATE INDEX IF NOT EXISTS idx_failure_recovery ON tb_push_task (failure_count, last_failure_time);
CREATE INDEX IF NOT EXISTS idx_task_execution ON tb_push_task (scheduled_time, status, failure_count);

-- 更新现有记录的默认值
UPDATE tb_push_task SET 
    failure_count = 0,
    max_failures = 5,
    last_failure_time = 0,
    failure_reason = '',
    version = 1
WHERE failure_count IS NULL OR max_failures IS NULL OR version IS NULL;
