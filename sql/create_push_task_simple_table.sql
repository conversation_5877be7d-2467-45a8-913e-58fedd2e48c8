-- 创建简化推送任务表
-- 执行时间：2025-07-09
-- 说明：简化的推送任务系统，支持"创建时调度，执行时验证"模式

CREATE TABLE IF NOT EXISTS `tb_push_task_simple` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `task_type` varchar(50) NOT NULL COMMENT '任务类型：habit_reminder, system_notice',
  `related_id` int(11) NOT NULL COMMENT '关联数据ID（如 user_habit_id）',
  `title` varchar(255) DEFAULT '' COMMENT '推送标题（可为空，执行时生成）',
  `content` text COMMENT '推送内容（可为空，执行时生成）',
  `scheduled_time` bigint(20) NOT NULL COMMENT '计划执行时间戳',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待执行，1-成功，2-失败，3-取消',
  `is_recurring` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否周期任务',
  `repeat_rule` varchar(20) DEFAULT '' COMMENT '重复规则：daily, weekly, monthly',
  `reminder_time` varchar(10) DEFAULT '' COMMENT '提醒时间 "07:30"',
  `execute_time` bigint(20) DEFAULT '0' COMMENT '实际执行时间',
  `error_msg` text COMMENT '错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_user_task_type` (`user_id`, `task_type`),
  KEY `idx_scheduled_time_status` (`scheduled_time`, `status`),
  KEY `idx_related_id` (`related_id`),
  KEY `idx_status_scheduled` (`status`, `scheduled_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简化推送任务表';

-- 为查询性能添加复合索引
CREATE INDEX IF NOT EXISTS `idx_pending_tasks` ON `tb_push_task_simple` (`status`, `scheduled_time`, `task_type`);
CREATE INDEX IF NOT EXISTS `idx_user_habit_tasks` ON `tb_push_task_simple` (`user_id`, `related_id`, `task_type`);
