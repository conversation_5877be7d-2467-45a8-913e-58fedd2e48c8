-- 为 tb_push_task 表添加周期性任务相关字段
-- 执行时间：2025-07-08
-- 说明：支持周期性推送任务的自动续期功能

-- 添加周期性任务相关字段
ALTER TABLE tb_push_task
ADD COLUMN IF NOT EXISTS is_recurring TINYINT(1) DEFAULT 0 COMMENT '是否为周期性任务',
ADD COLUMN IF NOT EXISTS repeat_rule VARCHAR(20) DEFAULT '' COMMENT '重复规则：daily, weekly, monthly',
ADD COLUMN IF NOT EXISTS next_execution BIGINT(20) DEFAULT 0 COMMENT '下次执行时间',
ADD COLUMN IF NOT EXISTS reminder_time VARCHAR(10) DEFAULT '' COMMENT '原始提醒时间 HH:MM',
ADD COLUMN IF NOT EXISTS max_executions INT(11) DEFAULT 0 COMMENT '最大执行次数（0表示无限）',
ADD COLUMN IF NOT EXISTS execution_count INT(11) DEFAULT 0 COMMENT '已执行次数',
ADD COLUMN IF NOT EXISTS is_active TINYINT(1) DEFAULT 1 COMMENT '任务是否激活';

-- 添加索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_push_task_recurring ON tb_push_task (is_recurring, status, scheduled_time);
CREATE INDEX IF NOT EXISTS idx_push_task_next_execution ON tb_push_task (next_execution, status);
CREATE INDEX IF NOT EXISTS idx_push_task_user_habit ON tb_push_task (user_id, user_habit_id, task_type);

-- 更新现有任务为非周期性任务（保持向后兼容）
UPDATE tb_push_task 
SET is_recurring = 0, 
    repeat_rule = '', 
    next_execution = 0, 
    reminder_time = '', 
    max_executions = 0, 
    execution_count = 0 
WHERE is_recurring IS NULL;
