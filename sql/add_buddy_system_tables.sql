-- 搭子系统数据库迁移脚本
-- 执行时间：2025-08-21
-- 说明：添加用户UID字段，创建搭子邀请表和搭子关系表

-- 1. 为用户表添加UID字段
ALTER TABLE tb_user 
ADD COLUMN IF NOT EXISTS uid VARCHAR(12) DEFAULT '' COMMENT '用户唯一标识符，8-12位字母数字组合';

-- 为UID字段添加唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_uid ON tb_user (uid);

-- 2. 创建搭子邀请表
CREATE TABLE IF NOT EXISTS `tb_buddy_invitation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `from_user_id` int(11) NOT NULL COMMENT '发送邀请的用户ID',
  `to_user_id` int(11) NOT NULL COMMENT '接收邀请的用户ID',
  `message` text COMMENT '邀请消息',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '邀请状态：0-待处理，1-已接受，2-已拒绝，3-已过期',
  `responded_at` bigint(20) DEFAULT '0' COMMENT '响应时间',
  `expires_at` bigint(20) NOT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  KEY `idx_to_user_status` (`to_user_id`, `status`),
  KEY `idx_from_user_created` (`from_user_id`, `created_at`),
  KEY `idx_expires_at` (`expires_at`),
  UNIQUE KEY `idx_unique_invitation` (`from_user_id`, `to_user_id`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搭子邀请表';

-- 3. 创建搭子关系表
CREATE TABLE IF NOT EXISTS `tb_buddy_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `buddy_user_id` int(11) NOT NULL COMMENT '搭子用户ID',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '关系状态：1-正常，2-已删除，3-已屏蔽',
  `established_at` bigint(20) NOT NULL COMMENT '建立关系时间',
  `remark` varchar(100) DEFAULT '' COMMENT '备注名称',
  PRIMARY KEY (`id`),
  KEY `idx_user_status` (`user_id`, `status`),
  KEY `idx_buddy_user_status` (`buddy_user_id`, `status`),
  UNIQUE KEY `idx_unique_relation` (`user_id`, `buddy_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搭子关系表';

-- 4. 创建搭子设置表
CREATE TABLE IF NOT EXISTS `tb_buddy_setting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `created_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `max_buddy_count` int(11) NOT NULL DEFAULT '50' COMMENT '最大搭子数量限制',
  `allow_invitation` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否允许接收邀请',
  `auto_accept_invitation` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否自动接受邀请',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搭子设置表';

-- 5. 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_buddy_invitation_status_expires` ON `tb_buddy_invitation` (`status`, `expires_at`);
CREATE INDEX IF NOT EXISTS `idx_buddy_relation_established` ON `tb_buddy_relation` (`established_at`);

-- 6. 添加复合索引优化批量查询
CREATE INDEX IF NOT EXISTS `idx_buddy_relation_user_buddy_status` ON `tb_buddy_relation` (`user_id`, `buddy_user_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_buddy_invitation_from_to_status` ON `tb_buddy_invitation` (`from_user_id`, `to_user_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_buddy_invitation_from_status` ON `tb_buddy_invitation` (`from_user_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_user_habit_user_status` ON `tb_user_habit` (`user_id`, `status`);

-- 6. 为现有用户创建默认搭子设置
INSERT IGNORE INTO tb_buddy_setting (user_id, max_buddy_count, allow_invitation, auto_accept_invitation, created_at, updated_at)
SELECT id, 50, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP() 
FROM tb_user 
WHERE id NOT IN (SELECT user_id FROM tb_buddy_setting);
